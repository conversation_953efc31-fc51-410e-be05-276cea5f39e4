{"version": 3, "file": "WebGLRenderer-BjKGPx6T.js", "sources": ["../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/batcher/gl/GlBatchAdaptor.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/buffer/const.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/buffer/GlBuffer.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/buffer/GlBufferSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/context/GlContextSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/const.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/geometry/utils/getGlTypeFromFormat.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/geometry/GlGeometrySystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/GlBackBufferSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/GlColorMaskSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/GlEncoderSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/GlRenderTarget.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/GlStencilSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/utils/createUboElementsSTD40.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/utils/generateArraySyncSTD40.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/utils/createUboSyncSTD40.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/GlUboSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/renderTarget/GlRenderTargetAdaptor.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/renderTarget/GlRenderTargetSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/GenerateShaderSyncCode.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/GlProgramData.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/program/compileShader.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/program/defaultValue.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/program/mapType.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/program/extractAttributesFromGlProgram.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/program/getUboData.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/program/getUniformData.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/program/logProgramError.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/program/generateProgram.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/GlShaderSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/utils/generateUniformsSyncTypes.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/utils/generateUniformsSync.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/GlUniformGroupSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/state/mapWebGLBlendModesToPixi.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/state/GlStateSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/GlTexture.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/uploaders/glUploadBufferImageResource.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/uploaders/glUploadCompressedTextureResource.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/uploaders/glUploadImageResource.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/uploaders/glUploadVideoResource.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/utils/pixiToGlMaps.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/utils/applyStyleParams.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/utils/mapFormatToGlFormat.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/utils/mapFormatToGlInternalFormat.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/utils/mapFormatToGlType.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/texture/GlTextureSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/graphics/gl/GlGraphicsAdaptor.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/mesh/gl/GlMeshAdaptor.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/WebGLRenderer.mjs"], "sourcesContent": ["import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { State } from '../../renderers/shared/state/State.mjs';\n\n\"use strict\";\nclass GlBatchAdaptor {\n  constructor() {\n    this._tempState = State.for2d();\n    /**\n     * We only want to sync the a batched shaders uniforms once on first use\n     * this is a hash of shader uids to a boolean value.  When the shader is first bound\n     * we set the value to true.  When the shader is bound again we check the value and\n     * if it is true we know that the uniforms have already been synced and we skip it.\n     */\n    this._didUploadHash = {};\n  }\n  init(batcherPipe) {\n    batcherPipe.renderer.runners.contextChange.add(this);\n  }\n  contextChange() {\n    this._didUploadHash = {};\n  }\n  start(batchPipe, geometry, shader) {\n    const renderer = batchPipe.renderer;\n    const didUpload = this._didUploadHash[shader.uid];\n    renderer.shader.bind(shader, didUpload);\n    if (!didUpload) {\n      this._didUploadHash[shader.uid] = true;\n    }\n    renderer.shader.updateUniformGroup(renderer.globalUniforms.uniformGroup);\n    renderer.geometry.bind(geometry, shader.glProgram);\n  }\n  execute(batchPipe, batch) {\n    const renderer = batchPipe.renderer;\n    this._tempState.blendMode = batch.blendMode;\n    renderer.state.set(this._tempState);\n    const textures = batch.textures.textures;\n    for (let i = 0; i < batch.textures.count; i++) {\n      renderer.texture.bind(textures[i], i);\n    }\n    renderer.geometry.draw(batch.topology, batch.size, batch.start);\n  }\n}\n/** @ignore */\nGlBatchAdaptor.extension = {\n  type: [\n    ExtensionType.WebGLPipesAdaptor\n  ],\n  name: \"batch\"\n};\n\nexport { GlBatchAdaptor };\n//# sourceMappingURL=GlBatchAdaptor.mjs.map\n", "\"use strict\";\nvar BUFFER_TYPE = /* @__PURE__ */ ((BUFFER_TYPE2) => {\n  BUFFER_TYPE2[BUFFER_TYPE2[\"ELEMENT_ARRAY_BUFFER\"] = 34963] = \"ELEMENT_ARRAY_BUFFER\";\n  BUFFER_TYPE2[BUFFER_TYPE2[\"ARRAY_BUFFER\"] = 34962] = \"ARRAY_BUFFER\";\n  BUFFER_TYPE2[BUFFER_TYPE2[\"UNIFORM_BUFFER\"] = 35345] = \"UNIFORM_BUFFER\";\n  return BUFFER_TYPE2;\n})(BUFFER_TYPE || {});\n\nexport { BUFFER_TYPE };\n//# sourceMappingURL=const.mjs.map\n", "\"use strict\";\nclass GlBuffer {\n  constructor(buffer, type) {\n    this._lastBindBaseLocation = -1;\n    this._lastBindCallId = -1;\n    this.buffer = buffer || null;\n    this.updateID = -1;\n    this.byteLength = -1;\n    this.type = type;\n  }\n}\n\nexport { GlBuffer };\n//# sourceMappingURL=GlBuffer.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { BufferUsage } from '../../shared/buffer/const.mjs';\nimport { BUFFER_TYPE } from './const.mjs';\nimport { Gl<PERSON>uffer } from './GlBuffer.mjs';\n\n\"use strict\";\nclass GlBufferSystem {\n  /**\n   * @param {Renderer} renderer - The renderer this System works for.\n   */\n  constructor(renderer) {\n    this._gpuBuffers = /* @__PURE__ */ Object.create(null);\n    /** Cache keeping track of the base bound buffer bases */\n    this._boundBufferBases = /* @__PURE__ */ Object.create(null);\n    this._minBaseLocation = 0;\n    this._nextBindBaseIndex = this._minBaseLocation;\n    this._bindCallId = 0;\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuBuffers\");\n  }\n  /**\n   * @ignore\n   */\n  destroy() {\n    this._renderer = null;\n    this._gl = null;\n    this._gpuBuffers = null;\n    this._boundBufferBases = null;\n  }\n  /** Sets up the renderer context and necessary buffers. */\n  contextChange() {\n    const gl = this._gl = this._renderer.gl;\n    this._gpuBuffers = /* @__PURE__ */ Object.create(null);\n    this._maxBindings = gl.MAX_UNIFORM_BUFFER_BINDINGS ? gl.getParameter(gl.MAX_UNIFORM_BUFFER_BINDINGS) : 0;\n  }\n  getGlBuffer(buffer) {\n    return this._gpuBuffers[buffer.uid] || this.createGLBuffer(buffer);\n  }\n  /**\n   * This binds specified buffer. On first run, it will create the webGL buffers for the context too\n   * @param buffer - the buffer to bind to the renderer\n   */\n  bind(buffer) {\n    const { _gl: gl } = this;\n    const glBuffer = this.getGlBuffer(buffer);\n    gl.bindBuffer(glBuffer.type, glBuffer.buffer);\n  }\n  /**\n   * Binds an uniform buffer to at the given index.\n   *\n   * A cache is used so a buffer will not be bound again if already bound.\n   * @param glBuffer - the buffer to bind\n   * @param index - the base index to bind it to.\n   */\n  bindBufferBase(glBuffer, index) {\n    const { _gl: gl } = this;\n    if (this._boundBufferBases[index] !== glBuffer) {\n      this._boundBufferBases[index] = glBuffer;\n      glBuffer._lastBindBaseLocation = index;\n      gl.bindBufferBase(gl.UNIFORM_BUFFER, index, glBuffer.buffer);\n    }\n  }\n  nextBindBase(hasTransformFeedback) {\n    this._bindCallId++;\n    this._minBaseLocation = 0;\n    if (hasTransformFeedback) {\n      this._boundBufferBases[0] = null;\n      this._minBaseLocation = 1;\n      if (this._nextBindBaseIndex < 1) {\n        this._nextBindBaseIndex = 1;\n      }\n    }\n  }\n  freeLocationForBufferBase(glBuffer) {\n    let freeIndex = this.getLastBindBaseLocation(glBuffer);\n    if (freeIndex >= this._minBaseLocation) {\n      glBuffer._lastBindCallId = this._bindCallId;\n      return freeIndex;\n    }\n    let loop = 0;\n    let nextIndex = this._nextBindBaseIndex;\n    while (loop < 2) {\n      if (nextIndex >= this._maxBindings) {\n        nextIndex = this._minBaseLocation;\n        loop++;\n      }\n      const curBuf = this._boundBufferBases[nextIndex];\n      if (curBuf && curBuf._lastBindCallId === this._bindCallId) {\n        nextIndex++;\n        continue;\n      }\n      break;\n    }\n    freeIndex = nextIndex;\n    this._nextBindBaseIndex = nextIndex + 1;\n    if (loop >= 2) {\n      return -1;\n    }\n    glBuffer._lastBindCallId = this._bindCallId;\n    this._boundBufferBases[freeIndex] = null;\n    return freeIndex;\n  }\n  getLastBindBaseLocation(glBuffer) {\n    const index = glBuffer._lastBindBaseLocation;\n    if (this._boundBufferBases[index] === glBuffer) {\n      return index;\n    }\n    return -1;\n  }\n  /**\n   * Binds a buffer whilst also binding its range.\n   * This will make the buffer start from the offset supplied rather than 0 when it is read.\n   * @param glBuffer - the buffer to bind\n   * @param index - the base index to bind at, defaults to 0\n   * @param offset - the offset to bind at (this is blocks of 256). 0 = 0, 1 = 256, 2 = 512 etc\n   * @param size - the size to bind at (this is blocks of 256).\n   */\n  bindBufferRange(glBuffer, index, offset, size) {\n    const { _gl: gl } = this;\n    offset || (offset = 0);\n    index || (index = 0);\n    this._boundBufferBases[index] = null;\n    gl.bindBufferRange(gl.UNIFORM_BUFFER, index || 0, glBuffer.buffer, offset * 256, size || 256);\n  }\n  /**\n   * Will ensure the data in the buffer is uploaded to the GPU.\n   * @param {Buffer} buffer - the buffer to update\n   */\n  updateBuffer(buffer) {\n    const { _gl: gl } = this;\n    const glBuffer = this.getGlBuffer(buffer);\n    if (buffer._updateID === glBuffer.updateID) {\n      return glBuffer;\n    }\n    glBuffer.updateID = buffer._updateID;\n    gl.bindBuffer(glBuffer.type, glBuffer.buffer);\n    const data = buffer.data;\n    const drawType = buffer.descriptor.usage & BufferUsage.STATIC ? gl.STATIC_DRAW : gl.DYNAMIC_DRAW;\n    if (data) {\n      if (glBuffer.byteLength >= data.byteLength) {\n        gl.bufferSubData(glBuffer.type, 0, data, 0, buffer._updateSize / data.BYTES_PER_ELEMENT);\n      } else {\n        glBuffer.byteLength = data.byteLength;\n        gl.bufferData(glBuffer.type, data, drawType);\n      }\n    } else {\n      glBuffer.byteLength = buffer.descriptor.size;\n      gl.bufferData(glBuffer.type, glBuffer.byteLength, drawType);\n    }\n    return glBuffer;\n  }\n  /** dispose all WebGL resources of all managed buffers */\n  destroyAll() {\n    const gl = this._gl;\n    for (const id in this._gpuBuffers) {\n      gl.deleteBuffer(this._gpuBuffers[id].buffer);\n    }\n    this._gpuBuffers = /* @__PURE__ */ Object.create(null);\n  }\n  /**\n   * Disposes buffer\n   * @param {Buffer} buffer - buffer with data\n   * @param {boolean} [contextLost=false] - If context was lost, we suppress deleteVertexArray\n   */\n  onBufferDestroy(buffer, contextLost) {\n    const glBuffer = this._gpuBuffers[buffer.uid];\n    const gl = this._gl;\n    if (!contextLost) {\n      gl.deleteBuffer(glBuffer.buffer);\n    }\n    this._gpuBuffers[buffer.uid] = null;\n  }\n  /**\n   * creates and attaches a GLBuffer object tied to the current context.\n   * @param buffer\n   * @protected\n   */\n  createGLBuffer(buffer) {\n    const { _gl: gl } = this;\n    let type = BUFFER_TYPE.ARRAY_BUFFER;\n    if (buffer.descriptor.usage & BufferUsage.INDEX) {\n      type = BUFFER_TYPE.ELEMENT_ARRAY_BUFFER;\n    } else if (buffer.descriptor.usage & BufferUsage.UNIFORM) {\n      type = BUFFER_TYPE.UNIFORM_BUFFER;\n    }\n    const glBuffer = new GlBuffer(gl.createBuffer(), type);\n    this._gpuBuffers[buffer.uid] = glBuffer;\n    buffer.on(\"destroy\", this.onBufferDestroy, this);\n    return glBuffer;\n  }\n  resetState() {\n    this._boundBufferBases = /* @__PURE__ */ Object.create(null);\n  }\n}\n/** @ignore */\nGlBufferSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"buffer\"\n};\n\nexport { GlBufferSystem };\n//# sourceMappingURL=GlBufferSystem.mjs.map\n", "import { DOMAdapter } from '../../../../environment/adapter.mjs';\nimport { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { warn } from '../../../../utils/logging/warn.mjs';\n\n\"use strict\";\nconst _GlContextSystem = class _GlContextSystem {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    /**\n     * Features supported by current renderer.\n     * @type {object}\n     * @readonly\n     */\n    this.supports = {\n      /** Support for 32-bit indices buffer. */\n      uint32Indices: true,\n      /** Support for UniformBufferObjects */\n      uniformBufferObject: true,\n      /** Support for VertexArrayObjects */\n      vertexArrayObject: true,\n      /** Support for SRGB texture format */\n      srgbTextures: true,\n      /** Support for wrapping modes if a texture is non-power of two */\n      nonPowOf2wrapping: true,\n      /** Support for MSAA (antialiasing of dynamic textures) */\n      msaa: true,\n      /** Support for mipmaps if a texture is non-power of two */\n      nonPowOf2mipmaps: true\n    };\n    this._renderer = renderer;\n    this.extensions = /* @__PURE__ */ Object.create(null);\n    this.handleContextLost = this.handleContextLost.bind(this);\n    this.handleContextRestored = this.handleContextRestored.bind(this);\n  }\n  /**\n   * `true` if the context is lost\n   * @readonly\n   */\n  get isLost() {\n    return !this.gl || this.gl.isContextLost();\n  }\n  /**\n   * Handles the context change event.\n   * @param {WebGLRenderingContext} gl - New WebGL context.\n   */\n  contextChange(gl) {\n    this.gl = gl;\n    this._renderer.gl = gl;\n  }\n  init(options) {\n    options = { ..._GlContextSystem.defaultOptions, ...options };\n    let multiView = this.multiView = options.multiView;\n    if (options.context && multiView) {\n      warn(\"Renderer created with both a context and multiview enabled. Disabling multiView as both cannot work together.\");\n      multiView = false;\n    }\n    if (multiView) {\n      this.canvas = DOMAdapter.get().createCanvas(this._renderer.canvas.width, this._renderer.canvas.height);\n    } else {\n      this.canvas = this._renderer.view.canvas;\n    }\n    if (options.context) {\n      this.initFromContext(options.context);\n    } else {\n      const alpha = this._renderer.background.alpha < 1;\n      const premultipliedAlpha = options.premultipliedAlpha ?? true;\n      const antialias = options.antialias && !this._renderer.backBuffer.useBackBuffer;\n      this.createContext(options.preferWebGLVersion, {\n        alpha,\n        premultipliedAlpha,\n        antialias,\n        stencil: true,\n        preserveDrawingBuffer: options.preserveDrawingBuffer,\n        powerPreference: options.powerPreference ?? \"default\"\n      });\n    }\n  }\n  ensureCanvasSize(targetCanvas) {\n    if (!this.multiView) {\n      if (targetCanvas !== this.canvas) {\n        warn(\"multiView is disabled, but targetCanvas is not the main canvas\");\n      }\n      return;\n    }\n    const { canvas } = this;\n    if (canvas.width < targetCanvas.width || canvas.height < targetCanvas.height) {\n      canvas.width = Math.max(targetCanvas.width, targetCanvas.width);\n      canvas.height = Math.max(targetCanvas.height, targetCanvas.height);\n    }\n  }\n  /**\n   * Initializes the context.\n   * @protected\n   * @param {WebGLRenderingContext} gl - WebGL context\n   */\n  initFromContext(gl) {\n    this.gl = gl;\n    this.webGLVersion = gl instanceof DOMAdapter.get().getWebGLRenderingContext() ? 1 : 2;\n    this.getExtensions();\n    this.validateContext(gl);\n    this._renderer.runners.contextChange.emit(gl);\n    const element = this._renderer.view.canvas;\n    element.addEventListener(\"webglcontextlost\", this.handleContextLost, false);\n    element.addEventListener(\"webglcontextrestored\", this.handleContextRestored, false);\n  }\n  /**\n   * Initialize from context options\n   * @protected\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/getContext\n   * @param preferWebGLVersion\n   * @param {object} options - context attributes\n   */\n  createContext(preferWebGLVersion, options) {\n    let gl;\n    const canvas = this.canvas;\n    if (preferWebGLVersion === 2) {\n      gl = canvas.getContext(\"webgl2\", options);\n    }\n    if (!gl) {\n      gl = canvas.getContext(\"webgl\", options);\n      if (!gl) {\n        throw new Error(\"This browser does not support WebGL. Try using the canvas renderer\");\n      }\n    }\n    this.gl = gl;\n    this.initFromContext(this.gl);\n  }\n  /** Auto-populate the {@link GlContextSystem.extensions extensions}. */\n  getExtensions() {\n    const { gl } = this;\n    const common = {\n      anisotropicFiltering: gl.getExtension(\"EXT_texture_filter_anisotropic\"),\n      floatTextureLinear: gl.getExtension(\"OES_texture_float_linear\"),\n      s3tc: gl.getExtension(\"WEBGL_compressed_texture_s3tc\"),\n      s3tc_sRGB: gl.getExtension(\"WEBGL_compressed_texture_s3tc_srgb\"),\n      // eslint-disable-line camelcase\n      etc: gl.getExtension(\"WEBGL_compressed_texture_etc\"),\n      etc1: gl.getExtension(\"WEBGL_compressed_texture_etc1\"),\n      pvrtc: gl.getExtension(\"WEBGL_compressed_texture_pvrtc\") || gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_pvrtc\"),\n      atc: gl.getExtension(\"WEBGL_compressed_texture_atc\"),\n      astc: gl.getExtension(\"WEBGL_compressed_texture_astc\"),\n      bptc: gl.getExtension(\"EXT_texture_compression_bptc\"),\n      rgtc: gl.getExtension(\"EXT_texture_compression_rgtc\"),\n      loseContext: gl.getExtension(\"WEBGL_lose_context\")\n    };\n    if (this.webGLVersion === 1) {\n      this.extensions = {\n        ...common,\n        drawBuffers: gl.getExtension(\"WEBGL_draw_buffers\"),\n        depthTexture: gl.getExtension(\"WEBGL_depth_texture\"),\n        vertexArrayObject: gl.getExtension(\"OES_vertex_array_object\") || gl.getExtension(\"MOZ_OES_vertex_array_object\") || gl.getExtension(\"WEBKIT_OES_vertex_array_object\"),\n        uint32ElementIndex: gl.getExtension(\"OES_element_index_uint\"),\n        // Floats and half-floats\n        floatTexture: gl.getExtension(\"OES_texture_float\"),\n        floatTextureLinear: gl.getExtension(\"OES_texture_float_linear\"),\n        textureHalfFloat: gl.getExtension(\"OES_texture_half_float\"),\n        textureHalfFloatLinear: gl.getExtension(\"OES_texture_half_float_linear\"),\n        vertexAttribDivisorANGLE: gl.getExtension(\"ANGLE_instanced_arrays\"),\n        srgb: gl.getExtension(\"EXT_sRGB\")\n      };\n    } else {\n      this.extensions = {\n        ...common,\n        colorBufferFloat: gl.getExtension(\"EXT_color_buffer_float\")\n      };\n      const provokeExt = gl.getExtension(\"WEBGL_provoking_vertex\");\n      if (provokeExt) {\n        provokeExt.provokingVertexWEBGL(provokeExt.FIRST_VERTEX_CONVENTION_WEBGL);\n      }\n    }\n  }\n  /**\n   * Handles a lost webgl context\n   * @param {WebGLContextEvent} event - The context lost event.\n   */\n  handleContextLost(event) {\n    event.preventDefault();\n    if (this._contextLossForced) {\n      this._contextLossForced = false;\n      setTimeout(() => {\n        if (this.gl.isContextLost()) {\n          this.extensions.loseContext?.restoreContext();\n        }\n      }, 0);\n    }\n  }\n  /** Handles a restored webgl context. */\n  handleContextRestored() {\n    this._renderer.runners.contextChange.emit(this.gl);\n  }\n  destroy() {\n    const element = this._renderer.view.canvas;\n    this._renderer = null;\n    element.removeEventListener(\"webglcontextlost\", this.handleContextLost);\n    element.removeEventListener(\"webglcontextrestored\", this.handleContextRestored);\n    this.gl.useProgram(null);\n    this.extensions.loseContext?.loseContext();\n  }\n  /**\n   * this function can be called to force a webGL context loss\n   * this will release all resources on the GPU.\n   * Useful if you need to put Pixi to sleep, and save some GPU memory\n   *\n   * As soon as render is called - all resources will be created again.\n   */\n  forceContextLoss() {\n    this.extensions.loseContext?.loseContext();\n    this._contextLossForced = true;\n  }\n  /**\n   * Validate context.\n   * @param {WebGLRenderingContext} gl - Render context.\n   */\n  validateContext(gl) {\n    const attributes = gl.getContextAttributes();\n    if (attributes && !attributes.stencil) {\n      warn(\"Provided WebGL context does not have a stencil buffer, masks may not render correctly\");\n    }\n    const supports = this.supports;\n    const isWebGl2 = this.webGLVersion === 2;\n    const extensions = this.extensions;\n    supports.uint32Indices = isWebGl2 || !!extensions.uint32ElementIndex;\n    supports.uniformBufferObject = isWebGl2;\n    supports.vertexArrayObject = isWebGl2 || !!extensions.vertexArrayObject;\n    supports.srgbTextures = isWebGl2 || !!extensions.srgb;\n    supports.nonPowOf2wrapping = isWebGl2;\n    supports.nonPowOf2mipmaps = isWebGl2;\n    supports.msaa = isWebGl2;\n    if (!supports.uint32Indices) {\n      warn(\"Provided WebGL context does not support 32 index buffer, large scenes may not render correctly\");\n    }\n  }\n};\n/** @ignore */\n_GlContextSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"context\"\n};\n/** The default options for the system. */\n_GlContextSystem.defaultOptions = {\n  /**\n   * {@link WebGLOptions.context}\n   * @default null\n   */\n  context: null,\n  /**\n   * {@link WebGLOptions.premultipliedAlpha}\n   * @default true\n   */\n  premultipliedAlpha: true,\n  /**\n   * {@link WebGLOptions.preserveDrawingBuffer}\n   * @default false\n   */\n  preserveDrawingBuffer: false,\n  /**\n   * {@link WebGLOptions.powerPreference}\n   * @default default\n   */\n  powerPreference: void 0,\n  /**\n   * {@link WebGLOptions.webGLVersion}\n   * @default 2\n   */\n  preferWebGLVersion: 2,\n  /**\n   * {@link WebGLOptions.multiView}\n   * @default false\n   */\n  multiView: false\n};\nlet GlContextSystem = _GlContextSystem;\n\nexport { GlContextSystem };\n//# sourceMappingURL=GlContextSystem.mjs.map\n", "\"use strict\";\nvar GL_FORMATS = /* @__PURE__ */ ((GL_FORMATS2) => {\n  GL_FORMATS2[GL_FORMATS2[\"RGBA\"] = 6408] = \"RGBA\";\n  GL_FORMATS2[GL_FORMATS2[\"RGB\"] = 6407] = \"RGB\";\n  GL_FORMATS2[GL_FORMATS2[\"RG\"] = 33319] = \"RG\";\n  GL_FORMATS2[GL_FORMATS2[\"RED\"] = 6403] = \"RED\";\n  GL_FORMATS2[GL_FORMATS2[\"RGBA_INTEGER\"] = 36249] = \"RGBA_INTEGER\";\n  GL_FORMATS2[GL_FORMATS2[\"RGB_INTEGER\"] = 36248] = \"RGB_INTEGER\";\n  GL_FORMATS2[GL_FORMATS2[\"RG_INTEGER\"] = 33320] = \"RG_INTEGER\";\n  GL_FORMATS2[GL_FORMATS2[\"RED_INTEGER\"] = 36244] = \"RED_INTEGER\";\n  GL_FORMATS2[GL_FORMATS2[\"ALPHA\"] = 6406] = \"ALPHA\";\n  GL_FORMATS2[GL_FORMATS2[\"LUMINANCE\"] = 6409] = \"LUMINANCE\";\n  GL_FORMATS2[GL_FORMATS2[\"LUMINANCE_ALPHA\"] = 6410] = \"LUMINANCE_ALPHA\";\n  GL_FORMATS2[GL_FORMATS2[\"DEPTH_COMPONENT\"] = 6402] = \"DEPTH_COMPONENT\";\n  GL_FORMATS2[GL_FORMATS2[\"DEPTH_STENCIL\"] = 34041] = \"DEPTH_STENCIL\";\n  return GL_FORMATS2;\n})(GL_FORMATS || {});\nvar GL_TARGETS = /* @__PURE__ */ ((GL_TARGETS2) => {\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_2D\"] = 3553] = \"TEXTURE_2D\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP\"] = 34067] = \"TEXTURE_CUBE_MAP\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_2D_ARRAY\"] = 35866] = \"TEXTURE_2D_ARRAY\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_POSITIVE_X\"] = 34069] = \"TEXTURE_CUBE_MAP_POSITIVE_X\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_NEGATIVE_X\"] = 34070] = \"TEXTURE_CUBE_MAP_NEGATIVE_X\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_POSITIVE_Y\"] = 34071] = \"TEXTURE_CUBE_MAP_POSITIVE_Y\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_NEGATIVE_Y\"] = 34072] = \"TEXTURE_CUBE_MAP_NEGATIVE_Y\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_POSITIVE_Z\"] = 34073] = \"TEXTURE_CUBE_MAP_POSITIVE_Z\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_NEGATIVE_Z\"] = 34074] = \"TEXTURE_CUBE_MAP_NEGATIVE_Z\";\n  return GL_TARGETS2;\n})(GL_TARGETS || {});\nvar GL_WRAP_MODES = /* @__PURE__ */ ((GL_WRAP_MODES2) => {\n  GL_WRAP_MODES2[GL_WRAP_MODES2[\"CLAMP\"] = 33071] = \"CLAMP\";\n  GL_WRAP_MODES2[GL_WRAP_MODES2[\"REPEAT\"] = 10497] = \"REPEAT\";\n  GL_WRAP_MODES2[GL_WRAP_MODES2[\"MIRRORED_REPEAT\"] = 33648] = \"MIRRORED_REPEAT\";\n  return GL_WRAP_MODES2;\n})(GL_WRAP_MODES || {});\nvar GL_TYPES = /* @__PURE__ */ ((GL_TYPES2) => {\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_BYTE\"] = 5121] = \"UNSIGNED_BYTE\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_SHORT\"] = 5123] = \"UNSIGNED_SHORT\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_SHORT_5_6_5\"] = 33635] = \"UNSIGNED_SHORT_5_6_5\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_SHORT_4_4_4_4\"] = 32819] = \"UNSIGNED_SHORT_4_4_4_4\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_SHORT_5_5_5_1\"] = 32820] = \"UNSIGNED_SHORT_5_5_5_1\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_INT\"] = 5125] = \"UNSIGNED_INT\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_INT_10F_11F_11F_REV\"] = 35899] = \"UNSIGNED_INT_10F_11F_11F_REV\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_INT_2_10_10_10_REV\"] = 33640] = \"UNSIGNED_INT_2_10_10_10_REV\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_INT_24_8\"] = 34042] = \"UNSIGNED_INT_24_8\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_INT_5_9_9_9_REV\"] = 35902] = \"UNSIGNED_INT_5_9_9_9_REV\";\n  GL_TYPES2[GL_TYPES2[\"BYTE\"] = 5120] = \"BYTE\";\n  GL_TYPES2[GL_TYPES2[\"SHORT\"] = 5122] = \"SHORT\";\n  GL_TYPES2[GL_TYPES2[\"INT\"] = 5124] = \"INT\";\n  GL_TYPES2[GL_TYPES2[\"FLOAT\"] = 5126] = \"FLOAT\";\n  GL_TYPES2[GL_TYPES2[\"FLOAT_32_UNSIGNED_INT_24_8_REV\"] = 36269] = \"FLOAT_32_UNSIGNED_INT_24_8_REV\";\n  GL_TYPES2[GL_TYPES2[\"HALF_FLOAT\"] = 36193] = \"HALF_FLOAT\";\n  return GL_TYPES2;\n})(GL_TYPES || {});\n\nexport { GL_FORMATS, GL_TARGETS, GL_TYPES, GL_WRAP_MODES };\n//# sourceMappingURL=const.mjs.map\n", "import { GL_TYPES } from '../../texture/const.mjs';\n\n\"use strict\";\nconst infoMap = {\n  uint8x2: GL_TYPES.UNSIGNED_BYTE,\n  uint8x4: GL_TYPES.UNSIGNED_BYTE,\n  sint8x2: GL_TYPES.BYTE,\n  sint8x4: GL_TYPES.BYTE,\n  unorm8x2: GL_TYPES.UNSIGNED_BYTE,\n  unorm8x4: GL_TYPES.UNSIGNED_BYTE,\n  snorm8x2: GL_TYPES.BYTE,\n  snorm8x4: GL_TYPES.BYTE,\n  uint16x2: GL_TYPES.UNSIGNED_SHORT,\n  uint16x4: GL_TYPES.UNSIGNED_SHORT,\n  sint16x2: GL_TYPES.SHORT,\n  sint16x4: GL_TYPES.SHORT,\n  unorm16x2: GL_TYPES.UNSIGNED_SHORT,\n  unorm16x4: GL_TYPES.UNSIGNED_SHORT,\n  snorm16x2: GL_TYPES.SHORT,\n  snorm16x4: GL_TYPES.SHORT,\n  float16x2: GL_TYPES.HALF_FLOAT,\n  float16x4: GL_TYPES.HALF_FLOAT,\n  float32: GL_TYPES.FLOAT,\n  float32x2: GL_TYPES.FLOAT,\n  float32x3: GL_TYPES.FLOAT,\n  float32x4: GL_TYPES.FLOAT,\n  uint32: GL_TYPES.UNSIGNED_INT,\n  uint32x2: GL_TYPES.UNSIGNED_INT,\n  uint32x3: GL_TYPES.UNSIGNED_INT,\n  uint32x4: GL_TYPES.UNSIGNED_INT,\n  sint32: GL_TYPES.INT,\n  sint32x2: GL_TYPES.INT,\n  sint32x3: GL_TYPES.INT,\n  sint32x4: GL_TYPES.INT\n};\nfunction getGlTypeFromFormat(format) {\n  return infoMap[format] ?? infoMap.float32;\n}\n\nexport { getGlTypeFromFormat };\n//# sourceMappingURL=getGlTypeFromFormat.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { getAttributeInfoFromFormat } from '../../shared/geometry/utils/getAttributeInfoFromFormat.mjs';\nimport { ensureAttributes } from '../shader/program/ensureAttributes.mjs';\nimport { getGlTypeFromFormat } from './utils/getGlTypeFromFormat.mjs';\n\n\"use strict\";\nconst topologyToGlMap = {\n  \"point-list\": 0,\n  \"line-list\": 1,\n  \"line-strip\": 3,\n  \"triangle-list\": 4,\n  \"triangle-strip\": 5\n};\nclass GlGeometrySystem {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this._geometryVaoHash = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n    this._activeGeometry = null;\n    this._activeVao = null;\n    this.hasVao = true;\n    this.hasInstance = true;\n    this._renderer.renderableGC.addManagedHash(this, \"_geometryVaoHash\");\n  }\n  /** Sets up the renderer context and necessary buffers. */\n  contextChange() {\n    const gl = this.gl = this._renderer.gl;\n    if (!this._renderer.context.supports.vertexArrayObject) {\n      throw new Error(\"[PixiJS] Vertex Array Objects are not supported on this device\");\n    }\n    const nativeVaoExtension = this._renderer.context.extensions.vertexArrayObject;\n    if (nativeVaoExtension) {\n      gl.createVertexArray = () => nativeVaoExtension.createVertexArrayOES();\n      gl.bindVertexArray = (vao) => nativeVaoExtension.bindVertexArrayOES(vao);\n      gl.deleteVertexArray = (vao) => nativeVaoExtension.deleteVertexArrayOES(vao);\n    }\n    const nativeInstancedExtension = this._renderer.context.extensions.vertexAttribDivisorANGLE;\n    if (nativeInstancedExtension) {\n      gl.drawArraysInstanced = (a, b, c, d) => {\n        nativeInstancedExtension.drawArraysInstancedANGLE(a, b, c, d);\n      };\n      gl.drawElementsInstanced = (a, b, c, d, e) => {\n        nativeInstancedExtension.drawElementsInstancedANGLE(a, b, c, d, e);\n      };\n      gl.vertexAttribDivisor = (a, b) => nativeInstancedExtension.vertexAttribDivisorANGLE(a, b);\n    }\n    this._activeGeometry = null;\n    this._activeVao = null;\n    this._geometryVaoHash = /* @__PURE__ */ Object.create(null);\n  }\n  /**\n   * Binds geometry so that is can be drawn. Creating a Vao if required\n   * @param geometry - Instance of geometry to bind.\n   * @param program - Instance of program to use vao for.\n   */\n  bind(geometry, program) {\n    const gl = this.gl;\n    this._activeGeometry = geometry;\n    const vao = this.getVao(geometry, program);\n    if (this._activeVao !== vao) {\n      this._activeVao = vao;\n      gl.bindVertexArray(vao);\n    }\n    this.updateBuffers();\n  }\n  /** Reset and unbind any active VAO and geometry. */\n  resetState() {\n    this.unbind();\n  }\n  /** Update buffers of the currently bound geometry. */\n  updateBuffers() {\n    const geometry = this._activeGeometry;\n    const bufferSystem = this._renderer.buffer;\n    for (let i = 0; i < geometry.buffers.length; i++) {\n      const buffer = geometry.buffers[i];\n      bufferSystem.updateBuffer(buffer);\n    }\n  }\n  /**\n   * Check compatibility between a geometry and a program\n   * @param geometry - Geometry instance.\n   * @param program - Program instance.\n   */\n  checkCompatibility(geometry, program) {\n    const geometryAttributes = geometry.attributes;\n    const shaderAttributes = program._attributeData;\n    for (const j in shaderAttributes) {\n      if (!geometryAttributes[j]) {\n        throw new Error(`shader and geometry incompatible, geometry missing the \"${j}\" attribute`);\n      }\n    }\n  }\n  /**\n   * Takes a geometry and program and generates a unique signature for them.\n   * @param geometry - To get signature from.\n   * @param program - To test geometry against.\n   * @returns - Unique signature of the geometry and program\n   */\n  getSignature(geometry, program) {\n    const attribs = geometry.attributes;\n    const shaderAttributes = program._attributeData;\n    const strings = [\"g\", geometry.uid];\n    for (const i in attribs) {\n      if (shaderAttributes[i]) {\n        strings.push(i, shaderAttributes[i].location);\n      }\n    }\n    return strings.join(\"-\");\n  }\n  getVao(geometry, program) {\n    return this._geometryVaoHash[geometry.uid]?.[program._key] || this.initGeometryVao(geometry, program);\n  }\n  /**\n   * Creates or gets Vao with the same structure as the geometry and stores it on the geometry.\n   * If vao is created, it is bound automatically. We use a shader to infer what and how to set up the\n   * attribute locations.\n   * @param geometry - Instance of geometry to to generate Vao for.\n   * @param program\n   * @param _incRefCount - Increment refCount of all geometry buffers.\n   */\n  initGeometryVao(geometry, program, _incRefCount = true) {\n    const gl = this._renderer.gl;\n    const bufferSystem = this._renderer.buffer;\n    this._renderer.shader._getProgramData(program);\n    this.checkCompatibility(geometry, program);\n    const signature = this.getSignature(geometry, program);\n    if (!this._geometryVaoHash[geometry.uid]) {\n      this._geometryVaoHash[geometry.uid] = /* @__PURE__ */ Object.create(null);\n      geometry.on(\"destroy\", this.onGeometryDestroy, this);\n    }\n    const vaoObjectHash = this._geometryVaoHash[geometry.uid];\n    let vao = vaoObjectHash[signature];\n    if (vao) {\n      vaoObjectHash[program._key] = vao;\n      return vao;\n    }\n    ensureAttributes(geometry, program._attributeData);\n    const buffers = geometry.buffers;\n    vao = gl.createVertexArray();\n    gl.bindVertexArray(vao);\n    for (let i = 0; i < buffers.length; i++) {\n      const buffer = buffers[i];\n      bufferSystem.bind(buffer);\n    }\n    this.activateVao(geometry, program);\n    vaoObjectHash[program._key] = vao;\n    vaoObjectHash[signature] = vao;\n    gl.bindVertexArray(null);\n    return vao;\n  }\n  /**\n   * Disposes geometry.\n   * @param geometry - Geometry with buffers. Only VAO will be disposed\n   * @param [contextLost=false] - If context was lost, we suppress deleteVertexArray\n   */\n  onGeometryDestroy(geometry, contextLost) {\n    const vaoObjectHash = this._geometryVaoHash[geometry.uid];\n    const gl = this.gl;\n    if (vaoObjectHash) {\n      if (contextLost) {\n        for (const i in vaoObjectHash) {\n          if (this._activeVao !== vaoObjectHash[i]) {\n            this.unbind();\n          }\n          gl.deleteVertexArray(vaoObjectHash[i]);\n        }\n      }\n      this._geometryVaoHash[geometry.uid] = null;\n    }\n  }\n  /**\n   * Dispose all WebGL resources of all managed geometries.\n   * @param [contextLost=false] - If context was lost, we suppress `gl.delete` calls\n   */\n  destroyAll(contextLost = false) {\n    const gl = this.gl;\n    for (const i in this._geometryVaoHash) {\n      if (contextLost) {\n        for (const j in this._geometryVaoHash[i]) {\n          const vaoObjectHash = this._geometryVaoHash[i];\n          if (this._activeVao !== vaoObjectHash) {\n            this.unbind();\n          }\n          gl.deleteVertexArray(vaoObjectHash[j]);\n        }\n      }\n      this._geometryVaoHash[i] = null;\n    }\n  }\n  /**\n   * Activate vertex array object.\n   * @param geometry - Geometry instance.\n   * @param program - Shader program instance.\n   */\n  activateVao(geometry, program) {\n    const gl = this._renderer.gl;\n    const bufferSystem = this._renderer.buffer;\n    const attributes = geometry.attributes;\n    if (geometry.indexBuffer) {\n      bufferSystem.bind(geometry.indexBuffer);\n    }\n    let lastBuffer = null;\n    for (const j in attributes) {\n      const attribute = attributes[j];\n      const buffer = attribute.buffer;\n      const glBuffer = bufferSystem.getGlBuffer(buffer);\n      const programAttrib = program._attributeData[j];\n      if (programAttrib) {\n        if (lastBuffer !== glBuffer) {\n          bufferSystem.bind(buffer);\n          lastBuffer = glBuffer;\n        }\n        const location = programAttrib.location;\n        gl.enableVertexAttribArray(location);\n        const attributeInfo = getAttributeInfoFromFormat(attribute.format);\n        const type = getGlTypeFromFormat(attribute.format);\n        if (programAttrib.format?.substring(1, 4) === \"int\") {\n          gl.vertexAttribIPointer(\n            location,\n            attributeInfo.size,\n            type,\n            attribute.stride,\n            attribute.offset\n          );\n        } else {\n          gl.vertexAttribPointer(\n            location,\n            attributeInfo.size,\n            type,\n            attributeInfo.normalised,\n            attribute.stride,\n            attribute.offset\n          );\n        }\n        if (attribute.instance) {\n          if (this.hasInstance) {\n            const divisor = attribute.divisor ?? 1;\n            gl.vertexAttribDivisor(location, divisor);\n          } else {\n            throw new Error(\"geometry error, GPU Instancing is not supported on this device\");\n          }\n        }\n      }\n    }\n  }\n  /**\n   * Draws the currently bound geometry.\n   * @param topology - The type primitive to render.\n   * @param size - The number of elements to be rendered. If not specified, all vertices after the\n   *  starting vertex will be drawn.\n   * @param start - The starting vertex in the geometry to start drawing from. If not specified,\n   *  drawing will start from the first vertex.\n   * @param instanceCount - The number of instances of the set of elements to execute. If not specified,\n   *  all instances will be drawn.\n   */\n  draw(topology, size, start, instanceCount) {\n    const { gl } = this._renderer;\n    const geometry = this._activeGeometry;\n    const glTopology = topologyToGlMap[topology || geometry.topology];\n    instanceCount ?? (instanceCount = geometry.instanceCount);\n    if (geometry.indexBuffer) {\n      const byteSize = geometry.indexBuffer.data.BYTES_PER_ELEMENT;\n      const glType = byteSize === 2 ? gl.UNSIGNED_SHORT : gl.UNSIGNED_INT;\n      if (instanceCount > 1) {\n        gl.drawElementsInstanced(glTopology, size || geometry.indexBuffer.data.length, glType, (start || 0) * byteSize, instanceCount);\n      } else {\n        gl.drawElements(glTopology, size || geometry.indexBuffer.data.length, glType, (start || 0) * byteSize);\n      }\n    } else if (instanceCount > 1) {\n      gl.drawArraysInstanced(glTopology, start || 0, size || geometry.getSize(), instanceCount);\n    } else {\n      gl.drawArrays(glTopology, start || 0, size || geometry.getSize());\n    }\n    return this;\n  }\n  /** Unbind/reset everything. */\n  unbind() {\n    this.gl.bindVertexArray(null);\n    this._activeVao = null;\n    this._activeGeometry = null;\n  }\n  destroy() {\n    this._renderer = null;\n    this.gl = null;\n    this._activeVao = null;\n    this._activeGeometry = null;\n  }\n}\n/** @ignore */\nGlGeometrySystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"geometry\"\n};\n\nexport { GlGeometrySystem };\n//# sourceMappingURL=GlGeometrySystem.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { warn } from '../../../utils/logging/warn.mjs';\nimport { Geometry } from '../shared/geometry/Geometry.mjs';\nimport { Shader } from '../shared/shader/Shader.mjs';\nimport { State } from '../shared/state/State.mjs';\nimport { TextureSource } from '../shared/texture/sources/TextureSource.mjs';\nimport { Texture } from '../shared/texture/Texture.mjs';\nimport { GlProgram } from './shader/GlProgram.mjs';\n\n\"use strict\";\nconst bigTriangleGeometry = new Geometry({\n  attributes: {\n    aPosition: [\n      -1,\n      -1,\n      // Bottom left corner\n      3,\n      -1,\n      // Bottom right corner, extending beyond right edge\n      -1,\n      3\n      // Top left corner, extending beyond top edge\n    ]\n  }\n});\nconst _GlBackBufferSystem = class _GlBackBufferSystem {\n  constructor(renderer) {\n    /** if true, the back buffer is used */\n    this.useBackBuffer = false;\n    this._useBackBufferThisRender = false;\n    this._renderer = renderer;\n  }\n  init(options = {}) {\n    const { useBackBuffer, antialias } = { ..._GlBackBufferSystem.defaultOptions, ...options };\n    this.useBackBuffer = useBackBuffer;\n    this._antialias = antialias;\n    if (!this._renderer.context.supports.msaa) {\n      warn(\"antialiasing, is not supported on when using the back buffer\");\n      this._antialias = false;\n    }\n    this._state = State.for2d();\n    const bigTriangleProgram = new GlProgram({\n      vertex: `\n                attribute vec2 aPosition;\n                out vec2 vUv;\n\n                void main() {\n                    gl_Position = vec4(aPosition, 0.0, 1.0);\n\n                    vUv = (aPosition + 1.0) / 2.0;\n\n                    // flip dem UVs\n                    vUv.y = 1.0 - vUv.y;\n                }`,\n      fragment: `\n                in vec2 vUv;\n                out vec4 finalColor;\n\n                uniform sampler2D uTexture;\n\n                void main() {\n                    finalColor = texture(uTexture, vUv);\n                }`,\n      name: \"big-triangle\"\n    });\n    this._bigTriangleShader = new Shader({\n      glProgram: bigTriangleProgram,\n      resources: {\n        uTexture: Texture.WHITE.source\n      }\n    });\n  }\n  /**\n   * This is called before the RenderTargetSystem is started. This is where\n   * we replace the target with the back buffer if required.\n   * @param options - The options for this render.\n   */\n  renderStart(options) {\n    const renderTarget = this._renderer.renderTarget.getRenderTarget(options.target);\n    this._useBackBufferThisRender = this.useBackBuffer && !!renderTarget.isRoot;\n    if (this._useBackBufferThisRender) {\n      const renderTarget2 = this._renderer.renderTarget.getRenderTarget(options.target);\n      this._targetTexture = renderTarget2.colorTexture;\n      options.target = this._getBackBufferTexture(renderTarget2.colorTexture);\n    }\n  }\n  renderEnd() {\n    this._presentBackBuffer();\n  }\n  _presentBackBuffer() {\n    const renderer = this._renderer;\n    renderer.renderTarget.finishRenderPass();\n    if (!this._useBackBufferThisRender)\n      return;\n    renderer.renderTarget.bind(this._targetTexture, false);\n    this._bigTriangleShader.resources.uTexture = this._backBufferTexture.source;\n    renderer.encoder.draw({\n      geometry: bigTriangleGeometry,\n      shader: this._bigTriangleShader,\n      state: this._state\n    });\n  }\n  _getBackBufferTexture(targetSourceTexture) {\n    this._backBufferTexture = this._backBufferTexture || new Texture({\n      source: new TextureSource({\n        width: targetSourceTexture.width,\n        height: targetSourceTexture.height,\n        resolution: targetSourceTexture._resolution,\n        antialias: this._antialias\n      })\n    });\n    this._backBufferTexture.source.resize(\n      targetSourceTexture.width,\n      targetSourceTexture.height,\n      targetSourceTexture._resolution\n    );\n    return this._backBufferTexture;\n  }\n  /** destroys the back buffer */\n  destroy() {\n    if (this._backBufferTexture) {\n      this._backBufferTexture.destroy();\n      this._backBufferTexture = null;\n    }\n  }\n};\n/** @ignore */\n_GlBackBufferSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"backBuffer\",\n  priority: 1\n};\n/** default options for the back buffer system */\n_GlBackBufferSystem.defaultOptions = {\n  /** if true will use the back buffer where required */\n  useBackBuffer: false\n};\nlet GlBackBufferSystem = _GlBackBufferSystem;\n\nexport { GlBackBufferSystem };\n//# sourceMappingURL=GlBackBufferSystem.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\n\n\"use strict\";\nclass GlColorMaskSystem {\n  constructor(renderer) {\n    this._colorMaskCache = 15;\n    this._renderer = renderer;\n  }\n  setMask(colorMask) {\n    if (this._colorMaskCache === colorMask)\n      return;\n    this._colorMaskCache = colorMask;\n    this._renderer.gl.colorMask(\n      !!(colorMask & 8),\n      !!(colorMask & 4),\n      !!(colorMask & 2),\n      !!(colorMask & 1)\n    );\n  }\n}\n/** @ignore */\nGlColorMaskSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"colorMask\"\n};\n\nexport { GlColorMaskSystem };\n//# sourceMappingURL=GlColorMaskSystem.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\n\n\"use strict\";\nclass GlEncoderSystem {\n  constructor(renderer) {\n    this.commandFinished = Promise.resolve();\n    this._renderer = renderer;\n  }\n  setGeometry(geometry, shader) {\n    this._renderer.geometry.bind(geometry, shader.glProgram);\n  }\n  finishRenderPass() {\n  }\n  draw(options) {\n    const renderer = this._renderer;\n    const { geometry, shader, state, skipSync, topology: type, size, start, instanceCount } = options;\n    renderer.shader.bind(shader, skipSync);\n    renderer.geometry.bind(geometry, renderer.shader._activeProgram);\n    if (state) {\n      renderer.state.set(state);\n    }\n    renderer.geometry.draw(type, size, start, instanceCount ?? geometry.instanceCount);\n  }\n  destroy() {\n    this._renderer = null;\n  }\n}\n/** @ignore */\nGlEncoderSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"encoder\"\n};\n\nexport { GlEncoderSystem };\n//# sourceMappingURL=GlEncoderSystem.mjs.map\n", "\"use strict\";\nclass GlRenderTarget {\n  constructor() {\n    this.width = -1;\n    this.height = -1;\n    this.msaa = false;\n    this.msaaRenderBuffer = [];\n  }\n}\n\nexport { GlRenderTarget };\n//# sourceMappingURL=GlRenderTarget.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { GpuStencilModesToPixi } from '../gpu/state/GpuStencilModesToPixi.mjs';\nimport { STENCIL_MODES } from '../shared/state/const.mjs';\n\n\"use strict\";\nclass GlStencilSystem {\n  constructor(renderer) {\n    this._stencilCache = {\n      enabled: false,\n      stencilReference: 0,\n      stencilMode: STENCIL_MODES.NONE\n    };\n    this._renderTargetStencilState = /* @__PURE__ */ Object.create(null);\n    renderer.renderTarget.onRenderTargetChange.add(this);\n  }\n  contextChange(gl) {\n    this._gl = gl;\n    this._comparisonFuncMapping = {\n      always: gl.ALWAYS,\n      never: gl.NEVER,\n      equal: gl.EQUAL,\n      \"not-equal\": gl.NOTEQUAL,\n      less: gl.LESS,\n      \"less-equal\": gl.LEQUAL,\n      greater: gl.GREATER,\n      \"greater-equal\": gl.GEQUAL\n    };\n    this._stencilOpsMapping = {\n      keep: gl.KEEP,\n      zero: gl.ZERO,\n      replace: gl.REPLACE,\n      invert: gl.INVERT,\n      \"increment-clamp\": gl.INCR,\n      \"decrement-clamp\": gl.DECR,\n      \"increment-wrap\": gl.INCR_WRAP,\n      \"decrement-wrap\": gl.DECR_WRAP\n    };\n    this._stencilCache.enabled = false;\n    this._stencilCache.stencilMode = STENCIL_MODES.NONE;\n    this._stencilCache.stencilReference = 0;\n  }\n  onRenderTargetChange(renderTarget) {\n    if (this._activeRenderTarget === renderTarget)\n      return;\n    this._activeRenderTarget = renderTarget;\n    let stencilState = this._renderTargetStencilState[renderTarget.uid];\n    if (!stencilState) {\n      stencilState = this._renderTargetStencilState[renderTarget.uid] = {\n        stencilMode: STENCIL_MODES.DISABLED,\n        stencilReference: 0\n      };\n    }\n    this.setStencilMode(stencilState.stencilMode, stencilState.stencilReference);\n  }\n  setStencilMode(stencilMode, stencilReference) {\n    const stencilState = this._renderTargetStencilState[this._activeRenderTarget.uid];\n    const gl = this._gl;\n    const mode = GpuStencilModesToPixi[stencilMode];\n    const _stencilCache = this._stencilCache;\n    stencilState.stencilMode = stencilMode;\n    stencilState.stencilReference = stencilReference;\n    if (stencilMode === STENCIL_MODES.DISABLED) {\n      if (this._stencilCache.enabled) {\n        this._stencilCache.enabled = false;\n        gl.disable(gl.STENCIL_TEST);\n      }\n      return;\n    }\n    if (!this._stencilCache.enabled) {\n      this._stencilCache.enabled = true;\n      gl.enable(gl.STENCIL_TEST);\n    }\n    if (stencilMode !== _stencilCache.stencilMode || _stencilCache.stencilReference !== stencilReference) {\n      _stencilCache.stencilMode = stencilMode;\n      _stencilCache.stencilReference = stencilReference;\n      gl.stencilFunc(this._comparisonFuncMapping[mode.stencilBack.compare], stencilReference, 255);\n      gl.stencilOp(gl.KEEP, gl.KEEP, this._stencilOpsMapping[mode.stencilBack.passOp]);\n    }\n  }\n}\n/** @ignore */\nGlStencilSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"stencil\"\n};\n\nexport { GlStencilSystem };\n//# sourceMappingURL=GlStencilSystem.mjs.map\n", "\"use strict\";\nconst WGSL_TO_STD40_SIZE = {\n  f32: 4,\n  i32: 4,\n  \"vec2<f32>\": 8,\n  \"vec3<f32>\": 12,\n  \"vec4<f32>\": 16,\n  \"vec2<i32>\": 8,\n  \"vec3<i32>\": 12,\n  \"vec4<i32>\": 16,\n  \"mat2x2<f32>\": 16 * 2,\n  \"mat3x3<f32>\": 16 * 3,\n  \"mat4x4<f32>\": 16 * 4\n  // TODO - not essential for now but support these in the future\n  // int:      4,\n  // ivec2:    8,\n  // ivec3:    12,\n  // ivec4:    16,\n  // uint:     4,\n  // uvec2:    8,\n  // uvec3:    12,\n  // uvec4:    16,\n  // bool:     4,\n  // bvec2:    8,\n  // bvec3:    12,\n  // bvec4:    16,\n  // mat2:     16 * 2,\n  // mat3:     16 * 3,\n  // mat4:     16 * 4,\n};\nfunction createUboElementsSTD40(uniformData) {\n  const uboElements = uniformData.map((data) => ({\n    data,\n    offset: 0,\n    size: 0\n  }));\n  const chunkSize = 16;\n  let size = 0;\n  let offset = 0;\n  for (let i = 0; i < uboElements.length; i++) {\n    const uboElement = uboElements[i];\n    size = WGSL_TO_STD40_SIZE[uboElement.data.type];\n    if (!size) {\n      throw new Error(`Unknown type ${uboElement.data.type}`);\n    }\n    if (uboElement.data.size > 1) {\n      size = Math.max(size, chunkSize) * uboElement.data.size;\n    }\n    const boundary = size === 12 ? 16 : size;\n    uboElement.size = size;\n    const curOffset = offset % chunkSize;\n    if (curOffset > 0 && chunkSize - curOffset < boundary) {\n      offset += (chunkSize - curOffset) % 16;\n    } else {\n      offset += (size - curOffset % size) % size;\n    }\n    uboElement.offset = offset;\n    offset += size;\n  }\n  offset = Math.ceil(offset / 16) * 16;\n  return { uboElements, size: offset };\n}\n\nexport { WGSL_TO_STD40_SIZE, createUboElementsSTD40 };\n//# sourceMappingURL=createUboElementsSTD40.mjs.map\n", "import { WGSL_TO_STD40_SIZE } from './createUboElementsSTD40.mjs';\n\n\"use strict\";\nfunction generateArraySyncSTD40(uboElement, offsetToAdd) {\n  const rowSize = Math.max(WGSL_TO_STD40_SIZE[uboElement.data.type] / 16, 1);\n  const elementSize = uboElement.data.value.length / uboElement.data.size;\n  const remainder = (4 - elementSize % 4) % 4;\n  const data = uboElement.data.type.indexOf(\"i32\") >= 0 ? \"dataInt32\" : \"data\";\n  return `\n        v = uv.${uboElement.data.name};\n        offset += ${offsetToAdd};\n\n        arrayOffset = offset;\n\n        t = 0;\n\n        for(var i=0; i < ${uboElement.data.size * rowSize}; i++)\n        {\n            for(var j = 0; j < ${elementSize}; j++)\n            {\n                ${data}[arrayOffset++] = v[t++];\n            }\n            ${remainder !== 0 ? `arrayOffset += ${remainder};` : \"\"}\n        }\n    `;\n}\n\nexport { generateArraySyncSTD40 };\n//# sourceMappingURL=generateArraySyncSTD40.mjs.map\n", "import { createUboSyncFunction } from '../../../shared/shader/utils/createUboSyncFunction.mjs';\nimport { uboSyncFunctionsSTD40 } from '../../../shared/shader/utils/uboSyncFunctions.mjs';\nimport { generateArraySyncSTD40 } from './generateArraySyncSTD40.mjs';\n\n\"use strict\";\nfunction createUboSyncFunctionSTD40(uboElements) {\n  return createUboSyncFunction(\n    uboElements,\n    \"uboStd40\",\n    generateArraySyncSTD40,\n    uboSyncFunctionsSTD40\n  );\n}\n\nexport { createUboSyncFunctionSTD40 };\n//# sourceMappingURL=createUboSyncSTD40.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { UboSystem } from '../shared/shader/UboSystem.mjs';\nimport { createUboElementsSTD40 } from './shader/utils/createUboElementsSTD40.mjs';\nimport { createUboSyncFunctionSTD40 } from './shader/utils/createUboSyncSTD40.mjs';\n\n\"use strict\";\nclass GlUboSystem extends UboSystem {\n  constructor() {\n    super({\n      createUboElements: createUboElementsSTD40,\n      generateUboSync: createUboSyncFunctionSTD40\n    });\n  }\n}\n/** @ignore */\nGlUboSystem.extension = {\n  type: [ExtensionType.WebGLSystem],\n  name: \"ubo\"\n};\n\nexport { GlUboSystem };\n//# sourceMappingURL=GlUboSystem.mjs.map\n", "import { Rectangle } from '../../../../maths/shapes/Rectangle.mjs';\nimport { warn } from '../../../../utils/logging/warn.mjs';\nimport { CanvasSource } from '../../shared/texture/sources/CanvasSource.mjs';\nimport { CLEAR } from '../const.mjs';\nimport { GlRenderTarget } from '../GlRenderTarget.mjs';\n\n\"use strict\";\nclass GlRenderTargetAdaptor {\n  constructor() {\n    this._clearColorCache = [0, 0, 0, 0];\n    this._viewPortCache = new Rectangle();\n  }\n  init(renderer, renderTargetSystem) {\n    this._renderer = renderer;\n    this._renderTargetSystem = renderTargetSystem;\n    renderer.runners.contextChange.add(this);\n  }\n  contextChange() {\n    this._clearColorCache = [0, 0, 0, 0];\n    this._viewPortCache = new Rectangle();\n  }\n  copyToTexture(sourceRenderSurfaceTexture, destinationTexture, originSrc, size, originDest) {\n    const renderTargetSystem = this._renderTargetSystem;\n    const renderer = this._renderer;\n    const glRenderTarget = renderTargetSystem.getGpuRenderTarget(sourceRenderSurfaceTexture);\n    const gl = renderer.gl;\n    this.finishRenderPass(sourceRenderSurfaceTexture);\n    gl.bindFramebuffer(gl.FRAMEBUFFER, glRenderTarget.resolveTargetFramebuffer);\n    renderer.texture.bind(destinationTexture, 0);\n    gl.copyTexSubImage2D(\n      gl.TEXTURE_2D,\n      0,\n      originDest.x,\n      originDest.y,\n      originSrc.x,\n      originSrc.y,\n      size.width,\n      size.height\n    );\n    return destinationTexture;\n  }\n  startRenderPass(renderTarget, clear = true, clearColor, viewport) {\n    const renderTargetSystem = this._renderTargetSystem;\n    const source = renderTarget.colorTexture;\n    const gpuRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n    let viewPortY = viewport.y;\n    if (renderTarget.isRoot) {\n      viewPortY = source.pixelHeight - viewport.height;\n    }\n    renderTarget.colorTextures.forEach((texture) => {\n      this._renderer.texture.unbind(texture);\n    });\n    const gl = this._renderer.gl;\n    gl.bindFramebuffer(gl.FRAMEBUFFER, gpuRenderTarget.framebuffer);\n    const viewPortCache = this._viewPortCache;\n    if (viewPortCache.x !== viewport.x || viewPortCache.y !== viewPortY || viewPortCache.width !== viewport.width || viewPortCache.height !== viewport.height) {\n      viewPortCache.x = viewport.x;\n      viewPortCache.y = viewPortY;\n      viewPortCache.width = viewport.width;\n      viewPortCache.height = viewport.height;\n      gl.viewport(\n        viewport.x,\n        viewPortY,\n        viewport.width,\n        viewport.height\n      );\n    }\n    if (!gpuRenderTarget.depthStencilRenderBuffer && (renderTarget.stencil || renderTarget.depth)) {\n      this._initStencil(gpuRenderTarget);\n    }\n    this.clear(renderTarget, clear, clearColor);\n  }\n  finishRenderPass(renderTarget) {\n    const renderTargetSystem = this._renderTargetSystem;\n    const glRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n    if (!glRenderTarget.msaa)\n      return;\n    const gl = this._renderer.gl;\n    gl.bindFramebuffer(gl.FRAMEBUFFER, glRenderTarget.resolveTargetFramebuffer);\n    gl.bindFramebuffer(gl.READ_FRAMEBUFFER, glRenderTarget.framebuffer);\n    gl.blitFramebuffer(\n      0,\n      0,\n      glRenderTarget.width,\n      glRenderTarget.height,\n      0,\n      0,\n      glRenderTarget.width,\n      glRenderTarget.height,\n      gl.COLOR_BUFFER_BIT,\n      gl.NEAREST\n    );\n    gl.bindFramebuffer(gl.FRAMEBUFFER, glRenderTarget.framebuffer);\n  }\n  initGpuRenderTarget(renderTarget) {\n    const renderer = this._renderer;\n    const gl = renderer.gl;\n    const glRenderTarget = new GlRenderTarget();\n    const colorTexture = renderTarget.colorTexture;\n    if (colorTexture.resource === renderer.canvas) {\n      this._renderer.context.ensureCanvasSize(renderTarget.colorTexture.resource);\n      glRenderTarget.framebuffer = null;\n      return glRenderTarget;\n    }\n    this._initColor(renderTarget, glRenderTarget);\n    gl.bindFramebuffer(gl.FRAMEBUFFER, null);\n    return glRenderTarget;\n  }\n  destroyGpuRenderTarget(gpuRenderTarget) {\n    const gl = this._renderer.gl;\n    if (gpuRenderTarget.framebuffer) {\n      gl.deleteFramebuffer(gpuRenderTarget.framebuffer);\n      gpuRenderTarget.framebuffer = null;\n    }\n    if (gpuRenderTarget.resolveTargetFramebuffer) {\n      gl.deleteFramebuffer(gpuRenderTarget.resolveTargetFramebuffer);\n      gpuRenderTarget.resolveTargetFramebuffer = null;\n    }\n    if (gpuRenderTarget.depthStencilRenderBuffer) {\n      gl.deleteRenderbuffer(gpuRenderTarget.depthStencilRenderBuffer);\n      gpuRenderTarget.depthStencilRenderBuffer = null;\n    }\n    gpuRenderTarget.msaaRenderBuffer.forEach((renderBuffer) => {\n      gl.deleteRenderbuffer(renderBuffer);\n    });\n    gpuRenderTarget.msaaRenderBuffer = null;\n  }\n  clear(_renderTarget, clear, clearColor) {\n    if (!clear)\n      return;\n    const renderTargetSystem = this._renderTargetSystem;\n    if (typeof clear === \"boolean\") {\n      clear = clear ? CLEAR.ALL : CLEAR.NONE;\n    }\n    const gl = this._renderer.gl;\n    if (clear & CLEAR.COLOR) {\n      clearColor ?? (clearColor = renderTargetSystem.defaultClearColor);\n      const clearColorCache = this._clearColorCache;\n      const clearColorArray = clearColor;\n      if (clearColorCache[0] !== clearColorArray[0] || clearColorCache[1] !== clearColorArray[1] || clearColorCache[2] !== clearColorArray[2] || clearColorCache[3] !== clearColorArray[3]) {\n        clearColorCache[0] = clearColorArray[0];\n        clearColorCache[1] = clearColorArray[1];\n        clearColorCache[2] = clearColorArray[2];\n        clearColorCache[3] = clearColorArray[3];\n        gl.clearColor(clearColorArray[0], clearColorArray[1], clearColorArray[2], clearColorArray[3]);\n      }\n    }\n    gl.clear(clear);\n  }\n  resizeGpuRenderTarget(renderTarget) {\n    if (renderTarget.isRoot)\n      return;\n    const renderTargetSystem = this._renderTargetSystem;\n    const glRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n    this._resizeColor(renderTarget, glRenderTarget);\n    if (renderTarget.stencil || renderTarget.depth) {\n      this._resizeStencil(glRenderTarget);\n    }\n  }\n  _initColor(renderTarget, glRenderTarget) {\n    const renderer = this._renderer;\n    const gl = renderer.gl;\n    const resolveTargetFramebuffer = gl.createFramebuffer();\n    glRenderTarget.resolveTargetFramebuffer = resolveTargetFramebuffer;\n    gl.bindFramebuffer(gl.FRAMEBUFFER, resolveTargetFramebuffer);\n    glRenderTarget.width = renderTarget.colorTexture.source.pixelWidth;\n    glRenderTarget.height = renderTarget.colorTexture.source.pixelHeight;\n    renderTarget.colorTextures.forEach((colorTexture, i) => {\n      const source = colorTexture.source;\n      if (source.antialias) {\n        if (renderer.context.supports.msaa) {\n          glRenderTarget.msaa = true;\n        } else {\n          warn(\"[RenderTexture] Antialiasing on textures is not supported in WebGL1\");\n        }\n      }\n      renderer.texture.bindSource(source, 0);\n      const glSource = renderer.texture.getGlSource(source);\n      const glTexture = glSource.texture;\n      gl.framebufferTexture2D(\n        gl.FRAMEBUFFER,\n        gl.COLOR_ATTACHMENT0 + i,\n        3553,\n        // texture.target,\n        glTexture,\n        0\n      );\n    });\n    if (glRenderTarget.msaa) {\n      const viewFramebuffer = gl.createFramebuffer();\n      glRenderTarget.framebuffer = viewFramebuffer;\n      gl.bindFramebuffer(gl.FRAMEBUFFER, viewFramebuffer);\n      renderTarget.colorTextures.forEach((_, i) => {\n        const msaaRenderBuffer = gl.createRenderbuffer();\n        glRenderTarget.msaaRenderBuffer[i] = msaaRenderBuffer;\n      });\n    } else {\n      glRenderTarget.framebuffer = resolveTargetFramebuffer;\n    }\n    this._resizeColor(renderTarget, glRenderTarget);\n  }\n  _resizeColor(renderTarget, glRenderTarget) {\n    const source = renderTarget.colorTexture.source;\n    glRenderTarget.width = source.pixelWidth;\n    glRenderTarget.height = source.pixelHeight;\n    renderTarget.colorTextures.forEach((colorTexture, i) => {\n      if (i === 0)\n        return;\n      colorTexture.source.resize(source.width, source.height, source._resolution);\n    });\n    if (glRenderTarget.msaa) {\n      const renderer = this._renderer;\n      const gl = renderer.gl;\n      const viewFramebuffer = glRenderTarget.framebuffer;\n      gl.bindFramebuffer(gl.FRAMEBUFFER, viewFramebuffer);\n      renderTarget.colorTextures.forEach((colorTexture, i) => {\n        const source2 = colorTexture.source;\n        renderer.texture.bindSource(source2, 0);\n        const glSource = renderer.texture.getGlSource(source2);\n        const glInternalFormat = glSource.internalFormat;\n        const msaaRenderBuffer = glRenderTarget.msaaRenderBuffer[i];\n        gl.bindRenderbuffer(\n          gl.RENDERBUFFER,\n          msaaRenderBuffer\n        );\n        gl.renderbufferStorageMultisample(\n          gl.RENDERBUFFER,\n          4,\n          glInternalFormat,\n          source2.pixelWidth,\n          source2.pixelHeight\n        );\n        gl.framebufferRenderbuffer(\n          gl.FRAMEBUFFER,\n          gl.COLOR_ATTACHMENT0 + i,\n          gl.RENDERBUFFER,\n          msaaRenderBuffer\n        );\n      });\n    }\n  }\n  _initStencil(glRenderTarget) {\n    if (glRenderTarget.framebuffer === null)\n      return;\n    const gl = this._renderer.gl;\n    const depthStencilRenderBuffer = gl.createRenderbuffer();\n    glRenderTarget.depthStencilRenderBuffer = depthStencilRenderBuffer;\n    gl.bindRenderbuffer(\n      gl.RENDERBUFFER,\n      depthStencilRenderBuffer\n    );\n    gl.framebufferRenderbuffer(\n      gl.FRAMEBUFFER,\n      gl.DEPTH_STENCIL_ATTACHMENT,\n      gl.RENDERBUFFER,\n      depthStencilRenderBuffer\n    );\n    this._resizeStencil(glRenderTarget);\n  }\n  _resizeStencil(glRenderTarget) {\n    const gl = this._renderer.gl;\n    gl.bindRenderbuffer(\n      gl.RENDERBUFFER,\n      glRenderTarget.depthStencilRenderBuffer\n    );\n    if (glRenderTarget.msaa) {\n      gl.renderbufferStorageMultisample(\n        gl.RENDERBUFFER,\n        4,\n        gl.DEPTH24_STENCIL8,\n        glRenderTarget.width,\n        glRenderTarget.height\n      );\n    } else {\n      gl.renderbufferStorage(\n        gl.RENDERBUFFER,\n        this._renderer.context.webGLVersion === 2 ? gl.DEPTH24_STENCIL8 : gl.DEPTH_STENCIL,\n        glRenderTarget.width,\n        glRenderTarget.height\n      );\n    }\n  }\n  prerender(renderTarget) {\n    const resource = renderTarget.colorTexture.resource;\n    if (this._renderer.context.multiView && CanvasSource.test(resource)) {\n      this._renderer.context.ensureCanvasSize(resource);\n    }\n  }\n  postrender(renderTarget) {\n    if (!this._renderer.context.multiView)\n      return;\n    if (CanvasSource.test(renderTarget.colorTexture.resource)) {\n      const contextCanvas = this._renderer.context.canvas;\n      const canvasSource = renderTarget.colorTexture;\n      canvasSource.context2D.drawImage(\n        contextCanvas,\n        0,\n        canvasSource.pixelHeight - contextCanvas.height\n      );\n    }\n  }\n}\n\nexport { GlRenderTargetAdaptor };\n//# sourceMappingURL=GlRenderTargetAdaptor.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { RenderTargetSystem } from '../../shared/renderTarget/RenderTargetSystem.mjs';\nimport { GlRenderTargetAdaptor } from './GlRenderTargetAdaptor.mjs';\n\n\"use strict\";\nclass GlRenderTargetSystem extends RenderTargetSystem {\n  constructor(renderer) {\n    super(renderer);\n    this.adaptor = new GlRenderTargetAdaptor();\n    this.adaptor.init(renderer, this);\n  }\n}\n/** @ignore */\nGlRenderTargetSystem.extension = {\n  type: [ExtensionType.WebGLSystem],\n  name: \"renderTarget\"\n};\n\nexport { GlRenderTargetSystem };\n//# sourceMappingURL=GlRenderTargetSystem.mjs.map\n", "import { BufferResource } from '../../shared/buffer/BufferResource.mjs';\nimport { UniformGroup } from '../../shared/shader/UniformGroup.mjs';\nimport { TextureSource } from '../../shared/texture/sources/TextureSource.mjs';\n\n\"use strict\";\nfunction generateShaderSyncCode(shader, shaderSystem) {\n  const funcFragments = [];\n  const headerFragments = [`\n        var g = s.groups;\n        var sS = r.shader;\n        var p = s.glProgram;\n        var ugS = r.uniformGroup;\n        var resources;\n    `];\n  let addedTextreSystem = false;\n  let textureCount = 0;\n  const programData = shaderSystem._getProgramData(shader.glProgram);\n  for (const i in shader.groups) {\n    const group = shader.groups[i];\n    funcFragments.push(`\n            resources = g[${i}].resources;\n        `);\n    for (const j in group.resources) {\n      const resource = group.resources[j];\n      if (resource instanceof UniformGroup) {\n        if (resource.ubo) {\n          const resName = shader._uniformBindMap[i][Number(j)];\n          funcFragments.push(`\n                        sS.bindUniformBlock(\n                            resources[${j}],\n                            '${resName}',\n                            ${shader.glProgram._uniformBlockData[resName].index}\n                        );\n                    `);\n        } else {\n          funcFragments.push(`\n                        ugS.updateUniformGroup(resources[${j}], p, sD);\n                    `);\n        }\n      } else if (resource instanceof BufferResource) {\n        const resName = shader._uniformBindMap[i][Number(j)];\n        funcFragments.push(`\n                    sS.bindUniformBlock(\n                        resources[${j}],\n                        '${resName}',\n                        ${shader.glProgram._uniformBlockData[resName].index}\n                    );\n                `);\n      } else if (resource instanceof TextureSource) {\n        const uniformName = shader._uniformBindMap[i][j];\n        const uniformData = programData.uniformData[uniformName];\n        if (uniformData) {\n          if (!addedTextreSystem) {\n            addedTextreSystem = true;\n            headerFragments.push(`\n                        var tS = r.texture;\n                        `);\n          }\n          shaderSystem._gl.uniform1i(uniformData.location, textureCount);\n          funcFragments.push(`\n                        tS.bind(resources[${j}], ${textureCount});\n                    `);\n          textureCount++;\n        }\n      }\n    }\n  }\n  const functionSource = [...headerFragments, ...funcFragments].join(\"\\n\");\n  return new Function(\"r\", \"s\", \"sD\", functionSource);\n}\n\nexport { generateShaderSyncCode };\n//# sourceMappingURL=GenerateShaderSyncCode.mjs.map\n", "\"use strict\";\nclass IGLUniformData {\n}\nclass GlProgramData {\n  /**\n   * Makes a new Pixi program.\n   * @param program - webgl program\n   * @param uniformData - uniforms\n   */\n  constructor(program, uniformData) {\n    this.program = program;\n    this.uniformData = uniformData;\n    this.uniformGroups = {};\n    this.uniformDirtyGroups = {};\n    this.uniformBlockBindings = {};\n  }\n  /** Destroys this program. */\n  destroy() {\n    this.uniformData = null;\n    this.uniformGroups = null;\n    this.uniformDirtyGroups = null;\n    this.uniformBlockBindings = null;\n    this.program = null;\n  }\n}\n\nexport { GlProgramData, IGLUniformData };\n//# sourceMappingURL=GlProgramData.mjs.map\n", "\"use strict\";\nfunction compileShader(gl, type, src) {\n  const shader = gl.createShader(type);\n  gl.shaderSource(shader, src);\n  gl.compileShader(shader);\n  return shader;\n}\n\nexport { compileShader };\n//# sourceMappingURL=compileShader.mjs.map\n", "\"use strict\";\nfunction booleanArray(size) {\n  const array = new Array(size);\n  for (let i = 0; i < array.length; i++) {\n    array[i] = false;\n  }\n  return array;\n}\nfunction defaultValue(type, size) {\n  switch (type) {\n    case \"float\":\n      return 0;\n    case \"vec2\":\n      return new Float32Array(2 * size);\n    case \"vec3\":\n      return new Float32Array(3 * size);\n    case \"vec4\":\n      return new Float32Array(4 * size);\n    case \"int\":\n    case \"uint\":\n    case \"sampler2D\":\n    case \"sampler2DArray\":\n      return 0;\n    case \"ivec2\":\n      return new Int32Array(2 * size);\n    case \"ivec3\":\n      return new Int32Array(3 * size);\n    case \"ivec4\":\n      return new Int32Array(4 * size);\n    case \"uvec2\":\n      return new Uint32Array(2 * size);\n    case \"uvec3\":\n      return new Uint32Array(3 * size);\n    case \"uvec4\":\n      return new Uint32Array(4 * size);\n    case \"bool\":\n      return false;\n    case \"bvec2\":\n      return booleanArray(2 * size);\n    case \"bvec3\":\n      return booleanArray(3 * size);\n    case \"bvec4\":\n      return booleanArray(4 * size);\n    case \"mat2\":\n      return new Float32Array([\n        1,\n        0,\n        0,\n        1\n      ]);\n    case \"mat3\":\n      return new Float32Array([\n        1,\n        0,\n        0,\n        0,\n        1,\n        0,\n        0,\n        0,\n        1\n      ]);\n    case \"mat4\":\n      return new Float32Array([\n        1,\n        0,\n        0,\n        0,\n        0,\n        1,\n        0,\n        0,\n        0,\n        0,\n        1,\n        0,\n        0,\n        0,\n        0,\n        1\n      ]);\n  }\n  return null;\n}\n\nexport { defaultValue };\n//# sourceMappingURL=defaultValue.mjs.map\n", "\"use strict\";\nlet GL_TABLE = null;\nconst GL_TO_GLSL_TYPES = {\n  FLOAT: \"float\",\n  FLOAT_VEC2: \"vec2\",\n  FLOAT_VEC3: \"vec3\",\n  FLOAT_VEC4: \"vec4\",\n  INT: \"int\",\n  INT_VEC2: \"ivec2\",\n  INT_VEC3: \"ivec3\",\n  INT_VEC4: \"ivec4\",\n  UNSIGNED_INT: \"uint\",\n  UNSIGNED_INT_VEC2: \"uvec2\",\n  UNSIGNED_INT_VEC3: \"uvec3\",\n  UNSIGNED_INT_VEC4: \"uvec4\",\n  BOOL: \"bool\",\n  BOOL_VEC2: \"bvec2\",\n  BOOL_VEC3: \"bvec3\",\n  BOOL_VEC4: \"bvec4\",\n  FLOAT_MAT2: \"mat2\",\n  FLOAT_MAT3: \"mat3\",\n  FLOAT_MAT4: \"mat4\",\n  SAMPLER_2D: \"sampler2D\",\n  INT_SAMPLER_2D: \"sampler2D\",\n  UNSIGNED_INT_SAMPLER_2D: \"sampler2D\",\n  SAMPLER_CUBE: \"samplerCube\",\n  INT_SAMPLER_CUBE: \"samplerCube\",\n  UNSIGNED_INT_SAMPLER_CUBE: \"samplerCube\",\n  SAMPLER_2D_ARRAY: \"sampler2DArray\",\n  INT_SAMPLER_2D_ARRAY: \"sampler2DArray\",\n  UNSIGNED_INT_SAMPLER_2D_ARRAY: \"sampler2DArray\"\n};\nconst GLSL_TO_VERTEX_TYPES = {\n  float: \"float32\",\n  vec2: \"float32x2\",\n  vec3: \"float32x3\",\n  vec4: \"float32x4\",\n  int: \"sint32\",\n  ivec2: \"sint32x2\",\n  ivec3: \"sint32x3\",\n  ivec4: \"sint32x4\",\n  uint: \"uint32\",\n  uvec2: \"uint32x2\",\n  uvec3: \"uint32x3\",\n  uvec4: \"uint32x4\",\n  bool: \"uint32\",\n  bvec2: \"uint32x2\",\n  bvec3: \"uint32x3\",\n  bvec4: \"uint32x4\"\n};\nfunction mapType(gl, type) {\n  if (!GL_TABLE) {\n    const typeNames = Object.keys(GL_TO_GLSL_TYPES);\n    GL_TABLE = {};\n    for (let i = 0; i < typeNames.length; ++i) {\n      const tn = typeNames[i];\n      GL_TABLE[gl[tn]] = GL_TO_GLSL_TYPES[tn];\n    }\n  }\n  return GL_TABLE[type];\n}\nfunction mapGlToVertexFormat(gl, type) {\n  const typeValue = mapType(gl, type);\n  return GLSL_TO_VERTEX_TYPES[typeValue] || \"float32\";\n}\n\nexport { mapGlToVertexFormat, mapType };\n//# sourceMappingURL=mapType.mjs.map\n", "import { getAttributeInfoFromFormat } from '../../../shared/geometry/utils/getAttributeInfoFromFormat.mjs';\nimport { mapGlToVertexFormat } from './mapType.mjs';\n\n\"use strict\";\nfunction extractAttributesFromGlProgram(program, gl, sortAttributes = false) {\n  const attributes = {};\n  const totalAttributes = gl.getProgramParameter(program, gl.ACTIVE_ATTRIBUTES);\n  for (let i = 0; i < totalAttributes; i++) {\n    const attribData = gl.getActiveAttrib(program, i);\n    if (attribData.name.startsWith(\"gl_\")) {\n      continue;\n    }\n    const format = mapGlToVertexFormat(gl, attribData.type);\n    attributes[attribData.name] = {\n      location: 0,\n      // set further down..\n      format,\n      stride: getAttributeInfoFromFormat(format).stride,\n      offset: 0,\n      instance: false,\n      start: 0\n    };\n  }\n  const keys = Object.keys(attributes);\n  if (sortAttributes) {\n    keys.sort((a, b) => a > b ? 1 : -1);\n    for (let i = 0; i < keys.length; i++) {\n      attributes[keys[i]].location = i;\n      gl.bindAttribLocation(program, i, keys[i]);\n    }\n    gl.linkProgram(program);\n  } else {\n    for (let i = 0; i < keys.length; i++) {\n      attributes[keys[i]].location = gl.getAttribLocation(program, keys[i]);\n    }\n  }\n  return attributes;\n}\n\nexport { extractAttributesFromGlProgram };\n//# sourceMappingURL=extractAttributesFromGlProgram.mjs.map\n", "\"use strict\";\nfunction getUboData(program, gl) {\n  if (!gl.ACTIVE_UNIFORM_BLOCKS)\n    return {};\n  const uniformBlocks = {};\n  const totalUniformsBlocks = gl.getProgramParameter(program, gl.ACTIVE_UNIFORM_BLOCKS);\n  for (let i = 0; i < totalUniformsBlocks; i++) {\n    const name = gl.getActiveUniformBlockName(program, i);\n    const uniformBlockIndex = gl.getUniformBlockIndex(program, name);\n    const size = gl.getActiveUniformBlockParameter(program, i, gl.UNIFORM_BLOCK_DATA_SIZE);\n    uniformBlocks[name] = {\n      name,\n      index: uniformBlockIndex,\n      size\n    };\n  }\n  return uniformBlocks;\n}\n\nexport { getUboData };\n//# sourceMappingURL=getUboData.mjs.map\n", "import { defaultValue } from './defaultValue.mjs';\nimport { mapType } from './mapType.mjs';\n\n\"use strict\";\nfunction getUniformData(program, gl) {\n  const uniforms = {};\n  const totalUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);\n  for (let i = 0; i < totalUniforms; i++) {\n    const uniformData = gl.getActiveUniform(program, i);\n    const name = uniformData.name.replace(/\\[.*?\\]$/, \"\");\n    const isArray = !!uniformData.name.match(/\\[.*?\\]$/);\n    const type = mapType(gl, uniformData.type);\n    uniforms[name] = {\n      name,\n      index: i,\n      type,\n      size: uniformData.size,\n      isArray,\n      value: defaultValue(type, uniformData.size)\n    };\n  }\n  return uniforms;\n}\n\nexport { getUniformData };\n//# sourceMappingURL=getUniformData.mjs.map\n", "\"use strict\";\nfunction logPrettyShaderError(gl, shader) {\n  const shaderSrc = gl.getShaderSource(shader).split(\"\\n\").map((line, index) => `${index}: ${line}`);\n  const shaderLog = gl.getShaderInfoLog(shader);\n  const splitShader = shaderLog.split(\"\\n\");\n  const dedupe = {};\n  const lineNumbers = splitShader.map((line) => parseFloat(line.replace(/^ERROR\\: 0\\:([\\d]+)\\:.*$/, \"$1\"))).filter((n) => {\n    if (n && !dedupe[n]) {\n      dedupe[n] = true;\n      return true;\n    }\n    return false;\n  });\n  const logArgs = [\"\"];\n  lineNumbers.forEach((number) => {\n    shaderSrc[number - 1] = `%c${shaderSrc[number - 1]}%c`;\n    logArgs.push(\"background: #FF0000; color:#FFFFFF; font-size: 10px\", \"font-size: 10px\");\n  });\n  const fragmentSourceToLog = shaderSrc.join(\"\\n\");\n  logArgs[0] = fragmentSourceToLog;\n  console.error(shaderLog);\n  console.groupCollapsed(\"click to view full shader code\");\n  console.warn(...logArgs);\n  console.groupEnd();\n}\nfunction logProgramError(gl, program, vertexShader, fragmentShader) {\n  if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {\n    if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS)) {\n      logPrettyShaderError(gl, vertexShader);\n    }\n    if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS)) {\n      logPrettyShaderError(gl, fragmentShader);\n    }\n    console.error(\"PixiJS Error: Could not initialize shader.\");\n    if (gl.getProgramInfoLog(program) !== \"\") {\n      console.warn(\"PixiJS Warning: gl.getProgramInfoLog()\", gl.getProgramInfoLog(program));\n    }\n  }\n}\n\nexport { logProgramError };\n//# sourceMappingURL=logProgramError.mjs.map\n", "import { warn } from '../../../../../utils/logging/warn.mjs';\nimport { GlProgramData } from '../GlProgramData.mjs';\nimport { compileShader } from './compileShader.mjs';\nimport { defaultValue } from './defaultValue.mjs';\nimport { extractAttributesFromGlProgram } from './extractAttributesFromGlProgram.mjs';\nimport { getUboData } from './getUboData.mjs';\nimport { getUniformData } from './getUniformData.mjs';\nimport { logProgramError } from './logProgramError.mjs';\n\n\"use strict\";\nfunction generateProgram(gl, program) {\n  const glVertShader = compileShader(gl, gl.VERTEX_SHADER, program.vertex);\n  const glFragShader = compileShader(gl, gl.FRAGMENT_SHADER, program.fragment);\n  const webGLProgram = gl.createProgram();\n  gl.attachShader(webGLProgram, glVertShader);\n  gl.attachShader(webGLProgram, glFragShader);\n  const transformFeedbackVaryings = program.transformFeedbackVaryings;\n  if (transformFeedbackVaryings) {\n    if (typeof gl.transformFeedbackVaryings !== \"function\") {\n      warn(`TransformFeedback is not supported but TransformFeedbackVaryings are given.`);\n    } else {\n      gl.transformFeedbackVaryings(\n        webGLProgram,\n        transformFeedbackVaryings.names,\n        transformFeedbackVaryings.bufferMode === \"separate\" ? gl.SEPARATE_ATTRIBS : gl.INTERLEAVED_ATTRIBS\n      );\n    }\n  }\n  gl.linkProgram(webGLProgram);\n  if (!gl.getProgramParameter(webGLProgram, gl.LINK_STATUS)) {\n    logProgramError(gl, webGLProgram, glVertShader, glFragShader);\n  }\n  program._attributeData = extractAttributesFromGlProgram(\n    webGLProgram,\n    gl,\n    !/^[ \\t]*#[ \\t]*version[ \\t]+300[ \\t]+es[ \\t]*$/m.test(program.vertex)\n  );\n  program._uniformData = getUniformData(webGLProgram, gl);\n  program._uniformBlockData = getUboData(webGLProgram, gl);\n  gl.deleteShader(glVertShader);\n  gl.deleteShader(glFragShader);\n  const uniformData = {};\n  for (const i in program._uniformData) {\n    const data = program._uniformData[i];\n    uniformData[i] = {\n      location: gl.getUniformLocation(webGLProgram, i),\n      value: defaultValue(data.type, data.size)\n    };\n  }\n  const glProgram = new GlProgramData(webGLProgram, uniformData);\n  return glProgram;\n}\n\nexport { generateProgram };\n//# sourceMappingURL=generateProgram.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { getMaxTexturesPerBatch } from '../../../batcher/gl/utils/maxRecommendedTextures.mjs';\nimport { generateShaderSyncCode } from './GenerateShaderSyncCode.mjs';\nimport { generateProgram } from './program/generateProgram.mjs';\n\n\"use strict\";\nconst defaultSyncData = {\n  textureCount: 0,\n  blockIndex: 0\n};\nclass GlShaderSystem {\n  constructor(renderer) {\n    /**\n     * @internal\n     * @private\n     */\n    this._activeProgram = null;\n    this._programDataHash = /* @__PURE__ */ Object.create(null);\n    this._shaderSyncFunctions = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_programDataHash\");\n  }\n  contextChange(gl) {\n    this._gl = gl;\n    this._programDataHash = /* @__PURE__ */ Object.create(null);\n    this._shaderSyncFunctions = /* @__PURE__ */ Object.create(null);\n    this._activeProgram = null;\n    this.maxTextures = getMaxTexturesPerBatch();\n  }\n  /**\n   * Changes the current shader to the one given in parameter.\n   * @param shader - the new shader\n   * @param skipSync - false if the shader should automatically sync its uniforms.\n   * @returns the glProgram that belongs to the shader.\n   */\n  bind(shader, skipSync) {\n    this._setProgram(shader.glProgram);\n    if (skipSync)\n      return;\n    defaultSyncData.textureCount = 0;\n    defaultSyncData.blockIndex = 0;\n    let syncFunction = this._shaderSyncFunctions[shader.glProgram._key];\n    if (!syncFunction) {\n      syncFunction = this._shaderSyncFunctions[shader.glProgram._key] = this._generateShaderSync(shader, this);\n    }\n    this._renderer.buffer.nextBindBase(!!shader.glProgram.transformFeedbackVaryings);\n    syncFunction(this._renderer, shader, defaultSyncData);\n  }\n  /**\n   * Updates the uniform group.\n   * @param uniformGroup - the uniform group to update\n   */\n  updateUniformGroup(uniformGroup) {\n    this._renderer.uniformGroup.updateUniformGroup(uniformGroup, this._activeProgram, defaultSyncData);\n  }\n  /**\n   * Binds a uniform block to the shader.\n   * @param uniformGroup - the uniform group to bind\n   * @param name - the name of the uniform block\n   * @param index - the index of the uniform block\n   */\n  bindUniformBlock(uniformGroup, name, index = 0) {\n    const bufferSystem = this._renderer.buffer;\n    const programData = this._getProgramData(this._activeProgram);\n    const isBufferResource = uniformGroup._bufferResource;\n    if (!isBufferResource) {\n      this._renderer.ubo.updateUniformGroup(uniformGroup);\n    }\n    const buffer = uniformGroup.buffer;\n    const glBuffer = bufferSystem.updateBuffer(buffer);\n    const boundLocation = bufferSystem.freeLocationForBufferBase(glBuffer);\n    if (isBufferResource) {\n      const { offset, size } = uniformGroup;\n      if (offset === 0 && size === buffer.data.byteLength) {\n        bufferSystem.bindBufferBase(glBuffer, boundLocation);\n      } else {\n        bufferSystem.bindBufferRange(glBuffer, boundLocation, offset);\n      }\n    } else if (bufferSystem.getLastBindBaseLocation(glBuffer) !== boundLocation) {\n      bufferSystem.bindBufferBase(glBuffer, boundLocation);\n    }\n    const uniformBlockIndex = this._activeProgram._uniformBlockData[name].index;\n    if (programData.uniformBlockBindings[index] === boundLocation)\n      return;\n    programData.uniformBlockBindings[index] = boundLocation;\n    this._renderer.gl.uniformBlockBinding(programData.program, uniformBlockIndex, boundLocation);\n  }\n  _setProgram(program) {\n    if (this._activeProgram === program)\n      return;\n    this._activeProgram = program;\n    const programData = this._getProgramData(program);\n    this._gl.useProgram(programData.program);\n  }\n  /**\n   * @param program - the program to get the data for\n   * @internal\n   * @private\n   */\n  _getProgramData(program) {\n    return this._programDataHash[program._key] || this._createProgramData(program);\n  }\n  _createProgramData(program) {\n    const key = program._key;\n    this._programDataHash[key] = generateProgram(this._gl, program);\n    return this._programDataHash[key];\n  }\n  destroy() {\n    for (const key of Object.keys(this._programDataHash)) {\n      const programData = this._programDataHash[key];\n      programData.destroy();\n      this._programDataHash[key] = null;\n    }\n    this._programDataHash = null;\n  }\n  /**\n   * Creates a function that can be executed that will sync the shader as efficiently as possible.\n   * Overridden by the unsafe eval package if you don't want eval used in your project.\n   * @param shader - the shader to generate the sync function for\n   * @param shaderSystem - the shader system to use\n   * @returns - the generated sync function\n   * @ignore\n   */\n  _generateShaderSync(shader, shaderSystem) {\n    return generateShaderSyncCode(shader, shaderSystem);\n  }\n  resetState() {\n    this._activeProgram = null;\n  }\n}\n/** @ignore */\nGlShaderSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"shader\"\n};\n\nexport { GlShaderSystem };\n//# sourceMappingURL=GlShaderSystem.mjs.map\n", "\"use strict\";\nconst UNIFORM_TO_SINGLE_SETTERS = {\n  f32: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1f(location, v);\n        }`,\n  \"vec2<f32>\": `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2f(location, v[0], v[1]);\n        }`,\n  \"vec3<f32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3f(location, v[0], v[1], v[2]);\n        }`,\n  \"vec4<f32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4f(location, v[0], v[1], v[2], v[3]);\n        }`,\n  i32: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1i(location, v);\n        }`,\n  \"vec2<i32>\": `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2i(location, v[0], v[1]);\n        }`,\n  \"vec3<i32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3i(location, v[0], v[1], v[2]);\n        }`,\n  \"vec4<i32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n        }`,\n  u32: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1ui(location, v);\n        }`,\n  \"vec2<u32>\": `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2ui(location, v[0], v[1]);\n        }`,\n  \"vec3<u32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3ui(location, v[0], v[1], v[2]);\n        }`,\n  \"vec4<u32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4ui(location, v[0], v[1], v[2], v[3]);\n        }`,\n  bool: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1i(location, v);\n        }`,\n  \"vec2<bool>\": `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2i(location, v[0], v[1]);\n        }`,\n  \"vec3<bool>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3i(location, v[0], v[1], v[2]);\n        }`,\n  \"vec4<bool>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n        }`,\n  \"mat2x2<f32>\": `gl.uniformMatrix2fv(location, false, v);`,\n  \"mat3x3<f32>\": `gl.uniformMatrix3fv(location, false, v);`,\n  \"mat4x4<f32>\": `gl.uniformMatrix4fv(location, false, v);`\n};\nconst UNIFORM_TO_ARRAY_SETTERS = {\n  f32: `gl.uniform1fv(location, v);`,\n  \"vec2<f32>\": `gl.uniform2fv(location, v);`,\n  \"vec3<f32>\": `gl.uniform3fv(location, v);`,\n  \"vec4<f32>\": `gl.uniform4fv(location, v);`,\n  \"mat2x2<f32>\": `gl.uniformMatrix2fv(location, false, v);`,\n  \"mat3x3<f32>\": `gl.uniformMatrix3fv(location, false, v);`,\n  \"mat4x4<f32>\": `gl.uniformMatrix4fv(location, false, v);`,\n  i32: `gl.uniform1iv(location, v);`,\n  \"vec2<i32>\": `gl.uniform2iv(location, v);`,\n  \"vec3<i32>\": `gl.uniform3iv(location, v);`,\n  \"vec4<i32>\": `gl.uniform4iv(location, v);`,\n  u32: `gl.uniform1iv(location, v);`,\n  \"vec2<u32>\": `gl.uniform2iv(location, v);`,\n  \"vec3<u32>\": `gl.uniform3iv(location, v);`,\n  \"vec4<u32>\": `gl.uniform4iv(location, v);`,\n  bool: `gl.uniform1iv(location, v);`,\n  \"vec2<bool>\": `gl.uniform2iv(location, v);`,\n  \"vec3<bool>\": `gl.uniform3iv(location, v);`,\n  \"vec4<bool>\": `gl.uniform4iv(location, v);`\n};\n\nexport { UNIFORM_TO_ARRAY_SETTERS, UNIFORM_TO_SINGLE_SETTERS };\n//# sourceMappingURL=generateUniformsSyncTypes.mjs.map\n", "import { BufferResource } from '../../../shared/buffer/BufferResource.mjs';\nimport { UniformGroup } from '../../../shared/shader/UniformGroup.mjs';\nimport { uniformParsers } from '../../../shared/shader/utils/uniformParsers.mjs';\nimport { UNIFORM_TO_SINGLE_SETTERS, UNIFORM_TO_ARRAY_SETTERS } from './generateUniformsSyncTypes.mjs';\n\n\"use strict\";\nfunction generateUniformsSync(group, uniformData) {\n  const funcFragments = [`\n        var v = null;\n        var cv = null;\n        var cu = null;\n        var t = 0;\n        var gl = renderer.gl;\n        var name = null;\n    `];\n  for (const i in group.uniforms) {\n    if (!uniformData[i]) {\n      if (group.uniforms[i] instanceof UniformGroup) {\n        if (group.uniforms[i].ubo) {\n          funcFragments.push(`\n                        renderer.shader.bindUniformBlock(uv.${i}, \"${i}\");\n                    `);\n        } else {\n          funcFragments.push(`\n                        renderer.shader.updateUniformGroup(uv.${i});\n                    `);\n        }\n      } else if (group.uniforms[i] instanceof BufferResource) {\n        funcFragments.push(`\n                        renderer.shader.bindBufferResource(uv.${i}, \"${i}\");\n                    `);\n      }\n      continue;\n    }\n    const uniform = group.uniformStructures[i];\n    let parsed = false;\n    for (let j = 0; j < uniformParsers.length; j++) {\n      const parser = uniformParsers[j];\n      if (uniform.type === parser.type && parser.test(uniform)) {\n        funcFragments.push(`name = \"${i}\";`, uniformParsers[j].uniform);\n        parsed = true;\n        break;\n      }\n    }\n    if (!parsed) {\n      const templateType = uniform.size === 1 ? UNIFORM_TO_SINGLE_SETTERS : UNIFORM_TO_ARRAY_SETTERS;\n      const template = templateType[uniform.type].replace(\"location\", `ud[\"${i}\"].location`);\n      funcFragments.push(`\n            cu = ud[\"${i}\"];\n            cv = cu.value;\n            v = uv[\"${i}\"];\n            ${template};`);\n    }\n  }\n  return new Function(\"ud\", \"uv\", \"renderer\", \"syncData\", funcFragments.join(\"\\n\"));\n}\n\nexport { generateUniformsSync };\n//# sourceMappingURL=generateUniformsSync.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { generateUniformsSync } from './utils/generateUniformsSync.mjs';\n\n\"use strict\";\nclass GlUniformGroupSystem {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    /** Cache to holds the generated functions. Stored against UniformObjects unique signature. */\n    this._cache = {};\n    this._uniformGroupSyncHash = {};\n    this._renderer = renderer;\n    this.gl = null;\n    this._cache = {};\n  }\n  contextChange(gl) {\n    this.gl = gl;\n  }\n  /**\n   * Uploads the uniforms values to the currently bound shader.\n   * @param group - the uniforms values that be applied to the current shader\n   * @param program\n   * @param syncData\n   * @param syncData.textureCount\n   */\n  updateUniformGroup(group, program, syncData) {\n    const programData = this._renderer.shader._getProgramData(program);\n    if (!group.isStatic || group._dirtyId !== programData.uniformDirtyGroups[group.uid]) {\n      programData.uniformDirtyGroups[group.uid] = group._dirtyId;\n      const syncFunc = this._getUniformSyncFunction(group, program);\n      syncFunc(programData.uniformData, group.uniforms, this._renderer, syncData);\n    }\n  }\n  /**\n   * Overridable by the pixi.js/unsafe-eval package to use static syncUniforms instead.\n   * @param group\n   * @param program\n   */\n  _getUniformSyncFunction(group, program) {\n    return this._uniformGroupSyncHash[group._signature]?.[program._key] || this._createUniformSyncFunction(group, program);\n  }\n  _createUniformSyncFunction(group, program) {\n    const uniformGroupSyncHash = this._uniformGroupSyncHash[group._signature] || (this._uniformGroupSyncHash[group._signature] = {});\n    const id = this._getSignature(group, program._uniformData, \"u\");\n    if (!this._cache[id]) {\n      this._cache[id] = this._generateUniformsSync(group, program._uniformData);\n    }\n    uniformGroupSyncHash[program._key] = this._cache[id];\n    return uniformGroupSyncHash[program._key];\n  }\n  _generateUniformsSync(group, uniformData) {\n    return generateUniformsSync(group, uniformData);\n  }\n  /**\n   * Takes a uniform group and data and generates a unique signature for them.\n   * @param group - The uniform group to get signature of\n   * @param group.uniforms\n   * @param uniformData - Uniform information generated by the shader\n   * @param preFix\n   * @returns Unique signature of the uniform group\n   */\n  _getSignature(group, uniformData, preFix) {\n    const uniforms = group.uniforms;\n    const strings = [`${preFix}-`];\n    for (const i in uniforms) {\n      strings.push(i);\n      if (uniformData[i]) {\n        strings.push(uniformData[i].type);\n      }\n    }\n    return strings.join(\"-\");\n  }\n  /** Destroys this System and removes all its textures. */\n  destroy() {\n    this._renderer = null;\n    this._cache = null;\n  }\n}\n/** @ignore */\nGlUniformGroupSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"uniformGroup\"\n};\n\nexport { GlUniformGroupSystem };\n//# sourceMappingURL=GlUniformGroupSystem.mjs.map\n", "import { DOMAdapter } from '../../../../environment/adapter.mjs';\n\n\"use strict\";\nfunction mapWebGLBlendModesToPixi(gl) {\n  const blendMap = {};\n  blendMap.normal = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n  blendMap.add = [gl.ONE, gl.ONE];\n  blendMap.multiply = [gl.DST_COLOR, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n  blendMap.screen = [gl.ONE, gl.ONE_MINUS_SRC_COLOR, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n  blendMap.none = [0, 0];\n  blendMap[\"normal-npm\"] = [gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n  blendMap[\"add-npm\"] = [gl.SRC_ALPHA, gl.ONE, gl.ONE, gl.ONE];\n  blendMap[\"screen-npm\"] = [gl.SRC_ALPHA, gl.ONE_MINUS_SRC_COLOR, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n  blendMap.erase = [gl.ZERO, gl.ONE_MINUS_SRC_ALPHA];\n  const isWebGl2 = !(gl instanceof DOMAdapter.get().getWebGLRenderingContext());\n  if (isWebGl2) {\n    blendMap.min = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, gl.MIN, gl.MIN];\n    blendMap.max = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, gl.MAX, gl.MAX];\n  } else {\n    const ext = gl.getExtension(\"EXT_blend_minmax\");\n    if (ext) {\n      blendMap.min = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, ext.MIN_EXT, ext.MIN_EXT];\n      blendMap.max = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, ext.MAX_EXT, ext.MAX_EXT];\n    }\n  }\n  return blendMap;\n}\n\nexport { mapWebGLBlendModesToPixi };\n//# sourceMappingURL=mapWebGLBlendModesToPixi.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { State } from '../../shared/state/State.mjs';\nimport { mapWebGLBlendModesToPixi } from './mapWebGLBlendModesToPixi.mjs';\n\n\"use strict\";\nconst BLEND = 0;\nconst OFFSET = 1;\nconst CULLING = 2;\nconst DEPTH_TEST = 3;\nconst WINDING = 4;\nconst DEPTH_MASK = 5;\nconst _GlStateSystem = class _GlStateSystem {\n  constructor() {\n    this.gl = null;\n    this.stateId = 0;\n    this.polygonOffset = 0;\n    this.blendMode = \"none\";\n    this._blendEq = false;\n    this.map = [];\n    this.map[BLEND] = this.setBlend;\n    this.map[OFFSET] = this.setOffset;\n    this.map[CULLING] = this.setCullFace;\n    this.map[DEPTH_TEST] = this.setDepthTest;\n    this.map[WINDING] = this.setFrontFace;\n    this.map[DEPTH_MASK] = this.setDepthMask;\n    this.checks = [];\n    this.defaultState = State.for2d();\n  }\n  contextChange(gl) {\n    this.gl = gl;\n    this.blendModesMap = mapWebGLBlendModesToPixi(gl);\n    this.resetState();\n  }\n  /**\n   * Sets the current state\n   * @param {*} state - The state to set.\n   */\n  set(state) {\n    state || (state = this.defaultState);\n    if (this.stateId !== state.data) {\n      let diff = this.stateId ^ state.data;\n      let i = 0;\n      while (diff) {\n        if (diff & 1) {\n          this.map[i].call(this, !!(state.data & 1 << i));\n        }\n        diff >>= 1;\n        i++;\n      }\n      this.stateId = state.data;\n    }\n    for (let i = 0; i < this.checks.length; i++) {\n      this.checks[i](this, state);\n    }\n  }\n  /**\n   * Sets the state, when previous state is unknown.\n   * @param {*} state - The state to set\n   */\n  forceState(state) {\n    state || (state = this.defaultState);\n    for (let i = 0; i < this.map.length; i++) {\n      this.map[i].call(this, !!(state.data & 1 << i));\n    }\n    for (let i = 0; i < this.checks.length; i++) {\n      this.checks[i](this, state);\n    }\n    this.stateId = state.data;\n  }\n  /**\n   * Sets whether to enable or disable blending.\n   * @param value - Turn on or off WebGl blending.\n   */\n  setBlend(value) {\n    this._updateCheck(_GlStateSystem._checkBlendMode, value);\n    this.gl[value ? \"enable\" : \"disable\"](this.gl.BLEND);\n  }\n  /**\n   * Sets whether to enable or disable polygon offset fill.\n   * @param value - Turn on or off webgl polygon offset testing.\n   */\n  setOffset(value) {\n    this._updateCheck(_GlStateSystem._checkPolygonOffset, value);\n    this.gl[value ? \"enable\" : \"disable\"](this.gl.POLYGON_OFFSET_FILL);\n  }\n  /**\n   * Sets whether to enable or disable depth test.\n   * @param value - Turn on or off webgl depth testing.\n   */\n  setDepthTest(value) {\n    this.gl[value ? \"enable\" : \"disable\"](this.gl.DEPTH_TEST);\n  }\n  /**\n   * Sets whether to enable or disable depth mask.\n   * @param value - Turn on or off webgl depth mask.\n   */\n  setDepthMask(value) {\n    this.gl.depthMask(value);\n  }\n  /**\n   * Sets whether to enable or disable cull face.\n   * @param {boolean} value - Turn on or off webgl cull face.\n   */\n  setCullFace(value) {\n    this.gl[value ? \"enable\" : \"disable\"](this.gl.CULL_FACE);\n  }\n  /**\n   * Sets the gl front face.\n   * @param {boolean} value - true is clockwise and false is counter-clockwise\n   */\n  setFrontFace(value) {\n    this.gl.frontFace(this.gl[value ? \"CW\" : \"CCW\"]);\n  }\n  /**\n   * Sets the blend mode.\n   * @param {number} value - The blend mode to set to.\n   */\n  setBlendMode(value) {\n    if (!this.blendModesMap[value]) {\n      value = \"normal\";\n    }\n    if (value === this.blendMode) {\n      return;\n    }\n    this.blendMode = value;\n    const mode = this.blendModesMap[value];\n    const gl = this.gl;\n    if (mode.length === 2) {\n      gl.blendFunc(mode[0], mode[1]);\n    } else {\n      gl.blendFuncSeparate(mode[0], mode[1], mode[2], mode[3]);\n    }\n    if (mode.length === 6) {\n      this._blendEq = true;\n      gl.blendEquationSeparate(mode[4], mode[5]);\n    } else if (this._blendEq) {\n      this._blendEq = false;\n      gl.blendEquationSeparate(gl.FUNC_ADD, gl.FUNC_ADD);\n    }\n  }\n  /**\n   * Sets the polygon offset.\n   * @param {number} value - the polygon offset\n   * @param {number} scale - the polygon offset scale\n   */\n  setPolygonOffset(value, scale) {\n    this.gl.polygonOffset(value, scale);\n  }\n  // used\n  /** Resets all the logic and disables the VAOs. */\n  resetState() {\n    this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL, false);\n    this.forceState(this.defaultState);\n    this._blendEq = true;\n    this.blendMode = \"\";\n    this.setBlendMode(\"normal\");\n  }\n  /**\n   * Checks to see which updates should be checked based on which settings have been activated.\n   *\n   * For example, if blend is enabled then we should check the blend modes each time the state is changed\n   * or if polygon fill is activated then we need to check if the polygon offset changes.\n   * The idea is that we only check what we have too.\n   * @param func - the checking function to add or remove\n   * @param value - should the check function be added or removed.\n   */\n  _updateCheck(func, value) {\n    const index = this.checks.indexOf(func);\n    if (value && index === -1) {\n      this.checks.push(func);\n    } else if (!value && index !== -1) {\n      this.checks.splice(index, 1);\n    }\n  }\n  /**\n   * A private little wrapper function that we call to check the blend mode.\n   * @param system - the System to perform the state check on\n   * @param state - the state that the blendMode will pulled from\n   */\n  static _checkBlendMode(system, state) {\n    system.setBlendMode(state.blendMode);\n  }\n  /**\n   * A private little wrapper function that we call to check the polygon offset.\n   * @param system - the System to perform the state check on\n   * @param state - the state that the blendMode will pulled from\n   */\n  static _checkPolygonOffset(system, state) {\n    system.setPolygonOffset(1, state.polygonOffset);\n  }\n  /**\n   * @ignore\n   */\n  destroy() {\n    this.gl = null;\n    this.checks.length = 0;\n  }\n};\n/** @ignore */\n_GlStateSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"state\"\n};\nlet GlStateSystem = _GlStateSystem;\n\nexport { GlStateSystem };\n//# sourceMappingURL=GlStateSystem.mjs.map\n", "import { GL_TARGETS, GL_TYPES, GL_FORMATS } from './const.mjs';\n\n\"use strict\";\nclass GlTexture {\n  constructor(texture) {\n    this.target = GL_TARGETS.TEXTURE_2D;\n    this.texture = texture;\n    this.width = -1;\n    this.height = -1;\n    this.type = GL_TYPES.UNSIGNED_BYTE;\n    this.internalFormat = GL_FORMATS.RGBA;\n    this.format = GL_FORMATS.RGBA;\n    this.samplerType = 0;\n  }\n}\n\nexport { GlTexture };\n//# sourceMappingURL=GlTexture.mjs.map\n", "\"use strict\";\nconst glUploadBufferImageResource = {\n  id: \"buffer\",\n  upload(source, glTexture, gl) {\n    if (glTexture.width === source.width || glTexture.height === source.height) {\n      gl.texSubImage2D(\n        gl.TEXTURE_2D,\n        0,\n        0,\n        0,\n        source.width,\n        source.height,\n        glTexture.format,\n        glTexture.type,\n        source.resource\n      );\n    } else {\n      gl.texImage2D(\n        glTexture.target,\n        0,\n        glTexture.internalFormat,\n        source.width,\n        source.height,\n        0,\n        glTexture.format,\n        glTexture.type,\n        source.resource\n      );\n    }\n    glTexture.width = source.width;\n    glTexture.height = source.height;\n  }\n};\n\nexport { glUploadBufferImageResource };\n//# sourceMappingURL=glUploadBufferImageResource.mjs.map\n", "\"use strict\";\nconst compressedFormatMap = {\n  \"bc1-rgba-unorm\": true,\n  \"bc1-rgba-unorm-srgb\": true,\n  \"bc2-rgba-unorm\": true,\n  \"bc2-rgba-unorm-srgb\": true,\n  \"bc3-rgba-unorm\": true,\n  \"bc3-rgba-unorm-srgb\": true,\n  \"bc4-r-unorm\": true,\n  \"bc4-r-snorm\": true,\n  \"bc5-rg-unorm\": true,\n  \"bc5-rg-snorm\": true,\n  \"bc6h-rgb-ufloat\": true,\n  \"bc6h-rgb-float\": true,\n  \"bc7-rgba-unorm\": true,\n  \"bc7-rgba-unorm-srgb\": true,\n  // ETC2 compressed formats usable if \"texture-compression-etc2\" is both\n  // supported by the device/user agent and enabled in requestDevice.\n  \"etc2-rgb8unorm\": true,\n  \"etc2-rgb8unorm-srgb\": true,\n  \"etc2-rgb8a1unorm\": true,\n  \"etc2-rgb8a1unorm-srgb\": true,\n  \"etc2-rgba8unorm\": true,\n  \"etc2-rgba8unorm-srgb\": true,\n  \"eac-r11unorm\": true,\n  \"eac-r11snorm\": true,\n  \"eac-rg11unorm\": true,\n  \"eac-rg11snorm\": true,\n  // ASTC compressed formats usable if \"texture-compression-astc\" is both\n  // supported by the device/user agent and enabled in requestDevice.\n  \"astc-4x4-unorm\": true,\n  \"astc-4x4-unorm-srgb\": true,\n  \"astc-5x4-unorm\": true,\n  \"astc-5x4-unorm-srgb\": true,\n  \"astc-5x5-unorm\": true,\n  \"astc-5x5-unorm-srgb\": true,\n  \"astc-6x5-unorm\": true,\n  \"astc-6x5-unorm-srgb\": true,\n  \"astc-6x6-unorm\": true,\n  \"astc-6x6-unorm-srgb\": true,\n  \"astc-8x5-unorm\": true,\n  \"astc-8x5-unorm-srgb\": true,\n  \"astc-8x6-unorm\": true,\n  \"astc-8x6-unorm-srgb\": true,\n  \"astc-8x8-unorm\": true,\n  \"astc-8x8-unorm-srgb\": true,\n  \"astc-10x5-unorm\": true,\n  \"astc-10x5-unorm-srgb\": true,\n  \"astc-10x6-unorm\": true,\n  \"astc-10x6-unorm-srgb\": true,\n  \"astc-10x8-unorm\": true,\n  \"astc-10x8-unorm-srgb\": true,\n  \"astc-10x10-unorm\": true,\n  \"astc-10x10-unorm-srgb\": true,\n  \"astc-12x10-unorm\": true,\n  \"astc-12x10-unorm-srgb\": true,\n  \"astc-12x12-unorm\": true,\n  \"astc-12x12-unorm-srgb\": true\n};\nconst glUploadCompressedTextureResource = {\n  id: \"compressed\",\n  upload(source, glTexture, gl) {\n    gl.pixelStorei(gl.UNPACK_ALIGNMENT, 4);\n    let mipWidth = source.pixelWidth;\n    let mipHeight = source.pixelHeight;\n    const compressed = !!compressedFormatMap[source.format];\n    for (let i = 0; i < source.resource.length; i++) {\n      const levelBuffer = source.resource[i];\n      if (compressed) {\n        gl.compressedTexImage2D(\n          gl.TEXTURE_2D,\n          i,\n          glTexture.internalFormat,\n          mipWidth,\n          mipHeight,\n          0,\n          levelBuffer\n        );\n      } else {\n        gl.texImage2D(\n          gl.TEXTURE_2D,\n          i,\n          glTexture.internalFormat,\n          mipWidth,\n          mipHeight,\n          0,\n          glTexture.format,\n          glTexture.type,\n          levelBuffer\n        );\n      }\n      mipWidth = Math.max(mipWidth >> 1, 1);\n      mipHeight = Math.max(mipHeight >> 1, 1);\n    }\n  }\n};\n\nexport { glUploadCompressedTextureResource };\n//# sourceMappingURL=glUploadCompressedTextureResource.mjs.map\n", "\"use strict\";\nconst glUploadImageResource = {\n  id: \"image\",\n  upload(source, glTexture, gl, webGLVersion) {\n    const glWidth = glTexture.width;\n    const glHeight = glTexture.height;\n    const textureWidth = source.pixelWidth;\n    const textureHeight = source.pixelHeight;\n    const resourceWidth = source.resourceWidth;\n    const resourceHeight = source.resourceHeight;\n    if (resourceWidth < textureWidth || resourceHeight < textureHeight) {\n      if (glWidth !== textureWidth || glHeight !== textureHeight) {\n        gl.texImage2D(\n          glTexture.target,\n          0,\n          glTexture.internalFormat,\n          textureWidth,\n          textureHeight,\n          0,\n          glTexture.format,\n          glTexture.type,\n          null\n        );\n      }\n      if (webGLVersion === 2) {\n        gl.texSubImage2D(\n          gl.TEXTURE_2D,\n          0,\n          0,\n          0,\n          resourceWidth,\n          resourceHeight,\n          glTexture.format,\n          glTexture.type,\n          source.resource\n        );\n      } else {\n        gl.texSubImage2D(\n          gl.TEXTURE_2D,\n          0,\n          0,\n          0,\n          glTexture.format,\n          glTexture.type,\n          source.resource\n        );\n      }\n    } else if (glWidth === textureWidth && glHeight === textureHeight) {\n      gl.texSubImage2D(\n        gl.TEXTURE_2D,\n        0,\n        0,\n        0,\n        glTexture.format,\n        glTexture.type,\n        source.resource\n      );\n    } else if (webGLVersion === 2) {\n      gl.texImage2D(\n        glTexture.target,\n        0,\n        glTexture.internalFormat,\n        textureWidth,\n        textureHeight,\n        0,\n        glTexture.format,\n        glTexture.type,\n        source.resource\n      );\n    } else {\n      gl.texImage2D(\n        glTexture.target,\n        0,\n        glTexture.internalFormat,\n        glTexture.format,\n        glTexture.type,\n        source.resource\n      );\n    }\n    glTexture.width = textureWidth;\n    glTexture.height = textureHeight;\n  }\n};\n\nexport { glUploadImageResource };\n//# sourceMappingURL=glUploadImageResource.mjs.map\n", "import { glUploadImageResource } from './glUploadImageResource.mjs';\n\n\"use strict\";\nconst glUploadVideoResource = {\n  id: \"video\",\n  upload(source, glTexture, gl, webGLVersion) {\n    if (!source.isValid) {\n      gl.texImage2D(\n        glTexture.target,\n        0,\n        glTexture.internalFormat,\n        1,\n        1,\n        0,\n        glTexture.format,\n        glTexture.type,\n        null\n      );\n      return;\n    }\n    glUploadImageResource.upload(source, glTexture, gl, webGLVersion);\n  }\n};\n\nexport { glUploadVideoResource };\n//# sourceMappingURL=glUploadVideoResource.mjs.map\n", "\"use strict\";\nconst scaleModeToGlFilter = {\n  linear: 9729,\n  nearest: 9728\n};\nconst mipmapScaleModeToGlFilter = {\n  linear: {\n    linear: 9987,\n    nearest: 9985\n  },\n  nearest: {\n    linear: 9986,\n    nearest: 9984\n  }\n};\nconst wrapModeToGlAddress = {\n  \"clamp-to-edge\": 33071,\n  repeat: 10497,\n  \"mirror-repeat\": 33648\n};\nconst compareModeToGlCompare = {\n  never: 512,\n  less: 513,\n  equal: 514,\n  \"less-equal\": 515,\n  greater: 516,\n  \"not-equal\": 517,\n  \"greater-equal\": 518,\n  always: 519\n};\n\nexport { compareModeToGlCompare, mipmapScaleModeToGlFilter, scaleModeToGlFilter, wrapModeToGlAddress };\n//# sourceMappingURL=pixiToGlMaps.mjs.map\n", "import { wrapModeToGlAddress, scaleModeToGlFilter, mipmapScaleModeToGlFilter, compareModeToGlCompare } from './pixiToGlMaps.mjs';\n\n\"use strict\";\nfunction applyStyleParams(style, gl, mipmaps, anisotropicExt, glFunctionName, firstParam, forceClamp, firstCreation) {\n  const castParam = firstParam;\n  if (!firstCreation || style.addressModeU !== \"repeat\" || style.addressModeV !== \"repeat\" || style.addressModeW !== \"repeat\") {\n    const wrapModeS = wrapModeToGlAddress[forceClamp ? \"clamp-to-edge\" : style.addressModeU];\n    const wrapModeT = wrapModeToGlAddress[forceClamp ? \"clamp-to-edge\" : style.addressModeV];\n    const wrapModeR = wrapModeToGlAddress[forceClamp ? \"clamp-to-edge\" : style.addressModeW];\n    gl[glFunctionName](castParam, gl.TEXTURE_WRAP_S, wrapModeS);\n    gl[glFunctionName](castParam, gl.TEXTURE_WRAP_T, wrapModeT);\n    if (gl.TEXTURE_WRAP_R)\n      gl[glFunctionName](castParam, gl.TEXTURE_WRAP_R, wrapModeR);\n  }\n  if (!firstCreation || style.magFilter !== \"linear\") {\n    gl[glFunctionName](castParam, gl.TEXTURE_MAG_FILTER, scaleModeToGlFilter[style.magFilter]);\n  }\n  if (mipmaps) {\n    if (!firstCreation || style.mipmapFilter !== \"linear\") {\n      const glFilterMode = mipmapScaleModeToGlFilter[style.minFilter][style.mipmapFilter];\n      gl[glFunctionName](castParam, gl.TEXTURE_MIN_FILTER, glFilterMode);\n    }\n  } else {\n    gl[glFunctionName](castParam, gl.TEXTURE_MIN_FILTER, scaleModeToGlFilter[style.minFilter]);\n  }\n  if (anisotropicExt && style.maxAnisotropy > 1) {\n    const level = Math.min(style.maxAnisotropy, gl.getParameter(anisotropicExt.MAX_TEXTURE_MAX_ANISOTROPY_EXT));\n    gl[glFunctionName](castParam, anisotropicExt.TEXTURE_MAX_ANISOTROPY_EXT, level);\n  }\n  if (style.compare) {\n    gl[glFunctionName](castParam, gl.TEXTURE_COMPARE_FUNC, compareModeToGlCompare[style.compare]);\n  }\n}\n\nexport { applyStyleParams };\n//# sourceMappingURL=applyStyleParams.mjs.map\n", "\"use strict\";\nfunction mapFormatToGlFormat(gl) {\n  return {\n    // 8-bit formats\n    r8unorm: gl.RED,\n    r8snorm: gl.RED,\n    r8uint: gl.RED,\n    r8sint: gl.RED,\n    // 16-bit formats\n    r16uint: gl.RED,\n    r16sint: gl.RED,\n    r16float: gl.RED,\n    rg8unorm: gl.RG,\n    rg8snorm: gl.RG,\n    rg8uint: gl.RG,\n    rg8sint: gl.RG,\n    // 32-bit formats\n    r32uint: gl.RED,\n    r32sint: gl.RED,\n    r32float: gl.RED,\n    rg16uint: gl.RG,\n    rg16sint: gl.RG,\n    rg16float: gl.RG,\n    rgba8unorm: gl.RGBA,\n    \"rgba8unorm-srgb\": gl.RGBA,\n    // Packed 32-bit formats\n    rgba8snorm: gl.RGBA,\n    rgba8uint: gl.RGBA,\n    rgba8sint: gl.RGBA,\n    bgra8unorm: gl.RGBA,\n    \"bgra8unorm-srgb\": gl.RGBA,\n    rgb9e5ufloat: gl.RGB,\n    rgb10a2unorm: gl.RGBA,\n    rg11b10ufloat: gl.RGB,\n    // 64-bit formats\n    rg32uint: gl.RG,\n    rg32sint: gl.RG,\n    rg32float: gl.RG,\n    rgba16uint: gl.RGBA,\n    rgba16sint: gl.RGBA,\n    rgba16float: gl.RGBA,\n    // 128-bit formats\n    rgba32uint: gl.RGBA,\n    rgba32sint: gl.RGBA,\n    rgba32float: gl.RGBA,\n    // Depth/stencil formats\n    stencil8: gl.STENCIL_INDEX8,\n    depth16unorm: gl.DEPTH_COMPONENT,\n    depth24plus: gl.DEPTH_COMPONENT,\n    \"depth24plus-stencil8\": gl.DEPTH_STENCIL,\n    depth32float: gl.DEPTH_COMPONENT,\n    \"depth32float-stencil8\": gl.DEPTH_STENCIL\n  };\n}\n\nexport { mapFormatToGlFormat };\n//# sourceMappingURL=mapFormatToGlFormat.mjs.map\n", "import { DOMAdapter } from '../../../../../environment/adapter.mjs';\n\n\"use strict\";\nfunction mapFormatToGlInternalFormat(gl, extensions) {\n  let srgb = {};\n  let bgra8unorm = gl.RGBA;\n  if (!(gl instanceof DOMAdapter.get().getWebGLRenderingContext())) {\n    srgb = {\n      \"rgba8unorm-srgb\": gl.SRGB8_ALPHA8,\n      \"bgra8unorm-srgb\": gl.SRGB8_ALPHA8\n    };\n    bgra8unorm = gl.RGBA8;\n  } else if (extensions.srgb) {\n    srgb = {\n      \"rgba8unorm-srgb\": extensions.srgb.SRGB8_ALPHA8_EXT,\n      \"bgra8unorm-srgb\": extensions.srgb.SRGB8_ALPHA8_EXT\n    };\n  }\n  return {\n    // 8-bit formats\n    r8unorm: gl.R8,\n    r8snorm: gl.R8_SNORM,\n    r8uint: gl.R8UI,\n    r8sint: gl.R8I,\n    // 16-bit formats\n    r16uint: gl.R16UI,\n    r16sint: gl.R16I,\n    r16float: gl.R16F,\n    rg8unorm: gl.RG8,\n    rg8snorm: gl.RG8_SNORM,\n    rg8uint: gl.RG8UI,\n    rg8sint: gl.RG8I,\n    // 32-bit formats\n    r32uint: gl.R32UI,\n    r32sint: gl.R32I,\n    r32float: gl.R32F,\n    rg16uint: gl.RG16UI,\n    rg16sint: gl.RG16I,\n    rg16float: gl.RG16F,\n    rgba8unorm: gl.RGBA,\n    ...srgb,\n    // Packed 32-bit formats\n    rgba8snorm: gl.RGBA8_SNORM,\n    rgba8uint: gl.RGBA8UI,\n    rgba8sint: gl.RGBA8I,\n    bgra8unorm,\n    rgb9e5ufloat: gl.RGB9_E5,\n    rgb10a2unorm: gl.RGB10_A2,\n    rg11b10ufloat: gl.R11F_G11F_B10F,\n    // 64-bit formats\n    rg32uint: gl.RG32UI,\n    rg32sint: gl.RG32I,\n    rg32float: gl.RG32F,\n    rgba16uint: gl.RGBA16UI,\n    rgba16sint: gl.RGBA16I,\n    rgba16float: gl.RGBA16F,\n    // 128-bit formats\n    rgba32uint: gl.RGBA32UI,\n    rgba32sint: gl.RGBA32I,\n    rgba32float: gl.RGBA32F,\n    // Depth/stencil formats\n    stencil8: gl.STENCIL_INDEX8,\n    depth16unorm: gl.DEPTH_COMPONENT16,\n    depth24plus: gl.DEPTH_COMPONENT24,\n    \"depth24plus-stencil8\": gl.DEPTH24_STENCIL8,\n    depth32float: gl.DEPTH_COMPONENT32F,\n    \"depth32float-stencil8\": gl.DEPTH32F_STENCIL8,\n    // Compressed formats\n    ...extensions.s3tc ? {\n      \"bc1-rgba-unorm\": extensions.s3tc.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n      \"bc2-rgba-unorm\": extensions.s3tc.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n      \"bc3-rgba-unorm\": extensions.s3tc.COMPRESSED_RGBA_S3TC_DXT5_EXT\n    } : {},\n    ...extensions.s3tc_sRGB ? {\n      \"bc1-rgba-unorm-srgb\": extensions.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,\n      \"bc2-rgba-unorm-srgb\": extensions.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,\n      \"bc3-rgba-unorm-srgb\": extensions.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT\n    } : {},\n    ...extensions.rgtc ? {\n      \"bc4-r-unorm\": extensions.rgtc.COMPRESSED_RED_RGTC1_EXT,\n      \"bc4-r-snorm\": extensions.rgtc.COMPRESSED_SIGNED_RED_RGTC1_EXT,\n      \"bc5-rg-unorm\": extensions.rgtc.COMPRESSED_RED_GREEN_RGTC2_EXT,\n      \"bc5-rg-snorm\": extensions.rgtc.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT\n    } : {},\n    ...extensions.bptc ? {\n      \"bc6h-rgb-float\": extensions.bptc.COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT,\n      \"bc6h-rgb-ufloat\": extensions.bptc.COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT,\n      \"bc7-rgba-unorm\": extensions.bptc.COMPRESSED_RGBA_BPTC_UNORM_EXT,\n      \"bc7-rgba-unorm-srgb\": extensions.bptc.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT\n    } : {},\n    ...extensions.etc ? {\n      \"etc2-rgb8unorm\": extensions.etc.COMPRESSED_RGB8_ETC2,\n      \"etc2-rgb8unorm-srgb\": extensions.etc.COMPRESSED_SRGB8_ETC2,\n      \"etc2-rgb8a1unorm\": extensions.etc.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2,\n      \"etc2-rgb8a1unorm-srgb\": extensions.etc.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2,\n      \"etc2-rgba8unorm\": extensions.etc.COMPRESSED_RGBA8_ETC2_EAC,\n      \"etc2-rgba8unorm-srgb\": extensions.etc.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC,\n      \"eac-r11unorm\": extensions.etc.COMPRESSED_R11_EAC,\n      // 'eac-r11snorm'\n      \"eac-rg11unorm\": extensions.etc.COMPRESSED_SIGNED_RG11_EAC\n      // 'eac-rg11snorm'\n    } : {},\n    ...extensions.astc ? {\n      \"astc-4x4-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_4x4_KHR,\n      \"astc-4x4-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR,\n      \"astc-5x4-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_5x4_KHR,\n      \"astc-5x4-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR,\n      \"astc-5x5-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_5x5_KHR,\n      \"astc-5x5-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR,\n      \"astc-6x5-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_6x5_KHR,\n      \"astc-6x5-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR,\n      \"astc-6x6-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_6x6_KHR,\n      \"astc-6x6-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR,\n      \"astc-8x5-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_8x5_KHR,\n      \"astc-8x5-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR,\n      \"astc-8x6-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_8x6_KHR,\n      \"astc-8x6-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR,\n      \"astc-8x8-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_8x8_KHR,\n      \"astc-8x8-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR,\n      \"astc-10x5-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_10x5_KHR,\n      \"astc-10x5-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR,\n      \"astc-10x6-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_10x6_KHR,\n      \"astc-10x6-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR,\n      \"astc-10x8-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_10x8_KHR,\n      \"astc-10x8-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR,\n      \"astc-10x10-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_10x10_KHR,\n      \"astc-10x10-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR,\n      \"astc-12x10-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_12x10_KHR,\n      \"astc-12x10-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR,\n      \"astc-12x12-unorm\": extensions.astc.COMPRESSED_RGBA_ASTC_12x12_KHR,\n      \"astc-12x12-unorm-srgb\": extensions.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR\n    } : {}\n  };\n}\n\nexport { mapFormatToGlInternalFormat };\n//# sourceMappingURL=mapFormatToGlInternalFormat.mjs.map\n", "\"use strict\";\nfunction mapFormatToGlType(gl) {\n  return {\n    // 8-bit formats\n    r8unorm: gl.UNSIGNED_BYTE,\n    r8snorm: gl.BYTE,\n    r8uint: gl.UNSIGNED_BYTE,\n    r8sint: gl.BYTE,\n    // 16-bit formats\n    r16uint: gl.UNSIGNED_SHORT,\n    r16sint: gl.SHORT,\n    r16float: gl.HALF_FLOAT,\n    rg8unorm: gl.UNSIGNED_BYTE,\n    rg8snorm: gl.BYTE,\n    rg8uint: gl.UNSIGNED_BYTE,\n    rg8sint: gl.BYTE,\n    // 32-bit formats\n    r32uint: gl.UNSIGNED_INT,\n    r32sint: gl.INT,\n    r32float: gl.FLOAT,\n    rg16uint: gl.UNSIGNED_SHORT,\n    rg16sint: gl.SHORT,\n    rg16float: gl.HALF_FLOAT,\n    rgba8unorm: gl.UNSIGNED_BYTE,\n    \"rgba8unorm-srgb\": gl.UNSIGNED_BYTE,\n    // Packed 32-bit formats\n    rgba8snorm: gl.BYTE,\n    rgba8uint: gl.UNSIGNED_BYTE,\n    rgba8sint: gl.BYTE,\n    bgra8unorm: gl.UNSIGNED_BYTE,\n    \"bgra8unorm-srgb\": gl.UNSIGNED_BYTE,\n    rgb9e5ufloat: gl.UNSIGNED_INT_5_9_9_9_REV,\n    rgb10a2unorm: gl.UNSIGNED_INT_2_10_10_10_REV,\n    rg11b10ufloat: gl.UNSIGNED_INT_10F_11F_11F_REV,\n    // 64-bit formats\n    rg32uint: gl.UNSIGNED_INT,\n    rg32sint: gl.INT,\n    rg32float: gl.FLOAT,\n    rgba16uint: gl.UNSIGNED_SHORT,\n    rgba16sint: gl.SHORT,\n    rgba16float: gl.HALF_FLOAT,\n    // 128-bit formats\n    rgba32uint: gl.UNSIGNED_INT,\n    rgba32sint: gl.INT,\n    rgba32float: gl.FLOAT,\n    // Depth/stencil formats\n    stencil8: gl.UNSIGNED_BYTE,\n    depth16unorm: gl.UNSIGNED_SHORT,\n    depth24plus: gl.UNSIGNED_INT,\n    \"depth24plus-stencil8\": gl.UNSIGNED_INT_24_8,\n    depth32float: gl.FLOAT,\n    \"depth32float-stencil8\": gl.FLOAT_32_UNSIGNED_INT_24_8_REV\n  };\n}\n\nexport { mapFormatToGlType };\n//# sourceMappingURL=mapFormatToGlType.mjs.map\n", "import { DOMAdapter } from '../../../../environment/adapter.mjs';\nimport { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { Texture } from '../../shared/texture/Texture.mjs';\nimport { GlTexture } from './GlTexture.mjs';\nimport { glUploadBufferImageResource } from './uploaders/glUploadBufferImageResource.mjs';\nimport { glUploadCompressedTextureResource } from './uploaders/glUploadCompressedTextureResource.mjs';\nimport { glUploadImageResource } from './uploaders/glUploadImageResource.mjs';\nimport { glUploadVideoResource } from './uploaders/glUploadVideoResource.mjs';\nimport { applyStyleParams } from './utils/applyStyleParams.mjs';\nimport { mapFormatToGlFormat } from './utils/mapFormatToGlFormat.mjs';\nimport { mapFormatToGlInternalFormat } from './utils/mapFormatToGlInternalFormat.mjs';\nimport { mapFormatToGlType } from './utils/mapFormatToGlType.mjs';\nimport './utils/unpremultiplyAlpha.mjs';\n\n\"use strict\";\nconst BYTES_PER_PIXEL = 4;\nclass GlTextureSystem {\n  constructor(renderer) {\n    this.managedTextures = [];\n    this._glTextures = /* @__PURE__ */ Object.create(null);\n    this._glSamplers = /* @__PURE__ */ Object.create(null);\n    this._boundTextures = [];\n    this._activeTextureLocation = -1;\n    this._boundSamplers = /* @__PURE__ */ Object.create(null);\n    this._uploads = {\n      image: glUploadImageResource,\n      buffer: glUploadBufferImageResource,\n      video: glUploadVideoResource,\n      compressed: glUploadCompressedTextureResource\n    };\n    this._premultiplyAlpha = false;\n    // TODO - separate samplers will be a cool thing to add, but not right now!\n    this._useSeparateSamplers = false;\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_glTextures\");\n    this._renderer.renderableGC.addManagedHash(this, \"_glSamplers\");\n  }\n  contextChange(gl) {\n    this._gl = gl;\n    if (!this._mapFormatToInternalFormat) {\n      this._mapFormatToInternalFormat = mapFormatToGlInternalFormat(gl, this._renderer.context.extensions);\n      this._mapFormatToType = mapFormatToGlType(gl);\n      this._mapFormatToFormat = mapFormatToGlFormat(gl);\n    }\n    this._glTextures = /* @__PURE__ */ Object.create(null);\n    this._glSamplers = /* @__PURE__ */ Object.create(null);\n    this._boundSamplers = /* @__PURE__ */ Object.create(null);\n    this._premultiplyAlpha = false;\n    for (let i = 0; i < 16; i++) {\n      this.bind(Texture.EMPTY, i);\n    }\n  }\n  initSource(source) {\n    this.bind(source);\n  }\n  bind(texture, location = 0) {\n    const source = texture.source;\n    if (texture) {\n      this.bindSource(source, location);\n      if (this._useSeparateSamplers) {\n        this._bindSampler(source.style, location);\n      }\n    } else {\n      this.bindSource(null, location);\n      if (this._useSeparateSamplers) {\n        this._bindSampler(null, location);\n      }\n    }\n  }\n  bindSource(source, location = 0) {\n    const gl = this._gl;\n    source._touched = this._renderer.textureGC.count;\n    if (this._boundTextures[location] !== source) {\n      this._boundTextures[location] = source;\n      this._activateLocation(location);\n      source || (source = Texture.EMPTY.source);\n      const glTexture = this.getGlSource(source);\n      gl.bindTexture(glTexture.target, glTexture.texture);\n    }\n  }\n  _bindSampler(style, location = 0) {\n    const gl = this._gl;\n    if (!style) {\n      this._boundSamplers[location] = null;\n      gl.bindSampler(location, null);\n      return;\n    }\n    const sampler = this._getGlSampler(style);\n    if (this._boundSamplers[location] !== sampler) {\n      this._boundSamplers[location] = sampler;\n      gl.bindSampler(location, sampler);\n    }\n  }\n  unbind(texture) {\n    const source = texture.source;\n    const boundTextures = this._boundTextures;\n    const gl = this._gl;\n    for (let i = 0; i < boundTextures.length; i++) {\n      if (boundTextures[i] === source) {\n        this._activateLocation(i);\n        const glTexture = this.getGlSource(source);\n        gl.bindTexture(glTexture.target, null);\n        boundTextures[i] = null;\n      }\n    }\n  }\n  _activateLocation(location) {\n    if (this._activeTextureLocation !== location) {\n      this._activeTextureLocation = location;\n      this._gl.activeTexture(this._gl.TEXTURE0 + location);\n    }\n  }\n  _initSource(source) {\n    const gl = this._gl;\n    const glTexture = new GlTexture(gl.createTexture());\n    glTexture.type = this._mapFormatToType[source.format];\n    glTexture.internalFormat = this._mapFormatToInternalFormat[source.format];\n    glTexture.format = this._mapFormatToFormat[source.format];\n    if (source.autoGenerateMipmaps && (this._renderer.context.supports.nonPowOf2mipmaps || source.isPowerOfTwo)) {\n      const biggestDimension = Math.max(source.width, source.height);\n      source.mipLevelCount = Math.floor(Math.log2(biggestDimension)) + 1;\n    }\n    this._glTextures[source.uid] = glTexture;\n    if (!this.managedTextures.includes(source)) {\n      source.on(\"update\", this.onSourceUpdate, this);\n      source.on(\"resize\", this.onSourceUpdate, this);\n      source.on(\"styleChange\", this.onStyleChange, this);\n      source.on(\"destroy\", this.onSourceDestroy, this);\n      source.on(\"unload\", this.onSourceUnload, this);\n      source.on(\"updateMipmaps\", this.onUpdateMipmaps, this);\n      this.managedTextures.push(source);\n    }\n    this.onSourceUpdate(source);\n    this.updateStyle(source, false);\n    return glTexture;\n  }\n  onStyleChange(source) {\n    this.updateStyle(source, false);\n  }\n  updateStyle(source, firstCreation) {\n    const gl = this._gl;\n    const glTexture = this.getGlSource(source);\n    gl.bindTexture(gl.TEXTURE_2D, glTexture.texture);\n    this._boundTextures[this._activeTextureLocation] = source;\n    applyStyleParams(\n      source.style,\n      gl,\n      source.mipLevelCount > 1,\n      this._renderer.context.extensions.anisotropicFiltering,\n      \"texParameteri\",\n      gl.TEXTURE_2D,\n      // will force a clamp to edge if the texture is not a power of two\n      !this._renderer.context.supports.nonPowOf2wrapping && !source.isPowerOfTwo,\n      firstCreation\n    );\n  }\n  onSourceUnload(source) {\n    const glTexture = this._glTextures[source.uid];\n    if (!glTexture)\n      return;\n    this.unbind(source);\n    this._glTextures[source.uid] = null;\n    this._gl.deleteTexture(glTexture.texture);\n  }\n  onSourceUpdate(source) {\n    const gl = this._gl;\n    const glTexture = this.getGlSource(source);\n    gl.bindTexture(gl.TEXTURE_2D, glTexture.texture);\n    this._boundTextures[this._activeTextureLocation] = source;\n    const premultipliedAlpha = source.alphaMode === \"premultiply-alpha-on-upload\";\n    if (this._premultiplyAlpha !== premultipliedAlpha) {\n      this._premultiplyAlpha = premultipliedAlpha;\n      gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, premultipliedAlpha);\n    }\n    if (this._uploads[source.uploadMethodId]) {\n      this._uploads[source.uploadMethodId].upload(source, glTexture, gl, this._renderer.context.webGLVersion);\n    } else {\n      gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, source.pixelWidth, source.pixelHeight, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);\n    }\n    if (source.autoGenerateMipmaps && source.mipLevelCount > 1) {\n      this.onUpdateMipmaps(source, false);\n    }\n  }\n  onUpdateMipmaps(source, bind = true) {\n    if (bind)\n      this.bindSource(source, 0);\n    const glTexture = this.getGlSource(source);\n    this._gl.generateMipmap(glTexture.target);\n  }\n  onSourceDestroy(source) {\n    source.off(\"destroy\", this.onSourceDestroy, this);\n    source.off(\"update\", this.onSourceUpdate, this);\n    source.off(\"resize\", this.onSourceUpdate, this);\n    source.off(\"unload\", this.onSourceUnload, this);\n    source.off(\"styleChange\", this.onStyleChange, this);\n    source.off(\"updateMipmaps\", this.onUpdateMipmaps, this);\n    this.managedTextures.splice(this.managedTextures.indexOf(source), 1);\n    this.onSourceUnload(source);\n  }\n  _initSampler(style) {\n    const gl = this._gl;\n    const glSampler = this._gl.createSampler();\n    this._glSamplers[style._resourceId] = glSampler;\n    applyStyleParams(\n      style,\n      gl,\n      this._boundTextures[this._activeTextureLocation].mipLevelCount > 1,\n      this._renderer.context.extensions.anisotropicFiltering,\n      \"samplerParameteri\",\n      glSampler,\n      false,\n      true\n    );\n    return this._glSamplers[style._resourceId];\n  }\n  _getGlSampler(sampler) {\n    return this._glSamplers[sampler._resourceId] || this._initSampler(sampler);\n  }\n  getGlSource(source) {\n    return this._glTextures[source.uid] || this._initSource(source);\n  }\n  generateCanvas(texture) {\n    const { pixels, width, height } = this.getPixels(texture);\n    const canvas = DOMAdapter.get().createCanvas();\n    canvas.width = width;\n    canvas.height = height;\n    const ctx = canvas.getContext(\"2d\");\n    if (ctx) {\n      const imageData = ctx.createImageData(width, height);\n      imageData.data.set(pixels);\n      ctx.putImageData(imageData, 0, 0);\n    }\n    return canvas;\n  }\n  getPixels(texture) {\n    const resolution = texture.source.resolution;\n    const frame = texture.frame;\n    const width = Math.max(Math.round(frame.width * resolution), 1);\n    const height = Math.max(Math.round(frame.height * resolution), 1);\n    const pixels = new Uint8Array(BYTES_PER_PIXEL * width * height);\n    const renderer = this._renderer;\n    const renderTarget = renderer.renderTarget.getRenderTarget(texture);\n    const glRenterTarget = renderer.renderTarget.getGpuRenderTarget(renderTarget);\n    const gl = renderer.gl;\n    gl.bindFramebuffer(gl.FRAMEBUFFER, glRenterTarget.resolveTargetFramebuffer);\n    gl.readPixels(\n      Math.round(frame.x * resolution),\n      Math.round(frame.y * resolution),\n      width,\n      height,\n      gl.RGBA,\n      gl.UNSIGNED_BYTE,\n      pixels\n    );\n    if (false) {\n      unpremultiplyAlpha(pixels);\n    }\n    return { pixels: new Uint8ClampedArray(pixels.buffer), width, height };\n  }\n  destroy() {\n    this.managedTextures.slice().forEach((source) => this.onSourceDestroy(source));\n    this.managedTextures = null;\n    this._renderer = null;\n  }\n  resetState() {\n    this._activeTextureLocation = -1;\n    this._boundTextures.fill(Texture.EMPTY.source);\n    this._boundSamplers = /* @__PURE__ */ Object.create(null);\n  }\n}\n/** @ignore */\nGlTextureSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"texture\"\n};\n\nexport { GlTextureSystem };\n//# sourceMappingURL=GlTextureSystem.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { Matrix } from '../../../maths/matrix/Matrix.mjs';\nimport { getMaxTexturesPerBatch } from '../../../rendering/batcher/gl/utils/maxRecommendedTextures.mjs';\nimport { compileHighShaderGlProgram } from '../../../rendering/high-shader/compileHighShaderToProgram.mjs';\nimport { colorBitGl } from '../../../rendering/high-shader/shader-bits/colorBit.mjs';\nimport { generateTextureBatchBitGl } from '../../../rendering/high-shader/shader-bits/generateTextureBatchBit.mjs';\nimport { localUniformBitGl } from '../../../rendering/high-shader/shader-bits/localUniformBit.mjs';\nimport { roundPixelsBitGl } from '../../../rendering/high-shader/shader-bits/roundPixelsBit.mjs';\nimport { getBatchSamplersUniformGroup } from '../../../rendering/renderers/gl/shader/getBatchSamplersUniformGroup.mjs';\nimport { Shader } from '../../../rendering/renderers/shared/shader/Shader.mjs';\nimport { UniformGroup } from '../../../rendering/renderers/shared/shader/UniformGroup.mjs';\n\n\"use strict\";\nclass GlGraphicsAdaptor {\n  init() {\n    const uniforms = new UniformGroup({\n      uColor: { value: new Float32Array([1, 1, 1, 1]), type: \"vec4<f32>\" },\n      uTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uRound: { value: 0, type: \"f32\" }\n    });\n    const maxTextures = getMaxTexturesPerBatch();\n    const glProgram = compileHighShaderGlProgram({\n      name: \"graphics\",\n      bits: [\n        colorBitGl,\n        generateTextureBatchBitGl(maxTextures),\n        localUniformBitGl,\n        roundPixelsBitGl\n      ]\n    });\n    this.shader = new Shader({\n      glProgram,\n      resources: {\n        localUniforms: uniforms,\n        batchSamplers: getBatchSamplersUniformGroup(maxTextures)\n      }\n    });\n  }\n  execute(graphicsPipe, renderable) {\n    const context = renderable.context;\n    const shader = context.customShader || this.shader;\n    const renderer = graphicsPipe.renderer;\n    const contextSystem = renderer.graphicsContext;\n    const {\n      batcher,\n      instructions\n    } = contextSystem.getContextRenderData(context);\n    shader.groups[0] = renderer.globalUniforms.bindGroup;\n    renderer.state.set(graphicsPipe.state);\n    renderer.shader.bind(shader);\n    renderer.geometry.bind(batcher.geometry, shader.glProgram);\n    const batches = instructions.instructions;\n    for (let i = 0; i < instructions.instructionSize; i++) {\n      const batch = batches[i];\n      if (batch.size) {\n        for (let j = 0; j < batch.textures.count; j++) {\n          renderer.texture.bind(batch.textures.textures[j], j);\n        }\n        renderer.geometry.draw(batch.topology, batch.size, batch.start);\n      }\n    }\n  }\n  destroy() {\n    this.shader.destroy(true);\n    this.shader = null;\n  }\n}\n/** @ignore */\nGlGraphicsAdaptor.extension = {\n  type: [\n    ExtensionType.WebGLPipesAdaptor\n  ],\n  name: \"graphics\"\n};\n\nexport { GlGraphicsAdaptor };\n//# sourceMappingURL=GlGraphicsAdaptor.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { Matrix } from '../../../maths/matrix/Matrix.mjs';\nimport { compileHighShaderGlProgram } from '../../../rendering/high-shader/compileHighShaderToProgram.mjs';\nimport { localUniformBitGl } from '../../../rendering/high-shader/shader-bits/localUniformBit.mjs';\nimport { roundPixelsBitGl } from '../../../rendering/high-shader/shader-bits/roundPixelsBit.mjs';\nimport { textureBitGl } from '../../../rendering/high-shader/shader-bits/textureBit.mjs';\nimport { Shader } from '../../../rendering/renderers/shared/shader/Shader.mjs';\nimport { Texture } from '../../../rendering/renderers/shared/texture/Texture.mjs';\nimport { warn } from '../../../utils/logging/warn.mjs';\n\n\"use strict\";\nclass GlMeshAdaptor {\n  init() {\n    const glProgram = compileHighShaderGlProgram({\n      name: \"mesh\",\n      bits: [\n        localUniformBitGl,\n        textureBitGl,\n        roundPixelsBitGl\n      ]\n    });\n    this._shader = new Shader({\n      glProgram,\n      resources: {\n        uTexture: Texture.EMPTY.source,\n        textureUniforms: {\n          uTextureMatrix: { type: \"mat3x3<f32>\", value: new Matrix() }\n        }\n      }\n    });\n  }\n  execute(meshPipe, mesh) {\n    const renderer = meshPipe.renderer;\n    let shader = mesh._shader;\n    if (!shader) {\n      shader = this._shader;\n      const texture = mesh.texture;\n      const source = texture.source;\n      shader.resources.uTexture = source;\n      shader.resources.uSampler = source.style;\n      shader.resources.textureUniforms.uniforms.uTextureMatrix = texture.textureMatrix.mapCoord;\n    } else if (!shader.glProgram) {\n      warn(\"Mesh shader has no glProgram\", mesh.shader);\n      return;\n    }\n    shader.groups[100] = renderer.globalUniforms.bindGroup;\n    shader.groups[101] = meshPipe.localUniformsBindGroup;\n    renderer.encoder.draw({\n      geometry: mesh._geometry,\n      shader,\n      state: mesh.state\n    });\n  }\n  destroy() {\n    this._shader.destroy(true);\n    this._shader = null;\n  }\n}\nGlMeshAdaptor.extension = {\n  type: [\n    ExtensionType.WebGLPipesAdaptor\n  ],\n  name: \"mesh\"\n};\n\nexport { GlMeshAdaptor };\n//# sourceMappingURL=GlMeshAdaptor.mjs.map\n", "import { extensions, ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { GlGraphicsAdaptor } from '../../../scene/graphics/gl/GlGraphicsAdaptor.mjs';\nimport { GlMeshAdaptor } from '../../../scene/mesh/gl/GlMeshAdaptor.mjs';\nimport { GlBatchAdaptor } from '../../batcher/gl/GlBatchAdaptor.mjs';\nimport { AbstractRenderer } from '../shared/system/AbstractRenderer.mjs';\nimport { SharedSystems, SharedRenderPipes } from '../shared/system/SharedSystems.mjs';\nimport { RendererType } from '../types.mjs';\nimport { GlBufferSystem } from './buffer/GlBufferSystem.mjs';\nimport { GlContextSystem } from './context/GlContextSystem.mjs';\nimport { GlGeometrySystem } from './geometry/GlGeometrySystem.mjs';\nimport { GlBackBufferSystem } from './GlBackBufferSystem.mjs';\nimport { GlColorMaskSystem } from './GlColorMaskSystem.mjs';\nimport { GlEncoderSystem } from './GlEncoderSystem.mjs';\nimport { GlStencilSystem } from './GlStencilSystem.mjs';\nimport { GlUboSystem } from './GlUboSystem.mjs';\nimport { GlRenderTargetSystem } from './renderTarget/GlRenderTargetSystem.mjs';\nimport { GlShaderSystem } from './shader/GlShaderSystem.mjs';\nimport { GlUniformGroupSystem } from './shader/GlUniformGroupSystem.mjs';\nimport { GlStateSystem } from './state/GlStateSystem.mjs';\nimport { GlTextureSystem } from './texture/GlTextureSystem.mjs';\n\n\"use strict\";\nconst DefaultWebGLSystems = [\n  ...SharedSystems,\n  GlUboSystem,\n  GlBackBufferSystem,\n  GlContextSystem,\n  GlBufferSystem,\n  GlTextureSystem,\n  GlRenderTargetSystem,\n  GlGeometrySystem,\n  GlUniformGroupSystem,\n  GlShaderSystem,\n  GlEncoderSystem,\n  GlStateSystem,\n  GlStencilSystem,\n  GlColorMaskSystem\n];\nconst DefaultWebGLPipes = [...SharedRenderPipes];\nconst DefaultWebGLAdapters = [GlBatchAdaptor, GlMeshAdaptor, GlGraphicsAdaptor];\nconst systems = [];\nconst renderPipes = [];\nconst renderPipeAdaptors = [];\nextensions.handleByNamedList(ExtensionType.WebGLSystem, systems);\nextensions.handleByNamedList(ExtensionType.WebGLPipes, renderPipes);\nextensions.handleByNamedList(ExtensionType.WebGLPipesAdaptor, renderPipeAdaptors);\nextensions.add(...DefaultWebGLSystems, ...DefaultWebGLPipes, ...DefaultWebGLAdapters);\nclass WebGLRenderer extends AbstractRenderer {\n  constructor() {\n    const systemConfig = {\n      name: \"webgl\",\n      type: RendererType.WEBGL,\n      systems,\n      renderPipes,\n      renderPipeAdaptors\n    };\n    super(systemConfig);\n  }\n}\n\nexport { WebGLRenderer };\n//# sourceMappingURL=WebGLRenderer.mjs.map\n"], "names": ["GlBatchAdaptor", "State", "batcherPipe", "batchPipe", "geometry", "shader", "renderer", "didUpload", "batch", "textures", "ExtensionType", "BUFFER_TYPE", "BUFFER_TYPE2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buffer", "type", "GlBufferSystem", "gl", "gl<PERSON>uffer", "index", "hasTransformFeedback", "freeIndex", "loop", "nextIndex", "cur<PERSON><PERSON>", "offset", "size", "data", "drawType", "BufferUsage", "id", "contextLost", "_GlContextSystem", "options", "multiView", "warn", "DOMAdapter", "alpha", "premultipliedAlpha", "antialias", "targetCanvas", "canvas", "element", "preferWebGLVersion", "common", "provokeExt", "event", "attributes", "supports", "isWebGl2", "extensions", "GlContextSystem", "GL_FORMATS", "GL_FORMATS2", "GL_TARGETS", "GL_TARGETS2", "GL_TYPES", "GL_TYPES2", "infoMap", "getGlTypeFromFormat", "format", "topologyToGlMap", "GlGeometrySystem", "nativeVaoExtension", "vao", "nativeInstancedExtension", "a", "b", "c", "d", "e", "program", "bufferSystem", "i", "geometryAttributes", "shaderAttributes", "j", "attribs", "strings", "_incRefCount", "signature", "vaoObjectHash", "ensureAttributes", "buffers", "lastBuffer", "attribute", "programAttrib", "location", "attributeInfo", "getAttributeInfoFromFormat", "divisor", "topology", "start", "instanceCount", "glTopology", "byteSize", "glType", "bigTriangleGeometry", "Geometry", "_GlBackBufferSystem", "useBackBuffer", "bigTriangleProgram", "GlProgram", "Shader", "Texture", "renderTarget", "renderTarget2", "targetSourceTexture", "TextureSource", "GlBackBufferSystem", "GlColorMaskSystem", "colorMask", "GlEncoderSystem", "state", "skipSync", "GlRenderTarget", "GlStencilSystem", "STENCIL_MODES", "stencilState", "stencilMode", "stencilReference", "mode", "GpuStencilModesToPixi", "_stencilCache", "WGSL_TO_STD40_SIZE", "createUboElementsSTD40", "uniformData", "uboElements", "chunkSize", "uboElement", "boundary", "curOffset", "generateArraySyncSTD40", "offsetToAdd", "rowSize", "elementSize", "remainder", "createUboSyncFunctionSTD40", "createUboSyncFunction", "uboSyncFunctionsSTD40", "GlUboSystem", "UboSystem", "GlRenderTargetAdaptor", "Rectangle", "renderTargetSystem", "sourceRenderSurfaceTexture", "destinationTexture", "originSrc", "originDest", "glRenderTarget", "clear", "clearColor", "viewport", "source", "gpuRenderTarget", "viewPortY", "texture", "viewPortCache", "renderBuffer", "_renderTarget", "CLEAR", "clearColorCache", "clearColorArray", "resolveTargetFramebuffer", "colorTexture", "glTexture", "viewFramebuffer", "_", "msaa<PERSON><PERSON><PERSON><PERSON>er", "source2", "glInternalFormat", "depthStencilRender<PERSON><PERSON>er", "resource", "CanvasSource", "contextCanvas", "canvasSource", "GlRenderTargetSystem", "RenderTargetSystem", "generateShaderSyncCode", "shaderSystem", "funcFragments", "headerFragments", "addedTextreSystem", "textureCount", "programData", "group", "UniformGroup", "resName", "BufferResource", "uniformName", "functionSource", "GlProgramData", "compileShader", "src", "booleanArray", "array", "defaultValue", "GL_TABLE", "GL_TO_GLSL_TYPES", "GLSL_TO_VERTEX_TYPES", "mapType", "typeNames", "tn", "mapGlToVertexFormat", "typeValue", "extractAttributesFromGlProgram", "sortAttributes", "totalAttributes", "attribData", "keys", "getUboData", "uniformBlocks", "totalUniformsBlocks", "name", "uniformBlockIndex", "getUniformData", "uniforms", "totalUniforms", "isArray", "logPrettyShaderError", "shaderSrc", "line", "shaderLog", "splitShader", "dedupe", "lineNumbers", "n", "logArgs", "number", "fragmentSourceToLog", "logProgramError", "vertexShader", "fragmentShader", "generateProgram", "gl<PERSON><PERSON><PERSON><PERSON><PERSON>", "gl<PERSON>rag<PERSON><PERSON>er", "webGLProgram", "transformFeedbackVaryings", "defaultSyncData", "GlShaderSystem", "getMaxTexturesPerBatch", "syncFunction", "uniformGroup", "isBufferResource", "boundLocation", "key", "UNIFORM_TO_SINGLE_SETTERS", "UNIFORM_TO_ARRAY_SETTERS", "generateUniformsSync", "uniform", "parsed", "uniformParsers", "parser", "template", "GlUniformGroupSystem", "syncData", "uniformGroupSyncHash", "preFix", "mapWebGLBlendModesToPixi", "blendMap", "ext", "BLEND", "OFFSET", "CULLING", "DEPTH_TEST", "WINDING", "DEPTH_MASK", "_GlStateSystem", "diff", "value", "scale", "func", "system", "GlStateSystem", "GlTexture", "glUploadBufferImageResource", "compressedFormatMap", "glUploadCompressedTextureResource", "mip<PERSON><PERSON><PERSON>", "mipHeight", "compressed", "<PERSON><PERSON><PERSON><PERSON>", "glUploadImageResource", "webGLVersion", "glWidth", "glHeight", "textureWidth", "textureHeight", "resourceWidth", "resourceHeight", "glUploadVideoResource", "scaleModeToGlFilter", "mipmapScaleModeToGlFilter", "wrapModeToGlAddress", "compareModeToGlCompare", "applyStyleParams", "style", "mipmaps", "anisotropicExt", "glFunctionName", "firstParam", "forceClamp", "firstCreation", "<PERSON><PERSON><PERSON><PERSON>", "wrapModeS", "wrapModeT", "wrapModeR", "glFilterMode", "level", "mapFormatToGlFormat", "mapFormatToGlInternalFormat", "srgb", "bgra8unorm", "mapFormatToGlType", "BYTES_PER_PIXEL", "GlTextureSystem", "sampler", "boundTextures", "biggestDimension", "bind", "glSampler", "pixels", "width", "height", "ctx", "imageData", "resolution", "frame", "glRenterTarget", "GlGraphicsAdaptor", "Matrix", "maxTextures", "glProgram", "compileHighShaderGlProgram", "colorBitGl", "generateTextureBatchBitGl", "localUniformBitGl", "roundPixelsBitGl", "getBatchSamplersUniformGroup", "graphicsPipe", "renderable", "context", "contextSystem", "batcher", "instructions", "batches", "GlMeshAdaptor", "textureBitGl", "meshPipe", "mesh", "DefaultWebGLSystems", "SharedSystems", "DefaultWebGLPipes", "SharedRenderPipes", "DefaultWebGLAdapters", "systems", "renderPipes", "renderPipeAdaptors", "WebGLRenderer", "Abstract<PERSON><PERSON><PERSON>", "systemConfig", "RendererType"], "mappings": "6zDAIA,MAAMA,CAAe,CACnB,aAAc,CACZ,KAAK,WAAaC,EAAM,QAOxB,KAAK,eAAiB,EACvB,CACD,KAAKC,EAAa,CAChBA,EAAY,SAAS,QAAQ,cAAc,IAAI,IAAI,CACpD,CACD,eAAgB,CACd,KAAK,eAAiB,EACvB,CACD,MAAMC,EAAWC,EAAUC,EAAQ,CACjC,MAAMC,EAAWH,EAAU,SACrBI,EAAY,KAAK,eAAeF,EAAO,GAAG,EAChDC,EAAS,OAAO,KAAKD,EAAQE,CAAS,EACjCA,IACH,KAAK,eAAeF,EAAO,GAAG,EAAI,IAEpCC,EAAS,OAAO,mBAAmBA,EAAS,eAAe,YAAY,EACvEA,EAAS,SAAS,KAAKF,EAAUC,EAAO,SAAS,CAClD,CACD,QAAQF,EAAWK,EAAO,CACxB,MAAMF,EAAWH,EAAU,SAC3B,KAAK,WAAW,UAAYK,EAAM,UAClCF,EAAS,MAAM,IAAI,KAAK,UAAU,EAClC,MAAMG,EAAWD,EAAM,SAAS,SAChC,QAAS,EAAI,EAAG,EAAIA,EAAM,SAAS,MAAO,IACxCF,EAAS,QAAQ,KAAKG,EAAS,CAAC,EAAG,CAAC,EAEtCH,EAAS,SAAS,KAAKE,EAAM,SAAUA,EAAM,KAAMA,EAAM,KAAK,CAC/D,CACH,CAEAR,EAAe,UAAY,CACzB,KAAM,CACJU,EAAc,iBACf,EACD,KAAM,OACR,EC/CA,IAAIC,GAAgCC,IAClCA,EAAaA,EAAa,qBAA0B,KAAK,EAAI,uBAC7DA,EAAaA,EAAa,aAAkB,KAAK,EAAI,eACrDA,EAAaA,EAAa,eAAoB,KAAK,EAAI,iBAChDA,IACND,GAAe,CAAA,CAAE,ECLpB,MAAME,EAAS,CACb,YAAYC,EAAQC,EAAM,CACxB,KAAK,sBAAwB,GAC7B,KAAK,gBAAkB,GACvB,KAAK,OAASD,GAAU,KACxB,KAAK,SAAW,GAChB,KAAK,WAAa,GAClB,KAAK,KAAOC,CACb,CACH,CCJA,MAAMC,EAAe,CAInB,YAAYV,EAAU,CACpB,KAAK,YAA8B,OAAO,OAAO,IAAI,EAErD,KAAK,kBAAoC,OAAO,OAAO,IAAI,EAC3D,KAAK,iBAAmB,EACxB,KAAK,mBAAqB,KAAK,iBAC/B,KAAK,YAAc,EACnB,KAAK,UAAYA,EACjB,KAAK,UAAU,aAAa,eAAe,KAAM,aAAa,CAC/D,CAID,SAAU,CACR,KAAK,UAAY,KACjB,KAAK,IAAM,KACX,KAAK,YAAc,KACnB,KAAK,kBAAoB,IAC1B,CAED,eAAgB,CACd,MAAMW,EAAK,KAAK,IAAM,KAAK,UAAU,GACrC,KAAK,YAA8B,OAAO,OAAO,IAAI,EACrD,KAAK,aAAeA,EAAG,4BAA8BA,EAAG,aAAaA,EAAG,2BAA2B,EAAI,CACxG,CACD,YAAYH,EAAQ,CAClB,OAAO,KAAK,YAAYA,EAAO,GAAG,GAAK,KAAK,eAAeA,CAAM,CAClE,CAKD,KAAKA,EAAQ,CACX,KAAM,CAAE,IAAKG,CAAI,EAAG,KACdC,EAAW,KAAK,YAAYJ,CAAM,EACxCG,EAAG,WAAWC,EAAS,KAAMA,EAAS,MAAM,CAC7C,CAQD,eAAeA,EAAUC,EAAO,CAC9B,KAAM,CAAE,IAAKF,CAAI,EAAG,KAChB,KAAK,kBAAkBE,CAAK,IAAMD,IACpC,KAAK,kBAAkBC,CAAK,EAAID,EAChCA,EAAS,sBAAwBC,EACjCF,EAAG,eAAeA,EAAG,eAAgBE,EAAOD,EAAS,MAAM,EAE9D,CACD,aAAaE,EAAsB,CACjC,KAAK,cACL,KAAK,iBAAmB,EACpBA,IACF,KAAK,kBAAkB,CAAC,EAAI,KAC5B,KAAK,iBAAmB,EACpB,KAAK,mBAAqB,IAC5B,KAAK,mBAAqB,GAG/B,CACD,0BAA0BF,EAAU,CAClC,IAAIG,EAAY,KAAK,wBAAwBH,CAAQ,EACrD,GAAIG,GAAa,KAAK,iBACpB,OAAAH,EAAS,gBAAkB,KAAK,YACzBG,EAET,IAAIC,EAAO,EACPC,EAAY,KAAK,mBACrB,KAAOD,EAAO,GAAG,CACXC,GAAa,KAAK,eACpBA,EAAY,KAAK,iBACjBD,KAEF,MAAME,EAAS,KAAK,kBAAkBD,CAAS,EAC/C,GAAIC,GAAUA,EAAO,kBAAoB,KAAK,YAAa,CACzDD,IACA,QACD,CACD,KACD,CAGD,OAFAF,EAAYE,EACZ,KAAK,mBAAqBA,EAAY,EAClCD,GAAQ,EACH,IAETJ,EAAS,gBAAkB,KAAK,YAChC,KAAK,kBAAkBG,CAAS,EAAI,KAC7BA,EACR,CACD,wBAAwBH,EAAU,CAChC,MAAMC,EAAQD,EAAS,sBACvB,OAAI,KAAK,kBAAkBC,CAAK,IAAMD,EAC7BC,EAEF,EACR,CASD,gBAAgBD,EAAUC,EAAOM,EAAQC,EAAM,CAC7C,KAAM,CAAE,IAAKT,CAAI,EAAG,KACpBQ,IAAWA,EAAS,GACpBN,IAAUA,EAAQ,GAClB,KAAK,kBAAkBA,CAAK,EAAI,KAChCF,EAAG,gBAAgBA,EAAG,eAAgBE,GAAS,EAAGD,EAAS,OAAQO,EAAS,IAAKC,GAAQ,GAAG,CAC7F,CAKD,aAAaZ,EAAQ,CACnB,KAAM,CAAE,IAAKG,CAAI,EAAG,KACdC,EAAW,KAAK,YAAYJ,CAAM,EACxC,GAAIA,EAAO,YAAcI,EAAS,SAChC,OAAOA,EAETA,EAAS,SAAWJ,EAAO,UAC3BG,EAAG,WAAWC,EAAS,KAAMA,EAAS,MAAM,EAC5C,MAAMS,EAAOb,EAAO,KACdc,EAAWd,EAAO,WAAW,MAAQe,EAAY,OAASZ,EAAG,YAAcA,EAAG,aACpF,OAAIU,EACET,EAAS,YAAcS,EAAK,WAC9BV,EAAG,cAAcC,EAAS,KAAM,EAAGS,EAAM,EAAGb,EAAO,YAAca,EAAK,iBAAiB,GAEvFT,EAAS,WAAaS,EAAK,WAC3BV,EAAG,WAAWC,EAAS,KAAMS,EAAMC,CAAQ,IAG7CV,EAAS,WAAaJ,EAAO,WAAW,KACxCG,EAAG,WAAWC,EAAS,KAAMA,EAAS,WAAYU,CAAQ,GAErDV,CACR,CAED,YAAa,CACX,MAAMD,EAAK,KAAK,IAChB,UAAWa,KAAM,KAAK,YACpBb,EAAG,aAAa,KAAK,YAAYa,CAAE,EAAE,MAAM,EAE7C,KAAK,YAA8B,OAAO,OAAO,IAAI,CACtD,CAMD,gBAAgBhB,EAAQiB,EAAa,CACnC,MAAMb,EAAW,KAAK,YAAYJ,EAAO,GAAG,EACtCG,EAAK,KAAK,IACXc,GACHd,EAAG,aAAaC,EAAS,MAAM,EAEjC,KAAK,YAAYJ,EAAO,GAAG,EAAI,IAChC,CAMD,eAAeA,EAAQ,CACrB,KAAM,CAAE,IAAKG,CAAI,EAAG,KACpB,IAAIF,EAAOJ,EAAY,aACnBG,EAAO,WAAW,MAAQe,EAAY,MACxCd,EAAOJ,EAAY,qBACVG,EAAO,WAAW,MAAQe,EAAY,UAC/Cd,EAAOJ,EAAY,gBAErB,MAAMO,EAAW,IAAIL,GAASI,EAAG,aAAY,EAAIF,CAAI,EACrD,YAAK,YAAYD,EAAO,GAAG,EAAII,EAC/BJ,EAAO,GAAG,UAAW,KAAK,gBAAiB,IAAI,EACxCI,CACR,CACD,YAAa,CACX,KAAK,kBAAoC,OAAO,OAAO,IAAI,CAC5D,CACH,CAEAF,GAAe,UAAY,CACzB,KAAM,CACJN,EAAc,WACf,EACD,KAAM,QACR,ECnMA,MAAMsB,EAAmB,MAAMA,EAAiB,CAE9C,YAAY1B,EAAU,CAMpB,KAAK,SAAW,CAEd,cAAe,GAEf,oBAAqB,GAErB,kBAAmB,GAEnB,aAAc,GAEd,kBAAmB,GAEnB,KAAM,GAEN,iBAAkB,EACxB,EACI,KAAK,UAAYA,EACjB,KAAK,WAA6B,OAAO,OAAO,IAAI,EACpD,KAAK,kBAAoB,KAAK,kBAAkB,KAAK,IAAI,EACzD,KAAK,sBAAwB,KAAK,sBAAsB,KAAK,IAAI,CAClE,CAKD,IAAI,QAAS,CACX,MAAO,CAAC,KAAK,IAAM,KAAK,GAAG,cAAa,CACzC,CAKD,cAAcW,EAAI,CAChB,KAAK,GAAKA,EACV,KAAK,UAAU,GAAKA,CACrB,CACD,KAAKgB,EAAS,CACZA,EAAU,CAAE,GAAGD,GAAiB,eAAgB,GAAGC,CAAO,EAC1D,IAAIC,EAAY,KAAK,UAAYD,EAAQ,UAUzC,GATIA,EAAQ,SAAWC,IACrBC,EAAK,+GAA+G,EACpHD,EAAY,IAEVA,EACF,KAAK,OAASE,EAAW,IAAK,EAAC,aAAa,KAAK,UAAU,OAAO,MAAO,KAAK,UAAU,OAAO,MAAM,EAErG,KAAK,OAAS,KAAK,UAAU,KAAK,OAEhCH,EAAQ,QACV,KAAK,gBAAgBA,EAAQ,OAAO,MAC/B,CACL,MAAMI,EAAQ,KAAK,UAAU,WAAW,MAAQ,EAC1CC,EAAqBL,EAAQ,oBAAsB,GACnDM,EAAYN,EAAQ,WAAa,CAAC,KAAK,UAAU,WAAW,cAClE,KAAK,cAAcA,EAAQ,mBAAoB,CAC7C,MAAAI,EACA,mBAAAC,EACA,UAAAC,EACA,QAAS,GACT,sBAAuBN,EAAQ,sBAC/B,gBAAiBA,EAAQ,iBAAmB,SACpD,CAAO,CACF,CACF,CACD,iBAAiBO,EAAc,CAC7B,GAAI,CAAC,KAAK,UAAW,CACfA,IAAiB,KAAK,QACxBL,EAAK,gEAAgE,EAEvE,MACD,CACD,KAAM,CAAE,OAAAM,CAAQ,EAAG,MACfA,EAAO,MAAQD,EAAa,OAASC,EAAO,OAASD,EAAa,UACpEC,EAAO,MAAQ,KAAK,IAAID,EAAa,MAAOA,EAAa,KAAK,EAC9DC,EAAO,OAAS,KAAK,IAAID,EAAa,OAAQA,EAAa,MAAM,EAEpE,CAMD,gBAAgBvB,EAAI,CAClB,KAAK,GAAKA,EACV,KAAK,aAAeA,aAAcmB,EAAW,IAAG,EAAG,yBAAwB,EAAK,EAAI,EACpF,KAAK,cAAa,EAClB,KAAK,gBAAgBnB,CAAE,EACvB,KAAK,UAAU,QAAQ,cAAc,KAAKA,CAAE,EAC5C,MAAMyB,EAAU,KAAK,UAAU,KAAK,OACpCA,EAAQ,iBAAiB,mBAAoB,KAAK,kBAAmB,EAAK,EAC1EA,EAAQ,iBAAiB,uBAAwB,KAAK,sBAAuB,EAAK,CACnF,CAQD,cAAcC,EAAoBV,EAAS,CACzC,IAAIhB,EACJ,MAAMwB,EAAS,KAAK,OAIpB,GAHIE,IAAuB,IACzB1B,EAAKwB,EAAO,WAAW,SAAUR,CAAO,GAEtC,CAAChB,IACHA,EAAKwB,EAAO,WAAW,QAASR,CAAO,EACnC,CAAChB,GACH,MAAM,IAAI,MAAM,oEAAoE,EAGxF,KAAK,GAAKA,EACV,KAAK,gBAAgB,KAAK,EAAE,CAC7B,CAED,eAAgB,CACd,KAAM,CAAE,GAAAA,CAAI,EAAG,KACT2B,EAAS,CACb,qBAAsB3B,EAAG,aAAa,gCAAgC,EACtE,mBAAoBA,EAAG,aAAa,0BAA0B,EAC9D,KAAMA,EAAG,aAAa,+BAA+B,EACrD,UAAWA,EAAG,aAAa,oCAAoC,EAE/D,IAAKA,EAAG,aAAa,8BAA8B,EACnD,KAAMA,EAAG,aAAa,+BAA+B,EACrD,MAAOA,EAAG,aAAa,gCAAgC,GAAKA,EAAG,aAAa,uCAAuC,EACnH,IAAKA,EAAG,aAAa,8BAA8B,EACnD,KAAMA,EAAG,aAAa,+BAA+B,EACrD,KAAMA,EAAG,aAAa,8BAA8B,EACpD,KAAMA,EAAG,aAAa,8BAA8B,EACpD,YAAaA,EAAG,aAAa,oBAAoB,CACvD,EACI,GAAI,KAAK,eAAiB,EACxB,KAAK,WAAa,CAChB,GAAG2B,EACH,YAAa3B,EAAG,aAAa,oBAAoB,EACjD,aAAcA,EAAG,aAAa,qBAAqB,EACnD,kBAAmBA,EAAG,aAAa,yBAAyB,GAAKA,EAAG,aAAa,6BAA6B,GAAKA,EAAG,aAAa,gCAAgC,EACnK,mBAAoBA,EAAG,aAAa,wBAAwB,EAE5D,aAAcA,EAAG,aAAa,mBAAmB,EACjD,mBAAoBA,EAAG,aAAa,0BAA0B,EAC9D,iBAAkBA,EAAG,aAAa,wBAAwB,EAC1D,uBAAwBA,EAAG,aAAa,+BAA+B,EACvE,yBAA0BA,EAAG,aAAa,wBAAwB,EAClE,KAAMA,EAAG,aAAa,UAAU,CACxC,MACW,CACL,KAAK,WAAa,CAChB,GAAG2B,EACH,iBAAkB3B,EAAG,aAAa,wBAAwB,CAClE,EACM,MAAM4B,EAAa5B,EAAG,aAAa,wBAAwB,EACvD4B,GACFA,EAAW,qBAAqBA,EAAW,6BAA6B,CAE3E,CACF,CAKD,kBAAkBC,EAAO,CACvBA,EAAM,eAAc,EAChB,KAAK,qBACP,KAAK,mBAAqB,GAC1B,WAAW,IAAM,CACX,KAAK,GAAG,iBACV,KAAK,WAAW,aAAa,gBAEhC,EAAE,CAAC,EAEP,CAED,uBAAwB,CACtB,KAAK,UAAU,QAAQ,cAAc,KAAK,KAAK,EAAE,CAClD,CACD,SAAU,CACR,MAAMJ,EAAU,KAAK,UAAU,KAAK,OACpC,KAAK,UAAY,KACjBA,EAAQ,oBAAoB,mBAAoB,KAAK,iBAAiB,EACtEA,EAAQ,oBAAoB,uBAAwB,KAAK,qBAAqB,EAC9E,KAAK,GAAG,WAAW,IAAI,EACvB,KAAK,WAAW,aAAa,aAC9B,CAQD,kBAAmB,CACjB,KAAK,WAAW,aAAa,cAC7B,KAAK,mBAAqB,EAC3B,CAKD,gBAAgBzB,EAAI,CAClB,MAAM8B,EAAa9B,EAAG,uBAClB8B,GAAc,CAACA,EAAW,SAC5BZ,EAAK,uFAAuF,EAE9F,MAAMa,EAAW,KAAK,SAChBC,EAAW,KAAK,eAAiB,EACjCC,EAAa,KAAK,WACxBF,EAAS,cAAgBC,GAAY,CAAC,CAACC,EAAW,mBAClDF,EAAS,oBAAsBC,EAC/BD,EAAS,kBAAoBC,GAAY,CAAC,CAACC,EAAW,kBACtDF,EAAS,aAAeC,GAAY,CAAC,CAACC,EAAW,KACjDF,EAAS,kBAAoBC,EAC7BD,EAAS,iBAAmBC,EAC5BD,EAAS,KAAOC,EACXD,EAAS,eACZb,EAAK,gGAAgG,CAExG,CACH,EAEAH,EAAiB,UAAY,CAC3B,KAAM,CACJtB,EAAc,WACf,EACD,KAAM,SACR,EAEAsB,EAAiB,eAAiB,CAKhC,QAAS,KAKT,mBAAoB,GAKpB,sBAAuB,GAKvB,gBAAiB,OAKjB,mBAAoB,EAKpB,UAAW,EACb,EACA,IAAImB,GAAkBnB,EChRtB,IAAIoB,GAA+BC,IACjCA,EAAYA,EAAY,KAAU,IAAI,EAAI,OAC1CA,EAAYA,EAAY,IAAS,IAAI,EAAI,MACzCA,EAAYA,EAAY,GAAQ,KAAK,EAAI,KACzCA,EAAYA,EAAY,IAAS,IAAI,EAAI,MACzCA,EAAYA,EAAY,aAAkB,KAAK,EAAI,eACnDA,EAAYA,EAAY,YAAiB,KAAK,EAAI,cAClDA,EAAYA,EAAY,WAAgB,KAAK,EAAI,aACjDA,EAAYA,EAAY,YAAiB,KAAK,EAAI,cAClDA,EAAYA,EAAY,MAAW,IAAI,EAAI,QAC3CA,EAAYA,EAAY,UAAe,IAAI,EAAI,YAC/CA,EAAYA,EAAY,gBAAqB,IAAI,EAAI,kBACrDA,EAAYA,EAAY,gBAAqB,IAAI,EAAI,kBACrDA,EAAYA,EAAY,cAAmB,KAAK,EAAI,gBAC7CA,IACND,GAAc,CAAA,CAAE,EACfE,IAA+BC,IACjCA,EAAYA,EAAY,WAAgB,IAAI,EAAI,aAChDA,EAAYA,EAAY,iBAAsB,KAAK,EAAI,mBACvDA,EAAYA,EAAY,iBAAsB,KAAK,EAAI,mBACvDA,EAAYA,EAAY,4BAAiC,KAAK,EAAI,8BAClEA,EAAYA,EAAY,4BAAiC,KAAK,EAAI,8BAClEA,EAAYA,EAAY,4BAAiC,KAAK,EAAI,8BAClEA,EAAYA,EAAY,4BAAiC,KAAK,EAAI,8BAClEA,EAAYA,EAAY,4BAAiC,KAAK,EAAI,8BAClEA,EAAYA,EAAY,4BAAiC,KAAK,EAAI,8BAC3DA,IACND,IAAc,CAAA,CAAE,EAOfE,GAA6BC,IAC/BA,EAAUA,EAAU,cAAmB,IAAI,EAAI,gBAC/CA,EAAUA,EAAU,eAAoB,IAAI,EAAI,iBAChDA,EAAUA,EAAU,qBAA0B,KAAK,EAAI,uBACvDA,EAAUA,EAAU,uBAA4B,KAAK,EAAI,yBACzDA,EAAUA,EAAU,uBAA4B,KAAK,EAAI,yBACzDA,EAAUA,EAAU,aAAkB,IAAI,EAAI,eAC9CA,EAAUA,EAAU,6BAAkC,KAAK,EAAI,+BAC/DA,EAAUA,EAAU,4BAAiC,KAAK,EAAI,8BAC9DA,EAAUA,EAAU,kBAAuB,KAAK,EAAI,oBACpDA,EAAUA,EAAU,yBAA8B,KAAK,EAAI,2BAC3DA,EAAUA,EAAU,KAAU,IAAI,EAAI,OACtCA,EAAUA,EAAU,MAAW,IAAI,EAAI,QACvCA,EAAUA,EAAU,IAAS,IAAI,EAAI,MACrCA,EAAUA,EAAU,MAAW,IAAI,EAAI,QACvCA,EAAUA,EAAU,+BAAoC,KAAK,EAAI,iCACjEA,EAAUA,EAAU,WAAgB,KAAK,EAAI,aACtCA,IACND,GAAY,CAAA,CAAE,EClDjB,MAAME,EAAU,CACd,QAASF,EAAS,cAClB,QAASA,EAAS,cAClB,QAASA,EAAS,KAClB,QAASA,EAAS,KAClB,SAAUA,EAAS,cACnB,SAAUA,EAAS,cACnB,SAAUA,EAAS,KACnB,SAAUA,EAAS,KACnB,SAAUA,EAAS,eACnB,SAAUA,EAAS,eACnB,SAAUA,EAAS,MACnB,SAAUA,EAAS,MACnB,UAAWA,EAAS,eACpB,UAAWA,EAAS,eACpB,UAAWA,EAAS,MACpB,UAAWA,EAAS,MACpB,UAAWA,EAAS,WACpB,UAAWA,EAAS,WACpB,QAASA,EAAS,MAClB,UAAWA,EAAS,MACpB,UAAWA,EAAS,MACpB,UAAWA,EAAS,MACpB,OAAQA,EAAS,aACjB,SAAUA,EAAS,aACnB,SAAUA,EAAS,aACnB,SAAUA,EAAS,aACnB,OAAQA,EAAS,IACjB,SAAUA,EAAS,IACnB,SAAUA,EAAS,IACnB,SAAUA,EAAS,GACrB,EACA,SAASG,GAAoBC,EAAQ,CACnC,OAAOF,EAAQE,CAAM,GAAKF,EAAQ,OACpC,CC/BA,MAAMG,GAAkB,CACtB,aAAc,EACd,YAAa,EACb,aAAc,EACd,gBAAiB,EACjB,iBAAkB,CACpB,EACA,MAAMC,EAAiB,CAErB,YAAYxD,EAAU,CACpB,KAAK,iBAAmC,OAAO,OAAO,IAAI,EAC1D,KAAK,UAAYA,EACjB,KAAK,gBAAkB,KACvB,KAAK,WAAa,KAClB,KAAK,OAAS,GACd,KAAK,YAAc,GACnB,KAAK,UAAU,aAAa,eAAe,KAAM,kBAAkB,CACpE,CAED,eAAgB,CACd,MAAMW,EAAK,KAAK,GAAK,KAAK,UAAU,GACpC,GAAI,CAAC,KAAK,UAAU,QAAQ,SAAS,kBACnC,MAAM,IAAI,MAAM,gEAAgE,EAElF,MAAM8C,EAAqB,KAAK,UAAU,QAAQ,WAAW,kBACzDA,IACF9C,EAAG,kBAAoB,IAAM8C,EAAmB,qBAAoB,EACpE9C,EAAG,gBAAmB+C,GAAQD,EAAmB,mBAAmBC,CAAG,EACvE/C,EAAG,kBAAqB+C,GAAQD,EAAmB,qBAAqBC,CAAG,GAE7E,MAAMC,EAA2B,KAAK,UAAU,QAAQ,WAAW,yBAC/DA,IACFhD,EAAG,oBAAsB,CAACiD,EAAGC,EAAGC,EAAGC,IAAM,CACvCJ,EAAyB,yBAAyBC,EAAGC,EAAGC,EAAGC,CAAC,CACpE,EACMpD,EAAG,sBAAwB,CAACiD,EAAGC,EAAGC,EAAGC,EAAGC,IAAM,CAC5CL,EAAyB,2BAA2BC,EAAGC,EAAGC,EAAGC,EAAGC,CAAC,CACzE,EACMrD,EAAG,oBAAsB,CAACiD,EAAGC,IAAMF,EAAyB,yBAAyBC,EAAGC,CAAC,GAE3F,KAAK,gBAAkB,KACvB,KAAK,WAAa,KAClB,KAAK,iBAAmC,OAAO,OAAO,IAAI,CAC3D,CAMD,KAAK/D,EAAUmE,EAAS,CACtB,MAAMtD,EAAK,KAAK,GAChB,KAAK,gBAAkBb,EACvB,MAAM4D,EAAM,KAAK,OAAO5D,EAAUmE,CAAO,EACrC,KAAK,aAAeP,IACtB,KAAK,WAAaA,EAClB/C,EAAG,gBAAgB+C,CAAG,GAExB,KAAK,cAAa,CACnB,CAED,YAAa,CACX,KAAK,OAAM,CACZ,CAED,eAAgB,CACd,MAAM5D,EAAW,KAAK,gBAChBoE,EAAe,KAAK,UAAU,OACpC,QAASC,EAAI,EAAGA,EAAIrE,EAAS,QAAQ,OAAQqE,IAAK,CAChD,MAAM3D,EAASV,EAAS,QAAQqE,CAAC,EACjCD,EAAa,aAAa1D,CAAM,CACjC,CACF,CAMD,mBAAmBV,EAAUmE,EAAS,CACpC,MAAMG,EAAqBtE,EAAS,WAC9BuE,EAAmBJ,EAAQ,eACjC,UAAWK,KAAKD,EACd,GAAI,CAACD,EAAmBE,CAAC,EACvB,MAAM,IAAI,MAAM,2DAA2DA,CAAC,aAAa,CAG9F,CAOD,aAAaxE,EAAUmE,EAAS,CAC9B,MAAMM,EAAUzE,EAAS,WACnBuE,EAAmBJ,EAAQ,eAC3BO,EAAU,CAAC,IAAK1E,EAAS,GAAG,EAClC,UAAWqE,KAAKI,EACVF,EAAiBF,CAAC,GACpBK,EAAQ,KAAKL,EAAGE,EAAiBF,CAAC,EAAE,QAAQ,EAGhD,OAAOK,EAAQ,KAAK,GAAG,CACxB,CACD,OAAO1E,EAAUmE,EAAS,CACxB,OAAO,KAAK,iBAAiBnE,EAAS,GAAG,IAAImE,EAAQ,IAAI,GAAK,KAAK,gBAAgBnE,EAAUmE,CAAO,CACrG,CASD,gBAAgBnE,EAAUmE,EAASQ,EAAe,GAAM,CACtD,MAAM9D,EAAK,KAAK,UAAU,GACpBuD,EAAe,KAAK,UAAU,OACpC,KAAK,UAAU,OAAO,gBAAgBD,CAAO,EAC7C,KAAK,mBAAmBnE,EAAUmE,CAAO,EACzC,MAAMS,EAAY,KAAK,aAAa5E,EAAUmE,CAAO,EAChD,KAAK,iBAAiBnE,EAAS,GAAG,IACrC,KAAK,iBAAiBA,EAAS,GAAG,EAAoB,OAAO,OAAO,IAAI,EACxEA,EAAS,GAAG,UAAW,KAAK,kBAAmB,IAAI,GAErD,MAAM6E,EAAgB,KAAK,iBAAiB7E,EAAS,GAAG,EACxD,IAAI4D,EAAMiB,EAAcD,CAAS,EACjC,GAAIhB,EACF,OAAAiB,EAAcV,EAAQ,IAAI,EAAIP,EACvBA,EAETkB,GAAiB9E,EAAUmE,EAAQ,cAAc,EACjD,MAAMY,EAAU/E,EAAS,QACzB4D,EAAM/C,EAAG,oBACTA,EAAG,gBAAgB+C,CAAG,EACtB,QAASS,EAAI,EAAGA,EAAIU,EAAQ,OAAQV,IAAK,CACvC,MAAM3D,EAASqE,EAAQV,CAAC,EACxBD,EAAa,KAAK1D,CAAM,CACzB,CACD,YAAK,YAAYV,EAAUmE,CAAO,EAClCU,EAAcV,EAAQ,IAAI,EAAIP,EAC9BiB,EAAcD,CAAS,EAAIhB,EAC3B/C,EAAG,gBAAgB,IAAI,EAChB+C,CACR,CAMD,kBAAkB5D,EAAU2B,EAAa,CACvC,MAAMkD,EAAgB,KAAK,iBAAiB7E,EAAS,GAAG,EAClDa,EAAK,KAAK,GAChB,GAAIgE,EAAe,CACjB,GAAIlD,EACF,UAAW,KAAKkD,EACV,KAAK,aAAeA,EAAc,CAAC,GACrC,KAAK,OAAM,EAEbhE,EAAG,kBAAkBgE,EAAc,CAAC,CAAC,EAGzC,KAAK,iBAAiB7E,EAAS,GAAG,EAAI,IACvC,CACF,CAKD,WAAW2B,EAAc,GAAO,CAC9B,MAAMd,EAAK,KAAK,GAChB,UAAWwD,KAAK,KAAK,iBAAkB,CACrC,GAAI1C,EACF,UAAW6C,KAAK,KAAK,iBAAiBH,CAAC,EAAG,CACxC,MAAMQ,EAAgB,KAAK,iBAAiBR,CAAC,EACzC,KAAK,aAAeQ,GACtB,KAAK,OAAM,EAEbhE,EAAG,kBAAkBgE,EAAcL,CAAC,CAAC,CACtC,CAEH,KAAK,iBAAiBH,CAAC,EAAI,IAC5B,CACF,CAMD,YAAYrE,EAAUmE,EAAS,CAC7B,MAAMtD,EAAK,KAAK,UAAU,GACpBuD,EAAe,KAAK,UAAU,OAC9BzB,EAAa3C,EAAS,WACxBA,EAAS,aACXoE,EAAa,KAAKpE,EAAS,WAAW,EAExC,IAAIgF,EAAa,KACjB,UAAWR,KAAK7B,EAAY,CAC1B,MAAMsC,EAAYtC,EAAW6B,CAAC,EACxB9D,EAASuE,EAAU,OACnBnE,EAAWsD,EAAa,YAAY1D,CAAM,EAC1CwE,EAAgBf,EAAQ,eAAeK,CAAC,EAC9C,GAAIU,EAAe,CACbF,IAAelE,IACjBsD,EAAa,KAAK1D,CAAM,EACxBsE,EAAalE,GAEf,MAAMqE,EAAWD,EAAc,SAC/BrE,EAAG,wBAAwBsE,CAAQ,EACnC,MAAMC,EAAgBC,EAA2BJ,EAAU,MAAM,EAC3DtE,EAAO4C,GAAoB0B,EAAU,MAAM,EAmBjD,GAlBIC,EAAc,QAAQ,UAAU,EAAG,CAAC,IAAM,MAC5CrE,EAAG,qBACDsE,EACAC,EAAc,KACdzE,EACAsE,EAAU,OACVA,EAAU,MACtB,EAEUpE,EAAG,oBACDsE,EACAC,EAAc,KACdzE,EACAyE,EAAc,WACdH,EAAU,OACVA,EAAU,MACtB,EAEYA,EAAU,SACZ,GAAI,KAAK,YAAa,CACpB,MAAMK,GAAUL,EAAU,SAAW,EACrCpE,EAAG,oBAAoBsE,EAAUG,EAAO,CACpD,KACY,OAAM,IAAI,MAAM,gEAAgE,CAGrF,CACF,CACF,CAWD,KAAKC,EAAUjE,EAAMkE,EAAOC,EAAe,CACzC,KAAM,CAAE,GAAA5E,CAAE,EAAK,KAAK,UACdb,EAAW,KAAK,gBAChB0F,EAAajC,GAAgB8B,GAAYvF,EAAS,QAAQ,EAEhE,GADAyF,IAAkBA,EAAgBzF,EAAS,eACvCA,EAAS,YAAa,CACxB,MAAM2F,EAAW3F,EAAS,YAAY,KAAK,kBACrC4F,EAASD,IAAa,EAAI9E,EAAG,eAAiBA,EAAG,aACnD4E,EAAgB,EAClB5E,EAAG,sBAAsB6E,EAAYpE,GAAQtB,EAAS,YAAY,KAAK,OAAQ4F,GAASJ,GAAS,GAAKG,EAAUF,CAAa,EAE7H5E,EAAG,aAAa6E,EAAYpE,GAAQtB,EAAS,YAAY,KAAK,OAAQ4F,GAASJ,GAAS,GAAKG,CAAQ,CAE7G,MAAeF,EAAgB,EACzB5E,EAAG,oBAAoB6E,EAAYF,GAAS,EAAGlE,GAAQtB,EAAS,UAAWyF,CAAa,EAExF5E,EAAG,WAAW6E,EAAYF,GAAS,EAAGlE,GAAQtB,EAAS,QAAO,CAAE,EAElE,OAAO,IACR,CAED,QAAS,CACP,KAAK,GAAG,gBAAgB,IAAI,EAC5B,KAAK,WAAa,KAClB,KAAK,gBAAkB,IACxB,CACD,SAAU,CACR,KAAK,UAAY,KACjB,KAAK,GAAK,KACV,KAAK,WAAa,KAClB,KAAK,gBAAkB,IACxB,CACH,CAEA0D,GAAiB,UAAY,CAC3B,KAAM,CACJpD,EAAc,WACf,EACD,KAAM,UACR,EC5RA,MAAMuF,GAAsB,IAAIC,GAAS,CACvC,WAAY,CACV,UAAW,CACT,GACA,GAEA,EACA,GAEA,GACA,CAED,CACF,CACH,CAAC,EACKC,EAAsB,MAAMA,EAAoB,CACpD,YAAY7F,EAAU,CAEpB,KAAK,cAAgB,GACrB,KAAK,yBAA2B,GAChC,KAAK,UAAYA,CAClB,CACD,KAAK2B,EAAU,GAAI,CACjB,KAAM,CAAE,cAAAmE,EAAe,UAAA7D,GAAc,CAAE,GAAG4D,GAAoB,eAAgB,GAAGlE,GACjF,KAAK,cAAgBmE,EACrB,KAAK,WAAa7D,EACb,KAAK,UAAU,QAAQ,SAAS,OACnCJ,EAAK,8DAA8D,EACnE,KAAK,WAAa,IAEpB,KAAK,OAASlC,EAAM,QACpB,MAAMoG,EAAqB,IAAIC,GAAU,CACvC,OAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAYR,SAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBASV,KAAM,cACZ,CAAK,EACD,KAAK,mBAAqB,IAAIC,EAAO,CACnC,UAAWF,EACX,UAAW,CACT,SAAUG,EAAQ,MAAM,MACzB,CACP,CAAK,CACF,CAMD,YAAYvE,EAAS,CACnB,MAAMwE,EAAe,KAAK,UAAU,aAAa,gBAAgBxE,EAAQ,MAAM,EAE/E,GADA,KAAK,yBAA2B,KAAK,eAAiB,CAAC,CAACwE,EAAa,OACjE,KAAK,yBAA0B,CACjC,MAAMC,EAAgB,KAAK,UAAU,aAAa,gBAAgBzE,EAAQ,MAAM,EAChF,KAAK,eAAiByE,EAAc,aACpCzE,EAAQ,OAAS,KAAK,sBAAsByE,EAAc,YAAY,CACvE,CACF,CACD,WAAY,CACV,KAAK,mBAAkB,CACxB,CACD,oBAAqB,CACnB,MAAMpG,EAAW,KAAK,UACtBA,EAAS,aAAa,mBACjB,KAAK,2BAEVA,EAAS,aAAa,KAAK,KAAK,eAAgB,EAAK,EACrD,KAAK,mBAAmB,UAAU,SAAW,KAAK,mBAAmB,OACrEA,EAAS,QAAQ,KAAK,CACpB,SAAU2F,GACV,OAAQ,KAAK,mBACb,MAAO,KAAK,MAClB,CAAK,EACF,CACD,sBAAsBU,EAAqB,CACzC,YAAK,mBAAqB,KAAK,oBAAsB,IAAIH,EAAQ,CAC/D,OAAQ,IAAII,EAAc,CACxB,MAAOD,EAAoB,MAC3B,OAAQA,EAAoB,OAC5B,WAAYA,EAAoB,YAChC,UAAW,KAAK,UACxB,CAAO,CACP,CAAK,EACD,KAAK,mBAAmB,OAAO,OAC7BA,EAAoB,MACpBA,EAAoB,OACpBA,EAAoB,WAC1B,EACW,KAAK,kBACb,CAED,SAAU,CACJ,KAAK,qBACP,KAAK,mBAAmB,UACxB,KAAK,mBAAqB,KAE7B,CACH,EAEAR,EAAoB,UAAY,CAC9B,KAAM,CACJzF,EAAc,WACf,EACD,KAAM,aACN,SAAU,CACZ,EAEAyF,EAAoB,eAAiB,CAEnC,cAAe,EACjB,EACA,IAAIU,GAAqBV,ECxIzB,MAAMW,EAAkB,CACtB,YAAYxG,EAAU,CACpB,KAAK,gBAAkB,GACvB,KAAK,UAAYA,CAClB,CACD,QAAQyG,EAAW,CACb,KAAK,kBAAoBA,IAE7B,KAAK,gBAAkBA,EACvB,KAAK,UAAU,GAAG,UAChB,CAAC,EAAEA,EAAY,GACf,CAAC,EAAEA,EAAY,GACf,CAAC,EAAEA,EAAY,GACf,CAAC,EAAEA,EAAY,EACrB,EACG,CACH,CAEAD,GAAkB,UAAY,CAC5B,KAAM,CACJpG,EAAc,WACf,EACD,KAAM,WACR,ECvBA,MAAMsG,EAAgB,CACpB,YAAY1G,EAAU,CACpB,KAAK,gBAAkB,QAAQ,UAC/B,KAAK,UAAYA,CAClB,CACD,YAAYF,EAAUC,EAAQ,CAC5B,KAAK,UAAU,SAAS,KAAKD,EAAUC,EAAO,SAAS,CACxD,CACD,kBAAmB,CAClB,CACD,KAAK4B,EAAS,CACZ,MAAM3B,EAAW,KAAK,UAChB,CAAE,SAAAF,EAAU,OAAAC,EAAQ,MAAA4G,EAAO,SAAAC,EAAU,SAAUnG,EAAM,KAAAW,EAAM,MAAAkE,EAAO,cAAAC,CAAa,EAAK5D,EAC1F3B,EAAS,OAAO,KAAKD,EAAQ6G,CAAQ,EACrC5G,EAAS,SAAS,KAAKF,EAAUE,EAAS,OAAO,cAAc,EAC3D2G,GACF3G,EAAS,MAAM,IAAI2G,CAAK,EAE1B3G,EAAS,SAAS,KAAKS,EAAMW,EAAMkE,EAAOC,GAAiBzF,EAAS,aAAa,CAClF,CACD,SAAU,CACR,KAAK,UAAY,IAClB,CACH,CAEA4G,GAAgB,UAAY,CAC1B,KAAM,CACJtG,EAAc,WACf,EACD,KAAM,SACR,EChCA,MAAMyG,EAAe,CACnB,aAAc,CACZ,KAAK,MAAQ,GACb,KAAK,OAAS,GACd,KAAK,KAAO,GACZ,KAAK,iBAAmB,EACzB,CACH,CCHA,MAAMC,EAAgB,CACpB,YAAY9G,EAAU,CACpB,KAAK,cAAgB,CACnB,QAAS,GACT,iBAAkB,EAClB,YAAa+G,EAAc,IACjC,EACI,KAAK,0BAA4C,OAAO,OAAO,IAAI,EACnE/G,EAAS,aAAa,qBAAqB,IAAI,IAAI,CACpD,CACD,cAAcW,EAAI,CAChB,KAAK,IAAMA,EACX,KAAK,uBAAyB,CAC5B,OAAQA,EAAG,OACX,MAAOA,EAAG,MACV,MAAOA,EAAG,MACV,YAAaA,EAAG,SAChB,KAAMA,EAAG,KACT,aAAcA,EAAG,OACjB,QAASA,EAAG,QACZ,gBAAiBA,EAAG,MAC1B,EACI,KAAK,mBAAqB,CACxB,KAAMA,EAAG,KACT,KAAMA,EAAG,KACT,QAASA,EAAG,QACZ,OAAQA,EAAG,OACX,kBAAmBA,EAAG,KACtB,kBAAmBA,EAAG,KACtB,iBAAkBA,EAAG,UACrB,iBAAkBA,EAAG,SAC3B,EACI,KAAK,cAAc,QAAU,GAC7B,KAAK,cAAc,YAAcoG,EAAc,KAC/C,KAAK,cAAc,iBAAmB,CACvC,CACD,qBAAqBZ,EAAc,CACjC,GAAI,KAAK,sBAAwBA,EAC/B,OACF,KAAK,oBAAsBA,EAC3B,IAAIa,EAAe,KAAK,0BAA0Bb,EAAa,GAAG,EAC7Da,IACHA,EAAe,KAAK,0BAA0Bb,EAAa,GAAG,EAAI,CAChE,YAAaY,EAAc,SAC3B,iBAAkB,CAC1B,GAEI,KAAK,eAAeC,EAAa,YAAaA,EAAa,gBAAgB,CAC5E,CACD,eAAeC,EAAaC,EAAkB,CAC5C,MAAMF,EAAe,KAAK,0BAA0B,KAAK,oBAAoB,GAAG,EAC1ErG,EAAK,KAAK,IACVwG,EAAOC,GAAsBH,CAAW,EACxCI,EAAgB,KAAK,cAG3B,GAFAL,EAAa,YAAcC,EAC3BD,EAAa,iBAAmBE,EAC5BD,IAAgBF,EAAc,SAAU,CACtC,KAAK,cAAc,UACrB,KAAK,cAAc,QAAU,GAC7BpG,EAAG,QAAQA,EAAG,YAAY,GAE5B,MACD,CACI,KAAK,cAAc,UACtB,KAAK,cAAc,QAAU,GAC7BA,EAAG,OAAOA,EAAG,YAAY,IAEvBsG,IAAgBI,EAAc,aAAeA,EAAc,mBAAqBH,KAClFG,EAAc,YAAcJ,EAC5BI,EAAc,iBAAmBH,EACjCvG,EAAG,YAAY,KAAK,uBAAuBwG,EAAK,YAAY,OAAO,EAAGD,EAAkB,GAAG,EAC3FvG,EAAG,UAAUA,EAAG,KAAMA,EAAG,KAAM,KAAK,mBAAmBwG,EAAK,YAAY,MAAM,CAAC,EAElF,CACH,CAEAL,GAAgB,UAAY,CAC1B,KAAM,CACJ1G,EAAc,WACf,EACD,KAAM,SACR,ECrFA,MAAMkH,GAAqB,CACzB,IAAK,EACL,IAAK,EACL,YAAa,EACb,YAAa,GACb,YAAa,GACb,YAAa,EACb,YAAa,GACb,YAAa,GACb,cAAe,GAAK,EACpB,cAAe,GAAK,EACpB,cAAe,GAAK,CAiBtB,EACA,SAASC,GAAuBC,EAAa,CAC3C,MAAMC,EAAcD,EAAY,IAAKnG,IAAU,CAC7C,KAAAA,EACA,OAAQ,EACR,KAAM,CACP,EAAC,EACIqG,EAAY,GAClB,IAAItG,EAAO,EACPD,EAAS,EACb,QAAS,EAAI,EAAG,EAAIsG,EAAY,OAAQ,IAAK,CAC3C,MAAME,EAAaF,EAAY,CAAC,EAEhC,GADArG,EAAOkG,GAAmBK,EAAW,KAAK,IAAI,EAC1C,CAACvG,EACH,MAAM,IAAI,MAAM,gBAAgBuG,EAAW,KAAK,IAAI,EAAE,EAEpDA,EAAW,KAAK,KAAO,IACzBvG,EAAO,KAAK,IAAIA,EAAMsG,CAAS,EAAIC,EAAW,KAAK,MAErD,MAAMC,EAAWxG,IAAS,GAAK,GAAKA,EACpCuG,EAAW,KAAOvG,EAClB,MAAMyG,EAAY1G,EAASuG,EACvBG,EAAY,GAAKH,EAAYG,EAAYD,EAC3CzG,IAAWuG,EAAYG,GAAa,GAEpC1G,IAAWC,EAAOyG,EAAYzG,GAAQA,EAExCuG,EAAW,OAASxG,EACpBA,GAAUC,CACX,CACD,OAAAD,EAAS,KAAK,KAAKA,EAAS,EAAE,EAAI,GAC3B,CAAE,YAAAsG,EAAa,KAAMtG,EAC9B,CC1DA,SAAS2G,GAAuBH,EAAYI,EAAa,CACvD,MAAMC,EAAU,KAAK,IAAIV,GAAmBK,EAAW,KAAK,IAAI,EAAI,GAAI,CAAC,EACnEM,EAAcN,EAAW,KAAK,MAAM,OAASA,EAAW,KAAK,KAC7DO,GAAa,EAAID,EAAc,GAAK,EACpC5G,EAAOsG,EAAW,KAAK,KAAK,QAAQ,KAAK,GAAK,EAAI,YAAc,OACtE,MAAO;AAAA,iBACQA,EAAW,KAAK,IAAI;AAAA,oBACjBI,CAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAMJJ,EAAW,KAAK,KAAOK,CAAO;AAAA;AAAA,iCAExBC,CAAW;AAAA;AAAA,kBAE1B5G,CAAI;AAAA;AAAA,cAER6G,IAAc,EAAI,kBAAkBA,CAAS,IAAM,EAAE;AAAA;AAAA,KAGnE,CCpBA,SAASC,GAA2BV,EAAa,CAC/C,OAAOW,GACLX,EACA,WACAK,GACAO,EACJ,CACA,CCNA,MAAMC,WAAoBC,EAAU,CAClC,aAAc,CACZ,MAAM,CACJ,kBAAmBhB,GACnB,gBAAiBY,EACvB,CAAK,CACF,CACH,CAEAG,GAAY,UAAY,CACtB,KAAM,CAAClI,EAAc,WAAW,EAChC,KAAM,KACR,ECXA,MAAMoI,EAAsB,CAC1B,aAAc,CACZ,KAAK,iBAAmB,CAAC,EAAG,EAAG,EAAG,CAAC,EACnC,KAAK,eAAiB,IAAIC,CAC3B,CACD,KAAKzI,EAAU0I,EAAoB,CACjC,KAAK,UAAY1I,EACjB,KAAK,oBAAsB0I,EAC3B1I,EAAS,QAAQ,cAAc,IAAI,IAAI,CACxC,CACD,eAAgB,CACd,KAAK,iBAAmB,CAAC,EAAG,EAAG,EAAG,CAAC,EACnC,KAAK,eAAiB,IAAIyI,CAC3B,CACD,cAAcE,EAA4BC,EAAoBC,EAAWzH,EAAM0H,EAAY,CACzF,MAAMJ,EAAqB,KAAK,oBAC1B1I,EAAW,KAAK,UAChB+I,EAAiBL,EAAmB,mBAAmBC,CAA0B,EACjFhI,EAAKX,EAAS,GACpB,YAAK,iBAAiB2I,CAA0B,EAChDhI,EAAG,gBAAgBA,EAAG,YAAaoI,EAAe,wBAAwB,EAC1E/I,EAAS,QAAQ,KAAK4I,EAAoB,CAAC,EAC3CjI,EAAG,kBACDA,EAAG,WACH,EACAmI,EAAW,EACXA,EAAW,EACXD,EAAU,EACVA,EAAU,EACVzH,EAAK,MACLA,EAAK,MACX,EACWwH,CACR,CACD,gBAAgBzC,EAAc6C,EAAQ,GAAMC,EAAYC,EAAU,CAChE,MAAMR,EAAqB,KAAK,oBAC1BS,EAAShD,EAAa,aACtBiD,EAAkBV,EAAmB,mBAAmBvC,CAAY,EAC1E,IAAIkD,EAAYH,EAAS,EACrB/C,EAAa,SACfkD,EAAYF,EAAO,YAAcD,EAAS,QAE5C/C,EAAa,cAAc,QAASmD,GAAY,CAC9C,KAAK,UAAU,QAAQ,OAAOA,CAAO,CAC3C,CAAK,EACD,MAAM3I,EAAK,KAAK,UAAU,GAC1BA,EAAG,gBAAgBA,EAAG,YAAayI,EAAgB,WAAW,EAC9D,MAAMG,EAAgB,KAAK,gBACvBA,EAAc,IAAML,EAAS,GAAKK,EAAc,IAAMF,GAAaE,EAAc,QAAUL,EAAS,OAASK,EAAc,SAAWL,EAAS,UACjJK,EAAc,EAAIL,EAAS,EAC3BK,EAAc,EAAIF,EAClBE,EAAc,MAAQL,EAAS,MAC/BK,EAAc,OAASL,EAAS,OAChCvI,EAAG,SACDuI,EAAS,EACTG,EACAH,EAAS,MACTA,EAAS,MACjB,GAEQ,CAACE,EAAgB,2BAA6BjD,EAAa,SAAWA,EAAa,QACrF,KAAK,aAAaiD,CAAe,EAEnC,KAAK,MAAMjD,EAAc6C,EAAOC,CAAU,CAC3C,CACD,iBAAiB9C,EAAc,CAE7B,MAAM4C,EADqB,KAAK,oBACU,mBAAmB5C,CAAY,EACzE,GAAI,CAAC4C,EAAe,KAClB,OACF,MAAMpI,EAAK,KAAK,UAAU,GAC1BA,EAAG,gBAAgBA,EAAG,YAAaoI,EAAe,wBAAwB,EAC1EpI,EAAG,gBAAgBA,EAAG,iBAAkBoI,EAAe,WAAW,EAClEpI,EAAG,gBACD,EACA,EACAoI,EAAe,MACfA,EAAe,OACf,EACA,EACAA,EAAe,MACfA,EAAe,OACfpI,EAAG,iBACHA,EAAG,OACT,EACIA,EAAG,gBAAgBA,EAAG,YAAaoI,EAAe,WAAW,CAC9D,CACD,oBAAoB5C,EAAc,CAChC,MAAMnG,EAAW,KAAK,UAChBW,EAAKX,EAAS,GACd+I,EAAiB,IAAIlC,GAE3B,OADqBV,EAAa,aACjB,WAAanG,EAAS,QACrC,KAAK,UAAU,QAAQ,iBAAiBmG,EAAa,aAAa,QAAQ,EAC1E4C,EAAe,YAAc,KACtBA,IAET,KAAK,WAAW5C,EAAc4C,CAAc,EAC5CpI,EAAG,gBAAgBA,EAAG,YAAa,IAAI,EAChCoI,EACR,CACD,uBAAuBK,EAAiB,CACtC,MAAMzI,EAAK,KAAK,UAAU,GACtByI,EAAgB,cAClBzI,EAAG,kBAAkByI,EAAgB,WAAW,EAChDA,EAAgB,YAAc,MAE5BA,EAAgB,2BAClBzI,EAAG,kBAAkByI,EAAgB,wBAAwB,EAC7DA,EAAgB,yBAA2B,MAEzCA,EAAgB,2BAClBzI,EAAG,mBAAmByI,EAAgB,wBAAwB,EAC9DA,EAAgB,yBAA2B,MAE7CA,EAAgB,iBAAiB,QAASI,GAAiB,CACzD7I,EAAG,mBAAmB6I,CAAY,CACxC,CAAK,EACDJ,EAAgB,iBAAmB,IACpC,CACD,MAAMK,EAAeT,EAAOC,EAAY,CACtC,GAAI,CAACD,EACH,OACF,MAAMN,EAAqB,KAAK,oBAC5B,OAAOM,GAAU,YACnBA,EAAQA,EAAQU,EAAM,IAAMA,EAAM,MAEpC,MAAM/I,EAAK,KAAK,UAAU,GAC1B,GAAIqI,EAAQU,EAAM,MAAO,CACvBT,IAAeA,EAAaP,EAAmB,mBAC/C,MAAMiB,EAAkB,KAAK,iBACvBC,EAAkBX,GACpBU,EAAgB,CAAC,IAAMC,EAAgB,CAAC,GAAKD,EAAgB,CAAC,IAAMC,EAAgB,CAAC,GAAKD,EAAgB,CAAC,IAAMC,EAAgB,CAAC,GAAKD,EAAgB,CAAC,IAAMC,EAAgB,CAAC,KACjLD,EAAgB,CAAC,EAAIC,EAAgB,CAAC,EACtCD,EAAgB,CAAC,EAAIC,EAAgB,CAAC,EACtCD,EAAgB,CAAC,EAAIC,EAAgB,CAAC,EACtCD,EAAgB,CAAC,EAAIC,EAAgB,CAAC,EACtCjJ,EAAG,WAAWiJ,EAAgB,CAAC,EAAGA,EAAgB,CAAC,EAAGA,EAAgB,CAAC,EAAGA,EAAgB,CAAC,CAAC,EAE/F,CACDjJ,EAAG,MAAMqI,CAAK,CACf,CACD,sBAAsB7C,EAAc,CAClC,GAAIA,EAAa,OACf,OAEF,MAAM4C,EADqB,KAAK,oBACU,mBAAmB5C,CAAY,EACzE,KAAK,aAAaA,EAAc4C,CAAc,GAC1C5C,EAAa,SAAWA,EAAa,QACvC,KAAK,eAAe4C,CAAc,CAErC,CACD,WAAW5C,EAAc4C,EAAgB,CACvC,MAAM/I,EAAW,KAAK,UAChBW,EAAKX,EAAS,GACd6J,EAA2BlJ,EAAG,oBA0BpC,GAzBAoI,EAAe,yBAA2Bc,EAC1ClJ,EAAG,gBAAgBA,EAAG,YAAakJ,CAAwB,EAC3Dd,EAAe,MAAQ5C,EAAa,aAAa,OAAO,WACxD4C,EAAe,OAAS5C,EAAa,aAAa,OAAO,YACzDA,EAAa,cAAc,QAAQ,CAAC2D,EAAc3F,IAAM,CACtD,MAAMgF,EAASW,EAAa,OACxBX,EAAO,YACLnJ,EAAS,QAAQ,SAAS,KAC5B+I,EAAe,KAAO,GAEtBlH,EAAK,qEAAqE,GAG9E7B,EAAS,QAAQ,WAAWmJ,EAAQ,CAAC,EAErC,MAAMY,EADW/J,EAAS,QAAQ,YAAYmJ,CAAM,EACzB,QAC3BxI,EAAG,qBACDA,EAAG,YACHA,EAAG,kBAAoBwD,EACvB,KAEA4F,EACA,CACR,CACA,CAAK,EACGhB,EAAe,KAAM,CACvB,MAAMiB,EAAkBrJ,EAAG,oBAC3BoI,EAAe,YAAciB,EAC7BrJ,EAAG,gBAAgBA,EAAG,YAAaqJ,CAAe,EAClD7D,EAAa,cAAc,QAAQ,CAAC8D,EAAG9F,IAAM,CAC3C,MAAM+F,EAAmBvJ,EAAG,qBAC5BoI,EAAe,iBAAiB5E,CAAC,EAAI+F,CAC7C,CAAO,CACP,MACMnB,EAAe,YAAcc,EAE/B,KAAK,aAAa1D,EAAc4C,CAAc,CAC/C,CACD,aAAa5C,EAAc4C,EAAgB,CACzC,MAAMI,EAAShD,EAAa,aAAa,OAQzC,GAPA4C,EAAe,MAAQI,EAAO,WAC9BJ,EAAe,OAASI,EAAO,YAC/BhD,EAAa,cAAc,QAAQ,CAAC2D,EAAc,IAAM,CAClD,IAAM,GAEVA,EAAa,OAAO,OAAOX,EAAO,MAAOA,EAAO,OAAQA,EAAO,WAAW,CAChF,CAAK,EACGJ,EAAe,KAAM,CACvB,MAAM/I,EAAW,KAAK,UAChBW,EAAKX,EAAS,GACdgK,EAAkBjB,EAAe,YACvCpI,EAAG,gBAAgBA,EAAG,YAAaqJ,CAAe,EAClD7D,EAAa,cAAc,QAAQ,CAAC2D,EAAc3F,IAAM,CACtD,MAAMgG,EAAUL,EAAa,OAC7B9J,EAAS,QAAQ,WAAWmK,EAAS,CAAC,EAEtC,MAAMC,EADWpK,EAAS,QAAQ,YAAYmK,CAAO,EACnB,eAC5BD,EAAmBnB,EAAe,iBAAiB5E,CAAC,EAC1DxD,EAAG,iBACDA,EAAG,aACHuJ,CACV,EACQvJ,EAAG,+BACDA,EAAG,aACH,EACAyJ,EACAD,EAAQ,WACRA,EAAQ,WAClB,EACQxJ,EAAG,wBACDA,EAAG,YACHA,EAAG,kBAAoBwD,EACvBxD,EAAG,aACHuJ,CACV,CACA,CAAO,CACF,CACF,CACD,aAAanB,EAAgB,CAC3B,GAAIA,EAAe,cAAgB,KACjC,OACF,MAAMpI,EAAK,KAAK,UAAU,GACpB0J,EAA2B1J,EAAG,qBACpCoI,EAAe,yBAA2BsB,EAC1C1J,EAAG,iBACDA,EAAG,aACH0J,CACN,EACI1J,EAAG,wBACDA,EAAG,YACHA,EAAG,yBACHA,EAAG,aACH0J,CACN,EACI,KAAK,eAAetB,CAAc,CACnC,CACD,eAAeA,EAAgB,CAC7B,MAAMpI,EAAK,KAAK,UAAU,GAC1BA,EAAG,iBACDA,EAAG,aACHoI,EAAe,wBACrB,EACQA,EAAe,KACjBpI,EAAG,+BACDA,EAAG,aACH,EACAA,EAAG,iBACHoI,EAAe,MACfA,EAAe,MACvB,EAEMpI,EAAG,oBACDA,EAAG,aACH,KAAK,UAAU,QAAQ,eAAiB,EAAIA,EAAG,iBAAmBA,EAAG,cACrEoI,EAAe,MACfA,EAAe,MACvB,CAEG,CACD,UAAU5C,EAAc,CACtB,MAAMmE,EAAWnE,EAAa,aAAa,SACvC,KAAK,UAAU,QAAQ,WAAaoE,EAAa,KAAKD,CAAQ,GAChE,KAAK,UAAU,QAAQ,iBAAiBA,CAAQ,CAEnD,CACD,WAAWnE,EAAc,CACvB,GAAK,KAAK,UAAU,QAAQ,WAExBoE,EAAa,KAAKpE,EAAa,aAAa,QAAQ,EAAG,CACzD,MAAMqE,EAAgB,KAAK,UAAU,QAAQ,OACvCC,EAAetE,EAAa,aAClCsE,EAAa,UAAU,UACrBD,EACA,EACAC,EAAa,YAAcD,EAAc,MACjD,CACK,CACF,CACH,CCxSA,MAAME,WAA6BC,EAAmB,CACpD,YAAY3K,EAAU,CACpB,MAAMA,CAAQ,EACd,KAAK,QAAU,IAAIwI,GACnB,KAAK,QAAQ,KAAKxI,EAAU,IAAI,CACjC,CACH,CAEA0K,GAAqB,UAAY,CAC/B,KAAM,CAACtK,EAAc,WAAW,EAChC,KAAM,cACR,ECXA,SAASwK,GAAuB7K,EAAQ8K,EAAc,CACpD,MAAMC,EAAgB,CAAA,EAChBC,EAAkB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAMtB,EACH,IAAIC,EAAoB,GACpBC,EAAe,EACnB,MAAMC,EAAcL,EAAa,gBAAgB9K,EAAO,SAAS,EACjE,UAAWoE,KAAKpE,EAAO,OAAQ,CAC7B,MAAMoL,EAAQpL,EAAO,OAAOoE,CAAC,EAC7B2G,EAAc,KAAK;AAAA,4BACK3G,CAAC;AAAA,SACpB,EACL,UAAWG,KAAK6G,EAAM,UAAW,CAC/B,MAAMb,EAAWa,EAAM,UAAU7G,CAAC,EAClC,GAAIgG,aAAoBc,EACtB,GAAId,EAAS,IAAK,CAChB,MAAMe,EAAUtL,EAAO,gBAAgBoE,CAAC,EAAE,OAAOG,CAAC,CAAC,EACnDwG,EAAc,KAAK;AAAA;AAAA,wCAEWxG,CAAC;AAAA,+BACV+G,CAAO;AAAA,8BACRtL,EAAO,UAAU,kBAAkBsL,CAAO,EAAE,KAAK;AAAA;AAAA,qBAE1D,CACrB,MACUP,EAAc,KAAK;AAAA,2DAC8BxG,CAAC;AAAA,qBACvC,UAEJgG,aAAoBgB,EAAgB,CAC7C,MAAMD,EAAUtL,EAAO,gBAAgBoE,CAAC,EAAE,OAAOG,CAAC,CAAC,EACnDwG,EAAc,KAAK;AAAA;AAAA,oCAESxG,CAAC;AAAA,2BACV+G,CAAO;AAAA,0BACRtL,EAAO,UAAU,kBAAkBsL,CAAO,EAAE,KAAK;AAAA;AAAA,iBAE1D,CACjB,SAAiBf,aAAoBhE,EAAe,CAC5C,MAAMiF,EAAcxL,EAAO,gBAAgBoE,CAAC,EAAEG,CAAC,EACzCkD,EAAc0D,EAAY,YAAYK,CAAW,EACnD/D,IACGwD,IACHA,EAAoB,GACpBD,EAAgB,KAAK;AAAA;AAAA,yBAER,GAEfF,EAAa,IAAI,UAAUrD,EAAY,SAAUyD,CAAY,EAC7DH,EAAc,KAAK;AAAA,4CACexG,CAAC,MAAM2G,CAAY;AAAA,qBAC1C,EACXA,IAEH,CACF,CACF,CACD,MAAMO,EAAiB,CAAC,GAAGT,EAAiB,GAAGD,CAAa,EAAE,KAAK;AAAA,CAAI,EACvE,OAAO,IAAI,SAAS,IAAK,IAAK,KAAMU,CAAc,CACpD,CClEA,MAAMC,EAAc,CAMlB,YAAYxH,EAASuD,EAAa,CAChC,KAAK,QAAUvD,EACf,KAAK,YAAcuD,EACnB,KAAK,cAAgB,GACrB,KAAK,mBAAqB,GAC1B,KAAK,qBAAuB,EAC7B,CAED,SAAU,CACR,KAAK,YAAc,KACnB,KAAK,cAAgB,KACrB,KAAK,mBAAqB,KAC1B,KAAK,qBAAuB,KAC5B,KAAK,QAAU,IAChB,CACH,CCvBA,SAASkE,EAAc/K,EAAIF,EAAMkL,EAAK,CACpC,MAAM5L,EAASY,EAAG,aAAaF,CAAI,EACnC,OAAAE,EAAG,aAAaZ,EAAQ4L,CAAG,EAC3BhL,EAAG,cAAcZ,CAAM,EAChBA,CACT,CCLA,SAAS6L,EAAaxK,EAAM,CAC1B,MAAMyK,EAAQ,IAAI,MAAMzK,CAAI,EAC5B,QAAS+C,EAAI,EAAGA,EAAI0H,EAAM,OAAQ1H,IAChC0H,EAAM1H,CAAC,EAAI,GAEb,OAAO0H,CACT,CACA,SAASC,GAAarL,EAAMW,EAAM,CAChC,OAAQX,EAAI,CACV,IAAK,QACH,MAAO,GACT,IAAK,OACH,OAAO,IAAI,aAAa,EAAIW,CAAI,EAClC,IAAK,OACH,OAAO,IAAI,aAAa,EAAIA,CAAI,EAClC,IAAK,OACH,OAAO,IAAI,aAAa,EAAIA,CAAI,EAClC,IAAK,MACL,IAAK,OACL,IAAK,YACL,IAAK,iBACH,MAAO,GACT,IAAK,QACH,OAAO,IAAI,WAAW,EAAIA,CAAI,EAChC,IAAK,QACH,OAAO,IAAI,WAAW,EAAIA,CAAI,EAChC,IAAK,QACH,OAAO,IAAI,WAAW,EAAIA,CAAI,EAChC,IAAK,QACH,OAAO,IAAI,YAAY,EAAIA,CAAI,EACjC,IAAK,QACH,OAAO,IAAI,YAAY,EAAIA,CAAI,EACjC,IAAK,QACH,OAAO,IAAI,YAAY,EAAIA,CAAI,EACjC,IAAK,OACH,MAAO,GACT,IAAK,QACH,OAAOwK,EAAa,EAAIxK,CAAI,EAC9B,IAAK,QACH,OAAOwK,EAAa,EAAIxK,CAAI,EAC9B,IAAK,QACH,OAAOwK,EAAa,EAAIxK,CAAI,EAC9B,IAAK,OACH,OAAO,IAAI,aAAa,CACtB,EACA,EACA,EACA,CACR,CAAO,EACH,IAAK,OACH,OAAO,IAAI,aAAa,CACtB,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,CACR,CAAO,EACH,IAAK,OACH,OAAO,IAAI,aAAa,CACtB,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,CACR,CAAO,CACJ,CACD,OAAO,IACT,CClFA,IAAI2K,EAAW,KACf,MAAMC,EAAmB,CACvB,MAAO,QACP,WAAY,OACZ,WAAY,OACZ,WAAY,OACZ,IAAK,MACL,SAAU,QACV,SAAU,QACV,SAAU,QACV,aAAc,OACd,kBAAmB,QACnB,kBAAmB,QACnB,kBAAmB,QACnB,KAAM,OACN,UAAW,QACX,UAAW,QACX,UAAW,QACX,WAAY,OACZ,WAAY,OACZ,WAAY,OACZ,WAAY,YACZ,eAAgB,YAChB,wBAAyB,YACzB,aAAc,cACd,iBAAkB,cAClB,0BAA2B,cAC3B,iBAAkB,iBAClB,qBAAsB,iBACtB,8BAA+B,gBACjC,EACMC,GAAuB,CAC3B,MAAO,UACP,KAAM,YACN,KAAM,YACN,KAAM,YACN,IAAK,SACL,MAAO,WACP,MAAO,WACP,MAAO,WACP,KAAM,SACN,MAAO,WACP,MAAO,WACP,MAAO,WACP,KAAM,SACN,MAAO,WACP,MAAO,WACP,MAAO,UACT,EACA,SAASC,GAAQvL,EAAIF,EAAM,CACzB,GAAI,CAACsL,EAAU,CACb,MAAMI,EAAY,OAAO,KAAKH,CAAgB,EAC9CD,EAAW,CAAA,EACX,QAAS5H,EAAI,EAAGA,EAAIgI,EAAU,OAAQ,EAAEhI,EAAG,CACzC,MAAMiI,EAAKD,EAAUhI,CAAC,EACtB4H,EAASpL,EAAGyL,CAAE,CAAC,EAAIJ,EAAiBI,CAAE,CACvC,CACF,CACD,OAAOL,EAAStL,CAAI,CACtB,CACA,SAAS4L,GAAoB1L,EAAIF,EAAM,CACrC,MAAM6L,EAAYJ,GAAQvL,EAAIF,CAAI,EAClC,OAAOwL,GAAqBK,CAAS,GAAK,SAC5C,CC5DA,SAASC,GAA+BtI,EAAStD,EAAI6L,EAAiB,GAAO,CAC3E,MAAM/J,EAAa,CAAA,EACbgK,EAAkB9L,EAAG,oBAAoBsD,EAAStD,EAAG,iBAAiB,EAC5E,QAASwD,EAAI,EAAGA,EAAIsI,EAAiBtI,IAAK,CACxC,MAAMuI,EAAa/L,EAAG,gBAAgBsD,EAASE,CAAC,EAChD,GAAIuI,EAAW,KAAK,WAAW,KAAK,EAClC,SAEF,MAAMpJ,EAAS+I,GAAoB1L,EAAI+L,EAAW,IAAI,EACtDjK,EAAWiK,EAAW,IAAI,EAAI,CAC5B,SAAU,EAEV,OAAApJ,EACA,OAAQ6B,EAA2B7B,CAAM,EAAE,OAC3C,OAAQ,EACR,SAAU,GACV,MAAO,CACb,CACG,CACD,MAAMqJ,EAAO,OAAO,KAAKlK,CAAU,EACnC,GAAI+J,EAAgB,CAClBG,EAAK,KAAK,CAAC,EAAG9I,IAAM,EAAIA,EAAI,EAAI,EAAE,EAClC,QAASM,EAAI,EAAGA,EAAIwI,EAAK,OAAQxI,IAC/B1B,EAAWkK,EAAKxI,CAAC,CAAC,EAAE,SAAWA,EAC/BxD,EAAG,mBAAmBsD,EAASE,EAAGwI,EAAKxI,CAAC,CAAC,EAE3CxD,EAAG,YAAYsD,CAAO,CAC1B,KACI,SAASE,EAAI,EAAGA,EAAIwI,EAAK,OAAQxI,IAC/B1B,EAAWkK,EAAKxI,CAAC,CAAC,EAAE,SAAWxD,EAAG,kBAAkBsD,EAAS0I,EAAKxI,CAAC,CAAC,EAGxE,OAAO1B,CACT,CCpCA,SAASmK,GAAW3I,EAAStD,EAAI,CAC/B,GAAI,CAACA,EAAG,sBACN,MAAO,GACT,MAAMkM,EAAgB,CAAA,EAChBC,EAAsBnM,EAAG,oBAAoBsD,EAAStD,EAAG,qBAAqB,EACpF,QAASwD,EAAI,EAAGA,EAAI2I,EAAqB3I,IAAK,CAC5C,MAAM4I,EAAOpM,EAAG,0BAA0BsD,EAASE,CAAC,EAC9C6I,EAAoBrM,EAAG,qBAAqBsD,EAAS8I,CAAI,EACzD3L,EAAOT,EAAG,+BAA+BsD,EAASE,EAAGxD,EAAG,uBAAuB,EACrFkM,EAAcE,CAAI,EAAI,CACpB,KAAAA,EACA,MAAOC,EACP,KAAA5L,CACN,CACG,CACD,OAAOyL,CACT,CCbA,SAASI,GAAehJ,EAAStD,EAAI,CACnC,MAAMuM,EAAW,CAAA,EACXC,EAAgBxM,EAAG,oBAAoBsD,EAAStD,EAAG,eAAe,EACxE,QAASwD,EAAI,EAAGA,EAAIgJ,EAAehJ,IAAK,CACtC,MAAMqD,EAAc7G,EAAG,iBAAiBsD,EAASE,CAAC,EAC5C4I,EAAOvF,EAAY,KAAK,QAAQ,WAAY,EAAE,EAC9C4F,EAAU,CAAC,CAAC5F,EAAY,KAAK,MAAM,UAAU,EAC7C/G,EAAOyL,GAAQvL,EAAI6G,EAAY,IAAI,EACzC0F,EAASH,CAAI,EAAI,CACf,KAAAA,EACA,MAAO5I,EACP,KAAA1D,EACA,KAAM+G,EAAY,KAClB,QAAA4F,EACA,MAAOtB,GAAarL,EAAM+G,EAAY,IAAI,CAChD,CACG,CACD,OAAO0F,CACT,CCrBA,SAASG,EAAqB1M,EAAIZ,EAAQ,CACxC,MAAMuN,EAAY3M,EAAG,gBAAgBZ,CAAM,EAAE,MAAM;AAAA,CAAI,EAAE,IAAI,CAACwN,EAAM1M,IAAU,GAAGA,CAAK,KAAK0M,CAAI,EAAE,EAC3FC,EAAY7M,EAAG,iBAAiBZ,CAAM,EACtC0N,EAAcD,EAAU,MAAM;AAAA,CAAI,EAClCE,EAAS,CAAA,EACTC,EAAcF,EAAY,IAAKF,GAAS,WAAWA,EAAK,QAAQ,2BAA4B,IAAI,CAAC,CAAC,EAAE,OAAQK,GAC5GA,GAAK,CAACF,EAAOE,CAAC,GAChBF,EAAOE,CAAC,EAAI,GACL,IAEF,EACR,EACKC,EAAU,CAAC,EAAE,EACnBF,EAAY,QAASG,GAAW,CAC9BR,EAAUQ,EAAS,CAAC,EAAI,KAAKR,EAAUQ,EAAS,CAAC,CAAC,KAClDD,EAAQ,KAAK,sDAAuD,iBAAiB,CACzF,CAAG,EACD,MAAME,EAAsBT,EAAU,KAAK;AAAA,CAAI,EAC/CO,EAAQ,CAAC,EAAIE,EACb,QAAQ,MAAMP,CAAS,EACvB,QAAQ,eAAe,gCAAgC,EACvD,QAAQ,KAAK,GAAGK,CAAO,EACvB,QAAQ,SAAQ,CAClB,CACA,SAASG,GAAgBrN,EAAIsD,EAASgK,EAAcC,EAAgB,CAC7DvN,EAAG,oBAAoBsD,EAAStD,EAAG,WAAW,IAC5CA,EAAG,mBAAmBsN,EAActN,EAAG,cAAc,GACxD0M,EAAqB1M,EAAIsN,CAAY,EAElCtN,EAAG,mBAAmBuN,EAAgBvN,EAAG,cAAc,GAC1D0M,EAAqB1M,EAAIuN,CAAc,EAEzC,QAAQ,MAAM,4CAA4C,EACtDvN,EAAG,kBAAkBsD,CAAO,IAAM,IACpC,QAAQ,KAAK,yCAA0CtD,EAAG,kBAAkBsD,CAAO,CAAC,EAG1F,CC5BA,SAASkK,GAAgBxN,EAAIsD,EAAS,CACpC,MAAMmK,EAAe1C,EAAc/K,EAAIA,EAAG,cAAesD,EAAQ,MAAM,EACjEoK,EAAe3C,EAAc/K,EAAIA,EAAG,gBAAiBsD,EAAQ,QAAQ,EACrEqK,EAAe3N,EAAG,gBACxBA,EAAG,aAAa2N,EAAcF,CAAY,EAC1CzN,EAAG,aAAa2N,EAAcD,CAAY,EAC1C,MAAME,EAA4BtK,EAAQ,0BACtCsK,IACE,OAAO5N,EAAG,2BAA8B,WAC1CkB,EAAK,6EAA6E,EAElFlB,EAAG,0BACD2N,EACAC,EAA0B,MAC1BA,EAA0B,aAAe,WAAa5N,EAAG,iBAAmBA,EAAG,mBACvF,GAGEA,EAAG,YAAY2N,CAAY,EACtB3N,EAAG,oBAAoB2N,EAAc3N,EAAG,WAAW,GACtDqN,GAAgBrN,EAAI2N,EAAcF,EAAcC,CAAY,EAE9DpK,EAAQ,eAAiBsI,GACvB+B,EACA3N,EACA,CAAC,iDAAiD,KAAKsD,EAAQ,MAAM,CACzE,EACEA,EAAQ,aAAegJ,GAAeqB,EAAc3N,CAAE,EACtDsD,EAAQ,kBAAoB2I,GAAW0B,EAAc3N,CAAE,EACvDA,EAAG,aAAayN,CAAY,EAC5BzN,EAAG,aAAa0N,CAAY,EAC5B,MAAM7G,EAAc,CAAA,EACpB,UAAWrD,KAAKF,EAAQ,aAAc,CACpC,MAAM5C,EAAO4C,EAAQ,aAAaE,CAAC,EACnCqD,EAAYrD,CAAC,EAAI,CACf,SAAUxD,EAAG,mBAAmB2N,EAAcnK,CAAC,EAC/C,MAAO2H,GAAazK,EAAK,KAAMA,EAAK,IAAI,CAC9C,CACG,CAED,OADkB,IAAIoK,GAAc6C,EAAc9G,CAAW,CAE/D,CC7CA,MAAMgH,EAAkB,CACtB,aAAc,EACd,WAAY,CACd,EACA,MAAMC,EAAe,CACnB,YAAYzO,EAAU,CAKpB,KAAK,eAAiB,KACtB,KAAK,iBAAmC,OAAO,OAAO,IAAI,EAC1D,KAAK,qBAAuC,OAAO,OAAO,IAAI,EAC9D,KAAK,UAAYA,EACjB,KAAK,UAAU,aAAa,eAAe,KAAM,kBAAkB,CACpE,CACD,cAAcW,EAAI,CAChB,KAAK,IAAMA,EACX,KAAK,iBAAmC,OAAO,OAAO,IAAI,EAC1D,KAAK,qBAAuC,OAAO,OAAO,IAAI,EAC9D,KAAK,eAAiB,KACtB,KAAK,YAAc+N,GACpB,CAOD,KAAK3O,EAAQ6G,EAAU,CAErB,GADA,KAAK,YAAY7G,EAAO,SAAS,EAC7B6G,EACF,OACF4H,EAAgB,aAAe,EAC/BA,EAAgB,WAAa,EAC7B,IAAIG,EAAe,KAAK,qBAAqB5O,EAAO,UAAU,IAAI,EAC7D4O,IACHA,EAAe,KAAK,qBAAqB5O,EAAO,UAAU,IAAI,EAAI,KAAK,oBAAoBA,EAAQ,IAAI,GAEzG,KAAK,UAAU,OAAO,aAAa,CAAC,CAACA,EAAO,UAAU,yBAAyB,EAC/E4O,EAAa,KAAK,UAAW5O,EAAQyO,CAAe,CACrD,CAKD,mBAAmBI,EAAc,CAC/B,KAAK,UAAU,aAAa,mBAAmBA,EAAc,KAAK,eAAgBJ,CAAe,CAClG,CAOD,iBAAiBI,EAAc7B,EAAMlM,EAAQ,EAAG,CAC9C,MAAMqD,EAAe,KAAK,UAAU,OAC9BgH,EAAc,KAAK,gBAAgB,KAAK,cAAc,EACtD2D,EAAmBD,EAAa,gBACjCC,GACH,KAAK,UAAU,IAAI,mBAAmBD,CAAY,EAEpD,MAAMpO,EAASoO,EAAa,OACtBhO,EAAWsD,EAAa,aAAa1D,CAAM,EAC3CsO,EAAgB5K,EAAa,0BAA0BtD,CAAQ,EACrE,GAAIiO,EAAkB,CACpB,KAAM,CAAE,OAAA1N,EAAQ,KAAAC,CAAM,EAAGwN,EACrBzN,IAAW,GAAKC,IAASZ,EAAO,KAAK,WACvC0D,EAAa,eAAetD,EAAUkO,CAAa,EAEnD5K,EAAa,gBAAgBtD,EAAUkO,EAAe3N,CAAM,CAE/D,MAAU+C,EAAa,wBAAwBtD,CAAQ,IAAMkO,GAC5D5K,EAAa,eAAetD,EAAUkO,CAAa,EAErD,MAAM9B,EAAoB,KAAK,eAAe,kBAAkBD,CAAI,EAAE,MAClE7B,EAAY,qBAAqBrK,CAAK,IAAMiO,IAEhD5D,EAAY,qBAAqBrK,CAAK,EAAIiO,EAC1C,KAAK,UAAU,GAAG,oBAAoB5D,EAAY,QAAS8B,EAAmB8B,CAAa,EAC5F,CACD,YAAY7K,EAAS,CACnB,GAAI,KAAK,iBAAmBA,EAC1B,OACF,KAAK,eAAiBA,EACtB,MAAMiH,EAAc,KAAK,gBAAgBjH,CAAO,EAChD,KAAK,IAAI,WAAWiH,EAAY,OAAO,CACxC,CAMD,gBAAgBjH,EAAS,CACvB,OAAO,KAAK,iBAAiBA,EAAQ,IAAI,GAAK,KAAK,mBAAmBA,CAAO,CAC9E,CACD,mBAAmBA,EAAS,CAC1B,MAAM8K,EAAM9K,EAAQ,KACpB,YAAK,iBAAiB8K,CAAG,EAAIZ,GAAgB,KAAK,IAAKlK,CAAO,EACvD,KAAK,iBAAiB8K,CAAG,CACjC,CACD,SAAU,CACR,UAAWA,KAAO,OAAO,KAAK,KAAK,gBAAgB,EAC7B,KAAK,iBAAiBA,CAAG,EACjC,QAAO,EACnB,KAAK,iBAAiBA,CAAG,EAAI,KAE/B,KAAK,iBAAmB,IACzB,CASD,oBAAoBhP,EAAQ8K,EAAc,CACxC,OAAOD,GAAuB7K,EAAQ8K,CAAY,CACnD,CACD,YAAa,CACX,KAAK,eAAiB,IACvB,CACH,CAEA4D,GAAe,UAAY,CACzB,KAAM,CACJrO,EAAc,WACf,EACD,KAAM,QACR,ECvIA,MAAM4O,GAA4B,CAChC,IAAK;AAAA;AAAA;AAAA,WAIL,YAAa;AAAA;AAAA;AAAA;AAAA,WAKb,YAAa;AAAA;AAAA;AAAA;AAAA;AAAA,WAMb,YAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAOb,IAAK;AAAA;AAAA;AAAA,WAIL,YAAa;AAAA;AAAA;AAAA;AAAA,WAKb,YAAa;AAAA;AAAA;AAAA;AAAA;AAAA,WAMb,YAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAOb,IAAK;AAAA;AAAA;AAAA,WAIL,YAAa;AAAA;AAAA;AAAA;AAAA,WAKb,YAAa;AAAA;AAAA;AAAA;AAAA;AAAA,WAMb,YAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAOb,KAAM;AAAA;AAAA;AAAA,WAIN,aAAc;AAAA;AAAA;AAAA;AAAA,WAKd,aAAc;AAAA;AAAA;AAAA;AAAA;AAAA,WAMd,aAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAOd,cAAe,2CACf,cAAe,2CACf,cAAe,0CACjB,EACMC,GAA2B,CAC/B,IAAK,8BACL,YAAa,8BACb,YAAa,8BACb,YAAa,8BACb,cAAe,2CACf,cAAe,2CACf,cAAe,2CACf,IAAK,8BACL,YAAa,8BACb,YAAa,8BACb,YAAa,8BACb,IAAK,8BACL,YAAa,8BACb,YAAa,8BACb,YAAa,8BACb,KAAM,8BACN,aAAc,8BACd,aAAc,8BACd,aAAc,6BAChB,EC5GA,SAASC,GAAqB/D,EAAO3D,EAAa,CAChD,MAAMsD,EAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAOpB,EACH,UAAW3G,KAAKgH,EAAM,SAAU,CAC9B,GAAI,CAAC3D,EAAYrD,CAAC,EAAG,CACfgH,EAAM,SAAShH,CAAC,YAAaiH,EAC3BD,EAAM,SAAShH,CAAC,EAAE,IACpB2G,EAAc,KAAK;AAAA,8DACiC3G,CAAC,MAAMA,CAAC;AAAA,qBACjD,EAEX2G,EAAc,KAAK;AAAA,gEACmC3G,CAAC;AAAA,qBAC5C,EAEJgH,EAAM,SAAShH,CAAC,YAAamH,GACtCR,EAAc,KAAK;AAAA,gEACqC3G,CAAC,MAAMA,CAAC;AAAA,qBACnD,EAEf,QACD,CACD,MAAMgL,EAAUhE,EAAM,kBAAkBhH,CAAC,EACzC,IAAIiL,EAAS,GACb,QAAS9K,EAAI,EAAGA,EAAI+K,EAAe,OAAQ/K,IAAK,CAC9C,MAAMgL,EAASD,EAAe/K,CAAC,EAC/B,GAAI6K,EAAQ,OAASG,EAAO,MAAQA,EAAO,KAAKH,CAAO,EAAG,CACxDrE,EAAc,KAAK,WAAW3G,CAAC,KAAMkL,EAAe/K,CAAC,EAAE,OAAO,EAC9D8K,EAAS,GACT,KACD,CACF,CACD,GAAI,CAACA,EAAQ,CAEX,MAAMG,GADeJ,EAAQ,OAAS,EAAIH,GAA4BC,IACxCE,EAAQ,IAAI,EAAE,QAAQ,WAAY,OAAOhL,CAAC,aAAa,EACrF2G,EAAc,KAAK;AAAA,uBACF3G,CAAC;AAAA;AAAA,sBAEFA,CAAC;AAAA,cACToL,CAAQ,GAAG,CACpB,CACF,CACD,OAAO,IAAI,SAAS,KAAM,KAAM,WAAY,WAAYzE,EAAc,KAAK;AAAA,CAAI,CAAC,CAClF,CCnDA,MAAM0E,EAAqB,CAEzB,YAAYxP,EAAU,CAEpB,KAAK,OAAS,GACd,KAAK,sBAAwB,GAC7B,KAAK,UAAYA,EACjB,KAAK,GAAK,KACV,KAAK,OAAS,EACf,CACD,cAAcW,EAAI,CAChB,KAAK,GAAKA,CACX,CAQD,mBAAmBwK,EAAOlH,EAASwL,EAAU,CAC3C,MAAMvE,EAAc,KAAK,UAAU,OAAO,gBAAgBjH,CAAO,GAC7D,CAACkH,EAAM,UAAYA,EAAM,WAAaD,EAAY,mBAAmBC,EAAM,GAAG,KAChFD,EAAY,mBAAmBC,EAAM,GAAG,EAAIA,EAAM,SACjC,KAAK,wBAAwBA,EAAOlH,CAAO,EACnDiH,EAAY,YAAaC,EAAM,SAAU,KAAK,UAAWsE,CAAQ,EAE7E,CAMD,wBAAwBtE,EAAOlH,EAAS,CACtC,OAAO,KAAK,sBAAsBkH,EAAM,UAAU,IAAIlH,EAAQ,IAAI,GAAK,KAAK,2BAA2BkH,EAAOlH,CAAO,CACtH,CACD,2BAA2BkH,EAAOlH,EAAS,CACzC,MAAMyL,EAAuB,KAAK,sBAAsBvE,EAAM,UAAU,IAAM,KAAK,sBAAsBA,EAAM,UAAU,EAAI,CAAE,GACzH3J,EAAK,KAAK,cAAc2J,EAAOlH,EAAQ,aAAc,GAAG,EAC9D,OAAK,KAAK,OAAOzC,CAAE,IACjB,KAAK,OAAOA,CAAE,EAAI,KAAK,sBAAsB2J,EAAOlH,EAAQ,YAAY,GAE1EyL,EAAqBzL,EAAQ,IAAI,EAAI,KAAK,OAAOzC,CAAE,EAC5CkO,EAAqBzL,EAAQ,IAAI,CACzC,CACD,sBAAsBkH,EAAO3D,EAAa,CACxC,OAAO0H,GAAqB/D,EAAO3D,CAAW,CAC/C,CASD,cAAc2D,EAAO3D,EAAamI,EAAQ,CACxC,MAAMzC,EAAW/B,EAAM,SACjB3G,EAAU,CAAC,GAAGmL,CAAM,GAAG,EAC7B,UAAWxL,KAAK+I,EACd1I,EAAQ,KAAKL,CAAC,EACVqD,EAAYrD,CAAC,GACfK,EAAQ,KAAKgD,EAAYrD,CAAC,EAAE,IAAI,EAGpC,OAAOK,EAAQ,KAAK,GAAG,CACxB,CAED,SAAU,CACR,KAAK,UAAY,KACjB,KAAK,OAAS,IACf,CACH,CAEAgL,GAAqB,UAAY,CAC/B,KAAM,CACJpP,EAAc,WACf,EACD,KAAM,cACR,EChFA,SAASwP,GAAyBjP,EAAI,CACpC,MAAMkP,EAAW,CAAA,EAWjB,GAVAA,EAAS,OAAS,CAAClP,EAAG,IAAKA,EAAG,mBAAmB,EACjDkP,EAAS,IAAM,CAAClP,EAAG,IAAKA,EAAG,GAAG,EAC9BkP,EAAS,SAAW,CAAClP,EAAG,UAAWA,EAAG,oBAAqBA,EAAG,IAAKA,EAAG,mBAAmB,EACzFkP,EAAS,OAAS,CAAClP,EAAG,IAAKA,EAAG,oBAAqBA,EAAG,IAAKA,EAAG,mBAAmB,EACjFkP,EAAS,KAAO,CAAC,EAAG,CAAC,EACrBA,EAAS,YAAY,EAAI,CAAClP,EAAG,UAAWA,EAAG,oBAAqBA,EAAG,IAAKA,EAAG,mBAAmB,EAC9FkP,EAAS,SAAS,EAAI,CAAClP,EAAG,UAAWA,EAAG,IAAKA,EAAG,IAAKA,EAAG,GAAG,EAC3DkP,EAAS,YAAY,EAAI,CAAClP,EAAG,UAAWA,EAAG,oBAAqBA,EAAG,IAAKA,EAAG,mBAAmB,EAC9FkP,EAAS,MAAQ,CAAClP,EAAG,KAAMA,EAAG,mBAAmB,EAChC,EAAEA,aAAcmB,EAAW,IAAK,EAAC,yBAAwB,GAExE+N,EAAS,IAAM,CAAClP,EAAG,IAAKA,EAAG,IAAKA,EAAG,IAAKA,EAAG,IAAKA,EAAG,IAAKA,EAAG,GAAG,EAC9DkP,EAAS,IAAM,CAAClP,EAAG,IAAKA,EAAG,IAAKA,EAAG,IAAKA,EAAG,IAAKA,EAAG,IAAKA,EAAG,GAAG,MACzD,CACL,MAAMmP,EAAMnP,EAAG,aAAa,kBAAkB,EAC1CmP,IACFD,EAAS,IAAM,CAAClP,EAAG,IAAKA,EAAG,IAAKA,EAAG,IAAKA,EAAG,IAAKmP,EAAI,QAASA,EAAI,OAAO,EACxED,EAAS,IAAM,CAAClP,EAAG,IAAKA,EAAG,IAAKA,EAAG,IAAKA,EAAG,IAAKmP,EAAI,QAASA,EAAI,OAAO,EAE3E,CACD,OAAOD,CACT,CCrBA,MAAME,GAAQ,EACRC,GAAS,EACTC,GAAU,EACVC,GAAa,EACbC,GAAU,EACVC,GAAa,EACbC,GAAiB,MAAMA,CAAe,CAC1C,aAAc,CACZ,KAAK,GAAK,KACV,KAAK,QAAU,EACf,KAAK,cAAgB,EACrB,KAAK,UAAY,OACjB,KAAK,SAAW,GAChB,KAAK,IAAM,GACX,KAAK,IAAIN,EAAK,EAAI,KAAK,SACvB,KAAK,IAAIC,EAAM,EAAI,KAAK,UACxB,KAAK,IAAIC,EAAO,EAAI,KAAK,YACzB,KAAK,IAAIC,EAAU,EAAI,KAAK,aAC5B,KAAK,IAAIC,EAAO,EAAI,KAAK,aACzB,KAAK,IAAIC,EAAU,EAAI,KAAK,aAC5B,KAAK,OAAS,GACd,KAAK,aAAezQ,EAAM,OAC3B,CACD,cAAcgB,EAAI,CAChB,KAAK,GAAKA,EACV,KAAK,cAAgBiP,GAAyBjP,CAAE,EAChD,KAAK,WAAU,CAChB,CAKD,IAAIgG,EAAO,CAET,GADAA,IAAUA,EAAQ,KAAK,cACnB,KAAK,UAAYA,EAAM,KAAM,CAC/B,IAAI2J,EAAO,KAAK,QAAU3J,EAAM,KAC5BxC,EAAI,EACR,KAAOmM,GACDA,EAAO,GACT,KAAK,IAAInM,CAAC,EAAE,KAAK,KAAM,CAAC,EAAEwC,EAAM,KAAO,GAAKxC,EAAE,EAEhDmM,IAAS,EACTnM,IAEF,KAAK,QAAUwC,EAAM,IACtB,CACD,QAASxC,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IACtC,KAAK,OAAOA,CAAC,EAAE,KAAMwC,CAAK,CAE7B,CAKD,WAAWA,EAAO,CAChBA,IAAUA,EAAQ,KAAK,cACvB,QAASxC,EAAI,EAAGA,EAAI,KAAK,IAAI,OAAQA,IACnC,KAAK,IAAIA,CAAC,EAAE,KAAK,KAAM,CAAC,EAAEwC,EAAM,KAAO,GAAKxC,EAAE,EAEhD,QAASA,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IACtC,KAAK,OAAOA,CAAC,EAAE,KAAMwC,CAAK,EAE5B,KAAK,QAAUA,EAAM,IACtB,CAKD,SAAS4J,EAAO,CACd,KAAK,aAAaF,EAAe,gBAAiBE,CAAK,EACvD,KAAK,GAAGA,EAAQ,SAAW,SAAS,EAAE,KAAK,GAAG,KAAK,CACpD,CAKD,UAAUA,EAAO,CACf,KAAK,aAAaF,EAAe,oBAAqBE,CAAK,EAC3D,KAAK,GAAGA,EAAQ,SAAW,SAAS,EAAE,KAAK,GAAG,mBAAmB,CAClE,CAKD,aAAaA,EAAO,CAClB,KAAK,GAAGA,EAAQ,SAAW,SAAS,EAAE,KAAK,GAAG,UAAU,CACzD,CAKD,aAAaA,EAAO,CAClB,KAAK,GAAG,UAAUA,CAAK,CACxB,CAKD,YAAYA,EAAO,CACjB,KAAK,GAAGA,EAAQ,SAAW,SAAS,EAAE,KAAK,GAAG,SAAS,CACxD,CAKD,aAAaA,EAAO,CAClB,KAAK,GAAG,UAAU,KAAK,GAAGA,EAAQ,KAAO,KAAK,CAAC,CAChD,CAKD,aAAaA,EAAO,CAIlB,GAHK,KAAK,cAAcA,CAAK,IAC3BA,EAAQ,UAENA,IAAU,KAAK,UACjB,OAEF,KAAK,UAAYA,EACjB,MAAMpJ,EAAO,KAAK,cAAcoJ,CAAK,EAC/B5P,EAAK,KAAK,GACZwG,EAAK,SAAW,EAClBxG,EAAG,UAAUwG,EAAK,CAAC,EAAGA,EAAK,CAAC,CAAC,EAE7BxG,EAAG,kBAAkBwG,EAAK,CAAC,EAAGA,EAAK,CAAC,EAAGA,EAAK,CAAC,EAAGA,EAAK,CAAC,CAAC,EAErDA,EAAK,SAAW,GAClB,KAAK,SAAW,GAChBxG,EAAG,sBAAsBwG,EAAK,CAAC,EAAGA,EAAK,CAAC,CAAC,GAChC,KAAK,WACd,KAAK,SAAW,GAChBxG,EAAG,sBAAsBA,EAAG,SAAUA,EAAG,QAAQ,EAEpD,CAMD,iBAAiB4P,EAAOC,EAAO,CAC7B,KAAK,GAAG,cAAcD,EAAOC,CAAK,CACnC,CAGD,YAAa,CACX,KAAK,GAAG,YAAY,KAAK,GAAG,oBAAqB,EAAK,EACtD,KAAK,WAAW,KAAK,YAAY,EACjC,KAAK,SAAW,GAChB,KAAK,UAAY,GACjB,KAAK,aAAa,QAAQ,CAC3B,CAUD,aAAaC,EAAMF,EAAO,CACxB,MAAM1P,EAAQ,KAAK,OAAO,QAAQ4P,CAAI,EAClCF,GAAS1P,IAAU,GACrB,KAAK,OAAO,KAAK4P,CAAI,EACZ,CAACF,GAAS1P,IAAU,IAC7B,KAAK,OAAO,OAAOA,EAAO,CAAC,CAE9B,CAMD,OAAO,gBAAgB6P,EAAQ/J,EAAO,CACpC+J,EAAO,aAAa/J,EAAM,SAAS,CACpC,CAMD,OAAO,oBAAoB+J,EAAQ/J,EAAO,CACxC+J,EAAO,iBAAiB,EAAG/J,EAAM,aAAa,CAC/C,CAID,SAAU,CACR,KAAK,GAAK,KACV,KAAK,OAAO,OAAS,CACtB,CACH,EAEA0J,GAAe,UAAY,CACzB,KAAM,CACJjQ,EAAc,WACf,EACD,KAAM,OACR,EACA,IAAIuQ,GAAgBN,GC1MpB,MAAMO,EAAU,CACd,YAAYtH,EAAS,CACnB,KAAK,OAAStG,GAAW,WACzB,KAAK,QAAUsG,EACf,KAAK,MAAQ,GACb,KAAK,OAAS,GACd,KAAK,KAAOpG,EAAS,cACrB,KAAK,eAAiBJ,EAAW,KACjC,KAAK,OAASA,EAAW,KACzB,KAAK,YAAc,CACpB,CACH,CCbA,MAAM+N,GAA8B,CAClC,GAAI,SACJ,OAAO1H,EAAQY,EAAWpJ,EAAI,CACxBoJ,EAAU,QAAUZ,EAAO,OAASY,EAAU,SAAWZ,EAAO,OAClExI,EAAG,cACDA,EAAG,WACH,EACA,EACA,EACAwI,EAAO,MACPA,EAAO,OACPY,EAAU,OACVA,EAAU,KACVZ,EAAO,QACf,EAEMxI,EAAG,WACDoJ,EAAU,OACV,EACAA,EAAU,eACVZ,EAAO,MACPA,EAAO,OACP,EACAY,EAAU,OACVA,EAAU,KACVZ,EAAO,QACf,EAEIY,EAAU,MAAQZ,EAAO,MACzBY,EAAU,OAASZ,EAAO,MAC3B,CACH,EC/BM2H,GAAsB,CAC1B,iBAAkB,GAClB,sBAAuB,GACvB,iBAAkB,GAClB,sBAAuB,GACvB,iBAAkB,GAClB,sBAAuB,GACvB,cAAe,GACf,cAAe,GACf,eAAgB,GAChB,eAAgB,GAChB,kBAAmB,GACnB,iBAAkB,GAClB,iBAAkB,GAClB,sBAAuB,GAGvB,iBAAkB,GAClB,sBAAuB,GACvB,mBAAoB,GACpB,wBAAyB,GACzB,kBAAmB,GACnB,uBAAwB,GACxB,eAAgB,GAChB,eAAgB,GAChB,gBAAiB,GACjB,gBAAiB,GAGjB,iBAAkB,GAClB,sBAAuB,GACvB,iBAAkB,GAClB,sBAAuB,GACvB,iBAAkB,GAClB,sBAAuB,GACvB,iBAAkB,GAClB,sBAAuB,GACvB,iBAAkB,GAClB,sBAAuB,GACvB,iBAAkB,GAClB,sBAAuB,GACvB,iBAAkB,GAClB,sBAAuB,GACvB,iBAAkB,GAClB,sBAAuB,GACvB,kBAAmB,GACnB,uBAAwB,GACxB,kBAAmB,GACnB,uBAAwB,GACxB,kBAAmB,GACnB,uBAAwB,GACxB,mBAAoB,GACpB,wBAAyB,GACzB,mBAAoB,GACpB,wBAAyB,GACzB,mBAAoB,GACpB,wBAAyB,EAC3B,EACMC,GAAoC,CACxC,GAAI,aACJ,OAAO5H,EAAQY,EAAWpJ,EAAI,CAC5BA,EAAG,YAAYA,EAAG,iBAAkB,CAAC,EACrC,IAAIqQ,EAAW7H,EAAO,WAClB8H,EAAY9H,EAAO,YACvB,MAAM+H,EAAa,CAAC,CAACJ,GAAoB3H,EAAO,MAAM,EACtD,QAAShF,EAAI,EAAGA,EAAIgF,EAAO,SAAS,OAAQhF,IAAK,CAC/C,MAAMgN,EAAchI,EAAO,SAAShF,CAAC,EACjC+M,EACFvQ,EAAG,qBACDA,EAAG,WACHwD,EACA4F,EAAU,eACViH,EACAC,EACA,EACAE,CACV,EAEQxQ,EAAG,WACDA,EAAG,WACHwD,EACA4F,EAAU,eACViH,EACAC,EACA,EACAlH,EAAU,OACVA,EAAU,KACVoH,CACV,EAEMH,EAAW,KAAK,IAAIA,GAAY,EAAG,CAAC,EACpCC,EAAY,KAAK,IAAIA,GAAa,EAAG,CAAC,CACvC,CACF,CACH,EC9FMG,GAAwB,CAC5B,GAAI,QACJ,OAAOjI,EAAQY,EAAWpJ,EAAI0Q,EAAc,CAC1C,MAAMC,EAAUvH,EAAU,MACpBwH,EAAWxH,EAAU,OACrByH,EAAerI,EAAO,WACtBsI,EAAgBtI,EAAO,YACvBuI,EAAgBvI,EAAO,cACvBwI,EAAiBxI,EAAO,eAC1BuI,EAAgBF,GAAgBG,EAAiBF,IAC/CH,IAAYE,GAAgBD,IAAaE,IAC3C9Q,EAAG,WACDoJ,EAAU,OACV,EACAA,EAAU,eACVyH,EACAC,EACA,EACA1H,EAAU,OACVA,EAAU,KACV,IACV,EAEUsH,IAAiB,EACnB1Q,EAAG,cACDA,EAAG,WACH,EACA,EACA,EACA+Q,EACAC,EACA5H,EAAU,OACVA,EAAU,KACVZ,EAAO,QACjB,EAEQxI,EAAG,cACDA,EAAG,WACH,EACA,EACA,EACAoJ,EAAU,OACVA,EAAU,KACVZ,EAAO,QACjB,GAEemI,IAAYE,GAAgBD,IAAaE,EAClD9Q,EAAG,cACDA,EAAG,WACH,EACA,EACA,EACAoJ,EAAU,OACVA,EAAU,KACVZ,EAAO,QACf,EACekI,IAAiB,EAC1B1Q,EAAG,WACDoJ,EAAU,OACV,EACAA,EAAU,eACVyH,EACAC,EACA,EACA1H,EAAU,OACVA,EAAU,KACVZ,EAAO,QACf,EAEMxI,EAAG,WACDoJ,EAAU,OACV,EACAA,EAAU,eACVA,EAAU,OACVA,EAAU,KACVZ,EAAO,QACf,EAEIY,EAAU,MAAQyH,EAClBzH,EAAU,OAAS0H,CACpB,CACH,EC/EMG,GAAwB,CAC5B,GAAI,QACJ,OAAOzI,EAAQY,EAAWpJ,EAAI0Q,EAAc,CAC1C,GAAI,CAAClI,EAAO,QAAS,CACnBxI,EAAG,WACDoJ,EAAU,OACV,EACAA,EAAU,eACV,EACA,EACA,EACAA,EAAU,OACVA,EAAU,KACV,IACR,EACM,MACD,CACDqH,GAAsB,OAAOjI,EAAQY,EAAWpJ,EAAI0Q,CAAY,CACjE,CACH,ECrBMQ,EAAsB,CAC1B,OAAQ,KACR,QAAS,IACX,EACMC,GAA4B,CAChC,OAAQ,CACN,OAAQ,KACR,QAAS,IACV,EACD,QAAS,CACP,OAAQ,KACR,QAAS,IACV,CACH,EACMC,EAAsB,CAC1B,gBAAiB,MACjB,OAAQ,MACR,gBAAiB,KACnB,EACMC,GAAyB,CAC7B,MAAO,IACP,KAAM,IACN,MAAO,IACP,aAAc,IACd,QAAS,IACT,YAAa,IACb,gBAAiB,IACjB,OAAQ,GACV,EC1BA,SAASC,EAAiBC,EAAOvR,EAAIwR,EAASC,EAAgBC,EAAgBC,EAAYC,EAAYC,EAAe,CACnH,MAAMC,EAAYH,EAClB,GAAI,CAACE,GAAiBN,EAAM,eAAiB,UAAYA,EAAM,eAAiB,UAAYA,EAAM,eAAiB,SAAU,CAC3H,MAAMQ,EAAYX,EAAoBQ,EAAa,gBAAkBL,EAAM,YAAY,EACjFS,EAAYZ,EAAoBQ,EAAa,gBAAkBL,EAAM,YAAY,EACjFU,EAAYb,EAAoBQ,EAAa,gBAAkBL,EAAM,YAAY,EACvFvR,EAAG0R,CAAc,EAAEI,EAAW9R,EAAG,eAAgB+R,CAAS,EAC1D/R,EAAG0R,CAAc,EAAEI,EAAW9R,EAAG,eAAgBgS,CAAS,EACtDhS,EAAG,gBACLA,EAAG0R,CAAc,EAAEI,EAAW9R,EAAG,eAAgBiS,CAAS,CAC7D,CAID,IAHI,CAACJ,GAAiBN,EAAM,YAAc,WACxCvR,EAAG0R,CAAc,EAAEI,EAAW9R,EAAG,mBAAoBkR,EAAoBK,EAAM,SAAS,CAAC,EAEvFC,GACF,GAAI,CAACK,GAAiBN,EAAM,eAAiB,SAAU,CACrD,MAAMW,EAAef,GAA0BI,EAAM,SAAS,EAAEA,EAAM,YAAY,EAClFvR,EAAG0R,CAAc,EAAEI,EAAW9R,EAAG,mBAAoBkS,CAAY,CAClE,OAEDlS,EAAG0R,CAAc,EAAEI,EAAW9R,EAAG,mBAAoBkR,EAAoBK,EAAM,SAAS,CAAC,EAE3F,GAAIE,GAAkBF,EAAM,cAAgB,EAAG,CAC7C,MAAMY,EAAQ,KAAK,IAAIZ,EAAM,cAAevR,EAAG,aAAayR,EAAe,8BAA8B,CAAC,EAC1GzR,EAAG0R,CAAc,EAAEI,EAAWL,EAAe,2BAA4BU,CAAK,CAC/E,CACGZ,EAAM,SACRvR,EAAG0R,CAAc,EAAEI,EAAW9R,EAAG,qBAAsBqR,GAAuBE,EAAM,OAAO,CAAC,CAEhG,CC/BA,SAASa,GAAoBpS,EAAI,CAC/B,MAAO,CAEL,QAASA,EAAG,IACZ,QAASA,EAAG,IACZ,OAAQA,EAAG,IACX,OAAQA,EAAG,IAEX,QAASA,EAAG,IACZ,QAASA,EAAG,IACZ,SAAUA,EAAG,IACb,SAAUA,EAAG,GACb,SAAUA,EAAG,GACb,QAASA,EAAG,GACZ,QAASA,EAAG,GAEZ,QAASA,EAAG,IACZ,QAASA,EAAG,IACZ,SAAUA,EAAG,IACb,SAAUA,EAAG,GACb,SAAUA,EAAG,GACb,UAAWA,EAAG,GACd,WAAYA,EAAG,KACf,kBAAmBA,EAAG,KAEtB,WAAYA,EAAG,KACf,UAAWA,EAAG,KACd,UAAWA,EAAG,KACd,WAAYA,EAAG,KACf,kBAAmBA,EAAG,KACtB,aAAcA,EAAG,IACjB,aAAcA,EAAG,KACjB,cAAeA,EAAG,IAElB,SAAUA,EAAG,GACb,SAAUA,EAAG,GACb,UAAWA,EAAG,GACd,WAAYA,EAAG,KACf,WAAYA,EAAG,KACf,YAAaA,EAAG,KAEhB,WAAYA,EAAG,KACf,WAAYA,EAAG,KACf,YAAaA,EAAG,KAEhB,SAAUA,EAAG,eACb,aAAcA,EAAG,gBACjB,YAAaA,EAAG,gBAChB,uBAAwBA,EAAG,cAC3B,aAAcA,EAAG,gBACjB,wBAAyBA,EAAG,aAChC,CACA,CClDA,SAASqS,GAA4BrS,EAAIiC,EAAY,CACnD,IAAIqQ,EAAO,CAAA,EACPC,EAAavS,EAAG,KACpB,OAAMA,aAAcmB,EAAW,IAAG,EAAG,yBAA0B,EAMpDc,EAAW,OACpBqQ,EAAO,CACL,kBAAmBrQ,EAAW,KAAK,iBACnC,kBAAmBA,EAAW,KAAK,gBACzC,IATIqQ,EAAO,CACL,kBAAmBtS,EAAG,aACtB,kBAAmBA,EAAG,YAC5B,EACIuS,EAAavS,EAAG,OAOX,CAEL,QAASA,EAAG,GACZ,QAASA,EAAG,SACZ,OAAQA,EAAG,KACX,OAAQA,EAAG,IAEX,QAASA,EAAG,MACZ,QAASA,EAAG,KACZ,SAAUA,EAAG,KACb,SAAUA,EAAG,IACb,SAAUA,EAAG,UACb,QAASA,EAAG,MACZ,QAASA,EAAG,KAEZ,QAASA,EAAG,MACZ,QAASA,EAAG,KACZ,SAAUA,EAAG,KACb,SAAUA,EAAG,OACb,SAAUA,EAAG,MACb,UAAWA,EAAG,MACd,WAAYA,EAAG,KACf,GAAGsS,EAEH,WAAYtS,EAAG,YACf,UAAWA,EAAG,QACd,UAAWA,EAAG,OACd,WAAAuS,EACA,aAAcvS,EAAG,QACjB,aAAcA,EAAG,SACjB,cAAeA,EAAG,eAElB,SAAUA,EAAG,OACb,SAAUA,EAAG,MACb,UAAWA,EAAG,MACd,WAAYA,EAAG,SACf,WAAYA,EAAG,QACf,YAAaA,EAAG,QAEhB,WAAYA,EAAG,SACf,WAAYA,EAAG,QACf,YAAaA,EAAG,QAEhB,SAAUA,EAAG,eACb,aAAcA,EAAG,kBACjB,YAAaA,EAAG,kBAChB,uBAAwBA,EAAG,iBAC3B,aAAcA,EAAG,mBACjB,wBAAyBA,EAAG,kBAE5B,GAAGiC,EAAW,KAAO,CACnB,iBAAkBA,EAAW,KAAK,8BAClC,iBAAkBA,EAAW,KAAK,8BAClC,iBAAkBA,EAAW,KAAK,6BACxC,EAAQ,CAAE,EACN,GAAGA,EAAW,UAAY,CACxB,sBAAuBA,EAAW,UAAU,oCAC5C,sBAAuBA,EAAW,UAAU,oCAC5C,sBAAuBA,EAAW,UAAU,mCAClD,EAAQ,CAAE,EACN,GAAGA,EAAW,KAAO,CACnB,cAAeA,EAAW,KAAK,yBAC/B,cAAeA,EAAW,KAAK,gCAC/B,eAAgBA,EAAW,KAAK,+BAChC,eAAgBA,EAAW,KAAK,qCACtC,EAAQ,CAAE,EACN,GAAGA,EAAW,KAAO,CACnB,iBAAkBA,EAAW,KAAK,qCAClC,kBAAmBA,EAAW,KAAK,uCACnC,iBAAkBA,EAAW,KAAK,+BAClC,sBAAuBA,EAAW,KAAK,oCAC7C,EAAQ,CAAE,EACN,GAAGA,EAAW,IAAM,CAClB,iBAAkBA,EAAW,IAAI,qBACjC,sBAAuBA,EAAW,IAAI,sBACtC,mBAAoBA,EAAW,IAAI,yCACnC,wBAAyBA,EAAW,IAAI,0CACxC,kBAAmBA,EAAW,IAAI,0BAClC,uBAAwBA,EAAW,IAAI,iCACvC,eAAgBA,EAAW,IAAI,mBAE/B,gBAAiBA,EAAW,IAAI,0BAEtC,EAAQ,CAAE,EACN,GAAGA,EAAW,KAAO,CACnB,iBAAkBA,EAAW,KAAK,6BAClC,sBAAuBA,EAAW,KAAK,qCACvC,iBAAkBA,EAAW,KAAK,6BAClC,sBAAuBA,EAAW,KAAK,qCACvC,iBAAkBA,EAAW,KAAK,6BAClC,sBAAuBA,EAAW,KAAK,qCACvC,iBAAkBA,EAAW,KAAK,6BAClC,sBAAuBA,EAAW,KAAK,qCACvC,iBAAkBA,EAAW,KAAK,6BAClC,sBAAuBA,EAAW,KAAK,qCACvC,iBAAkBA,EAAW,KAAK,6BAClC,sBAAuBA,EAAW,KAAK,qCACvC,iBAAkBA,EAAW,KAAK,6BAClC,sBAAuBA,EAAW,KAAK,qCACvC,iBAAkBA,EAAW,KAAK,6BAClC,sBAAuBA,EAAW,KAAK,qCACvC,kBAAmBA,EAAW,KAAK,8BACnC,uBAAwBA,EAAW,KAAK,sCACxC,kBAAmBA,EAAW,KAAK,8BACnC,uBAAwBA,EAAW,KAAK,sCACxC,kBAAmBA,EAAW,KAAK,8BACnC,uBAAwBA,EAAW,KAAK,sCACxC,mBAAoBA,EAAW,KAAK,+BACpC,wBAAyBA,EAAW,KAAK,uCACzC,mBAAoBA,EAAW,KAAK,+BACpC,wBAAyBA,EAAW,KAAK,uCACzC,mBAAoBA,EAAW,KAAK,+BACpC,wBAAyBA,EAAW,KAAK,sCAC/C,EAAQ,CAAE,CACV,CACA,CCpIA,SAASuQ,GAAkBxS,EAAI,CAC7B,MAAO,CAEL,QAASA,EAAG,cACZ,QAASA,EAAG,KACZ,OAAQA,EAAG,cACX,OAAQA,EAAG,KAEX,QAASA,EAAG,eACZ,QAASA,EAAG,MACZ,SAAUA,EAAG,WACb,SAAUA,EAAG,cACb,SAAUA,EAAG,KACb,QAASA,EAAG,cACZ,QAASA,EAAG,KAEZ,QAASA,EAAG,aACZ,QAASA,EAAG,IACZ,SAAUA,EAAG,MACb,SAAUA,EAAG,eACb,SAAUA,EAAG,MACb,UAAWA,EAAG,WACd,WAAYA,EAAG,cACf,kBAAmBA,EAAG,cAEtB,WAAYA,EAAG,KACf,UAAWA,EAAG,cACd,UAAWA,EAAG,KACd,WAAYA,EAAG,cACf,kBAAmBA,EAAG,cACtB,aAAcA,EAAG,yBACjB,aAAcA,EAAG,4BACjB,cAAeA,EAAG,6BAElB,SAAUA,EAAG,aACb,SAAUA,EAAG,IACb,UAAWA,EAAG,MACd,WAAYA,EAAG,eACf,WAAYA,EAAG,MACf,YAAaA,EAAG,WAEhB,WAAYA,EAAG,aACf,WAAYA,EAAG,IACf,YAAaA,EAAG,MAEhB,SAAUA,EAAG,cACb,aAAcA,EAAG,eACjB,YAAaA,EAAG,aAChB,uBAAwBA,EAAG,kBAC3B,aAAcA,EAAG,MACjB,wBAAyBA,EAAG,8BAChC,CACA,CCtCA,MAAMyS,GAAkB,EACxB,MAAMC,EAAgB,CACpB,YAAYrT,EAAU,CACpB,KAAK,gBAAkB,GACvB,KAAK,YAA8B,OAAO,OAAO,IAAI,EACrD,KAAK,YAA8B,OAAO,OAAO,IAAI,EACrD,KAAK,eAAiB,GACtB,KAAK,uBAAyB,GAC9B,KAAK,eAAiC,OAAO,OAAO,IAAI,EACxD,KAAK,SAAW,CACd,MAAOoR,GACP,OAAQP,GACR,MAAOe,GACP,WAAYb,EAClB,EACI,KAAK,kBAAoB,GAEzB,KAAK,qBAAuB,GAC5B,KAAK,UAAY/Q,EACjB,KAAK,UAAU,aAAa,eAAe,KAAM,aAAa,EAC9D,KAAK,UAAU,aAAa,eAAe,KAAM,aAAa,CAC/D,CACD,cAAcW,EAAI,CAChB,KAAK,IAAMA,EACN,KAAK,6BACR,KAAK,2BAA6BqS,GAA4BrS,EAAI,KAAK,UAAU,QAAQ,UAAU,EACnG,KAAK,iBAAmBwS,GAAkBxS,CAAE,EAC5C,KAAK,mBAAqBoS,GAAoBpS,CAAE,GAElD,KAAK,YAA8B,OAAO,OAAO,IAAI,EACrD,KAAK,YAA8B,OAAO,OAAO,IAAI,EACrD,KAAK,eAAiC,OAAO,OAAO,IAAI,EACxD,KAAK,kBAAoB,GACzB,QAASwD,EAAI,EAAGA,EAAI,GAAIA,IACtB,KAAK,KAAK+B,EAAQ,MAAO/B,CAAC,CAE7B,CACD,WAAWgF,EAAQ,CACjB,KAAK,KAAKA,CAAM,CACjB,CACD,KAAKG,EAASrE,EAAW,EAAG,CAC1B,MAAMkE,EAASG,EAAQ,OACnBA,GACF,KAAK,WAAWH,EAAQlE,CAAQ,EAC5B,KAAK,sBACP,KAAK,aAAakE,EAAO,MAAOlE,CAAQ,IAG1C,KAAK,WAAW,KAAMA,CAAQ,EAC1B,KAAK,sBACP,KAAK,aAAa,KAAMA,CAAQ,EAGrC,CACD,WAAWkE,EAAQlE,EAAW,EAAG,CAC/B,MAAMtE,EAAK,KAAK,IAEhB,GADAwI,EAAO,SAAW,KAAK,UAAU,UAAU,MACvC,KAAK,eAAelE,CAAQ,IAAMkE,EAAQ,CAC5C,KAAK,eAAelE,CAAQ,EAAIkE,EAChC,KAAK,kBAAkBlE,CAAQ,EAC/BkE,IAAWA,EAASjD,EAAQ,MAAM,QAClC,MAAM6D,EAAY,KAAK,YAAYZ,CAAM,EACzCxI,EAAG,YAAYoJ,EAAU,OAAQA,EAAU,OAAO,CACnD,CACF,CACD,aAAamI,EAAOjN,EAAW,EAAG,CAChC,MAAMtE,EAAK,KAAK,IAChB,GAAI,CAACuR,EAAO,CACV,KAAK,eAAejN,CAAQ,EAAI,KAChCtE,EAAG,YAAYsE,EAAU,IAAI,EAC7B,MACD,CACD,MAAMqO,EAAU,KAAK,cAAcpB,CAAK,EACpC,KAAK,eAAejN,CAAQ,IAAMqO,IACpC,KAAK,eAAerO,CAAQ,EAAIqO,EAChC3S,EAAG,YAAYsE,EAAUqO,CAAO,EAEnC,CACD,OAAOhK,EAAS,CACd,MAAMH,EAASG,EAAQ,OACjBiK,EAAgB,KAAK,eACrB5S,EAAK,KAAK,IAChB,QAAS,EAAI,EAAG,EAAI4S,EAAc,OAAQ,IACxC,GAAIA,EAAc,CAAC,IAAMpK,EAAQ,CAC/B,KAAK,kBAAkB,CAAC,EACxB,MAAMY,EAAY,KAAK,YAAYZ,CAAM,EACzCxI,EAAG,YAAYoJ,EAAU,OAAQ,IAAI,EACrCwJ,EAAc,CAAC,EAAI,IACpB,CAEJ,CACD,kBAAkBtO,EAAU,CACtB,KAAK,yBAA2BA,IAClC,KAAK,uBAAyBA,EAC9B,KAAK,IAAI,cAAc,KAAK,IAAI,SAAWA,CAAQ,EAEtD,CACD,YAAYkE,EAAQ,CAClB,MAAMxI,EAAK,KAAK,IACVoJ,EAAY,IAAI6G,GAAUjQ,EAAG,cAAe,CAAA,EAIlD,GAHAoJ,EAAU,KAAO,KAAK,iBAAiBZ,EAAO,MAAM,EACpDY,EAAU,eAAiB,KAAK,2BAA2BZ,EAAO,MAAM,EACxEY,EAAU,OAAS,KAAK,mBAAmBZ,EAAO,MAAM,EACpDA,EAAO,sBAAwB,KAAK,UAAU,QAAQ,SAAS,kBAAoBA,EAAO,cAAe,CAC3G,MAAMqK,EAAmB,KAAK,IAAIrK,EAAO,MAAOA,EAAO,MAAM,EAC7DA,EAAO,cAAgB,KAAK,MAAM,KAAK,KAAKqK,CAAgB,CAAC,EAAI,CAClE,CACD,YAAK,YAAYrK,EAAO,GAAG,EAAIY,EAC1B,KAAK,gBAAgB,SAASZ,CAAM,IACvCA,EAAO,GAAG,SAAU,KAAK,eAAgB,IAAI,EAC7CA,EAAO,GAAG,SAAU,KAAK,eAAgB,IAAI,EAC7CA,EAAO,GAAG,cAAe,KAAK,cAAe,IAAI,EACjDA,EAAO,GAAG,UAAW,KAAK,gBAAiB,IAAI,EAC/CA,EAAO,GAAG,SAAU,KAAK,eAAgB,IAAI,EAC7CA,EAAO,GAAG,gBAAiB,KAAK,gBAAiB,IAAI,EACrD,KAAK,gBAAgB,KAAKA,CAAM,GAElC,KAAK,eAAeA,CAAM,EAC1B,KAAK,YAAYA,EAAQ,EAAK,EACvBY,CACR,CACD,cAAcZ,EAAQ,CACpB,KAAK,YAAYA,EAAQ,EAAK,CAC/B,CACD,YAAYA,EAAQqJ,EAAe,CACjC,MAAM7R,EAAK,KAAK,IACVoJ,EAAY,KAAK,YAAYZ,CAAM,EACzCxI,EAAG,YAAYA,EAAG,WAAYoJ,EAAU,OAAO,EAC/C,KAAK,eAAe,KAAK,sBAAsB,EAAIZ,EACnD8I,EACE9I,EAAO,MACPxI,EACAwI,EAAO,cAAgB,EACvB,KAAK,UAAU,QAAQ,WAAW,qBAClC,gBACAxI,EAAG,WAEH,CAAC,KAAK,UAAU,QAAQ,SAAS,mBAAqB,CAACwI,EAAO,aAC9DqJ,CACN,CACG,CACD,eAAerJ,EAAQ,CACrB,MAAMY,EAAY,KAAK,YAAYZ,EAAO,GAAG,EACxCY,IAEL,KAAK,OAAOZ,CAAM,EAClB,KAAK,YAAYA,EAAO,GAAG,EAAI,KAC/B,KAAK,IAAI,cAAcY,EAAU,OAAO,EACzC,CACD,eAAeZ,EAAQ,CACrB,MAAMxI,EAAK,KAAK,IACVoJ,EAAY,KAAK,YAAYZ,CAAM,EACzCxI,EAAG,YAAYA,EAAG,WAAYoJ,EAAU,OAAO,EAC/C,KAAK,eAAe,KAAK,sBAAsB,EAAIZ,EACnD,MAAMnH,EAAqBmH,EAAO,YAAc,8BAC5C,KAAK,oBAAsBnH,IAC7B,KAAK,kBAAoBA,EACzBrB,EAAG,YAAYA,EAAG,+BAAgCqB,CAAkB,GAElE,KAAK,SAASmH,EAAO,cAAc,EACrC,KAAK,SAASA,EAAO,cAAc,EAAE,OAAOA,EAAQY,EAAWpJ,EAAI,KAAK,UAAU,QAAQ,YAAY,EAEtGA,EAAG,WAAWA,EAAG,WAAY,EAAGA,EAAG,KAAMwI,EAAO,WAAYA,EAAO,YAAa,EAAGxI,EAAG,KAAMA,EAAG,cAAe,IAAI,EAEhHwI,EAAO,qBAAuBA,EAAO,cAAgB,GACvD,KAAK,gBAAgBA,EAAQ,EAAK,CAErC,CACD,gBAAgBA,EAAQsK,EAAO,GAAM,CAC/BA,GACF,KAAK,WAAWtK,EAAQ,CAAC,EAC3B,MAAMY,EAAY,KAAK,YAAYZ,CAAM,EACzC,KAAK,IAAI,eAAeY,EAAU,MAAM,CACzC,CACD,gBAAgBZ,EAAQ,CACtBA,EAAO,IAAI,UAAW,KAAK,gBAAiB,IAAI,EAChDA,EAAO,IAAI,SAAU,KAAK,eAAgB,IAAI,EAC9CA,EAAO,IAAI,SAAU,KAAK,eAAgB,IAAI,EAC9CA,EAAO,IAAI,SAAU,KAAK,eAAgB,IAAI,EAC9CA,EAAO,IAAI,cAAe,KAAK,cAAe,IAAI,EAClDA,EAAO,IAAI,gBAAiB,KAAK,gBAAiB,IAAI,EACtD,KAAK,gBAAgB,OAAO,KAAK,gBAAgB,QAAQA,CAAM,EAAG,CAAC,EACnE,KAAK,eAAeA,CAAM,CAC3B,CACD,aAAa+I,EAAO,CAClB,MAAMvR,EAAK,KAAK,IACV+S,EAAY,KAAK,IAAI,cAAa,EACxC,YAAK,YAAYxB,EAAM,WAAW,EAAIwB,EACtCzB,EACEC,EACAvR,EACA,KAAK,eAAe,KAAK,sBAAsB,EAAE,cAAgB,EACjE,KAAK,UAAU,QAAQ,WAAW,qBAClC,oBACA+S,EACA,GACA,EACN,EACW,KAAK,YAAYxB,EAAM,WAAW,CAC1C,CACD,cAAcoB,EAAS,CACrB,OAAO,KAAK,YAAYA,EAAQ,WAAW,GAAK,KAAK,aAAaA,CAAO,CAC1E,CACD,YAAYnK,EAAQ,CAClB,OAAO,KAAK,YAAYA,EAAO,GAAG,GAAK,KAAK,YAAYA,CAAM,CAC/D,CACD,eAAeG,EAAS,CACtB,KAAM,CAAE,OAAAqK,EAAQ,MAAAC,EAAO,OAAAC,CAAQ,EAAG,KAAK,UAAUvK,CAAO,EAClDnH,EAASL,EAAW,IAAK,EAAC,aAAY,EAC5CK,EAAO,MAAQyR,EACfzR,EAAO,OAAS0R,EAChB,MAAMC,EAAM3R,EAAO,WAAW,IAAI,EAClC,GAAI2R,EAAK,CACP,MAAMC,EAAYD,EAAI,gBAAgBF,EAAOC,CAAM,EACnDE,EAAU,KAAK,IAAIJ,CAAM,EACzBG,EAAI,aAAaC,EAAW,EAAG,CAAC,CACjC,CACD,OAAO5R,CACR,CACD,UAAUmH,EAAS,CACjB,MAAM0K,EAAa1K,EAAQ,OAAO,WAC5B2K,EAAQ3K,EAAQ,MAChBsK,EAAQ,KAAK,IAAI,KAAK,MAAMK,EAAM,MAAQD,CAAU,EAAG,CAAC,EACxDH,EAAS,KAAK,IAAI,KAAK,MAAMI,EAAM,OAASD,CAAU,EAAG,CAAC,EAC1DL,EAAS,IAAI,WAAWP,GAAkBQ,EAAQC,CAAM,EACxD7T,EAAW,KAAK,UAChBmG,EAAenG,EAAS,aAAa,gBAAgBsJ,CAAO,EAC5D4K,EAAiBlU,EAAS,aAAa,mBAAmBmG,CAAY,EACtExF,EAAKX,EAAS,GACpB,OAAAW,EAAG,gBAAgBA,EAAG,YAAauT,EAAe,wBAAwB,EAC1EvT,EAAG,WACD,KAAK,MAAMsT,EAAM,EAAID,CAAU,EAC/B,KAAK,MAAMC,EAAM,EAAID,CAAU,EAC/BJ,EACAC,EACAlT,EAAG,KACHA,EAAG,cACHgT,CACN,EAIW,CAAE,OAAQ,IAAI,kBAAkBA,EAAO,MAAM,EAAG,MAAAC,EAAO,OAAAC,EAC/D,CACD,SAAU,CACR,KAAK,gBAAgB,MAAK,EAAG,QAAS1K,GAAW,KAAK,gBAAgBA,CAAM,CAAC,EAC7E,KAAK,gBAAkB,KACvB,KAAK,UAAY,IAClB,CACD,YAAa,CACX,KAAK,uBAAyB,GAC9B,KAAK,eAAe,KAAKjD,EAAQ,MAAM,MAAM,EAC7C,KAAK,eAAiC,OAAO,OAAO,IAAI,CACzD,CACH,CAEAmN,GAAgB,UAAY,CAC1B,KAAM,CACJjT,EAAc,WACf,EACD,KAAM,SACR,ECvQA,MAAM+T,EAAkB,CACtB,MAAO,CACL,MAAMjH,EAAW,IAAI9B,EAAa,CAChC,OAAQ,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,CAAC,CAAC,EAAG,KAAM,WAAa,EACpE,iBAAkB,CAAE,MAAO,IAAIgJ,EAAU,KAAM,aAAe,EAC9D,OAAQ,CAAE,MAAO,EAAG,KAAM,KAAO,CACvC,CAAK,EACKC,EAAc3F,IACd4F,EAAYC,EAA2B,CAC3C,KAAM,WACN,KAAM,CACJC,GACAC,GAA0BJ,CAAW,EACrCK,EACAC,CACD,CACP,CAAK,EACD,KAAK,OAAS,IAAI1O,EAAO,CACvB,UAAAqO,EACA,UAAW,CACT,cAAepH,EACf,cAAe0H,GAA6BP,CAAW,CACxD,CACP,CAAK,CACF,CACD,QAAQQ,EAAcC,EAAY,CAChC,MAAMC,EAAUD,EAAW,QACrB/U,EAASgV,EAAQ,cAAgB,KAAK,OACtC/U,EAAW6U,EAAa,SACxBG,EAAgBhV,EAAS,gBACzB,CACJ,QAAAiV,EACA,aAAAC,CACN,EAAQF,EAAc,qBAAqBD,CAAO,EAC9ChV,EAAO,OAAO,CAAC,EAAIC,EAAS,eAAe,UAC3CA,EAAS,MAAM,IAAI6U,EAAa,KAAK,EACrC7U,EAAS,OAAO,KAAKD,CAAM,EAC3BC,EAAS,SAAS,KAAKiV,EAAQ,SAAUlV,EAAO,SAAS,EACzD,MAAMoV,EAAUD,EAAa,aAC7B,QAAS/Q,EAAI,EAAGA,EAAI+Q,EAAa,gBAAiB/Q,IAAK,CACrD,MAAMjE,EAAQiV,EAAQhR,CAAC,EACvB,GAAIjE,EAAM,KAAM,CACd,QAASoE,EAAI,EAAGA,EAAIpE,EAAM,SAAS,MAAOoE,IACxCtE,EAAS,QAAQ,KAAKE,EAAM,SAAS,SAASoE,CAAC,EAAGA,CAAC,EAErDtE,EAAS,SAAS,KAAKE,EAAM,SAAUA,EAAM,KAAMA,EAAM,KAAK,CAC/D,CACF,CACF,CACD,SAAU,CACR,KAAK,OAAO,QAAQ,EAAI,EACxB,KAAK,OAAS,IACf,CACH,CAEAiU,GAAkB,UAAY,CAC5B,KAAM,CACJ/T,EAAc,iBACf,EACD,KAAM,UACR,EC9DA,MAAMgV,EAAc,CAClB,MAAO,CACL,MAAMd,EAAYC,EAA2B,CAC3C,KAAM,OACN,KAAM,CACJG,EACAW,GACAV,CACD,CACP,CAAK,EACD,KAAK,QAAU,IAAI1O,EAAO,CACxB,UAAAqO,EACA,UAAW,CACT,SAAUpO,EAAQ,MAAM,OACxB,gBAAiB,CACf,eAAgB,CAAE,KAAM,cAAe,MAAO,IAAIkO,CAAU,CAC7D,CACF,CACP,CAAK,CACF,CACD,QAAQkB,EAAUC,EAAM,CACtB,MAAMvV,EAAWsV,EAAS,SAC1B,IAAIvV,EAASwV,EAAK,QAClB,GAAKxV,GAOE,GAAI,CAACA,EAAO,UAAW,CAC5B8B,EAAK,+BAAgC0T,EAAK,MAAM,EAChD,MACD,MAVY,CACXxV,EAAS,KAAK,QACd,MAAMuJ,EAAUiM,EAAK,QACfpM,EAASG,EAAQ,OACvBvJ,EAAO,UAAU,SAAWoJ,EAC5BpJ,EAAO,UAAU,SAAWoJ,EAAO,MACnCpJ,EAAO,UAAU,gBAAgB,SAAS,eAAiBuJ,EAAQ,cAAc,QACvF,CAIIvJ,EAAO,OAAO,GAAG,EAAIC,EAAS,eAAe,UAC7CD,EAAO,OAAO,GAAG,EAAIuV,EAAS,uBAC9BtV,EAAS,QAAQ,KAAK,CACpB,SAAUuV,EAAK,UACf,OAAAxV,EACA,MAAOwV,EAAK,KAClB,CAAK,CACF,CACD,SAAU,CACR,KAAK,QAAQ,QAAQ,EAAI,EACzB,KAAK,QAAU,IAChB,CACH,CACAH,GAAc,UAAY,CACxB,KAAM,CACJhV,EAAc,iBACf,EACD,KAAM,MACR,ECzCA,MAAMoV,GAAsB,CAC1B,GAAGC,GACHnN,GACA/B,GACA1D,GACAnC,GACA2S,GACA3I,GACAlH,GACAgM,GACAf,GACA/H,GACAiK,GACA7J,GACAN,EACF,EACMkP,GAAoB,CAAC,GAAGC,EAAiB,EACzCC,GAAuB,CAAClW,EAAgB0V,GAAejB,EAAiB,EACxE0B,GAAU,CAAA,EACVC,GAAc,CAAA,EACdC,GAAqB,CAAA,EAC3BnT,EAAW,kBAAkBxC,EAAc,YAAayV,EAAO,EAC/DjT,EAAW,kBAAkBxC,EAAc,WAAY0V,EAAW,EAClElT,EAAW,kBAAkBxC,EAAc,kBAAmB2V,EAAkB,EAChFnT,EAAW,IAAI,GAAG4S,GAAqB,GAAGE,GAAmB,GAAGE,EAAoB,EACpF,MAAMI,WAAsBC,EAAiB,CAC3C,aAAc,CACZ,MAAMC,EAAe,CACnB,KAAM,QACN,KAAMC,GAAa,MACnB,QAAAN,GACA,YAAAC,GACA,mBAAAC,EACN,EACI,MAAMG,CAAY,CACnB,CACH", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48]}