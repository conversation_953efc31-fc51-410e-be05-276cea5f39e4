import{T as c}from"./index-De_pgNSz.js";class f extends c{constructor(e,n,s,t,u,r=!0,i=!1,a=3,h=0,l,_,x){super(null,u,!r,i,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,l),this.format=t,this._engine&&(!this._engine._caps.textureFloatLinearFiltering&&h===1&&(a=1),!this._engine._caps.textureHalfFloatLinearFiltering&&h===2&&(a=1),this._texture=this._engine.createRawTexture(e,n,s,t,r,i,a,null,h,l??0,_??!1),this.wrapU=c.CLAMP_ADDRESSMODE,this.wrapV=c.CLAMP_ADDRESSMODE,this._waitingForData=!!x&&!e)}update(e){this._getEngine().updateRawTexture(this._texture,e,this._texture.format,this._texture.invertY,null,this._texture.type,this._texture._useSRGBBuffer),this._waitingForData=!1}clone(){if(!this._texture)return super.clone();const e=new f(null,this.getSize().width,this.getSize().height,this.format,this.getScene(),this._texture.generateMipMaps,this._invertY,this.samplingMode,this._texture.type,this._texture._creationFlags,this._useSRGBBuffer);return e._texture=this._texture,this._texture.incrementReferences(),e}isReady(){return super.isReady()&&!this._waitingForData}static CreateLuminanceTexture(e,n,s,t,u=!0,r=!1,i=3){return new f(e,n,s,1,t,u,r,i)}static CreateLuminanceAlphaTexture(e,n,s,t,u=!0,r=!1,i=3){return new f(e,n,s,2,t,u,r,i)}static CreateAlphaTexture(e,n,s,t,u=!0,r=!1,i=3){return new f(e,n,s,0,t,u,r,i)}static CreateRGBTexture(e,n,s,t,u=!0,r=!1,i=3,a=0,h=0,l=!1){return new f(e,n,s,4,t,u,r,i,a,h,l)}static CreateRGBATexture(e,n,s,t,u=!0,r=!1,i=3,a=0,h=0,l=!1,_=!1){return new f(e,n,s,5,t,u,r,i,a,h,l,_)}static CreateRGBAStorageTexture(e,n,s,t,u=!0,r=!1,i=3,a=0,h=!1){return new f(e,n,s,5,t,u,r,i,a,1,h)}static CreateRTexture(e,n,s,t,u=!0,r=!1,i=c.TRILINEAR_SAMPLINGMODE,a=1){return new f(e,n,s,6,t,u,r,i,a)}static CreateRStorageTexture(e,n,s,t,u=!0,r=!1,i=c.TRILINEAR_SAMPLINGMODE,a=1){return new f(e,n,s,6,t,u,r,i,a,1)}}export{f as R};
//# sourceMappingURL=rawTexture-Ce6_SCE0.js.map
