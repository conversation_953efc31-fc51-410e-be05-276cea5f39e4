{"version": 3, "file": "time-Bgyi_H-V.js", "sources": ["../../../../node_modules/.pnpm/d3-array@3.2.4/node_modules/d3-array/src/max.js", "../../../../node_modules/.pnpm/d3-array@3.2.4/node_modules/d3-array/src/min.js", "../../../../node_modules/.pnpm/d3-color@3.1.0/node_modules/d3-color/src/math.js", "../../../../node_modules/.pnpm/d3-color@3.1.0/node_modules/d3-color/src/lab.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/hcl.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/nice.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/interval.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/millisecond.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/duration.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/second.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/minute.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/hour.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/day.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/week.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/month.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/year.js", "../../../../node_modules/.pnpm/d3-time@3.1.0/node_modules/d3-time/src/ticks.js", "../../../../node_modules/.pnpm/d3-time-format@4.1.0/node_modules/d3-time-format/src/locale.js", "../../../../node_modules/.pnpm/d3-time-format@4.1.0/node_modules/d3-time-format/src/defaultLocale.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/time.js"], "sourcesContent": ["export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "export const radians = Math.PI / 180;\nexport const degrees = 180 / Math.PI;\n", "import define, {extend} from \"./define.js\";\nimport {Color, rgbConvert, Rgb} from \"./color.js\";\nimport {degrees, radians} from \"./math.js\";\n\n// https://observablehq.com/@mbostock/lab-and-rgb\nconst K = 18,\n    Xn = 0.96422,\n    Yn = 1,\n    Zn = 0.82521,\n    t0 = 4 / 29,\n    t1 = 6 / 29,\n    t2 = 3 * t1 * t1,\n    t3 = t1 * t1 * t1;\n\nfunction labConvert(o) {\n  if (o instanceof Lab) return new Lab(o.l, o.a, o.b, o.opacity);\n  if (o instanceof Hcl) return hcl2lab(o);\n  if (!(o instanceof Rgb)) o = rgbConvert(o);\n  var r = rgb2lrgb(o.r),\n      g = rgb2lrgb(o.g),\n      b = rgb2lrgb(o.b),\n      y = xyz2lab((0.2225045 * r + 0.7168786 * g + 0.0606169 * b) / Yn), x, z;\n  if (r === g && g === b) x = z = y; else {\n    x = xyz2lab((0.4360747 * r + 0.3850649 * g + 0.1430804 * b) / Xn);\n    z = xyz2lab((0.0139322 * r + 0.0971045 * g + 0.7141733 * b) / Zn);\n  }\n  return new Lab(116 * y - 16, 500 * (x - y), 200 * (y - z), o.opacity);\n}\n\nexport function gray(l, opacity) {\n  return new Lab(l, 0, 0, opacity == null ? 1 : opacity);\n}\n\nexport default function lab(l, a, b, opacity) {\n  return arguments.length === 1 ? labConvert(l) : new Lab(l, a, b, opacity == null ? 1 : opacity);\n}\n\nexport function Lab(l, a, b, opacity) {\n  this.l = +l;\n  this.a = +a;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Lab, lab, extend(Color, {\n  brighter(k) {\n    return new Lab(this.l + K * (k == null ? 1 : k), this.a, this.b, this.opacity);\n  },\n  darker(k) {\n    return new Lab(this.l - K * (k == null ? 1 : k), this.a, this.b, this.opacity);\n  },\n  rgb() {\n    var y = (this.l + 16) / 116,\n        x = isNaN(this.a) ? y : y + this.a / 500,\n        z = isNaN(this.b) ? y : y - this.b / 200;\n    x = Xn * lab2xyz(x);\n    y = Yn * lab2xyz(y);\n    z = Zn * lab2xyz(z);\n    return new Rgb(\n      lrgb2rgb( 3.1338561 * x - 1.6168667 * y - 0.4906146 * z),\n      lrgb2rgb(-0.9787684 * x + 1.9161415 * y + 0.0334540 * z),\n      lrgb2rgb( 0.0719453 * x - 0.2289914 * y + 1.4052427 * z),\n      this.opacity\n    );\n  }\n}));\n\nfunction xyz2lab(t) {\n  return t > t3 ? Math.pow(t, 1 / 3) : t / t2 + t0;\n}\n\nfunction lab2xyz(t) {\n  return t > t1 ? t * t * t : t2 * (t - t0);\n}\n\nfunction lrgb2rgb(x) {\n  return 255 * (x <= 0.0031308 ? 12.92 * x : 1.055 * Math.pow(x, 1 / 2.4) - 0.055);\n}\n\nfunction rgb2lrgb(x) {\n  return (x /= 255) <= 0.04045 ? x / 12.92 : Math.pow((x + 0.055) / 1.055, 2.4);\n}\n\nfunction hclConvert(o) {\n  if (o instanceof Hcl) return new Hcl(o.h, o.c, o.l, o.opacity);\n  if (!(o instanceof Lab)) o = labConvert(o);\n  if (o.a === 0 && o.b === 0) return new Hcl(NaN, 0 < o.l && o.l < 100 ? 0 : NaN, o.l, o.opacity);\n  var h = Math.atan2(o.b, o.a) * degrees;\n  return new Hcl(h < 0 ? h + 360 : h, Math.sqrt(o.a * o.a + o.b * o.b), o.l, o.opacity);\n}\n\nexport function lch(l, c, h, opacity) {\n  return arguments.length === 1 ? hclConvert(l) : new Hcl(h, c, l, opacity == null ? 1 : opacity);\n}\n\nexport function hcl(h, c, l, opacity) {\n  return arguments.length === 1 ? hclConvert(h) : new Hcl(h, c, l, opacity == null ? 1 : opacity);\n}\n\nexport function Hcl(h, c, l, opacity) {\n  this.h = +h;\n  this.c = +c;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\nfunction hcl2lab(o) {\n  if (isNaN(o.h)) return new Lab(o.l, 0, 0, o.opacity);\n  var h = o.h * radians;\n  return new Lab(o.l, Math.cos(h) * o.c, Math.sin(h) * o.c, o.opacity);\n}\n\ndefine(Hcl, hcl, extend(Color, {\n  brighter(k) {\n    return new Hcl(this.h, this.c, this.l + K * (k == null ? 1 : k), this.opacity);\n  },\n  darker(k) {\n    return new Hcl(this.h, this.c, this.l - K * (k == null ? 1 : k), this.opacity);\n  },\n  rgb() {\n    return hcl2lab(this).rgb();\n  }\n}));\n", "import {hcl as colorHcl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hcl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHcl(start)).h, (end = colorHcl(end)).h),\n        c = color(start.c, end.c),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.c = c(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hcl(hue);\nexport var hclLong = hcl(color);\n", "export default function nice(domain, interval) {\n  domain = domain.slice();\n\n  var i0 = 0,\n      i1 = domain.length - 1,\n      x0 = domain[i0],\n      x1 = domain[i1],\n      t;\n\n  if (x1 < x0) {\n    t = i0, i0 = i1, i1 = t;\n    t = x0, x0 = x1, x1 = t;\n  }\n\n  domain[i0] = interval.floor(x0);\n  domain[i1] = interval.ceil(x1);\n  return domain;\n}\n", "const t0 = new Date, t1 = new Date;\n\nexport function timeInterval(floori, offseti, count, field) {\n\n  function interval(date) {\n    return floori(date = arguments.length === 0 ? new Date : new Date(+date)), date;\n  }\n\n  interval.floor = (date) => {\n    return floori(date = new Date(+date)), date;\n  };\n\n  interval.ceil = (date) => {\n    return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n  };\n\n  interval.round = (date) => {\n    const d0 = interval(date), d1 = interval.ceil(date);\n    return date - d0 < d1 - date ? d0 : d1;\n  };\n\n  interval.offset = (date, step) => {\n    return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n  };\n\n  interval.range = (start, stop, step) => {\n    const range = [];\n    start = interval.ceil(start);\n    step = step == null ? 1 : Math.floor(step);\n    if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n    let previous;\n    do range.push(previous = new Date(+start)), offseti(start, step), floori(start);\n    while (previous < start && start < stop);\n    return range;\n  };\n\n  interval.filter = (test) => {\n    return timeInterval((date) => {\n      if (date >= date) while (floori(date), !test(date)) date.setTime(date - 1);\n    }, (date, step) => {\n      if (date >= date) {\n        if (step < 0) while (++step <= 0) {\n          while (offseti(date, -1), !test(date)) {} // eslint-disable-line no-empty\n        } else while (--step >= 0) {\n          while (offseti(date, +1), !test(date)) {} // eslint-disable-line no-empty\n        }\n      }\n    });\n  };\n\n  if (count) {\n    interval.count = (start, end) => {\n      t0.setTime(+start), t1.setTime(+end);\n      floori(t0), floori(t1);\n      return Math.floor(count(t0, t1));\n    };\n\n    interval.every = (step) => {\n      step = Math.floor(step);\n      return !isFinite(step) || !(step > 0) ? null\n          : !(step > 1) ? interval\n          : interval.filter(field\n              ? (d) => field(d) % step === 0\n              : (d) => interval.count(0, d) % step === 0);\n    };\n  }\n\n  return interval;\n}\n", "import {timeInterval} from \"./interval.js\";\n\nexport const millisecond = timeInterval(() => {\n  // noop\n}, (date, step) => {\n  date.setTime(+date + step);\n}, (start, end) => {\n  return end - start;\n});\n\n// An optimized implementation for this simple case.\nmillisecond.every = (k) => {\n  k = Math.floor(k);\n  if (!isFinite(k) || !(k > 0)) return null;\n  if (!(k > 1)) return millisecond;\n  return timeInterval((date) => {\n    date.setTime(Math.floor(date / k) * k);\n  }, (date, step) => {\n    date.setTime(+date + step * k);\n  }, (start, end) => {\n    return (end - start) / k;\n  });\n};\n\nexport const milliseconds = millisecond.range;\n", "export const durationSecond = 1000;\nexport const durationMinute = durationSecond * 60;\nexport const durationHour = durationMinute * 60;\nexport const durationDay = durationHour * 24;\nexport const durationWeek = durationDay * 7;\nexport const durationMonth = durationDay * 30;\nexport const durationYear = durationDay * 365;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationSecond} from \"./duration.js\";\n\nexport const second = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds());\n}, (date, step) => {\n  date.setTime(+date + step * durationSecond);\n}, (start, end) => {\n  return (end - start) / durationSecond;\n}, (date) => {\n  return date.getUTCSeconds();\n});\n\nexport const seconds = second.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeMinute = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getMinutes();\n});\n\nexport const timeMinutes = timeMinute.range;\n\nexport const utcMinute = timeInterval((date) => {\n  date.setUTCSeconds(0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getUTCMinutes();\n});\n\nexport const utcMinutes = utcMinute.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationHour, durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeHour = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond - date.getMinutes() * durationMinute);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getHours();\n});\n\nexport const timeHours = timeHour.range;\n\nexport const utcHour = timeInterval((date) => {\n  date.setUTCMinutes(0, 0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getUTCHours();\n});\n\nexport const utcHours = utcHour.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationDay, durationMinute} from \"./duration.js\";\n\nexport const timeDay = timeInterval(\n  date => date.setHours(0, 0, 0, 0),\n  (date, step) => date.setDate(date.getDate() + step),\n  (start, end) => (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationDay,\n  date => date.getDate() - 1\n);\n\nexport const timeDays = timeDay.range;\n\nexport const utcDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return date.getUTCDate() - 1;\n});\n\nexport const utcDays = utcDay.range;\n\nexport const unixDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return Math.floor(date / durationDay);\n});\n\nexport const unixDays = unixDay.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationWeek} from \"./duration.js\";\n\nfunction timeWeekday(i) {\n  return timeInterval((date) => {\n    date.setDate(date.getDate() - (date.getDay() + 7 - i) % 7);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setDate(date.getDate() + step * 7);\n  }, (start, end) => {\n    return (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationWeek;\n  });\n}\n\nexport const timeSunday = timeWeekday(0);\nexport const timeMonday = timeWeekday(1);\nexport const timeTuesday = timeWeekday(2);\nexport const timeWednesday = timeWeekday(3);\nexport const timeThursday = timeWeekday(4);\nexport const timeFriday = timeWeekday(5);\nexport const timeSaturday = timeWeekday(6);\n\nexport const timeSundays = timeSunday.range;\nexport const timeMondays = timeMonday.range;\nexport const timeTuesdays = timeTuesday.range;\nexport const timeWednesdays = timeWednesday.range;\nexport const timeThursdays = timeThursday.range;\nexport const timeFridays = timeFriday.range;\nexport const timeSaturdays = timeSaturday.range;\n\nfunction utcWeekday(i) {\n  return timeInterval((date) => {\n    date.setUTCDate(date.getUTCDate() - (date.getUTCDay() + 7 - i) % 7);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCDate(date.getUTCDate() + step * 7);\n  }, (start, end) => {\n    return (end - start) / durationWeek;\n  });\n}\n\nexport const utcSunday = utcWeekday(0);\nexport const utcMonday = utcWeekday(1);\nexport const utcTuesday = utcWeekday(2);\nexport const utcWednesday = utcWeekday(3);\nexport const utcThursday = utcWeekday(4);\nexport const utcFriday = utcWeekday(5);\nexport const utcSaturday = utcWeekday(6);\n\nexport const utcSundays = utcSunday.range;\nexport const utcMondays = utcMonday.range;\nexport const utcTuesdays = utcTuesday.range;\nexport const utcWednesdays = utcWednesday.range;\nexport const utcThursdays = utcThursday.range;\nexport const utcFridays = utcFriday.range;\nexport const utcSaturdays = utcSaturday.range;\n", "import {timeInterval} from \"./interval.js\";\n\nexport const timeMonth = timeInterval((date) => {\n  date.setDate(1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setMonth(date.getMonth() + step);\n}, (start, end) => {\n  return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, (date) => {\n  return date.getMonth();\n});\n\nexport const timeMonths = timeMonth.range;\n\nexport const utcMonth = timeInterval((date) => {\n  date.setUTCDate(1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCMonth(date.getUTCMonth() + step);\n}, (start, end) => {\n  return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, (date) => {\n  return date.getUTCMonth();\n});\n\nexport const utcMonths = utcMonth.range;\n", "import {timeInterval} from \"./interval.js\";\n\nexport const timeYear = timeInterval((date) => {\n  date.setMonth(0, 1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setFullYear(date.getFullYear() + step);\n}, (start, end) => {\n  return end.getFullYear() - start.getFullYear();\n}, (date) => {\n  return date.getFullYear();\n});\n\n// An optimized implementation for this simple case.\ntimeYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setFullYear(date.getFullYear() + step * k);\n  });\n};\n\nexport const timeYears = timeYear.range;\n\nexport const utcYear = timeInterval((date) => {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, (start, end) => {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, (date) => {\n  return date.getUTCFullYear();\n});\n\n// An optimized implementation for this simple case.\nutcYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\n\nexport const utcYears = utcYear.range;\n", "import {bisector, tickStep} from \"d3-array\";\nimport {durationDay, durationHour, durationMinute, durationMonth, durationSecond, durationWeek, durationYear} from \"./duration.js\";\nimport {millisecond} from \"./millisecond.js\";\nimport {second} from \"./second.js\";\nimport {timeMinute, utcMinute} from \"./minute.js\";\nimport {timeHour, utcHour} from \"./hour.js\";\nimport {timeDay, unixDay} from \"./day.js\";\nimport {timeSunday, utcSunday} from \"./week.js\";\nimport {timeMonth, utcMonth} from \"./month.js\";\nimport {timeYear, utcYear} from \"./year.js\";\n\nfunction ticker(year, month, week, day, hour, minute) {\n\n  const tickIntervals = [\n    [second,  1,      durationSecond],\n    [second,  5,  5 * durationSecond],\n    [second, 15, 15 * durationSecond],\n    [second, 30, 30 * durationSecond],\n    [minute,  1,      durationMinute],\n    [minute,  5,  5 * durationMinute],\n    [minute, 15, 15 * durationMinute],\n    [minute, 30, 30 * durationMinute],\n    [  hour,  1,      durationHour  ],\n    [  hour,  3,  3 * durationHour  ],\n    [  hour,  6,  6 * durationHour  ],\n    [  hour, 12, 12 * durationHour  ],\n    [   day,  1,      durationDay   ],\n    [   day,  2,  2 * durationDay   ],\n    [  week,  1,      durationWeek  ],\n    [ month,  1,      durationMonth ],\n    [ month,  3,  3 * durationMonth ],\n    [  year,  1,      durationYear  ]\n  ];\n\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n    return reverse ? ticks.reverse() : ticks;\n  }\n\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = bisector(([,, step]) => step).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every(tickStep(start / durationYear, stop / durationYear, count));\n    if (i === 0) return millisecond.every(Math.max(tickStep(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n\n  return [ticks, tickInterval];\n}\n\nconst [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcSunday, unixDay, utcHour, utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute);\n\nexport {utcTicks, utcTickInterval, timeTicks, timeTickInterval};\n", "import {\n  timeDay,\n  timeSunday,\n  timeMonday,\n  timeThursday,\n  timeYear,\n  utcDay,\n  utcSunday,\n  utcMonday,\n  utcThursday,\n  utcYear\n} from \"d3-time\";\n\nfunction localDate(d) {\n  if (0 <= d.y && d.y < 100) {\n    var date = new Date(-1, d.m, d.d, d.H, d.M, d.S, d.L);\n    date.setFullYear(d.y);\n    return date;\n  }\n  return new Date(d.y, d.m, d.d, d.H, d.M, d.S, d.L);\n}\n\nfunction utcDate(d) {\n  if (0 <= d.y && d.y < 100) {\n    var date = new Date(Date.UTC(-1, d.m, d.d, d.H, d.M, d.S, d.L));\n    date.setUTCFullYear(d.y);\n    return date;\n  }\n  return new Date(Date.UTC(d.y, d.m, d.d, d.H, d.M, d.S, d.L));\n}\n\nfunction newDate(y, m, d) {\n  return {y: y, m: m, d: d, H: 0, M: 0, S: 0, L: 0};\n}\n\nexport default function formatLocale(locale) {\n  var locale_dateTime = locale.dateTime,\n      locale_date = locale.date,\n      locale_time = locale.time,\n      locale_periods = locale.periods,\n      locale_weekdays = locale.days,\n      locale_shortWeekdays = locale.shortDays,\n      locale_months = locale.months,\n      locale_shortMonths = locale.shortMonths;\n\n  var periodRe = formatRe(locale_periods),\n      periodLookup = formatLookup(locale_periods),\n      weekdayRe = formatRe(locale_weekdays),\n      weekdayLookup = formatLookup(locale_weekdays),\n      shortWeekdayRe = formatRe(locale_shortWeekdays),\n      shortWeekdayLookup = formatLookup(locale_shortWeekdays),\n      monthRe = formatRe(locale_months),\n      monthLookup = formatLookup(locale_months),\n      shortMonthRe = formatRe(locale_shortMonths),\n      shortMonthLookup = formatLookup(locale_shortMonths);\n\n  var formats = {\n    \"a\": formatShortWeekday,\n    \"A\": formatWeekday,\n    \"b\": formatShortMonth,\n    \"B\": formatMonth,\n    \"c\": null,\n    \"d\": formatDayOfMonth,\n    \"e\": formatDayOfMonth,\n    \"f\": formatMicroseconds,\n    \"g\": formatYearISO,\n    \"G\": formatFullYearISO,\n    \"H\": formatHour24,\n    \"I\": formatHour12,\n    \"j\": formatDayOfYear,\n    \"L\": formatMilliseconds,\n    \"m\": formatMonthNumber,\n    \"M\": formatMinutes,\n    \"p\": formatPeriod,\n    \"q\": formatQuarter,\n    \"Q\": formatUnixTimestamp,\n    \"s\": formatUnixTimestampSeconds,\n    \"S\": formatSeconds,\n    \"u\": formatWeekdayNumberMonday,\n    \"U\": formatWeekNumberSunday,\n    \"V\": formatWeekNumberISO,\n    \"w\": formatWeekdayNumberSunday,\n    \"W\": formatWeekNumberMonday,\n    \"x\": null,\n    \"X\": null,\n    \"y\": formatYear,\n    \"Y\": formatFullYear,\n    \"Z\": formatZone,\n    \"%\": formatLiteralPercent\n  };\n\n  var utcFormats = {\n    \"a\": formatUTCShortWeekday,\n    \"A\": formatUTCWeekday,\n    \"b\": formatUTCShortMonth,\n    \"B\": formatUTCMonth,\n    \"c\": null,\n    \"d\": formatUTCDayOfMonth,\n    \"e\": formatUTCDayOfMonth,\n    \"f\": formatUTCMicroseconds,\n    \"g\": formatUTCYearISO,\n    \"G\": formatUTCFullYearISO,\n    \"H\": formatUTCHour24,\n    \"I\": formatUTCHour12,\n    \"j\": formatUTCDayOfYear,\n    \"L\": formatUTCMilliseconds,\n    \"m\": formatUTCMonthNumber,\n    \"M\": formatUTCMinutes,\n    \"p\": formatUTCPeriod,\n    \"q\": formatUTCQuarter,\n    \"Q\": formatUnixTimestamp,\n    \"s\": formatUnixTimestampSeconds,\n    \"S\": formatUTCSeconds,\n    \"u\": formatUTCWeekdayNumberMonday,\n    \"U\": formatUTCWeekNumberSunday,\n    \"V\": formatUTCWeekNumberISO,\n    \"w\": formatUTCWeekdayNumberSunday,\n    \"W\": formatUTCWeekNumberMonday,\n    \"x\": null,\n    \"X\": null,\n    \"y\": formatUTCYear,\n    \"Y\": formatUTCFullYear,\n    \"Z\": formatUTCZone,\n    \"%\": formatLiteralPercent\n  };\n\n  var parses = {\n    \"a\": parseShortWeekday,\n    \"A\": parseWeekday,\n    \"b\": parseShortMonth,\n    \"B\": parseMonth,\n    \"c\": parseLocaleDateTime,\n    \"d\": parseDayOfMonth,\n    \"e\": parseDayOfMonth,\n    \"f\": parseMicroseconds,\n    \"g\": parseYear,\n    \"G\": parseFullYear,\n    \"H\": parseHour24,\n    \"I\": parseHour24,\n    \"j\": parseDayOfYear,\n    \"L\": parseMilliseconds,\n    \"m\": parseMonthNumber,\n    \"M\": parseMinutes,\n    \"p\": parsePeriod,\n    \"q\": parseQuarter,\n    \"Q\": parseUnixTimestamp,\n    \"s\": parseUnixTimestampSeconds,\n    \"S\": parseSeconds,\n    \"u\": parseWeekdayNumberMonday,\n    \"U\": parseWeekNumberSunday,\n    \"V\": parseWeekNumberISO,\n    \"w\": parseWeekdayNumberSunday,\n    \"W\": parseWeekNumberMonday,\n    \"x\": parseLocaleDate,\n    \"X\": parseLocaleTime,\n    \"y\": parseYear,\n    \"Y\": parseFullYear,\n    \"Z\": parseZone,\n    \"%\": parseLiteralPercent\n  };\n\n  // These recursive directive definitions must be deferred.\n  formats.x = newFormat(locale_date, formats);\n  formats.X = newFormat(locale_time, formats);\n  formats.c = newFormat(locale_dateTime, formats);\n  utcFormats.x = newFormat(locale_date, utcFormats);\n  utcFormats.X = newFormat(locale_time, utcFormats);\n  utcFormats.c = newFormat(locale_dateTime, utcFormats);\n\n  function newFormat(specifier, formats) {\n    return function(date) {\n      var string = [],\n          i = -1,\n          j = 0,\n          n = specifier.length,\n          c,\n          pad,\n          format;\n\n      if (!(date instanceof Date)) date = new Date(+date);\n\n      while (++i < n) {\n        if (specifier.charCodeAt(i) === 37) {\n          string.push(specifier.slice(j, i));\n          if ((pad = pads[c = specifier.charAt(++i)]) != null) c = specifier.charAt(++i);\n          else pad = c === \"e\" ? \" \" : \"0\";\n          if (format = formats[c]) c = format(date, pad);\n          string.push(c);\n          j = i + 1;\n        }\n      }\n\n      string.push(specifier.slice(j, i));\n      return string.join(\"\");\n    };\n  }\n\n  function newParse(specifier, Z) {\n    return function(string) {\n      var d = newDate(1900, undefined, 1),\n          i = parseSpecifier(d, specifier, string += \"\", 0),\n          week, day;\n      if (i != string.length) return null;\n\n      // If a UNIX timestamp is specified, return it.\n      if (\"Q\" in d) return new Date(d.Q);\n      if (\"s\" in d) return new Date(d.s * 1000 + (\"L\" in d ? d.L : 0));\n\n      // If this is utcParse, never use the local timezone.\n      if (Z && !(\"Z\" in d)) d.Z = 0;\n\n      // The am-pm flag is 0 for AM, and 1 for PM.\n      if (\"p\" in d) d.H = d.H % 12 + d.p * 12;\n\n      // If the month was not specified, inherit from the quarter.\n      if (d.m === undefined) d.m = \"q\" in d ? d.q : 0;\n\n      // Convert day-of-week and week-of-year to day-of-year.\n      if (\"V\" in d) {\n        if (d.V < 1 || d.V > 53) return null;\n        if (!(\"w\" in d)) d.w = 1;\n        if (\"Z\" in d) {\n          week = utcDate(newDate(d.y, 0, 1)), day = week.getUTCDay();\n          week = day > 4 || day === 0 ? utcMonday.ceil(week) : utcMonday(week);\n          week = utcDay.offset(week, (d.V - 1) * 7);\n          d.y = week.getUTCFullYear();\n          d.m = week.getUTCMonth();\n          d.d = week.getUTCDate() + (d.w + 6) % 7;\n        } else {\n          week = localDate(newDate(d.y, 0, 1)), day = week.getDay();\n          week = day > 4 || day === 0 ? timeMonday.ceil(week) : timeMonday(week);\n          week = timeDay.offset(week, (d.V - 1) * 7);\n          d.y = week.getFullYear();\n          d.m = week.getMonth();\n          d.d = week.getDate() + (d.w + 6) % 7;\n        }\n      } else if (\"W\" in d || \"U\" in d) {\n        if (!(\"w\" in d)) d.w = \"u\" in d ? d.u % 7 : \"W\" in d ? 1 : 0;\n        day = \"Z\" in d ? utcDate(newDate(d.y, 0, 1)).getUTCDay() : localDate(newDate(d.y, 0, 1)).getDay();\n        d.m = 0;\n        d.d = \"W\" in d ? (d.w + 6) % 7 + d.W * 7 - (day + 5) % 7 : d.w + d.U * 7 - (day + 6) % 7;\n      }\n\n      // If a time zone is specified, all fields are interpreted as UTC and then\n      // offset according to the specified time zone.\n      if (\"Z\" in d) {\n        d.H += d.Z / 100 | 0;\n        d.M += d.Z % 100;\n        return utcDate(d);\n      }\n\n      // Otherwise, all fields are in local time.\n      return localDate(d);\n    };\n  }\n\n  function parseSpecifier(d, specifier, string, j) {\n    var i = 0,\n        n = specifier.length,\n        m = string.length,\n        c,\n        parse;\n\n    while (i < n) {\n      if (j >= m) return -1;\n      c = specifier.charCodeAt(i++);\n      if (c === 37) {\n        c = specifier.charAt(i++);\n        parse = parses[c in pads ? specifier.charAt(i++) : c];\n        if (!parse || ((j = parse(d, string, j)) < 0)) return -1;\n      } else if (c != string.charCodeAt(j++)) {\n        return -1;\n      }\n    }\n\n    return j;\n  }\n\n  function parsePeriod(d, string, i) {\n    var n = periodRe.exec(string.slice(i));\n    return n ? (d.p = periodLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseShortWeekday(d, string, i) {\n    var n = shortWeekdayRe.exec(string.slice(i));\n    return n ? (d.w = shortWeekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseWeekday(d, string, i) {\n    var n = weekdayRe.exec(string.slice(i));\n    return n ? (d.w = weekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseShortMonth(d, string, i) {\n    var n = shortMonthRe.exec(string.slice(i));\n    return n ? (d.m = shortMonthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseMonth(d, string, i) {\n    var n = monthRe.exec(string.slice(i));\n    return n ? (d.m = monthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseLocaleDateTime(d, string, i) {\n    return parseSpecifier(d, locale_dateTime, string, i);\n  }\n\n  function parseLocaleDate(d, string, i) {\n    return parseSpecifier(d, locale_date, string, i);\n  }\n\n  function parseLocaleTime(d, string, i) {\n    return parseSpecifier(d, locale_time, string, i);\n  }\n\n  function formatShortWeekday(d) {\n    return locale_shortWeekdays[d.getDay()];\n  }\n\n  function formatWeekday(d) {\n    return locale_weekdays[d.getDay()];\n  }\n\n  function formatShortMonth(d) {\n    return locale_shortMonths[d.getMonth()];\n  }\n\n  function formatMonth(d) {\n    return locale_months[d.getMonth()];\n  }\n\n  function formatPeriod(d) {\n    return locale_periods[+(d.getHours() >= 12)];\n  }\n\n  function formatQuarter(d) {\n    return 1 + ~~(d.getMonth() / 3);\n  }\n\n  function formatUTCShortWeekday(d) {\n    return locale_shortWeekdays[d.getUTCDay()];\n  }\n\n  function formatUTCWeekday(d) {\n    return locale_weekdays[d.getUTCDay()];\n  }\n\n  function formatUTCShortMonth(d) {\n    return locale_shortMonths[d.getUTCMonth()];\n  }\n\n  function formatUTCMonth(d) {\n    return locale_months[d.getUTCMonth()];\n  }\n\n  function formatUTCPeriod(d) {\n    return locale_periods[+(d.getUTCHours() >= 12)];\n  }\n\n  function formatUTCQuarter(d) {\n    return 1 + ~~(d.getUTCMonth() / 3);\n  }\n\n  return {\n    format: function(specifier) {\n      var f = newFormat(specifier += \"\", formats);\n      f.toString = function() { return specifier; };\n      return f;\n    },\n    parse: function(specifier) {\n      var p = newParse(specifier += \"\", false);\n      p.toString = function() { return specifier; };\n      return p;\n    },\n    utcFormat: function(specifier) {\n      var f = newFormat(specifier += \"\", utcFormats);\n      f.toString = function() { return specifier; };\n      return f;\n    },\n    utcParse: function(specifier) {\n      var p = newParse(specifier += \"\", true);\n      p.toString = function() { return specifier; };\n      return p;\n    }\n  };\n}\n\nvar pads = {\"-\": \"\", \"_\": \" \", \"0\": \"0\"},\n    numberRe = /^\\s*\\d+/, // note: ignores next directive\n    percentRe = /^%/,\n    requoteRe = /[\\\\^$*+?|[\\]().{}]/g;\n\nfunction pad(value, fill, width) {\n  var sign = value < 0 ? \"-\" : \"\",\n      string = (sign ? -value : value) + \"\",\n      length = string.length;\n  return sign + (length < width ? new Array(width - length + 1).join(fill) + string : string);\n}\n\nfunction requote(s) {\n  return s.replace(requoteRe, \"\\\\$&\");\n}\n\nfunction formatRe(names) {\n  return new RegExp(\"^(?:\" + names.map(requote).join(\"|\") + \")\", \"i\");\n}\n\nfunction formatLookup(names) {\n  return new Map(names.map((name, i) => [name.toLowerCase(), i]));\n}\n\nfunction parseWeekdayNumberSunday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.w = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekdayNumberMonday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.u = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberSunday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.U = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberISO(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.V = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberMonday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.W = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseFullYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 4));\n  return n ? (d.y = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.y = +n[0] + (+n[0] > 68 ? 1900 : 2000), i + n[0].length) : -1;\n}\n\nfunction parseZone(d, string, i) {\n  var n = /^(Z)|([+-]\\d\\d)(?::?(\\d\\d))?/.exec(string.slice(i, i + 6));\n  return n ? (d.Z = n[1] ? 0 : -(n[2] + (n[3] || \"00\")), i + n[0].length) : -1;\n}\n\nfunction parseQuarter(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.q = n[0] * 3 - 3, i + n[0].length) : -1;\n}\n\nfunction parseMonthNumber(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.m = n[0] - 1, i + n[0].length) : -1;\n}\n\nfunction parseDayOfMonth(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.d = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseDayOfYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 3));\n  return n ? (d.m = 0, d.d = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseHour24(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.H = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMinutes(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.M = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseSeconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.S = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMilliseconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 3));\n  return n ? (d.L = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMicroseconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 6));\n  return n ? (d.L = Math.floor(n[0] / 1000), i + n[0].length) : -1;\n}\n\nfunction parseLiteralPercent(d, string, i) {\n  var n = percentRe.exec(string.slice(i, i + 1));\n  return n ? i + n[0].length : -1;\n}\n\nfunction parseUnixTimestamp(d, string, i) {\n  var n = numberRe.exec(string.slice(i));\n  return n ? (d.Q = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseUnixTimestampSeconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i));\n  return n ? (d.s = +n[0], i + n[0].length) : -1;\n}\n\nfunction formatDayOfMonth(d, p) {\n  return pad(d.getDate(), p, 2);\n}\n\nfunction formatHour24(d, p) {\n  return pad(d.getHours(), p, 2);\n}\n\nfunction formatHour12(d, p) {\n  return pad(d.getHours() % 12 || 12, p, 2);\n}\n\nfunction formatDayOfYear(d, p) {\n  return pad(1 + timeDay.count(timeYear(d), d), p, 3);\n}\n\nfunction formatMilliseconds(d, p) {\n  return pad(d.getMilliseconds(), p, 3);\n}\n\nfunction formatMicroseconds(d, p) {\n  return formatMilliseconds(d, p) + \"000\";\n}\n\nfunction formatMonthNumber(d, p) {\n  return pad(d.getMonth() + 1, p, 2);\n}\n\nfunction formatMinutes(d, p) {\n  return pad(d.getMinutes(), p, 2);\n}\n\nfunction formatSeconds(d, p) {\n  return pad(d.getSeconds(), p, 2);\n}\n\nfunction formatWeekdayNumberMonday(d) {\n  var day = d.getDay();\n  return day === 0 ? 7 : day;\n}\n\nfunction formatWeekNumberSunday(d, p) {\n  return pad(timeSunday.count(timeYear(d) - 1, d), p, 2);\n}\n\nfunction dISO(d) {\n  var day = d.getDay();\n  return (day >= 4 || day === 0) ? timeThursday(d) : timeThursday.ceil(d);\n}\n\nfunction formatWeekNumberISO(d, p) {\n  d = dISO(d);\n  return pad(timeThursday.count(timeYear(d), d) + (timeYear(d).getDay() === 4), p, 2);\n}\n\nfunction formatWeekdayNumberSunday(d) {\n  return d.getDay();\n}\n\nfunction formatWeekNumberMonday(d, p) {\n  return pad(timeMonday.count(timeYear(d) - 1, d), p, 2);\n}\n\nfunction formatYear(d, p) {\n  return pad(d.getFullYear() % 100, p, 2);\n}\n\nfunction formatYearISO(d, p) {\n  d = dISO(d);\n  return pad(d.getFullYear() % 100, p, 2);\n}\n\nfunction formatFullYear(d, p) {\n  return pad(d.getFullYear() % 10000, p, 4);\n}\n\nfunction formatFullYearISO(d, p) {\n  var day = d.getDay();\n  d = (day >= 4 || day === 0) ? timeThursday(d) : timeThursday.ceil(d);\n  return pad(d.getFullYear() % 10000, p, 4);\n}\n\nfunction formatZone(d) {\n  var z = d.getTimezoneOffset();\n  return (z > 0 ? \"-\" : (z *= -1, \"+\"))\n      + pad(z / 60 | 0, \"0\", 2)\n      + pad(z % 60, \"0\", 2);\n}\n\nfunction formatUTCDayOfMonth(d, p) {\n  return pad(d.getUTCDate(), p, 2);\n}\n\nfunction formatUTCHour24(d, p) {\n  return pad(d.getUTCHours(), p, 2);\n}\n\nfunction formatUTCHour12(d, p) {\n  return pad(d.getUTCHours() % 12 || 12, p, 2);\n}\n\nfunction formatUTCDayOfYear(d, p) {\n  return pad(1 + utcDay.count(utcYear(d), d), p, 3);\n}\n\nfunction formatUTCMilliseconds(d, p) {\n  return pad(d.getUTCMilliseconds(), p, 3);\n}\n\nfunction formatUTCMicroseconds(d, p) {\n  return formatUTCMilliseconds(d, p) + \"000\";\n}\n\nfunction formatUTCMonthNumber(d, p) {\n  return pad(d.getUTCMonth() + 1, p, 2);\n}\n\nfunction formatUTCMinutes(d, p) {\n  return pad(d.getUTCMinutes(), p, 2);\n}\n\nfunction formatUTCSeconds(d, p) {\n  return pad(d.getUTCSeconds(), p, 2);\n}\n\nfunction formatUTCWeekdayNumberMonday(d) {\n  var dow = d.getUTCDay();\n  return dow === 0 ? 7 : dow;\n}\n\nfunction formatUTCWeekNumberSunday(d, p) {\n  return pad(utcSunday.count(utcYear(d) - 1, d), p, 2);\n}\n\nfunction UTCdISO(d) {\n  var day = d.getUTCDay();\n  return (day >= 4 || day === 0) ? utcThursday(d) : utcThursday.ceil(d);\n}\n\nfunction formatUTCWeekNumberISO(d, p) {\n  d = UTCdISO(d);\n  return pad(utcThursday.count(utcYear(d), d) + (utcYear(d).getUTCDay() === 4), p, 2);\n}\n\nfunction formatUTCWeekdayNumberSunday(d) {\n  return d.getUTCDay();\n}\n\nfunction formatUTCWeekNumberMonday(d, p) {\n  return pad(utcMonday.count(utcYear(d) - 1, d), p, 2);\n}\n\nfunction formatUTCYear(d, p) {\n  return pad(d.getUTCFullYear() % 100, p, 2);\n}\n\nfunction formatUTCYearISO(d, p) {\n  d = UTCdISO(d);\n  return pad(d.getUTCFullYear() % 100, p, 2);\n}\n\nfunction formatUTCFullYear(d, p) {\n  return pad(d.getUTCFullYear() % 10000, p, 4);\n}\n\nfunction formatUTCFullYearISO(d, p) {\n  var day = d.getUTCDay();\n  d = (day >= 4 || day === 0) ? utcThursday(d) : utcThursday.ceil(d);\n  return pad(d.getUTCFullYear() % 10000, p, 4);\n}\n\nfunction formatUTCZone() {\n  return \"+0000\";\n}\n\nfunction formatLiteralPercent() {\n  return \"%\";\n}\n\nfunction formatUnixTimestamp(d) {\n  return +d;\n}\n\nfunction formatUnixTimestampSeconds(d) {\n  return Math.floor(+d / 1000);\n}\n", "import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var timeFormat;\nexport var timeParse;\nexport var utcFormat;\nexport var utcParse;\n\ndefaultLocale({\n  dateTime: \"%x, %X\",\n  date: \"%-m/%-d/%Y\",\n  time: \"%-I:%M:%S %p\",\n  periods: [\"AM\", \"PM\"],\n  days: [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"],\n  shortDays: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n  months: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"],\n  shortMonths: [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  timeFormat = locale.format;\n  timeParse = locale.parse;\n  utcFormat = locale.utcFormat;\n  utcParse = locale.utcParse;\n  return locale;\n}\n", "import {timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeTicks, timeTickInterval} from \"d3-time\";\nimport {timeFormat} from \"d3-time-format\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport nice from \"./nice.js\";\n\nfunction date(t) {\n  return new Date(t);\n}\n\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\n\nexport function calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {\n  var scale = continuous(),\n      invert = scale.invert,\n      domain = scale.domain;\n\n  var formatMillisecond = format(\".%L\"),\n      formatSecond = format(\":%S\"),\n      formatMinute = format(\"%I:%M\"),\n      formatHour = format(\"%I %p\"),\n      formatDay = format(\"%a %d\"),\n      formatWeek = format(\"%b %d\"),\n      formatMonth = format(\"%B\"),\n      formatYear = format(\"%Y\");\n\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond\n        : minute(date) < date ? formatSecond\n        : hour(date) < date ? formatMinute\n        : day(date) < date ? formatHour\n        : month(date) < date ? (week(date) < date ? formatDay : formatWeek)\n        : year(date) < date ? formatMonth\n        : formatYear)(date);\n  }\n\n  scale.invert = function(y) {\n    return new Date(invert(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? domain(Array.from(_, number)) : domain().map(date);\n  };\n\n  scale.ticks = function(interval) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n\n  scale.nice = function(interval) {\n    var d = domain();\n    if (!interval || typeof interval.range !== \"function\") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);\n    return interval ? domain(nice(d, interval)) : scale;\n  };\n\n  scale.copy = function() {\n    return copy(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));\n  };\n\n  return scale;\n}\n\nexport default function time() {\n  return initRange.apply(calendar(timeTicks, timeTickInterval, timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}\n"], "names": ["max", "values", "valueof", "value", "index", "min", "radians", "degrees", "K", "Xn", "Yn", "Zn", "t0", "t1", "t2", "t3", "labConvert", "o", "Lab", "Hcl", "hcl2lab", "Rgb", "rgbConvert", "r", "rgb2lrgb", "g", "b", "y", "xyz2lab", "x", "z", "lab", "l", "a", "opacity", "define", "extend", "Color", "k", "lab2xyz", "lrgb2rgb", "t", "hclConvert", "h", "hcl", "c", "hue", "start", "end", "colorHcl", "color", "interpolateHcl", "hclLong", "nice", "domain", "interval", "i0", "i1", "x0", "x1", "timeInterval", "floori", "offseti", "count", "field", "date", "d0", "d1", "step", "stop", "range", "previous", "test", "d", "millisecond", "durationSecond", "durationMinute", "durationHour", "durationDay", "durationWeek", "durationMonth", "durationYear", "second", "timeMinute", "utcMinute", "timeHour", "utcHour", "timeDay", "utcDay", "unixDay", "timeWeekday", "i", "timeSunday", "timeMonday", "timeTuesday", "timeWednesday", "timeThursday", "timeFriday", "timeSaturday", "utcWeekday", "utcSunday", "utcMonday", "utcTuesday", "utcWednesday", "utcThursday", "utcFriday", "utcSaturday", "timeMonth", "utcMonth", "timeYear", "utcYear", "ticker", "year", "month", "week", "day", "hour", "minute", "tickIntervals", "ticks", "reverse", "tickInterval", "target", "bisector", "tickStep", "utcTicks", "utcTickInterval", "timeTicks", "timeTickInterval", "localDate", "utcDate", "newDate", "m", "formatLocale", "locale", "locale_dateTime", "locale_date", "locale_time", "locale_periods", "locale_weekdays", "locale_shortWeekdays", "locale_months", "locale_shortMonths", "periodRe", "formatRe", "periodLookup", "formatLookup", "weekdayRe", "weekdayLookup", "shortWeekdayRe", "shortWeekdayLookup", "monthRe", "monthLookup", "shortMonthRe", "shortMonthLookup", "formats", "formatShortWeekday", "formatWeekday", "formatShortMonth", "formatMonth", "formatDayOfMonth", "formatMicroseconds", "formatYearISO", "formatFullYearISO", "formatHour24", "formatHour12", "formatDayOfYear", "formatMilliseconds", "formatMonthNumber", "formatMinutes", "formatPeriod", "formatQuarter", "formatUnixTimestamp", "formatUnixTimestampSeconds", "formatSeconds", "formatWeekdayNumberMonday", "formatWeekNumberSunday", "formatWeekNumberISO", "formatWeekdayNumberSunday", "formatWeekNumberMonday", "formatYear", "formatFullYear", "formatZone", "formatLiteralPercent", "utcFormats", "formatUTCShortWeekday", "formatUTCWeekday", "formatUTCShortMonth", "formatUTCMonth", "formatUTCDayOfMonth", "formatUTCMicroseconds", "formatUTCYearISO", "formatUTCFullYearISO", "formatUTCHour24", "formatUTCHour12", "formatUTCDayOfYear", "formatUTCMilliseconds", "formatUTCMonthNumber", "formatUTCMinutes", "formatUTCPeriod", "formatUTCQuarter", "formatUTCSeconds", "formatUTCWeekdayNumberMonday", "formatUTCWeekNumberSunday", "formatUTCWeekNumberISO", "formatUTCWeekdayNumberSunday", "formatUTCWeekNumberMonday", "formatUTCYear", "formatUTCFullYear", "formatUTCZone", "parses", "parseShortWeekday", "parseWeekday", "parseShortMonth", "parseMonth", "parseLocaleDateTime", "parseDayOfMonth", "parseMicroseconds", "parseYear", "parseFullYear", "parseHour24", "parseDayOfYear", "parseMilliseconds", "parseMonthNumber", "parseMinutes", "parsePeriod", "parseQuarter", "parseUnixTimestamp", "parseUnixTimestampSeconds", "parseSeconds", "parseWeekdayNumberMonday", "parseWeekNumberSunday", "parseWeekNumberISO", "parseWeekdayNumberSunday", "parseWeekNumberMonday", "parseLocaleDate", "parseLocaleTime", "parseZone", "parseLiteralPercent", "newFormat", "specifier", "string", "j", "n", "pad", "format", "pads", "newParse", "Z", "parseSpecifier", "parse", "f", "p", "numberRe", "percentRe", "requoteRe", "fill", "width", "sign", "length", "requote", "s", "names", "name", "dISO", "dow", "UTCdISO", "timeFormat", "timeParse", "utcFormat", "utcParse", "defaultLocale", "definition", "number", "calendar", "scale", "continuous", "invert", "formatMillisecond", "formatSecond", "formatMinute", "formatHour", "formatDay", "formatWeek", "tickFormat", "_", "copy", "time", "initRange", "timeWeek", "timeSecond"], "mappings": "iMAAe,SAASA,GAAIC,EAAQC,EAAS,CAC3C,IAAIF,EACJ,GAAIE,IAAY,OACd,UAAWC,KAASF,EACdE,GAAS,OACLH,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzCD,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,EAGX,CACD,OAAOH,CACT,CCnBe,SAASK,GAAIJ,EAAQC,EAAS,CAC3C,IAAIG,EACJ,GAAIH,IAAY,OACd,UAAWC,KAASF,EACdE,GAAS,OACLE,EAAMF,GAAUE,IAAQ,QAAaF,GAASA,KACpDE,EAAMF,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzCI,EAAMF,GAAUE,IAAQ,QAAaF,GAASA,KACpDE,EAAMF,EAGX,CACD,OAAOE,CACT,CCnBY,MAACC,GAAU,KAAK,GAAK,IACpBC,GAAU,IAAM,KAAK,GCI5BC,GAAI,GACNC,GAAK,OACLC,GAAK,EACLC,GAAK,OACLC,GAAK,EAAI,GACTC,EAAK,EAAI,GACTC,GAAK,EAAID,EAAKA,EACdE,GAAKF,EAAKA,EAAKA,EAEnB,SAASG,GAAWC,EAAG,CACrB,GAAIA,aAAaC,EAAK,OAAO,IAAIA,EAAID,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,EAC7D,GAAIA,aAAaE,EAAK,OAAOC,GAAQH,CAAC,EAChCA,aAAaI,KAAMJ,EAAIK,GAAWL,CAAC,GACzC,IAAIM,EAAIC,GAASP,EAAE,CAAC,EAChBQ,EAAID,GAASP,EAAE,CAAC,EAChBS,EAAIF,GAASP,EAAE,CAAC,EAChBU,EAAIC,IAAS,SAAYL,EAAI,SAAYE,EAAI,SAAYC,GAAKhB,EAAE,EAAGmB,EAAGC,EAC1E,OAAIP,IAAME,GAAKA,IAAMC,EAAGG,EAAIC,EAAIH,GAC9BE,EAAID,IAAS,SAAYL,EAAI,SAAYE,EAAI,SAAYC,GAAKjB,EAAE,EAChEqB,EAAIF,IAAS,SAAYL,EAAI,SAAYE,EAAI,SAAYC,GAAKf,EAAE,GAE3D,IAAIO,EAAI,IAAMS,EAAI,GAAI,KAAOE,EAAIF,GAAI,KAAOA,EAAIG,GAAIb,EAAE,OAAO,CACtE,CAMe,SAASc,GAAIC,EAAGC,EAAGP,EAAGQ,EAAS,CAC5C,OAAO,UAAU,SAAW,EAAIlB,GAAWgB,CAAC,EAAI,IAAId,EAAIc,EAAGC,EAAGP,EAAGQ,GAAkB,CAAW,CAChG,CAEO,SAAShB,EAAIc,EAAGC,EAAGP,EAAGQ,EAAS,CACpC,KAAK,EAAI,CAACF,EACV,KAAK,EAAI,CAACC,EACV,KAAK,EAAI,CAACP,EACV,KAAK,QAAU,CAACQ,CAClB,CAEAC,GAAOjB,EAAKa,GAAKK,GAAOC,GAAO,CAC7B,SAASC,EAAG,CACV,OAAO,IAAIpB,EAAI,KAAK,EAAIV,IAAK8B,GAAY,GAAQ,KAAK,EAAG,KAAK,EAAG,KAAK,OAAO,CAC9E,EACD,OAAOA,EAAG,CACR,OAAO,IAAIpB,EAAI,KAAK,EAAIV,IAAK8B,GAAY,GAAQ,KAAK,EAAG,KAAK,EAAG,KAAK,OAAO,CAC9E,EACD,KAAM,CACJ,IAAIX,GAAK,KAAK,EAAI,IAAM,IACpBE,EAAI,MAAM,KAAK,CAAC,EAAIF,EAAIA,EAAI,KAAK,EAAI,IACrCG,EAAI,MAAM,KAAK,CAAC,EAAIH,EAAIA,EAAI,KAAK,EAAI,IACzC,OAAAE,EAAIpB,GAAK8B,GAAQV,CAAC,EAClBF,EAAIjB,GAAK6B,GAAQZ,CAAC,EAClBG,EAAInB,GAAK4B,GAAQT,CAAC,EACX,IAAIT,GACTmB,GAAU,UAAYX,EAAI,UAAYF,EAAI,SAAYG,CAAC,EACvDU,GAAS,UAAaX,EAAI,UAAYF,EAAI,QAAYG,CAAC,EACvDU,GAAU,SAAYX,EAAI,SAAYF,EAAI,UAAYG,CAAC,EACvD,KAAK,OACX,CACG,CACH,CAAC,CAAC,EAEF,SAASF,GAAQa,EAAG,CAClB,OAAOA,EAAI1B,GAAK,KAAK,IAAI0B,EAAG,EAAI,CAAC,EAAIA,EAAI3B,GAAKF,EAChD,CAEA,SAAS2B,GAAQE,EAAG,CAClB,OAAOA,EAAI5B,EAAK4B,EAAIA,EAAIA,EAAI3B,IAAM2B,EAAI7B,GACxC,CAEA,SAAS4B,GAASX,EAAG,CACnB,MAAO,MAAOA,GAAK,SAAY,MAAQA,EAAI,MAAQ,KAAK,IAAIA,EAAG,EAAI,GAAG,EAAI,KAC5E,CAEA,SAASL,GAASK,EAAG,CACnB,OAAQA,GAAK,MAAQ,OAAUA,EAAI,MAAQ,KAAK,KAAKA,EAAI,MAAS,MAAO,GAAG,CAC9E,CAEA,SAASa,GAAWzB,EAAG,CACrB,GAAIA,aAAaE,EAAK,OAAO,IAAIA,EAAIF,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,EAE7D,GADMA,aAAaC,IAAMD,EAAID,GAAWC,CAAC,GACrCA,EAAE,IAAM,GAAKA,EAAE,IAAM,EAAG,OAAO,IAAIE,EAAI,IAAK,EAAIF,EAAE,GAAKA,EAAE,EAAI,IAAM,EAAI,IAAKA,EAAE,EAAGA,EAAE,OAAO,EAC9F,IAAI0B,EAAI,KAAK,MAAM1B,EAAE,EAAGA,EAAE,CAAC,EAAIV,GAC/B,OAAO,IAAIY,EAAIwB,EAAI,EAAIA,EAAI,IAAMA,EAAG,KAAK,KAAK1B,EAAE,EAAIA,EAAE,EAAIA,EAAE,EAAIA,EAAE,CAAC,EAAGA,EAAE,EAAGA,EAAE,OAAO,CACtF,CAMO,SAAS2B,GAAID,EAAGE,EAAGb,EAAGE,EAAS,CACpC,OAAO,UAAU,SAAW,EAAIQ,GAAWC,CAAC,EAAI,IAAIxB,EAAIwB,EAAGE,EAAGb,EAAGE,GAAkB,CAAW,CAChG,CAEO,SAASf,EAAIwB,EAAGE,EAAGb,EAAGE,EAAS,CACpC,KAAK,EAAI,CAACS,EACV,KAAK,EAAI,CAACE,EACV,KAAK,EAAI,CAACb,EACV,KAAK,QAAU,CAACE,CAClB,CAEA,SAASd,GAAQH,EAAG,CAClB,GAAI,MAAMA,EAAE,CAAC,EAAG,OAAO,IAAIC,EAAID,EAAE,EAAG,EAAG,EAAGA,EAAE,OAAO,EACnD,IAAI0B,EAAI1B,EAAE,EAAIX,GACd,OAAO,IAAIY,EAAID,EAAE,EAAG,KAAK,IAAI0B,CAAC,EAAI1B,EAAE,EAAG,KAAK,IAAI0B,CAAC,EAAI1B,EAAE,EAAGA,EAAE,OAAO,CACrE,CAEAkB,GAAOhB,EAAKyB,GAAKR,GAAOC,GAAO,CAC7B,SAASC,EAAG,CACV,OAAO,IAAInB,EAAI,KAAK,EAAG,KAAK,EAAG,KAAK,EAAIX,IAAK8B,GAAY,GAAQ,KAAK,OAAO,CAC9E,EACD,OAAOA,EAAG,CACR,OAAO,IAAInB,EAAI,KAAK,EAAG,KAAK,EAAG,KAAK,EAAIX,IAAK8B,GAAY,GAAQ,KAAK,OAAO,CAC9E,EACD,KAAM,CACJ,OAAOlB,GAAQ,IAAI,EAAE,KACtB,CACH,CAAC,CAAC,ECvHF,SAASwB,GAAIE,EAAK,CAChB,OAAO,SAASC,EAAOC,EAAK,CAC1B,IAAIL,EAAIG,GAAKC,EAAQE,GAASF,CAAK,GAAG,GAAIC,EAAMC,GAASD,CAAG,GAAG,CAAC,EAC5DH,EAAIK,EAAMH,EAAM,EAAGC,EAAI,CAAC,EACxBhB,EAAIkB,EAAMH,EAAM,EAAGC,EAAI,CAAC,EACxBd,EAAUgB,EAAMH,EAAM,QAASC,EAAI,OAAO,EAC9C,OAAO,SAASP,EAAG,CACjB,OAAAM,EAAM,EAAIJ,EAAEF,CAAC,EACbM,EAAM,EAAIF,EAAEJ,CAAC,EACbM,EAAM,EAAIf,EAAES,CAAC,EACbM,EAAM,QAAUb,EAAQO,CAAC,EAClBM,EAAQ,EACrB,CACG,CACH,CAEA,MAAAI,GAAeP,GAAIE,EAAG,EACZ,IAACM,GAAUR,GAAIM,CAAK,ECpBf,SAASG,GAAKC,EAAQC,EAAU,CAC7CD,EAASA,EAAO,QAEhB,IAAIE,EAAK,EACLC,EAAKH,EAAO,OAAS,EACrBI,EAAKJ,EAAOE,CAAE,EACdG,EAAKL,EAAOG,CAAE,EACdhB,EAEJ,OAAIkB,EAAKD,IACPjB,EAAIe,EAAIA,EAAKC,EAAIA,EAAKhB,EACtBA,EAAIiB,EAAIA,EAAKC,EAAIA,EAAKlB,GAGxBa,EAAOE,CAAE,EAAID,EAAS,MAAMG,CAAE,EAC9BJ,EAAOG,CAAE,EAAIF,EAAS,KAAKI,CAAE,EACtBL,CACT,CCjBA,MAAM1C,GAAK,IAAI,KAAMC,GAAK,IAAI,KAEvB,SAAS+C,EAAaC,EAAQC,EAASC,EAAOC,EAAO,CAE1D,SAAST,EAASU,EAAM,CACtB,OAAOJ,EAAOI,EAAO,UAAU,SAAW,EAAI,IAAI,KAAO,IAAI,KAAK,CAACA,CAAI,CAAC,EAAGA,CAC5E,CAED,OAAAV,EAAS,MAASU,IACTJ,EAAOI,EAAO,IAAI,KAAK,CAACA,CAAI,CAAC,EAAGA,GAGzCV,EAAS,KAAQU,IACRJ,EAAOI,EAAO,IAAI,KAAKA,EAAO,CAAC,CAAC,EAAGH,EAAQG,EAAM,CAAC,EAAGJ,EAAOI,CAAI,EAAGA,GAG5EV,EAAS,MAASU,GAAS,CACzB,MAAMC,EAAKX,EAASU,CAAI,EAAGE,EAAKZ,EAAS,KAAKU,CAAI,EAClD,OAAOA,EAAOC,EAAKC,EAAKF,EAAOC,EAAKC,CACxC,EAEEZ,EAAS,OAAS,CAACU,EAAMG,KAChBN,EAAQG,EAAO,IAAI,KAAK,CAACA,CAAI,EAAGG,GAAQ,KAAO,EAAI,KAAK,MAAMA,CAAI,CAAC,EAAGH,GAG/EV,EAAS,MAAQ,CAACR,EAAOsB,EAAMD,IAAS,CACtC,MAAME,EAAQ,CAAA,EAGd,GAFAvB,EAAQQ,EAAS,KAAKR,CAAK,EAC3BqB,EAAOA,GAAQ,KAAO,EAAI,KAAK,MAAMA,CAAI,EACrC,EAAErB,EAAQsB,IAAS,EAAED,EAAO,GAAI,OAAOE,EAC3C,IAAIC,EACJ,GAAGD,EAAM,KAAKC,EAAW,IAAI,KAAK,CAACxB,CAAK,CAAC,EAAGe,EAAQf,EAAOqB,CAAI,EAAGP,EAAOd,CAAK,QACvEwB,EAAWxB,GAASA,EAAQsB,GACnC,OAAOC,CACX,EAEEf,EAAS,OAAUiB,GACVZ,EAAcK,GAAS,CAC5B,GAAIA,GAAQA,EAAM,KAAOJ,EAAOI,CAAI,EAAG,CAACO,EAAKP,CAAI,GAAGA,EAAK,QAAQA,EAAO,CAAC,CAC/E,EAAO,CAACA,EAAMG,IAAS,CACjB,GAAIH,GAAQA,EACV,GAAIG,EAAO,EAAG,KAAO,EAAEA,GAAQ,GAC7B,KAAON,EAAQG,EAAM,EAAE,EAAG,CAACO,EAAKP,CAAI,GAAG,KAClC,MAAO,EAAEG,GAAQ,GACtB,KAAON,EAAQG,EAAM,CAAE,EAAG,CAACO,EAAKP,CAAI,GAAG,CAGjD,CAAK,EAGCF,IACFR,EAAS,MAAQ,CAACR,EAAOC,KACvBpC,GAAG,QAAQ,CAACmC,CAAK,EAAGlC,GAAG,QAAQ,CAACmC,CAAG,EACnCa,EAAOjD,EAAE,EAAGiD,EAAOhD,EAAE,EACd,KAAK,MAAMkD,EAAMnD,GAAIC,EAAE,CAAC,GAGjC0C,EAAS,MAASa,IAChBA,EAAO,KAAK,MAAMA,CAAI,EACf,CAAC,SAASA,CAAI,GAAK,EAAEA,EAAO,GAAK,KAChCA,EAAO,EACTb,EAAS,OAAOS,EACXS,GAAMT,EAAMS,CAAC,EAAIL,IAAS,EAC1BK,GAAMlB,EAAS,MAAM,EAAGkB,CAAC,EAAIL,IAAS,CAAC,EAH9Bb,IAOjBA,CACT,CClEY,MAACmB,GAAcd,EAAa,IAAM,CAE9C,EAAG,CAACK,EAAMG,IAAS,CACjBH,EAAK,QAAQ,CAACA,EAAOG,CAAI,CAC3B,EAAG,CAACrB,EAAOC,IACFA,EAAMD,CACd,EAGD2B,GAAY,MAASpC,IACnBA,EAAI,KAAK,MAAMA,CAAC,EACZ,CAAC,SAASA,CAAC,GAAK,EAAEA,EAAI,GAAW,KAC/BA,EAAI,EACHsB,EAAcK,GAAS,CAC5BA,EAAK,QAAQ,KAAK,MAAMA,EAAO3B,CAAC,EAAIA,CAAC,CACzC,EAAK,CAAC2B,EAAMG,IAAS,CACjBH,EAAK,QAAQ,CAACA,EAAOG,EAAO9B,CAAC,CACjC,EAAK,CAACS,EAAOC,KACDA,EAAMD,GAAST,CACxB,EAPoBoC,IAUKA,GAAY,MCxBjC,MAAMC,EAAiB,IACjBC,EAAiBD,EAAiB,GAClCE,EAAeD,EAAiB,GAChCE,EAAcD,EAAe,GAC7BE,GAAeD,EAAc,EAC7BE,GAAgBF,EAAc,GAC9BG,GAAeH,EAAc,ICH7BI,EAAStB,EAAcK,GAAS,CAC3CA,EAAK,QAAQA,EAAOA,EAAK,gBAAiB,CAAA,CAC5C,EAAG,CAACA,EAAMG,IAAS,CACjBH,EAAK,QAAQ,CAACA,EAAOG,EAAOO,CAAc,CAC5C,EAAG,CAAC5B,EAAOC,KACDA,EAAMD,GAAS4B,EACrBV,GACKA,EAAK,eACb,EAEsBiB,EAAO,MCVlB,MAACC,GAAavB,EAAcK,GAAS,CAC/CA,EAAK,QAAQA,EAAOA,EAAK,gBAAiB,EAAGA,EAAK,aAAeU,CAAc,CACjF,EAAG,CAACV,EAAMG,IAAS,CACjBH,EAAK,QAAQ,CAACA,EAAOG,EAAOQ,CAAc,CAC5C,EAAG,CAAC7B,EAAOC,KACDA,EAAMD,GAAS6B,EACrBX,GACKA,EAAK,YACb,EAE0BkB,GAAW,MAE1B,MAACC,GAAYxB,EAAcK,GAAS,CAC9CA,EAAK,cAAc,EAAG,CAAC,CACzB,EAAG,CAACA,EAAMG,IAAS,CACjBH,EAAK,QAAQ,CAACA,EAAOG,EAAOQ,CAAc,CAC5C,EAAG,CAAC7B,EAAOC,KACDA,EAAMD,GAAS6B,EACrBX,GACKA,EAAK,eACb,EAEyBmB,GAAU,MCtBxB,MAACC,GAAWzB,EAAcK,GAAS,CAC7CA,EAAK,QAAQA,EAAOA,EAAK,gBAAe,EAAKA,EAAK,WAAU,EAAKU,EAAiBV,EAAK,WAAY,EAAGW,CAAc,CACtH,EAAG,CAACX,EAAMG,IAAS,CACjBH,EAAK,QAAQ,CAACA,EAAOG,EAAOS,CAAY,CAC1C,EAAG,CAAC9B,EAAOC,KACDA,EAAMD,GAAS8B,EACrBZ,GACKA,EAAK,UACb,EAEwBoB,GAAS,MAEtB,MAACC,GAAU1B,EAAcK,GAAS,CAC5CA,EAAK,cAAc,EAAG,EAAG,CAAC,CAC5B,EAAG,CAACA,EAAMG,IAAS,CACjBH,EAAK,QAAQ,CAACA,EAAOG,EAAOS,CAAY,CAC1C,EAAG,CAAC9B,EAAOC,KACDA,EAAMD,GAAS8B,EACrBZ,GACKA,EAAK,aACb,EAEuBqB,GAAQ,MCtBpB,MAACC,EAAU3B,EACrBK,GAAQA,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,EAChC,CAACA,EAAMG,IAASH,EAAK,QAAQA,EAAK,QAAS,EAAGG,CAAI,EAClD,CAACrB,EAAOC,KAASA,EAAMD,GAASC,EAAI,kBAAmB,EAAGD,EAAM,kBAAmB,GAAI6B,GAAkBE,EACzGb,GAAQA,EAAK,QAAO,EAAK,CAC3B,EAEwBsB,EAAQ,MAEpB,MAACC,GAAS5B,EAAcK,GAAS,CAC3CA,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC7B,EAAG,CAACA,EAAMG,IAAS,CACjBH,EAAK,WAAWA,EAAK,WAAY,EAAGG,CAAI,CAC1C,EAAG,CAACrB,EAAOC,KACDA,EAAMD,GAAS+B,EACrBb,GACKA,EAAK,WAAY,EAAG,CAC5B,EAEsBuB,GAAO,MAEvB,MAAMC,GAAU7B,EAAcK,GAAS,CAC5CA,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC7B,EAAG,CAACA,EAAMG,IAAS,CACjBH,EAAK,WAAWA,EAAK,WAAY,EAAGG,CAAI,CAC1C,EAAG,CAACrB,EAAOC,KACDA,EAAMD,GAAS+B,EACrBb,GACK,KAAK,MAAMA,EAAOa,CAAW,CACrC,EAEuBW,GAAQ,MC/BhC,SAASC,EAAYC,EAAG,CACtB,OAAO/B,EAAcK,GAAS,CAC5BA,EAAK,QAAQA,EAAK,WAAaA,EAAK,SAAW,EAAI0B,GAAK,CAAC,EACzD1B,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,CAC5B,EAAK,CAACA,EAAMG,IAAS,CACjBH,EAAK,QAAQA,EAAK,QAAO,EAAKG,EAAO,CAAC,CAC1C,EAAK,CAACrB,EAAOC,KACDA,EAAMD,GAASC,EAAI,oBAAsBD,EAAM,kBAAiB,GAAM6B,GAAkBG,EACjG,CACH,CAEY,MAACa,GAAaF,EAAY,CAAC,EAC1BG,GAAaH,EAAY,CAAC,EAC1BI,GAAcJ,EAAY,CAAC,EAC3BK,GAAgBL,EAAY,CAAC,EAC7BM,EAAeN,EAAY,CAAC,EAC5BO,GAAaP,EAAY,CAAC,EAC1BQ,GAAeR,EAAY,CAAC,EAEdE,GAAW,MACXC,GAAW,MACVC,GAAY,MACVC,GAAc,MACfC,EAAa,MACfC,GAAW,MACTC,GAAa,MAE1C,SAASC,EAAWR,EAAG,CACrB,OAAO/B,EAAcK,GAAS,CAC5BA,EAAK,WAAWA,EAAK,cAAgBA,EAAK,YAAc,EAAI0B,GAAK,CAAC,EAClE1B,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC/B,EAAK,CAACA,EAAMG,IAAS,CACjBH,EAAK,WAAWA,EAAK,WAAU,EAAKG,EAAO,CAAC,CAChD,EAAK,CAACrB,EAAOC,KACDA,EAAMD,GAASgC,EACxB,CACH,CAEY,MAACqB,GAAYD,EAAW,CAAC,EACxBE,GAAYF,EAAW,CAAC,EACxBG,GAAaH,EAAW,CAAC,EACzBI,GAAeJ,EAAW,CAAC,EAC3BK,EAAcL,EAAW,CAAC,EAC1BM,GAAYN,EAAW,CAAC,EACxBO,GAAcP,EAAW,CAAC,EAEbC,GAAU,MACVC,GAAU,MACTC,GAAW,MACTC,GAAa,MACdC,EAAY,MACdC,GAAU,MACRC,GAAY,MCrD5B,MAACC,GAAY/C,EAAcK,GAAS,CAC9CA,EAAK,QAAQ,CAAC,EACdA,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,CAC1B,EAAG,CAACA,EAAMG,IAAS,CACjBH,EAAK,SAASA,EAAK,SAAU,EAAGG,CAAI,CACtC,EAAG,CAACrB,EAAOC,IACFA,EAAI,WAAaD,EAAM,SAAU,GAAIC,EAAI,YAAW,EAAKD,EAAM,YAAW,GAAM,GACrFkB,GACKA,EAAK,UACb,EAEyB0C,GAAU,MAExB,MAACC,GAAWhD,EAAcK,GAAS,CAC7CA,EAAK,WAAW,CAAC,EACjBA,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC7B,EAAG,CAACA,EAAMG,IAAS,CACjBH,EAAK,YAAYA,EAAK,YAAa,EAAGG,CAAI,CAC5C,EAAG,CAACrB,EAAOC,IACFA,EAAI,cAAgBD,EAAM,YAAa,GAAIC,EAAI,eAAc,EAAKD,EAAM,eAAc,GAAM,GACjGkB,GACKA,EAAK,aACb,EAEwB2C,GAAS,MCxBtB,MAACC,EAAWjD,EAAcK,GAAS,CAC7CA,EAAK,SAAS,EAAG,CAAC,EAClBA,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,CAC1B,EAAG,CAACA,EAAMG,IAAS,CACjBH,EAAK,YAAYA,EAAK,YAAa,EAAGG,CAAI,CAC5C,EAAG,CAACrB,EAAOC,IACFA,EAAI,YAAW,EAAKD,EAAM,YAAW,EAC1CkB,GACKA,EAAK,aACb,EAGD4C,EAAS,MAASvE,GACT,CAAC,SAASA,EAAI,KAAK,MAAMA,CAAC,CAAC,GAAK,EAAEA,EAAI,GAAK,KAAOsB,EAAcK,GAAS,CAC9EA,EAAK,YAAY,KAAK,MAAMA,EAAK,cAAgB3B,CAAC,EAAIA,CAAC,EACvD2B,EAAK,SAAS,EAAG,CAAC,EAClBA,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,CAC5B,EAAK,CAACA,EAAMG,IAAS,CACjBH,EAAK,YAAYA,EAAK,YAAW,EAAKG,EAAO9B,CAAC,CAClD,CAAG,EAGsBuE,EAAS,MAEtB,MAACC,EAAUlD,EAAcK,GAAS,CAC5CA,EAAK,YAAY,EAAG,CAAC,EACrBA,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC7B,EAAG,CAACA,EAAMG,IAAS,CACjBH,EAAK,eAAeA,EAAK,eAAgB,EAAGG,CAAI,CAClD,EAAG,CAACrB,EAAOC,IACFA,EAAI,eAAc,EAAKD,EAAM,eAAc,EAChDkB,GACKA,EAAK,gBACb,EAGD6C,EAAQ,MAASxE,GACR,CAAC,SAASA,EAAI,KAAK,MAAMA,CAAC,CAAC,GAAK,EAAEA,EAAI,GAAK,KAAOsB,EAAcK,GAAS,CAC9EA,EAAK,eAAe,KAAK,MAAMA,EAAK,iBAAmB3B,CAAC,EAAIA,CAAC,EAC7D2B,EAAK,YAAY,EAAG,CAAC,EACrBA,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC/B,EAAK,CAACA,EAAMG,IAAS,CACjBH,EAAK,eAAeA,EAAK,eAAc,EAAKG,EAAO9B,CAAC,CACxD,CAAG,EAGqBwE,EAAQ,MCrChC,SAASC,GAAOC,EAAMC,EAAOC,EAAMC,EAAKC,EAAMC,EAAQ,CAEpD,MAAMC,EAAgB,CACpB,CAACpC,EAAS,EAAQP,CAAc,EAChC,CAACO,EAAS,EAAI,EAAIP,CAAc,EAChC,CAACO,EAAQ,GAAI,GAAKP,CAAc,EAChC,CAACO,EAAQ,GAAI,GAAKP,CAAc,EAChC,CAAC0C,EAAS,EAAQzC,CAAc,EAChC,CAACyC,EAAS,EAAI,EAAIzC,CAAc,EAChC,CAACyC,EAAQ,GAAI,GAAKzC,CAAc,EAChC,CAACyC,EAAQ,GAAI,GAAKzC,CAAc,EAChC,CAAGwC,EAAO,EAAQvC,CAAe,EACjC,CAAGuC,EAAO,EAAI,EAAIvC,CAAe,EACjC,CAAGuC,EAAO,EAAI,EAAIvC,CAAe,EACjC,CAAGuC,EAAM,GAAI,GAAKvC,CAAe,EACjC,CAAIsC,EAAM,EAAQrC,CAAe,EACjC,CAAIqC,EAAM,EAAI,EAAIrC,CAAe,EACjC,CAAGoC,EAAO,EAAQnC,EAAe,EACjC,CAAEkC,EAAQ,EAAQjC,EAAe,EACjC,CAAEiC,EAAQ,EAAI,EAAIjC,EAAe,EACjC,CAAGgC,EAAO,EAAQ/B,EAAe,CACrC,EAEE,SAASsC,EAAMxE,EAAOsB,EAAMN,EAAO,CACjC,MAAMyD,EAAUnD,EAAOtB,EACnByE,IAAS,CAACzE,EAAOsB,CAAI,EAAI,CAACA,EAAMtB,CAAK,GACzC,MAAMQ,EAAWQ,GAAS,OAAOA,EAAM,OAAU,WAAaA,EAAQ0D,EAAa1E,EAAOsB,EAAMN,CAAK,EAC/FwD,EAAQhE,EAAWA,EAAS,MAAMR,EAAO,CAACsB,EAAO,CAAC,EAAI,GAC5D,OAAOmD,EAAUD,EAAM,QAAO,EAAKA,CACpC,CAED,SAASE,EAAa1E,EAAOsB,EAAMN,EAAO,CACxC,MAAM2D,EAAS,KAAK,IAAIrD,EAAOtB,CAAK,EAAIgB,EAClC4B,EAAIgC,GAAS,CAAC,GAAIvD,CAAI,IAAMA,CAAI,EAAE,MAAMkD,EAAeI,CAAM,EACnE,GAAI/B,IAAM2B,EAAc,OAAQ,OAAON,EAAK,MAAMY,GAAS7E,EAAQkC,GAAcZ,EAAOY,GAAclB,CAAK,CAAC,EAC5G,GAAI4B,IAAM,EAAG,OAAOjB,GAAY,MAAM,KAAK,IAAIkD,GAAS7E,EAAOsB,EAAMN,CAAK,EAAG,CAAC,CAAC,EAC/E,KAAM,CAACtB,EAAG2B,CAAI,EAAIkD,EAAcI,EAASJ,EAAc3B,EAAI,CAAC,EAAE,CAAC,EAAI2B,EAAc3B,CAAC,EAAE,CAAC,EAAI+B,EAAS/B,EAAI,EAAIA,CAAC,EAC3G,OAAOlD,EAAE,MAAM2B,CAAI,CACpB,CAED,MAAO,CAACmD,EAAOE,CAAY,CAC7B,CAEK,KAAC,CAACI,GAAUC,EAAe,EAAIf,GAAOD,EAASF,GAAUR,GAAWX,GAASH,GAASF,EAAS,EAC9F,CAAC2C,GAAWC,EAAgB,EAAIjB,GAAOF,EAAUF,GAAWf,GAAYL,EAASF,GAAUF,EAAU,EC1C3G,SAAS8C,GAAUxD,EAAG,CACpB,GAAI,GAAKA,EAAE,GAAKA,EAAE,EAAI,IAAK,CACzB,IAAIR,EAAO,IAAI,KAAK,GAAIQ,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,EACpD,OAAAR,EAAK,YAAYQ,EAAE,CAAC,EACbR,CACR,CACD,OAAO,IAAI,KAAKQ,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,CACnD,CAEA,SAASyD,GAAQzD,EAAG,CAClB,GAAI,GAAKA,EAAE,GAAKA,EAAE,EAAI,IAAK,CACzB,IAAIR,EAAO,IAAI,KAAK,KAAK,IAAI,GAAIQ,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,CAAC,EAC9D,OAAAR,EAAK,eAAeQ,EAAE,CAAC,EAChBR,CACR,CACD,OAAO,IAAI,KAAK,KAAK,IAAIQ,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,CAAC,CAC7D,CAEA,SAAS0D,EAAQxG,EAAGyG,EAAG3D,EAAG,CACxB,MAAO,CAAC,EAAG9C,EAAG,EAAGyG,EAAG,EAAG3D,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CAClD,CAEe,SAAS4D,GAAaC,EAAQ,CAC3C,IAAIC,EAAkBD,EAAO,SACzBE,EAAcF,EAAO,KACrBG,EAAcH,EAAO,KACrBI,EAAiBJ,EAAO,QACxBK,EAAkBL,EAAO,KACzBM,EAAuBN,EAAO,UAC9BO,EAAgBP,EAAO,OACvBQ,EAAqBR,EAAO,YAE5BS,EAAWC,EAASN,CAAc,EAClCO,EAAeC,EAAaR,CAAc,EAC1CS,EAAYH,EAASL,CAAe,EACpCS,EAAgBF,EAAaP,CAAe,EAC5CU,EAAiBL,EAASJ,CAAoB,EAC9CU,EAAqBJ,EAAaN,CAAoB,EACtDW,EAAUP,EAASH,CAAa,EAChCW,EAAcN,EAAaL,CAAa,EACxCY,GAAeT,EAASF,CAAkB,EAC1CY,GAAmBR,EAAaJ,CAAkB,EAElDa,EAAU,CACZ,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAK,KACL,EAAKC,GACL,EAAKA,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAK,KACL,EAAK,KACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,IAAKC,EACT,EAEMC,EAAa,CACf,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAK,KACL,EAAKC,GACL,EAAKA,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAK5B,GACL,EAAKC,GACL,EAAK4B,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAK,KACL,EAAK,KACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,IAAK1B,EACT,EAEM2B,GAAS,CACX,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKA,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKA,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKlB,GACL,EAAKC,GACL,EAAKkB,GACL,IAAKC,EACT,EAGEnF,EAAQ,EAAIoF,EAAUvG,EAAamB,CAAO,EAC1CA,EAAQ,EAAIoF,EAAUtG,EAAakB,CAAO,EAC1CA,EAAQ,EAAIoF,EAAUxG,EAAiBoB,CAAO,EAC9C6B,EAAW,EAAIuD,EAAUvG,EAAagD,CAAU,EAChDA,EAAW,EAAIuD,EAAUtG,EAAa+C,CAAU,EAChDA,EAAW,EAAIuD,EAAUxG,EAAiBiD,CAAU,EAEpD,SAASuD,EAAUC,EAAWrF,EAAS,CACrC,OAAO,SAAS1F,EAAM,CACpB,IAAIgL,EAAS,CAAE,EACXtJ,EAAI,GACJuJ,EAAI,EACJC,EAAIH,EAAU,OACdnM,EACAuM,EACAC,GAIJ,IAFMpL,aAAgB,OAAOA,EAAO,IAAI,KAAK,CAACA,CAAI,GAE3C,EAAE0B,EAAIwJ,GACPH,EAAU,WAAWrJ,CAAC,IAAM,KAC9BsJ,EAAO,KAAKD,EAAU,MAAME,EAAGvJ,CAAC,CAAC,GAC5ByJ,EAAME,GAAKzM,EAAImM,EAAU,OAAO,EAAErJ,CAAC,CAAC,IAAM,KAAM9C,EAAImM,EAAU,OAAO,EAAErJ,CAAC,EACxEyJ,EAAMvM,IAAM,IAAM,IAAM,KACzBwM,GAAS1F,EAAQ9G,CAAC,KAAGA,EAAIwM,GAAOpL,EAAMmL,CAAG,GAC7CH,EAAO,KAAKpM,CAAC,EACbqM,EAAIvJ,EAAI,GAIZ,OAAAsJ,EAAO,KAAKD,EAAU,MAAME,EAAGvJ,CAAC,CAAC,EAC1BsJ,EAAO,KAAK,EAAE,CAC3B,CACG,CAED,SAASM,EAASP,EAAWQ,EAAG,CAC9B,OAAO,SAASP,EAAQ,CACtB,IAAIxK,EAAI0D,EAAQ,KAAM,OAAW,CAAC,EAC9BxC,EAAI8J,EAAehL,EAAGuK,EAAWC,GAAU,GAAI,CAAC,EAChD/H,EAAMC,EACV,GAAIxB,GAAKsJ,EAAO,OAAQ,OAAO,KAG/B,GAAI,MAAOxK,EAAG,OAAO,IAAI,KAAKA,EAAE,CAAC,EACjC,GAAI,MAAOA,EAAG,OAAO,IAAI,KAAKA,EAAE,EAAI,KAAQ,MAAOA,EAAIA,EAAE,EAAI,EAAE,EAY/D,GATI+K,GAAK,EAAE,MAAO/K,KAAIA,EAAE,EAAI,GAGxB,MAAOA,IAAGA,EAAE,EAAIA,EAAE,EAAI,GAAKA,EAAE,EAAI,IAGjCA,EAAE,IAAM,SAAWA,EAAE,EAAI,MAAOA,EAAIA,EAAE,EAAI,GAG1C,MAAOA,EAAG,CACZ,GAAIA,EAAE,EAAI,GAAKA,EAAE,EAAI,GAAI,OAAO,KAC1B,MAAOA,IAAIA,EAAE,EAAI,GACnB,MAAOA,GACTyC,EAAOgB,GAAQC,EAAQ1D,EAAE,EAAG,EAAG,CAAC,CAAC,EAAG0C,EAAMD,EAAK,UAAS,EACxDA,EAAOC,EAAM,GAAKA,IAAQ,EAAId,GAAU,KAAKa,CAAI,EAAIb,GAAUa,CAAI,EACnEA,EAAO1B,GAAO,OAAO0B,GAAOzC,EAAE,EAAI,GAAK,CAAC,EACxCA,EAAE,EAAIyC,EAAK,iBACXzC,EAAE,EAAIyC,EAAK,cACXzC,EAAE,EAAIyC,EAAK,WAAU,GAAMzC,EAAE,EAAI,GAAK,IAEtCyC,EAAOe,GAAUE,EAAQ1D,EAAE,EAAG,EAAG,CAAC,CAAC,EAAG0C,EAAMD,EAAK,OAAM,EACvDA,EAAOC,EAAM,GAAKA,IAAQ,EAAItB,GAAW,KAAKqB,CAAI,EAAIrB,GAAWqB,CAAI,EACrEA,EAAO3B,EAAQ,OAAO2B,GAAOzC,EAAE,EAAI,GAAK,CAAC,EACzCA,EAAE,EAAIyC,EAAK,cACXzC,EAAE,EAAIyC,EAAK,WACXzC,EAAE,EAAIyC,EAAK,QAAO,GAAMzC,EAAE,EAAI,GAAK,EAEtC,MAAU,MAAOA,GAAK,MAAOA,KACtB,MAAOA,IAAIA,EAAE,EAAI,MAAOA,EAAIA,EAAE,EAAI,EAAI,MAAOA,EAAI,EAAI,GAC3D0C,EAAM,MAAO1C,EAAIyD,GAAQC,EAAQ1D,EAAE,EAAG,EAAG,CAAC,CAAC,EAAE,YAAcwD,GAAUE,EAAQ1D,EAAE,EAAG,EAAG,CAAC,CAAC,EAAE,SACzFA,EAAE,EAAI,EACNA,EAAE,EAAI,MAAOA,GAAKA,EAAE,EAAI,GAAK,EAAIA,EAAE,EAAI,GAAK0C,EAAM,GAAK,EAAI1C,EAAE,EAAIA,EAAE,EAAI,GAAK0C,EAAM,GAAK,GAKzF,MAAI,MAAO1C,GACTA,EAAE,GAAKA,EAAE,EAAI,IAAM,EACnBA,EAAE,GAAKA,EAAE,EAAI,IACNyD,GAAQzD,CAAC,GAIXwD,GAAUxD,CAAC,CACxB,CACG,CAED,SAASgL,EAAehL,EAAGuK,EAAWC,EAAQC,EAAG,CAO/C,QANIvJ,EAAI,EACJwJ,EAAIH,EAAU,OACd5G,EAAI6G,EAAO,OACXpM,EACA6M,EAEG/J,EAAIwJ,GAAG,CACZ,GAAID,GAAK9G,EAAG,MAAO,GAEnB,GADAvF,EAAImM,EAAU,WAAWrJ,GAAG,EACxB9C,IAAM,IAGR,GAFAA,EAAImM,EAAU,OAAOrJ,GAAG,EACxB+J,EAAQxC,GAAOrK,KAAKyM,GAAON,EAAU,OAAOrJ,GAAG,EAAI9C,CAAC,EAChD,CAAC6M,IAAWR,EAAIQ,EAAMjL,EAAGwK,EAAQC,CAAC,GAAK,EAAI,MAAO,WAC7CrM,GAAKoM,EAAO,WAAWC,GAAG,EACnC,MAAO,EAEV,CAED,OAAOA,CACR,CAED,SAASjB,GAAYxJ,EAAGwK,EAAQtJ,EAAG,CACjC,IAAIwJ,EAAIpG,EAAS,KAAKkG,EAAO,MAAMtJ,CAAC,CAAC,EACrC,OAAOwJ,GAAK1K,EAAE,EAAIwE,EAAa,IAAIkG,EAAE,CAAC,EAAE,YAAa,CAAA,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC5E,CAED,SAAShC,GAAkB1I,EAAGwK,EAAQtJ,EAAG,CACvC,IAAIwJ,EAAI9F,EAAe,KAAK4F,EAAO,MAAMtJ,CAAC,CAAC,EAC3C,OAAOwJ,GAAK1K,EAAE,EAAI6E,EAAmB,IAAI6F,EAAE,CAAC,EAAE,YAAa,CAAA,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAClF,CAED,SAAS/B,GAAa3I,EAAGwK,EAAQtJ,EAAG,CAClC,IAAIwJ,EAAIhG,EAAU,KAAK8F,EAAO,MAAMtJ,CAAC,CAAC,EACtC,OAAOwJ,GAAK1K,EAAE,EAAI2E,EAAc,IAAI+F,EAAE,CAAC,EAAE,YAAa,CAAA,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC7E,CAED,SAAS9B,GAAgB5I,EAAGwK,EAAQtJ,EAAG,CACrC,IAAIwJ,EAAI1F,GAAa,KAAKwF,EAAO,MAAMtJ,CAAC,CAAC,EACzC,OAAOwJ,GAAK1K,EAAE,EAAIiF,GAAiB,IAAIyF,EAAE,CAAC,EAAE,YAAa,CAAA,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAChF,CAED,SAAS7B,GAAW7I,EAAGwK,EAAQtJ,EAAG,CAChC,IAAIwJ,EAAI5F,EAAQ,KAAK0F,EAAO,MAAMtJ,CAAC,CAAC,EACpC,OAAOwJ,GAAK1K,EAAE,EAAI+E,EAAY,IAAI2F,EAAE,CAAC,EAAE,YAAa,CAAA,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC3E,CAED,SAAS5B,GAAoB9I,EAAGwK,EAAQtJ,EAAG,CACzC,OAAO8J,EAAehL,EAAG8D,EAAiB0G,EAAQtJ,CAAC,CACpD,CAED,SAASgJ,GAAgBlK,EAAGwK,EAAQtJ,EAAG,CACrC,OAAO8J,EAAehL,EAAG+D,EAAayG,EAAQtJ,CAAC,CAChD,CAED,SAASiJ,GAAgBnK,EAAGwK,EAAQtJ,EAAG,CACrC,OAAO8J,EAAehL,EAAGgE,EAAawG,EAAQtJ,CAAC,CAChD,CAED,SAASiE,GAAmBnF,EAAG,CAC7B,OAAOmE,EAAqBnE,EAAE,OAAM,CAAE,CACvC,CAED,SAASoF,GAAcpF,EAAG,CACxB,OAAOkE,EAAgBlE,EAAE,OAAM,CAAE,CAClC,CAED,SAASqF,GAAiBrF,EAAG,CAC3B,OAAOqE,EAAmBrE,EAAE,SAAQ,CAAE,CACvC,CAED,SAASsF,GAAYtF,EAAG,CACtB,OAAOoE,EAAcpE,EAAE,SAAQ,CAAE,CAClC,CAED,SAASiG,GAAajG,EAAG,CACvB,OAAOiE,EAAe,EAAEjE,EAAE,SAAQ,GAAM,GAAG,CAC5C,CAED,SAASkG,GAAclG,EAAG,CACxB,MAAO,GAAI,CAAC,EAAEA,EAAE,SAAU,EAAG,EAC9B,CAED,SAASgH,GAAsBhH,EAAG,CAChC,OAAOmE,EAAqBnE,EAAE,UAAS,CAAE,CAC1C,CAED,SAASiH,GAAiBjH,EAAG,CAC3B,OAAOkE,EAAgBlE,EAAE,UAAS,CAAE,CACrC,CAED,SAASkH,GAAoBlH,EAAG,CAC9B,OAAOqE,EAAmBrE,EAAE,YAAW,CAAE,CAC1C,CAED,SAASmH,GAAenH,EAAG,CACzB,OAAOoE,EAAcpE,EAAE,YAAW,CAAE,CACrC,CAED,SAAS8H,GAAgB9H,EAAG,CAC1B,OAAOiE,EAAe,EAAEjE,EAAE,YAAW,GAAM,GAAG,CAC/C,CAED,SAAS+H,GAAiB/H,EAAG,CAC3B,MAAO,GAAI,CAAC,EAAEA,EAAE,YAAa,EAAG,EACjC,CAED,MAAO,CACL,OAAQ,SAASuK,EAAW,CAC1B,IAAIW,EAAIZ,EAAUC,GAAa,GAAIrF,CAAO,EAC1C,OAAAgG,EAAE,SAAW,UAAW,CAAE,OAAOX,CAAU,EACpCW,CACR,EACD,MAAO,SAASX,EAAW,CACzB,IAAIY,EAAIL,EAASP,GAAa,GAAI,EAAK,EACvC,OAAAY,EAAE,SAAW,UAAW,CAAE,OAAOZ,CAAU,EACpCY,CACR,EACD,UAAW,SAASZ,EAAW,CAC7B,IAAIW,EAAIZ,EAAUC,GAAa,GAAIxD,CAAU,EAC7C,OAAAmE,EAAE,SAAW,UAAW,CAAE,OAAOX,CAAU,EACpCW,CACR,EACD,SAAU,SAASX,EAAW,CAC5B,IAAIY,EAAIL,EAASP,GAAa,GAAI,EAAI,EACtC,OAAAY,EAAE,SAAW,UAAW,CAAE,OAAOZ,CAAU,EACpCY,CACR,CACL,CACA,CAEA,IAAIN,GAAO,CAAC,IAAK,GAAI,EAAK,IAAK,EAAK,GAAG,EACnCO,EAAW,UACXC,GAAY,KACZC,GAAY,sBAEhB,SAASX,EAAIjP,EAAO6P,EAAMC,EAAO,CAC/B,IAAIC,EAAO/P,EAAQ,EAAI,IAAM,GACzB8O,GAAUiB,EAAO,CAAC/P,EAAQA,GAAS,GACnCgQ,EAASlB,EAAO,OACpB,OAAOiB,GAAQC,EAASF,EAAQ,IAAI,MAAMA,EAAQE,EAAS,CAAC,EAAE,KAAKH,CAAI,EAAIf,EAASA,EACtF,CAEA,SAASmB,GAAQC,EAAG,CAClB,OAAOA,EAAE,QAAQN,GAAW,MAAM,CACpC,CAEA,SAAS/G,EAASsH,EAAO,CACvB,OAAO,IAAI,OAAO,OAASA,EAAM,IAAIF,EAAO,EAAE,KAAK,GAAG,EAAI,IAAK,GAAG,CACpE,CAEA,SAASlH,EAAaoH,EAAO,CAC3B,OAAO,IAAI,IAAIA,EAAM,IAAI,CAACC,EAAM5K,IAAM,CAAC4K,EAAK,YAAW,EAAI5K,CAAC,CAAC,CAAC,CAChE,CAEA,SAAS8I,GAAyBhK,EAAGwK,EAAQtJ,EAAG,CAC9C,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASb,GAAyB7J,EAAGwK,EAAQtJ,EAAG,CAC9C,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASZ,GAAsB9J,EAAGwK,EAAQtJ,EAAG,CAC3C,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASX,GAAmB/J,EAAGwK,EAAQtJ,EAAG,CACxC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAAST,GAAsBjK,EAAGwK,EAAQtJ,EAAG,CAC3C,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASxB,GAAclJ,EAAGwK,EAAQtJ,EAAG,CACnC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASzB,GAAUjJ,EAAGwK,EAAQtJ,EAAG,CAC/B,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,GAAK,CAACA,EAAE,CAAC,EAAI,GAAK,KAAO,KAAOxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC3E,CAEA,SAASN,GAAUpK,EAAGwK,EAAQtJ,EAAG,CAC/B,IAAIwJ,EAAI,+BAA+B,KAAKF,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAClE,OAAOwJ,GAAK1K,EAAE,EAAI0K,EAAE,CAAC,EAAI,EAAI,EAAEA,EAAE,CAAC,GAAKA,EAAE,CAAC,GAAK,OAAQxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC5E,CAEA,SAASjB,GAAazJ,EAAGwK,EAAQtJ,EAAG,CAClC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI0K,EAAE,CAAC,EAAI,EAAI,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EACrD,CAEA,SAASpB,GAAiBtJ,EAAGwK,EAAQtJ,EAAG,CACtC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI0K,EAAE,CAAC,EAAI,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EACjD,CAEA,SAAS3B,GAAgB/I,EAAGwK,EAAQtJ,EAAG,CACrC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAAStB,GAAepJ,EAAGwK,EAAQtJ,EAAG,CACpC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,EAAGA,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EACvD,CAEA,SAASvB,GAAYnJ,EAAGwK,EAAQtJ,EAAG,CACjC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASnB,GAAavJ,EAAGwK,EAAQtJ,EAAG,CAClC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASd,GAAa5J,EAAGwK,EAAQtJ,EAAG,CAClC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASrB,GAAkBrJ,EAAGwK,EAAQtJ,EAAG,CACvC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAAS1B,GAAkBhJ,EAAGwK,EAAQtJ,EAAG,CACvC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOwJ,GAAK1K,EAAE,EAAI,KAAK,MAAM0K,EAAE,CAAC,EAAI,GAAI,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAChE,CAEA,SAASL,GAAoBrK,EAAGwK,EAAQtJ,EAAG,CACzC,IAAIwJ,EAAIW,GAAU,KAAKb,EAAO,MAAMtJ,EAAGA,EAAI,CAAC,CAAC,EAC7C,OAAOwJ,EAAIxJ,EAAIwJ,EAAE,CAAC,EAAE,OAAS,EAC/B,CAEA,SAAShB,GAAmB1J,EAAGwK,EAAQtJ,EAAG,CACxC,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,CAAC,CAAC,EACrC,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASf,GAA0B3J,EAAGwK,EAAQtJ,EAAG,CAC/C,IAAIwJ,EAAIU,EAAS,KAAKZ,EAAO,MAAMtJ,CAAC,CAAC,EACrC,OAAOwJ,GAAK1K,EAAE,EAAI,CAAC0K,EAAE,CAAC,EAAGxJ,EAAIwJ,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASnF,GAAiBvF,EAAGmL,EAAG,CAC9B,OAAOR,EAAI3K,EAAE,QAAS,EAAEmL,EAAG,CAAC,CAC9B,CAEA,SAASxF,GAAa3F,EAAGmL,EAAG,CAC1B,OAAOR,EAAI3K,EAAE,SAAU,EAAEmL,EAAG,CAAC,CAC/B,CAEA,SAASvF,GAAa5F,EAAGmL,EAAG,CAC1B,OAAOR,EAAI3K,EAAE,SAAU,EAAG,IAAM,GAAImL,EAAG,CAAC,CAC1C,CAEA,SAAStF,GAAgB7F,EAAGmL,EAAG,CAC7B,OAAOR,EAAI,EAAI7J,EAAQ,MAAMsB,EAASpC,CAAC,EAAGA,CAAC,EAAGmL,EAAG,CAAC,CACpD,CAEA,SAASrF,GAAmB9F,EAAGmL,EAAG,CAChC,OAAOR,EAAI3K,EAAE,gBAAiB,EAAEmL,EAAG,CAAC,CACtC,CAEA,SAAS3F,GAAmBxF,EAAGmL,EAAG,CAChC,OAAOrF,GAAmB9F,EAAGmL,CAAC,EAAI,KACpC,CAEA,SAASpF,GAAkB/F,EAAGmL,EAAG,CAC/B,OAAOR,EAAI3K,EAAE,SAAQ,EAAK,EAAGmL,EAAG,CAAC,CACnC,CAEA,SAASnF,GAAchG,EAAGmL,EAAG,CAC3B,OAAOR,EAAI3K,EAAE,WAAY,EAAEmL,EAAG,CAAC,CACjC,CAEA,SAAS9E,GAAcrG,EAAGmL,EAAG,CAC3B,OAAOR,EAAI3K,EAAE,WAAY,EAAEmL,EAAG,CAAC,CACjC,CAEA,SAAS7E,GAA0BtG,EAAG,CACpC,IAAI0C,EAAM1C,EAAE,SACZ,OAAO0C,IAAQ,EAAI,EAAIA,CACzB,CAEA,SAAS6D,GAAuBvG,EAAGmL,EAAG,CACpC,OAAOR,EAAIxJ,GAAW,MAAMiB,EAASpC,CAAC,EAAI,EAAGA,CAAC,EAAGmL,EAAG,CAAC,CACvD,CAEA,SAASY,GAAK/L,EAAG,CACf,IAAI0C,EAAM1C,EAAE,SACZ,OAAQ0C,GAAO,GAAKA,IAAQ,EAAKnB,EAAavB,CAAC,EAAIuB,EAAa,KAAKvB,CAAC,CACxE,CAEA,SAASwG,GAAoBxG,EAAGmL,EAAG,CACjC,OAAAnL,EAAI+L,GAAK/L,CAAC,EACH2K,EAAIpJ,EAAa,MAAMa,EAASpC,CAAC,EAAGA,CAAC,GAAKoC,EAASpC,CAAC,EAAE,OAAQ,IAAK,GAAImL,EAAG,CAAC,CACpF,CAEA,SAAS1E,GAA0BzG,EAAG,CACpC,OAAOA,EAAE,QACX,CAEA,SAAS0G,GAAuB1G,EAAGmL,EAAG,CACpC,OAAOR,EAAIvJ,GAAW,MAAMgB,EAASpC,CAAC,EAAI,EAAGA,CAAC,EAAGmL,EAAG,CAAC,CACvD,CAEA,SAASxE,GAAW3G,EAAGmL,EAAG,CACxB,OAAOR,EAAI3K,EAAE,YAAW,EAAK,IAAKmL,EAAG,CAAC,CACxC,CAEA,SAAS1F,GAAczF,EAAGmL,EAAG,CAC3B,OAAAnL,EAAI+L,GAAK/L,CAAC,EACH2K,EAAI3K,EAAE,YAAW,EAAK,IAAKmL,EAAG,CAAC,CACxC,CAEA,SAASvE,GAAe5G,EAAGmL,EAAG,CAC5B,OAAOR,EAAI3K,EAAE,YAAW,EAAK,IAAOmL,EAAG,CAAC,CAC1C,CAEA,SAASzF,GAAkB1F,EAAGmL,EAAG,CAC/B,IAAIzI,EAAM1C,EAAE,SACZ,OAAAA,EAAK0C,GAAO,GAAKA,IAAQ,EAAKnB,EAAavB,CAAC,EAAIuB,EAAa,KAAKvB,CAAC,EAC5D2K,EAAI3K,EAAE,YAAW,EAAK,IAAOmL,EAAG,CAAC,CAC1C,CAEA,SAAStE,GAAW7G,EAAG,CACrB,IAAI3C,EAAI2C,EAAE,oBACV,OAAQ3C,EAAI,EAAI,KAAOA,GAAK,GAAI,MAC1BsN,EAAItN,EAAI,GAAK,EAAG,IAAK,CAAC,EACtBsN,EAAItN,EAAI,GAAI,IAAK,CAAC,CAC1B,CAEA,SAAS+J,GAAoBpH,EAAGmL,EAAG,CACjC,OAAOR,EAAI3K,EAAE,WAAY,EAAEmL,EAAG,CAAC,CACjC,CAEA,SAAS3D,GAAgBxH,EAAGmL,EAAG,CAC7B,OAAOR,EAAI3K,EAAE,YAAa,EAAEmL,EAAG,CAAC,CAClC,CAEA,SAAS1D,GAAgBzH,EAAGmL,EAAG,CAC7B,OAAOR,EAAI3K,EAAE,YAAa,EAAG,IAAM,GAAImL,EAAG,CAAC,CAC7C,CAEA,SAASzD,GAAmB1H,EAAGmL,EAAG,CAChC,OAAOR,EAAI,EAAI5J,GAAO,MAAMsB,EAAQrC,CAAC,EAAGA,CAAC,EAAGmL,EAAG,CAAC,CAClD,CAEA,SAASxD,GAAsB3H,EAAGmL,EAAG,CACnC,OAAOR,EAAI3K,EAAE,mBAAoB,EAAEmL,EAAG,CAAC,CACzC,CAEA,SAAS9D,GAAsBrH,EAAGmL,EAAG,CACnC,OAAOxD,GAAsB3H,EAAGmL,CAAC,EAAI,KACvC,CAEA,SAASvD,GAAqB5H,EAAGmL,EAAG,CAClC,OAAOR,EAAI3K,EAAE,YAAW,EAAK,EAAGmL,EAAG,CAAC,CACtC,CAEA,SAAStD,GAAiB7H,EAAGmL,EAAG,CAC9B,OAAOR,EAAI3K,EAAE,cAAe,EAAEmL,EAAG,CAAC,CACpC,CAEA,SAASnD,GAAiBhI,EAAGmL,EAAG,CAC9B,OAAOR,EAAI3K,EAAE,cAAe,EAAEmL,EAAG,CAAC,CACpC,CAEA,SAASlD,GAA6BjI,EAAG,CACvC,IAAIgM,EAAMhM,EAAE,YACZ,OAAOgM,IAAQ,EAAI,EAAIA,CACzB,CAEA,SAAS9D,GAA0BlI,EAAGmL,EAAG,CACvC,OAAOR,EAAIhJ,GAAU,MAAMU,EAAQrC,CAAC,EAAI,EAAGA,CAAC,EAAGmL,EAAG,CAAC,CACrD,CAEA,SAASc,GAAQjM,EAAG,CAClB,IAAI0C,EAAM1C,EAAE,YACZ,OAAQ0C,GAAO,GAAKA,IAAQ,EAAKX,EAAY/B,CAAC,EAAI+B,EAAY,KAAK/B,CAAC,CACtE,CAEA,SAASmI,GAAuBnI,EAAGmL,EAAG,CACpC,OAAAnL,EAAIiM,GAAQjM,CAAC,EACN2K,EAAI5I,EAAY,MAAMM,EAAQrC,CAAC,EAAGA,CAAC,GAAKqC,EAAQrC,CAAC,EAAE,UAAW,IAAK,GAAImL,EAAG,CAAC,CACpF,CAEA,SAAS/C,GAA6BpI,EAAG,CACvC,OAAOA,EAAE,WACX,CAEA,SAASqI,GAA0BrI,EAAGmL,EAAG,CACvC,OAAOR,EAAI/I,GAAU,MAAMS,EAAQrC,CAAC,EAAI,EAAGA,CAAC,EAAGmL,EAAG,CAAC,CACrD,CAEA,SAAS7C,GAActI,EAAGmL,EAAG,CAC3B,OAAOR,EAAI3K,EAAE,eAAc,EAAK,IAAKmL,EAAG,CAAC,CAC3C,CAEA,SAAS7D,GAAiBtH,EAAGmL,EAAG,CAC9B,OAAAnL,EAAIiM,GAAQjM,CAAC,EACN2K,EAAI3K,EAAE,eAAc,EAAK,IAAKmL,EAAG,CAAC,CAC3C,CAEA,SAAS5C,GAAkBvI,EAAGmL,EAAG,CAC/B,OAAOR,EAAI3K,EAAE,eAAc,EAAK,IAAOmL,EAAG,CAAC,CAC7C,CAEA,SAAS5D,GAAqBvH,EAAGmL,EAAG,CAClC,IAAIzI,EAAM1C,EAAE,YACZ,OAAAA,EAAK0C,GAAO,GAAKA,IAAQ,EAAKX,EAAY/B,CAAC,EAAI+B,EAAY,KAAK/B,CAAC,EAC1D2K,EAAI3K,EAAE,eAAc,EAAK,IAAOmL,EAAG,CAAC,CAC7C,CAEA,SAAS3C,IAAgB,CACvB,MAAO,OACT,CAEA,SAAS1B,IAAuB,CAC9B,MAAO,GACT,CAEA,SAASX,GAAoBnG,EAAG,CAC9B,MAAO,CAACA,CACV,CAEA,SAASoG,GAA2BpG,EAAG,CACrC,OAAO,KAAK,MAAM,CAACA,EAAI,GAAI,CAC7B,CCtrBA,IAAI6D,EACOqI,GACAC,GACAC,GACAC,GAEXC,GAAc,CACZ,SAAU,SACV,KAAM,aACN,KAAM,eACN,QAAS,CAAC,KAAM,IAAI,EACpB,KAAM,CAAC,SAAU,SAAU,UAAW,YAAa,WAAY,SAAU,UAAU,EACnF,UAAW,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAK,EAC3D,OAAQ,CAAC,UAAW,WAAY,QAAS,QAAS,MAAO,OAAQ,OAAQ,SAAU,YAAa,UAAW,WAAY,UAAU,EACjI,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAK,CAClG,CAAC,EAEc,SAASA,GAAcC,EAAY,CAChD,OAAA1I,EAASD,GAAa2I,CAAU,EAChCL,GAAarI,EAAO,OACpBsI,GAAYtI,EAAO,MACnBuI,GAAYvI,EAAO,UACnBwI,GAAWxI,EAAO,SACXA,CACT,CCpBA,SAASrE,GAAKxB,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,CACnB,CAEA,SAASwO,GAAOxO,EAAG,CACjB,OAAOA,aAAa,KAAO,CAACA,EAAI,CAAC,IAAI,KAAK,CAACA,CAAC,CAC9C,CAEO,SAASyO,GAAS3J,EAAOE,EAAcT,EAAMC,EAAOC,EAAMC,EAAKC,EAAMC,EAAQnC,EAAQmK,EAAQ,CAClG,IAAI8B,EAAQC,GAAY,EACpBC,EAASF,EAAM,OACf7N,EAAS6N,EAAM,OAEfG,EAAoBjC,EAAO,KAAK,EAChCkC,EAAelC,EAAO,KAAK,EAC3BmC,EAAenC,EAAO,OAAO,EAC7BoC,EAAapC,EAAO,OAAO,EAC3BqC,GAAYrC,EAAO,OAAO,EAC1BsC,GAAatC,EAAO,OAAO,EAC3BtF,EAAcsF,EAAO,IAAI,EACzBjE,EAAaiE,EAAO,IAAI,EAE5B,SAASuC,GAAW3N,EAAM,CACxB,OAAQiB,EAAOjB,CAAI,EAAIA,EAAOqN,EACxBjK,EAAOpD,CAAI,EAAIA,EAAOsN,EACtBnK,EAAKnD,CAAI,EAAIA,EAAOuN,EACpBrK,EAAIlD,CAAI,EAAIA,EAAOwN,EACnBxK,EAAMhD,CAAI,EAAIA,EAAQiD,EAAKjD,CAAI,EAAIA,EAAOyN,GAAYC,GACtD3K,EAAK/C,CAAI,EAAIA,EAAO8F,EACpBqB,GAAYnH,CAAI,CACvB,CAED,OAAAkN,EAAM,OAAS,SAASxP,EAAG,CACzB,OAAO,IAAI,KAAK0P,EAAO1P,CAAC,CAAC,CAC7B,EAEEwP,EAAM,OAAS,SAASU,EAAG,CACzB,OAAO,UAAU,OAASvO,EAAO,MAAM,KAAKuO,EAAGZ,EAAM,CAAC,EAAI3N,EAAM,EAAG,IAAIW,EAAI,CAC/E,EAEEkN,EAAM,MAAQ,SAAS5N,EAAU,CAC/B,IAAIkB,EAAInB,IACR,OAAOiE,EAAM9C,EAAE,CAAC,EAAGA,EAAEA,EAAE,OAAS,CAAC,EAAGlB,GAAmB,EAAa,CACxE,EAEE4N,EAAM,WAAa,SAASpN,EAAOiL,EAAW,CAC5C,OAAOA,GAAa,KAAO4C,GAAavC,EAAOL,CAAS,CAC5D,EAEEmC,EAAM,KAAO,SAAS5N,EAAU,CAC9B,IAAIkB,EAAInB,IACR,OAAI,CAACC,GAAY,OAAOA,EAAS,OAAU,cAAYA,EAAWkE,EAAahD,EAAE,CAAC,EAAGA,EAAEA,EAAE,OAAS,CAAC,EAAGlB,GAAmB,EAAa,GAC/HA,EAAWD,EAAOD,GAAKoB,EAAGlB,CAAQ,CAAC,EAAI4N,CAClD,EAEEA,EAAM,KAAO,UAAW,CACtB,OAAOW,GAAKX,EAAOD,GAAS3J,EAAOE,EAAcT,EAAMC,EAAOC,EAAMC,EAAKC,EAAMC,EAAQnC,EAAQmK,CAAM,CAAC,CAC1G,EAES8B,CACT,CAEe,SAASY,IAAO,CAC7B,OAAOC,GAAU,MAAMd,GAASnJ,GAAWC,GAAkBnB,EAAUF,GAAWsL,GAAU1M,EAASF,GAAUF,GAAY+M,EAAYvB,EAAU,EAAE,OAAO,CAAC,IAAI,KAAK,IAAM,EAAG,CAAC,EAAG,IAAI,KAAK,IAAM,EAAG,CAAC,CAAC,CAAC,EAAG,SAAS,CACpN", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}