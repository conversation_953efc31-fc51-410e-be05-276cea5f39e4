{"version": 3, "file": "yaml-DsCXHVTH.js", "sources": ["../../../../node_modules/.pnpm/@codemirror+legacy-modes@6.4.3/node_modules/@codemirror/legacy-modes/mode/yaml.js"], "sourcesContent": ["var cons = ['true', 'false', 'on', 'off', 'yes', 'no'];\nvar keywordRegex = new RegExp(\"\\\\b((\"+cons.join(\")|(\")+\"))$\", 'i');\n\nexport const yaml = {\n  name: \"yaml\",\n  token: function(stream, state) {\n    var ch = stream.peek();\n    var esc = state.escaped;\n    state.escaped = false;\n    /* comments */\n    if (ch == \"#\" && (stream.pos == 0 || /\\s/.test(stream.string.charAt(stream.pos - 1)))) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n\n    if (stream.match(/^('([^']|\\\\.)*'?|\"([^\"]|\\\\.)*\"?)/))\n      return \"string\";\n\n    if (state.literal && stream.indentation() > state.keyCol) {\n      stream.skipToEnd(); return \"string\";\n    } else if (state.literal) { state.literal = false; }\n    if (stream.sol()) {\n      state.keyCol = 0;\n      state.pair = false;\n      state.pairStart = false;\n      /* document start */\n      if(stream.match('---')) { return \"def\"; }\n      /* document end */\n      if (stream.match('...')) { return \"def\"; }\n      /* array list item */\n      if (stream.match(/^\\s*-\\s+/)) { return 'meta'; }\n    }\n    /* inline pairs/lists */\n    if (stream.match(/^(\\{|\\}|\\[|\\])/)) {\n      if (ch == '{')\n        state.inlinePairs++;\n      else if (ch == '}')\n        state.inlinePairs--;\n      else if (ch == '[')\n        state.inlineList++;\n      else\n        state.inlineList--;\n      return 'meta';\n    }\n\n    /* list separator */\n    if (state.inlineList > 0 && !esc && ch == ',') {\n      stream.next();\n      return 'meta';\n    }\n    /* pairs separator */\n    if (state.inlinePairs > 0 && !esc && ch == ',') {\n      state.keyCol = 0;\n      state.pair = false;\n      state.pairStart = false;\n      stream.next();\n      return 'meta';\n    }\n\n    /* start of value of a pair */\n    if (state.pairStart) {\n      /* block literals */\n      if (stream.match(/^\\s*(\\||\\>)\\s*/)) { state.literal = true; return 'meta'; };\n      /* references */\n      if (stream.match(/^\\s*(\\&|\\*)[a-z0-9\\._-]+\\b/i)) { return 'variable'; }\n      /* numbers */\n      if (state.inlinePairs == 0 && stream.match(/^\\s*-?[0-9\\.\\,]+\\s?$/)) { return 'number'; }\n      if (state.inlinePairs > 0 && stream.match(/^\\s*-?[0-9\\.\\,]+\\s?(?=(,|}))/)) { return 'number'; }\n      /* keywords */\n      if (stream.match(keywordRegex)) { return 'keyword'; }\n    }\n\n    /* pairs (associative arrays) -> key */\n    if (!state.pair && stream.match(/^\\s*(?:[,\\[\\]{}&*!|>'\"%@`][^\\s'\":]|[^,\\[\\]{}#&*!|>'\"%@`])[^#]*?(?=\\s*:($|\\s))/)) {\n      state.pair = true;\n      state.keyCol = stream.indentation();\n      return \"atom\";\n    }\n    if (state.pair && stream.match(/^:\\s*/)) { state.pairStart = true; return 'meta'; }\n\n    /* nothing found, continue */\n    state.pairStart = false;\n    state.escaped = (ch == '\\\\');\n    stream.next();\n    return null;\n  },\n  startState: function() {\n    return {\n      pair: false,\n      pairStart: false,\n      keyCol: 0,\n      inlinePairs: 0,\n      inlineList: 0,\n      literal: false,\n      escaped: false\n    };\n  },\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": ["cons", "keywordRegex", "yaml", "stream", "state", "ch", "esc"], "mappings": "AAAA,IAAIA,EAAO,CAAC,OAAQ,QAAS,KAAM,MAAO,MAAO,IAAI,EACjDC,EAAe,IAAI,OAAO,QAAQD,EAAK,KAAK,KAAK,EAAE,MAAO,GAAG,EAErD,MAACE,EAAO,CAClB,KAAM,OACN,MAAO,SAASC,EAAQC,EAAO,CAC7B,IAAIC,EAAKF,EAAO,OACZG,EAAMF,EAAM,QAGhB,GAFAA,EAAM,QAAU,GAEZC,GAAM,MAAQF,EAAO,KAAO,GAAK,KAAK,KAAKA,EAAO,OAAO,OAAOA,EAAO,IAAM,CAAC,CAAC,GACjF,OAAAA,EAAO,UAAS,EACT,UAGT,GAAIA,EAAO,MAAM,kCAAkC,EACjD,MAAO,SAET,GAAIC,EAAM,SAAWD,EAAO,YAAa,EAAGC,EAAM,OAChD,OAAAD,EAAO,UAAS,EAAW,SAE7B,GADWC,EAAM,UAAWA,EAAM,QAAU,IACxCD,EAAO,MAAO,CAOhB,GANAC,EAAM,OAAS,EACfA,EAAM,KAAO,GACbA,EAAM,UAAY,GAEfD,EAAO,MAAM,KAAK,GAEjBA,EAAO,MAAM,KAAK,EAAK,MAAO,MAElC,GAAIA,EAAO,MAAM,UAAU,EAAK,MAAO,MACxC,CAED,GAAIA,EAAO,MAAM,gBAAgB,EAC/B,OAAIE,GAAM,IACRD,EAAM,cACCC,GAAM,IACbD,EAAM,cACCC,GAAM,IACbD,EAAM,aAENA,EAAM,aACD,OAIT,GAAIA,EAAM,WAAa,GAAK,CAACE,GAAOD,GAAM,IACxC,OAAAF,EAAO,KAAI,EACJ,OAGT,GAAIC,EAAM,YAAc,GAAK,CAACE,GAAOD,GAAM,IACzC,OAAAD,EAAM,OAAS,EACfA,EAAM,KAAO,GACbA,EAAM,UAAY,GAClBD,EAAO,KAAI,EACJ,OAIT,GAAIC,EAAM,UAAW,CAEnB,GAAID,EAAO,MAAM,gBAAgB,EAAK,OAAAC,EAAM,QAAU,GAAa,OAEnE,GAAID,EAAO,MAAM,6BAA6B,EAAK,MAAO,WAG1D,GADIC,EAAM,aAAe,GAAKD,EAAO,MAAM,sBAAsB,GAC7DC,EAAM,YAAc,GAAKD,EAAO,MAAM,8BAA8B,EAAK,MAAO,SAEpF,GAAIA,EAAO,MAAMF,CAAY,EAAK,MAAO,SAC1C,CAGD,MAAI,CAACG,EAAM,MAAQD,EAAO,MAAM,+EAA+E,GAC7GC,EAAM,KAAO,GACbA,EAAM,OAASD,EAAO,cACf,QAELC,EAAM,MAAQD,EAAO,MAAM,OAAO,GAAKC,EAAM,UAAY,GAAa,SAG1EA,EAAM,UAAY,GAClBA,EAAM,QAAWC,GAAM,KACvBF,EAAO,KAAI,EACJ,KACR,EACD,WAAY,UAAW,CACrB,MAAO,CACL,KAAM,GACN,UAAW,GACX,OAAQ,EACR,YAAa,EACb,WAAY,EACZ,QAAS,GACT,QAAS,EACf,CACG,EACD,aAAc,CACZ,cAAe,CAAC,KAAM,GAAG,CAC1B,CACH", "x_google_ignoreList": [0]}