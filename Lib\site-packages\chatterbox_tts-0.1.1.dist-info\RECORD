chatterbox/__init__.py,sha256=G7pCEKWSBQeJapa5aiZs5jCuGBZNOqPus1So-L5twek,60
chatterbox/__pycache__/__init__.cpython-311.pyc,,
chatterbox/__pycache__/tts.cpython-311.pyc,,
chatterbox/__pycache__/vc.cpython-311.pyc,,
chatterbox/models/s3gen/__init__.py,sha256=HrmLeFxIFQLlFWdtoOZa-_2PoiVK4wO2-pcU22pScww,68
chatterbox/models/s3gen/__pycache__/__init__.cpython-311.pyc,,
chatterbox/models/s3gen/__pycache__/const.cpython-311.pyc,,
chatterbox/models/s3gen/__pycache__/decoder.cpython-311.pyc,,
chatterbox/models/s3gen/__pycache__/f0_predictor.cpython-311.pyc,,
chatterbox/models/s3gen/__pycache__/flow.cpython-311.pyc,,
chatterbox/models/s3gen/__pycache__/flow_matching.cpython-311.pyc,,
chatterbox/models/s3gen/__pycache__/hifigan.cpython-311.pyc,,
chatterbox/models/s3gen/__pycache__/s3gen.cpython-311.pyc,,
chatterbox/models/s3gen/__pycache__/xvector.cpython-311.pyc,,
chatterbox/models/s3gen/const.py,sha256=n6hu6U3jNUuOMvjQnZZ0iRapql0jJi5KJ-qDdvsOFlo,17
chatterbox/models/s3gen/decoder.py,sha256=pUjwl6FcuJKJtJiXj3h2za39mZBL1OSPzqUiWqmaJL4,12709
chatterbox/models/s3gen/f0_predictor.py,sha256=kXgl7sNTuWAbn3rwwONgPrEGHYtkFTkiXnjJENe8IlU,1993
chatterbox/models/s3gen/flow.py,sha256=YwXF1LbpcnxlsuOlb-2vqx55hZW_XzZERYvhWURZ6Wk,10655
chatterbox/models/s3gen/flow_matching.py,sha256=WJjSo-pBIwEmF3ynM58Z69ezKfV1siD6FMurgt70Xg0,10074
chatterbox/models/s3gen/hifigan.py,sha256=5J9HIzI5N0k-z19sfxk09Xdm70bHTq98YL9Fahxv6Jc,17652
chatterbox/models/s3gen/matcha/__pycache__/decoder.cpython-311.pyc,,
chatterbox/models/s3gen/matcha/__pycache__/flow_matching.cpython-311.pyc,,
chatterbox/models/s3gen/matcha/__pycache__/text_encoder.cpython-311.pyc,,
chatterbox/models/s3gen/matcha/__pycache__/transformer.cpython-311.pyc,,
chatterbox/models/s3gen/matcha/decoder.py,sha256=qfNd97PrHH3vuHRGJrMu8FBKUZoGbzJA_ekDk6ObNIg,14435
chatterbox/models/s3gen/matcha/flow_matching.py,sha256=VmFLkqrVL3E8GYrgidR-an3xNFDzz10RRPZfs0w1EH0,4556
chatterbox/models/s3gen/matcha/text_encoder.py,sha256=oemTrSGdld7oZOAQ2EFecFerthMaIVUAk9g9sCebAcs,14965
chatterbox/models/s3gen/matcha/transformer.py,sha256=Zv8gktl0qZslG52OXAi7zszq7fr0HivYrytLx6Vty-o,13237
chatterbox/models/s3gen/s3gen.py,sha256=pRk1SDJLKEkkDzYy40fY9-tOjwmi4bttuWgzI1HlSUQ,11060
chatterbox/models/s3gen/transformer/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chatterbox/models/s3gen/transformer/__pycache__/__init__.cpython-311.pyc,,
chatterbox/models/s3gen/transformer/__pycache__/activation.cpython-311.pyc,,
chatterbox/models/s3gen/transformer/__pycache__/attention.cpython-311.pyc,,
chatterbox/models/s3gen/transformer/__pycache__/convolution.cpython-311.pyc,,
chatterbox/models/s3gen/transformer/__pycache__/embedding.cpython-311.pyc,,
chatterbox/models/s3gen/transformer/__pycache__/encoder_layer.cpython-311.pyc,,
chatterbox/models/s3gen/transformer/__pycache__/positionwise_feed_forward.cpython-311.pyc,,
chatterbox/models/s3gen/transformer/__pycache__/subsampling.cpython-311.pyc,,
chatterbox/models/s3gen/transformer/__pycache__/upsample_encoder.cpython-311.pyc,,
chatterbox/models/s3gen/transformer/activation.py,sha256=pKlsrqn3sFERKG3l6nYL39-cTlNEj1NCCFfcBKUEQMI,3089
chatterbox/models/s3gen/transformer/attention.py,sha256=KMOPHj6hrqwVJumPg0nZ8GjiXx9q4kbGC63D1UDWJ5Q,14415
chatterbox/models/s3gen/transformer/convolution.py,sha256=619B8ySpciXHO5xDCvi7IxvXc4bvGEULsP0yn0aatOE,5230
chatterbox/models/s3gen/transformer/embedding.py,sha256=NkbnSHg_5sBptCTMnL_oG8VKXM_qGucEHfOYpgPMzT4,11399
chatterbox/models/s3gen/transformer/encoder_layer.py,sha256=GSBYK-LJt894Nee1ORGOweudqPLHEcYlf4WYs3kpUbk,9602
chatterbox/models/s3gen/transformer/positionwise_feed_forward.py,sha256=boA447zIyght3KUI-5udQL86uYvrq89clJNdAyMp0Pg,4219
chatterbox/models/s3gen/transformer/subsampling.py,sha256=MfwDR6hRq8EgXf1M9oCZwMQWWJw-maB7JQ6GMM7OGdA,12666
chatterbox/models/s3gen/transformer/upsample_encoder.py,sha256=F5T_AbZ4eRrWZdewwJmUSOdg_B3LO8ggjeX1hRDB9ow,13694
chatterbox/models/s3gen/utils/__pycache__/class_utils.cpython-311.pyc,,
chatterbox/models/s3gen/utils/__pycache__/mask.cpython-311.pyc,,
chatterbox/models/s3gen/utils/__pycache__/mel.cpython-311.pyc,,
chatterbox/models/s3gen/utils/class_utils.py,sha256=AvPLOlXuEkwnl3ckWwkGH6FagYLm59d2L4-kXApjGUg,2334
chatterbox/models/s3gen/utils/mask.py,sha256=6uQPJ001lBt4lWs-H8izamma4vmLKAPUc1CJyvtVmO0,7637
chatterbox/models/s3gen/utils/mel.py,sha256=OnjR5GtIndnMvu21J6A0Aj6Qfx2wPda23_dx4yc4jX8,2364
chatterbox/models/s3gen/xvector.py,sha256=x8dZkgy_GqyfEuChXBvz042lHKnT6np7U890DIb-F_o,14141
chatterbox/models/s3tokenizer/__init__.py,sha256=c1fOmgNJuuUejoyrzdAbqFNN1qI3PB4JIpHstxZBT-A,595
chatterbox/models/s3tokenizer/__pycache__/__init__.cpython-311.pyc,,
chatterbox/models/s3tokenizer/__pycache__/s3tokenizer.cpython-311.pyc,,
chatterbox/models/s3tokenizer/s3tokenizer.py,sha256=durp-VsUYIxIR8nXrPzwgTjDZWQLDUWJ3gHnQrDl60M,5155
chatterbox/models/t3/__init__.py,sha256=z-zBx999IjmBV9QGBav6ZPMO-baXGQ3E9Xsikpn3BTg,19
chatterbox/models/t3/__pycache__/__init__.cpython-311.pyc,,
chatterbox/models/t3/__pycache__/llama_configs.cpython-311.pyc,,
chatterbox/models/t3/__pycache__/t3.cpython-311.pyc,,
chatterbox/models/t3/inference/__pycache__/alignment_stream_analyzer.cpython-311.pyc,,
chatterbox/models/t3/inference/__pycache__/t3_hf_backend.cpython-311.pyc,,
chatterbox/models/t3/inference/alignment_stream_analyzer.py,sha256=e9vmXVoK20hzKAzcnyXNHknnA-eKMyGKifn7oKs_9gw,6850
chatterbox/models/t3/inference/t3_hf_backend.py,sha256=qHrgRNl3dYmsUxNguz-Qlq3-ckz4S5LKb0MG9AXeYd0,4271
chatterbox/models/t3/llama_configs.py,sha256=Ahl8MW0CNZqW4c9sPHqwz1XsFF4WVSTp7db-kDhKT3Q,1005
chatterbox/models/t3/modules/__pycache__/cond_enc.cpython-311.pyc,,
chatterbox/models/t3/modules/__pycache__/learned_pos_emb.cpython-311.pyc,,
chatterbox/models/t3/modules/__pycache__/perceiver.cpython-311.pyc,,
chatterbox/models/t3/modules/__pycache__/t3_config.cpython-311.pyc,,
chatterbox/models/t3/modules/cond_enc.py,sha256=2BKIAwkUIxQQUcryld5iCBE9qYf-U33lVmxLhOGd-Ec,3293
chatterbox/models/t3/modules/learned_pos_emb.py,sha256=LsWwnOlNcSYenCWDOgj2Zr1uLVQ5sjFBI-A1wC34t68,1099
chatterbox/models/t3/modules/perceiver.py,sha256=-cSwEwZS6U0Fhz6tkx7NOZqd1kS3oX7fG3bX1_K_v0I,7719
chatterbox/models/t3/modules/t3_config.py,sha256=6MLr-S3jjiVMm71a6Bq6PZUx69fX0kEB0c3kd5cwfDg,647
chatterbox/models/t3/t3.py,sha256=XsnAURvYBiJKhuFj6zzDiHpGrgKxbAlLIur5mLkz3rg,14530
chatterbox/models/tokenizers/__init__.py,sha256=gbKCJQwD_r0P5vF6CGvBaIluJy_NTkNj2wGynG-wWdU,35
chatterbox/models/tokenizers/__pycache__/__init__.cpython-311.pyc,,
chatterbox/models/tokenizers/__pycache__/tokenizer.cpython-311.pyc,,
chatterbox/models/tokenizers/tokenizer.py,sha256=7rjUZMy0Xs_p1hELRGo_B5itxTEtfd9MqsAO9HoFxM0,1367
chatterbox/models/voice_encoder/__init__.py,sha256=rfG873TpDa4Py1wpRRA8vAvZrxXMm-Qoj5YK19Is5ng,56
chatterbox/models/voice_encoder/__pycache__/__init__.cpython-311.pyc,,
chatterbox/models/voice_encoder/__pycache__/config.cpython-311.pyc,,
chatterbox/models/voice_encoder/__pycache__/melspec.cpython-311.pyc,,
chatterbox/models/voice_encoder/__pycache__/voice_encoder.cpython-311.pyc,,
chatterbox/models/voice_encoder/config.py,sha256=C0HXlKFv2Ukps3_SKdxh02NBeKEQRqa1g04QkQmo48g,406
chatterbox/models/voice_encoder/melspec.py,sha256=D1_qFzhBxRUPpuqSNhYHmMjuJK-6ck1l2mHc54pNSOg,1940
chatterbox/models/voice_encoder/voice_encoder.py,sha256=SxLp1Jy7fOgeZzHALU83qbdj73QZ26lafmbqB41JqcM,10555
chatterbox/tts.py,sha256=cQkPVSqgZw3-SvzQR5YcnwhVb7qIWNDm557n2waqWBY,7711
chatterbox/vc.py,sha256=Ye9US7-7DLy9LPQPztAt3UHT2UlR43rjP2pm-YgqSaQ,2701
chatterbox_tts-0.1.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
chatterbox_tts-0.1.1.dist-info/METADATA,sha256=CTT4EWudKoqrsijprkxhPsKbai8Kl7kZcQcaRfaDHmw,5896
chatterbox_tts-0.1.1.dist-info/RECORD,,
chatterbox_tts-0.1.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chatterbox_tts-0.1.1.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
chatterbox_tts-0.1.1.dist-info/licenses/LICENSE,sha256=QkjpEKkohJ_lgVoPkjbhf6B3aNlbkZMhJ1LEZLk9bKo,1067
chatterbox_tts-0.1.1.dist-info/top_level.txt,sha256=rBjAeGn5vfA69esbcnInQtvn4XUfS4U-hxvZi63HDDg,11
