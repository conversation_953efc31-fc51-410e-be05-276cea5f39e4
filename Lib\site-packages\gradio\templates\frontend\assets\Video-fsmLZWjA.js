const{SvelteComponent:d,append:l,attr:t,detach:h,init:p,insert:g,noop:i,safe_not_equal:w,svg_element:s}=window.__gradio__svelte__internal;function _(a){let e,n,o;return{c(){e=s("svg"),n=s("polygon"),o=s("rect"),t(n,"points","23 7 16 12 23 17 23 7"),t(o,"x","1"),t(o,"y","5"),t(o,"width","15"),t(o,"height","14"),t(o,"rx","2"),t(o,"ry","2"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-video")},m(r,c){g(r,e,c),l(e,n),l(e,o)},p:i,i,o:i,d(r){r&&h(e)}}}class u extends d{constructor(e){super(),p(this,e,null,_,w,{})}}export{u as V};
//# sourceMappingURL=Video-fsmLZWjA.js.map
