import{h as Gn,i as ie,a as I,w as xo,s as fi,$ as B,b as X,c as di,d as N,e as ce,l as Ep,W as $p,f as wp,m as Rr,g as pi,j as Cp,p as Np,v as Fp,k as Wl,n as kp,o as Tp,H as Ap,q as Ns}from"./vega-tooltip.module-D8LSd2Om.js";import"./index-CKzrTzGp.js";import"./svelte/svelte.js";import"./time-Bgyi_H-V.js";import"./step-Ce-xBr2D.js";import"./linear-CV3SENcB.js";import"./init-Dmth1JHB.js";import"./dsv-DB8NKgIY.js";import"./range-OtVwhkKS.js";import"./ordinal-BeghXfj9.js";import"./arc-Ctxh2KTd.js";import"./dispatch-kxCwF96_.js";const Op="vega-lite",_p='<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> "<PERSON>" <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>er',Rp="5.17.0",Ip=["Kanit <PERSON>suphasawat (http://kanitw.yellowpigz.com)","Dominik Moritz (https://www.domoritz.de)","Arvind Satyanarayan (https://arvindsatya.com)","Jeffrey Heer (https://jheer.org)"],Lp="https://vega.github.io/vega-lite/",Pp="Vega-Lite is a concise high-level language for interactive visualization.",zp=["vega","chart","visualization"],Dp="build/vega-lite.js",jp="build/vega-lite.min.js",Mp="build/vega-lite.min.js",Up="build/src/index",Bp="build/src/index.d.ts",Wp={vl2pdf:"./bin/vl2pdf",vl2png:"./bin/vl2png",vl2svg:"./bin/vl2svg",vl2vg:"./bin/vl2vg"},Gp=["bin","build","src","vega-lite*","tsconfig.json"],Hp={changelog:"conventional-changelog -p angular -r 2",prebuild:"yarn clean:build",build:"yarn build:only","build:only":"tsc -p tsconfig.build.json && rollup -c","prebuild:examples":"yarn build:only","build:examples":"yarn data && TZ=America/Los_Angeles scripts/build-examples.sh","prebuild:examples-full":"yarn build:only","build:examples-full":"TZ=America/Los_Angeles scripts/build-examples.sh 1","build:example":"TZ=America/Los_Angeles scripts/build-example.sh","build:toc":"yarn build:jekyll && scripts/generate-toc","build:site":"rollup -c site/rollup.config.mjs","build:jekyll":"pushd site && bundle exec jekyll build -q && popd","build:versions":"scripts/update-version.sh",clean:"yarn clean:build && del-cli 'site/data/*' 'examples/compiled/*.png' && find site/examples ! -name 'index.md' ! -name 'data' -type f -delete","clean:build":"del-cli 'build/*' !build/vega-lite-schema.json",data:"rsync -r node_modules/vega-datasets/data/* site/data",schema:"mkdir -p build && ts-json-schema-generator -f tsconfig.json -p src/index.ts -t TopLevelSpec --no-type-check --no-ref-encode > build/vega-lite-schema.json && yarn renameschema && cp build/vega-lite-schema.json site/_data/",renameschema:"scripts/rename-schema.sh",presite:"yarn data && yarn schema && yarn build:site && yarn build:versions && scripts/create-example-pages.sh",site:"yarn site:only","site:only":"pushd site && bundle exec jekyll serve -I -l && popd",prettierbase:"prettier '**/*.{md,css,yml}'",format:"eslint . --fix && yarn prettierbase --write",lint:"eslint . && yarn prettierbase --check",test:"yarn jest test/ && yarn lint && yarn schema && yarn jest examples/ && yarn test:runtime","test:cover":"yarn jest --collectCoverage test/","test:inspect":"node --inspect-brk ./node_modules/.bin/jest --runInBand test","test:runtime":"TZ=America/Los_Angeles npx jest test-runtime/ --config test-runtime/jest-config.json","test:runtime:generate":"yarn build:only && del-cli test-runtime/resources && VL_GENERATE_TESTS=true yarn test:runtime",watch:"tsc -p tsconfig.build.json -w","watch:site":"yarn build:site -w","watch:test":"yarn jest --watch test/","watch:test:runtime":"TZ=America/Los_Angeles npx jest --watch test-runtime/ --config test-runtime/jest-config.json",release:"release-it"},qp={type:"git",url:"https://github.com/vega/vega-lite.git"},Vp="BSD-3-Clause",Xp={url:"https://github.com/vega/vega-lite/issues"},Yp={"@babel/core":"^7.23.9","@babel/preset-env":"^7.23.9","@babel/preset-typescript":"^7.23.3","@release-it/conventional-changelog":"^8.0.1","@rollup/plugin-alias":"^5.1.0","@rollup/plugin-babel":"^6.0.4","@rollup/plugin-commonjs":"^25.0.7","@rollup/plugin-json":"^6.1.0","@rollup/plugin-node-resolve":"^15.2.3","@rollup/plugin-terser":"^0.4.4","@types/d3":"^7.4.3","@types/jest":"^29.5.11","@types/pako":"^2.0.3","@typescript-eslint/eslint-plugin":"^6.20.0","@typescript-eslint/parser":"^6.20.0",ajv:"^8.12.0","ajv-formats":"^2.1.1",cheerio:"^1.0.0-rc.12","conventional-changelog-cli":"^4.1.0",d3:"^7.8.5","del-cli":"^5.1.0",eslint:"^8.56.0","eslint-config-prettier":"^9.1.0","eslint-plugin-jest":"^27.6.3","eslint-plugin-prettier":"^5.1.3","fast-json-stable-stringify":"~2.1.0","highlight.js":"^11.9.0",jest:"^29.7.0","jest-dev-server":"^9.0.2",mkdirp:"^3.0.1",pako:"^2.1.0",prettier:"^3.2.4",puppeteer:"^15.0.0","release-it":"^17.0.3",rollup:"^4.9.6","rollup-plugin-bundle-size":"^1.0.3",serve:"^14.2.1",terser:"^5.27.0","ts-jest":"^29.1.2","ts-json-schema-generator":"^1.5.0",typescript:"~5.3.3","vega-cli":"^5.27.0","vega-datasets":"^2.8.0","vega-embed":"^6.24.0","vega-tooltip":"^0.34.0","yaml-front-matter":"^4.1.1"},Kp={"json-stringify-pretty-compact":"~3.0.0",tslib:"~2.6.2","vega-event-selector":"~3.0.1","vega-expression":"~5.1.0","vega-util":"~1.17.2",yargs:"~17.7.2"},Qp={vega:"^5.24.0"},Jp={node:">=18"},Zp={name:Op,author:_p,version:Rp,collaborators:Ip,homepage:Lp,description:Pp,keywords:zp,main:Dp,unpkg:jp,jsdelivr:Mp,module:Up,types:Bp,bin:Wp,files:Gp,scripts:Hp,repository:qp,license:Vp,bugs:Xp,devDependencies:Yp,dependencies:Kp,peerDependencies:Qp,engines:Jp};function vo(e){return!!e.or}function So(e){return!!e.and}function Eo(e){return!!e.not}function gr(e,t){if(Eo(e))gr(e.not,t);else if(So(e))for(const n of e.and)gr(n,t);else if(vo(e))for(const n of e.or)gr(n,t);else t(e)}function Hn(e,t){return Eo(e)?{not:Hn(e.not,t)}:So(e)?{and:e.and.map(n=>Hn(n,t))}:vo(e)?{or:e.or.map(n=>Hn(n,t))}:t(e)}const z=structuredClone;function Gl(e){throw new Error(e)}function Qn(e,t){const n={};for(const i of t)Gn(e,i)&&(n[i]=e[i]);return n}function ke(e,t){const n={...e};for(const i of t)delete n[i];return n}Set.prototype.toJSON=function(){return`Set(${[...this].map(e=>Q(e)).join(",")})`};function W(e){if(ie(e))return e;const t=I(e)?e:Q(e);if(t.length<250)return t;let n=0;for(let i=0;i<t.length;i++){const r=t.charCodeAt(i);n=(n<<5)-n+r,n=n&n}return n}function Xs(e){return e===!1||e===null}function G(e,t){return e.includes(t)}function vn(e,t){let n=0;for(const[i,r]of e.entries())if(t(r,i,n++))return!0;return!1}function $o(e,t){let n=0;for(const[i,r]of e.entries())if(!t(r,i,n++))return!1;return!0}function Hl(e,...t){for(const n of t)eg(e,n??{});return e}function eg(e,t){for(const n of x(t))xo(e,n,t[n],!0)}function lt(e,t){const n=[],i={};let r;for(const s of e)r=t(s),!(r in i)&&(i[r]=1,n.push(s));return n}function tg(e,t){const n=x(e),i=x(t);if(n.length!==i.length)return!1;for(const r of n)if(e[r]!==t[r])return!1;return!0}function ql(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}function wo(e,t){for(const n of e)if(t.has(n))return!0;return!1}function Ys(e){const t=new Set;for(const n of e){const r=fi(n).map((o,a)=>a===0?o:`[${o}]`),s=r.map((o,a)=>r.slice(0,a+1).join(""));for(const o of s)t.add(o)}return t}function Co(e,t){return e===void 0||t===void 0?!0:wo(Ys(e),Ys(t))}function K(e){return x(e).length===0}const x=Object.keys,ve=Object.values,Wt=Object.entries;function Li(e){return e===!0||e===!1}function re(e){const t=e.replace(/\W/g,"_");return(e.match(/^\d+/)?"_":"")+t}function Oi(e,t){return Eo(e)?`!(${Oi(e.not,t)})`:So(e)?`(${e.and.map(n=>Oi(n,t)).join(") && (")})`:vo(e)?`(${e.or.map(n=>Oi(n,t)).join(") || (")})`:t(e)}function hr(e,t){if(t.length===0)return!0;const n=t.shift();return n in e&&hr(e[n],t)&&delete e[n],K(e)}function Wi(e){return e.charAt(0).toUpperCase()+e.substr(1)}function No(e,t="datum"){const n=fi(e),i=[];for(let r=1;r<=n.length;r++){const s=`[${n.slice(0,r).map(B).join("][")}]`;i.push(`${t}${s}`)}return i.join(" && ")}function Vl(e,t="datum"){return`${t}[${B(fi(e).join("."))}]`}function ng(e){return e.replace(/(\[|\]|\.|'|")/g,"\\$1")}function Ue(e){return`${fi(e).map(ng).join("\\.")}`}function Sn(e,t,n){return e.replace(new RegExp(t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"g"),n)}function Fo(e){return`${fi(e).join(".")}`}function Jn(e){return e?fi(e).length:0}function le(...e){for(const t of e)if(t!==void 0)return t}let Xl=42;function Yl(e){const t=++Xl;return e?String(e)+t:t}function ig(){Xl=42}function Kl(e){return Ql(e)?e:`__${e}`}function Ql(e){return e.startsWith("__")}function Pi(e){if(e!==void 0)return(e%360+360)%360}function Ir(e){return ie(e)?!0:!isNaN(e)&&!isNaN(parseFloat(e))}const tc=Object.getPrototypeOf(structuredClone({}));function Pe(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor.name!==t.constructor.name)return!1;let n,i;if(Array.isArray(e)){if(n=e.length,n!=t.length)return!1;for(i=n;i--!==0;)if(!Pe(e[i],t[i]))return!1;return!0}if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(i of e.entries())if(!t.has(i[0]))return!1;for(i of e.entries())if(!Pe(i[1],t.get(i[0])))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(i of e.entries())if(!t.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){if(n=e.length,n!=t.length)return!1;for(i=n;i--!==0;)if(e[i]!==t[i])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf&&e.valueOf!==tc.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString&&e.toString!==tc.toString)return e.toString()===t.toString();const r=Object.keys(e);if(n=r.length,n!==Object.keys(t).length)return!1;for(i=n;i--!==0;)if(!Object.prototype.hasOwnProperty.call(t,r[i]))return!1;for(i=n;i--!==0;){const s=r[i];if(!Pe(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Q(e){const t=[];return function n(i){if(i&&i.toJSON&&typeof i.toJSON=="function"&&(i=i.toJSON()),i===void 0)return;if(typeof i=="number")return isFinite(i)?""+i:"null";if(typeof i!="object")return JSON.stringify(i);let r,s;if(Array.isArray(i)){for(s="[",r=0;r<i.length;r++)r&&(s+=","),s+=n(i[r])||"null";return s+"]"}if(i===null)return"null";if(t.includes(i))throw new TypeError("Converting circular structure to JSON");const o=t.push(i)-1,a=Object.keys(i).sort();for(s="",r=0;r<a.length;r++){const c=a[r],l=n(i[c]);l&&(s&&(s+=","),s+=JSON.stringify(c)+":"+l)}return t.splice(o,1),`{${s}}`}(e)}const Nt="row",Ft="column",Lr="facet",se="x",be="y",et="x2",mt="y2",Kt="xOffset",gi="yOffset",tt="radius",_t="radius2",We="theta",Rt="theta2",nt="latitude",it="longitude",rt="latitude2",Be="longitude2",Ae="color",yt="fill",bt="stroke",Oe="shape",It="size",On="angle",Lt="opacity",Qt="fillOpacity",Jt="strokeOpacity",Zt="strokeWidth",en="strokeDash",Gi="text",Zn="order",Hi="detail",Pr="key",En="tooltip",zr="href",Dr="url",jr="description",rg={x:1,y:1,x2:1,y2:1},Jl={theta:1,theta2:1,radius:1,radius2:1};function Zl(e){return e in Jl}const ko={longitude:1,longitude2:1,latitude:1,latitude2:1};function eu(e){switch(e){case nt:return"y";case rt:return"y2";case it:return"x";case Be:return"x2"}}function tu(e){return e in ko}const sg=x(ko),To={...rg,...Jl,...ko,xOffset:1,yOffset:1,color:1,fill:1,stroke:1,opacity:1,fillOpacity:1,strokeOpacity:1,strokeWidth:1,strokeDash:1,size:1,angle:1,shape:1,order:1,text:1,detail:1,key:1,tooltip:1,href:1,url:1,description:1};function qn(e){return e===Ae||e===yt||e===bt}const nu={row:1,column:1,facet:1},Me=x(nu),Ao={...To,...nu},og=x(Ao),{order:xw,detail:vw,tooltip:Sw,...ag}=Ao,{row:Ew,column:$w,facet:ww,...cg}=ag;function lg(e){return!!cg[e]}function iu(e){return!!Ao[e]}const ug=[et,mt,rt,Be,Rt,_t];function ru(e){return _n(e)!==e}function _n(e){switch(e){case et:return se;case mt:return be;case rt:return nt;case Be:return it;case Rt:return We;case _t:return tt}return e}function Gt(e){if(Zl(e))switch(e){case We:return"startAngle";case Rt:return"endAngle";case tt:return"outerRadius";case _t:return"innerRadius"}return e}function xt(e){switch(e){case se:return et;case be:return mt;case nt:return rt;case it:return Be;case We:return Rt;case tt:return _t}}function _e(e){switch(e){case se:case et:return"width";case be:case mt:return"height"}}function su(e){switch(e){case se:return"xOffset";case be:return"yOffset";case et:return"x2Offset";case mt:return"y2Offset";case We:return"thetaOffset";case tt:return"radiusOffset";case Rt:return"theta2Offset";case _t:return"radius2Offset"}}function Oo(e){switch(e){case se:return"xOffset";case be:return"yOffset"}}function fg(e){switch(e){case"xOffset":return"x";case"yOffset":return"y"}}const dg=x(To),{x:Cw,y:Nw,x2:Fw,y2:kw,xOffset:Tw,yOffset:Aw,latitude:Ow,longitude:_w,latitude2:Rw,longitude2:Iw,theta:Lw,theta2:Pw,radius:zw,radius2:Dw,..._o}=To,pg=x(_o),Ro={x:1,y:1},vt=x(Ro);function ue(e){return e in Ro}const Io={theta:1,radius:1},gg=x(Io);function Mr(e){return e==="width"?se:be}const ou={xOffset:1,yOffset:1};function qi(e){return e in ou}const{text:jw,tooltip:Mw,href:Uw,url:Bw,description:Ww,detail:Gw,key:Hw,order:qw,...au}=_o,hg=x(au);function mg(e){return!!_o[e]}function yg(e){switch(e){case Ae:case yt:case bt:case It:case Oe:case Lt:case Zt:case en:return!0;case Qt:case Jt:case On:return!1}}const cu={...Ro,...Io,...ou,...au},Ur=x(cu);function Pt(e){return!!cu[e]}function bg(e,t){return vg(e)[t]}const lu={arc:"always",area:"always",bar:"always",circle:"always",geoshape:"always",image:"always",line:"always",rule:"always",point:"always",rect:"always",square:"always",trail:"always",text:"always",tick:"always"},{geoshape:Vw,...xg}=lu;function vg(e){switch(e){case Ae:case yt:case bt:case jr:case Hi:case Pr:case En:case zr:case Zn:case Lt:case Qt:case Jt:case Zt:case Lr:case Nt:case Ft:return lu;case se:case be:case Kt:case gi:case nt:case it:return xg;case et:case mt:case rt:case Be:return{area:"always",bar:"always",image:"always",rect:"always",rule:"always",circle:"binned",point:"binned",square:"binned",tick:"binned",line:"binned",trail:"binned"};case It:return{point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",text:"always",line:"always",trail:"always"};case en:return{line:"always",point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",geoshape:"always"};case Oe:return{point:"always",geoshape:"always"};case Gi:return{text:"always"};case On:return{point:"always",square:"always",text:"always"};case Dr:return{image:"always"};case We:return{text:"always",arc:"always"};case tt:return{text:"always",arc:"always"};case Rt:case _t:return{arc:"always"}}}function Fs(e){switch(e){case se:case be:case We:case tt:case Kt:case gi:case It:case On:case Zt:case Lt:case Qt:case Jt:case et:case mt:case Rt:case _t:return;case Lr:case Nt:case Ft:case Oe:case en:case Gi:case En:case zr:case Dr:case jr:return"discrete";case Ae:case yt:case bt:return"flexible";case nt:case it:case rt:case Be:case Hi:case Pr:case Zn:return}}const Sg={argmax:1,argmin:1,average:1,count:1,distinct:1,exponential:1,exponentialb:1,product:1,max:1,mean:1,median:1,min:1,missing:1,q1:1,q3:1,ci0:1,ci1:1,stderr:1,stdev:1,stdevp:1,sum:1,valid:1,values:1,variance:1,variancep:1},Eg={count:1,min:1,max:1};function At(e){return!!e&&!!e.argmin}function tn(e){return!!e&&!!e.argmax}function Lo(e){return I(e)&&!!Sg[e]}const $g=new Set(["count","valid","missing","distinct"]);function uu(e){return I(e)&&$g.has(e)}function wg(e){return I(e)&&G(["min","max"],e)}const Cg=new Set(["count","sum","distinct","valid","missing"]),Ng=new Set(["mean","average","median","q1","q3","min","max"]);function fu(e){return di(e)&&(e=Zr(e,void 0)),"bin"+x(e).map(t=>Br(e[t])?re(`_${t}_${Wt(e[t])}`):re(`_${t}_${e[t]}`)).join("")}function ee(e){return e===!0||Rn(e)&&!e.binned}function xe(e){return e==="binned"||Rn(e)&&e.binned===!0}function Rn(e){return X(e)}function Br(e){return e?.param}function nc(e){switch(e){case Nt:case Ft:case It:case Ae:case yt:case bt:case Zt:case Lt:case Qt:case Jt:case Oe:return 6;case en:return 4;default:return 10}}function Vi(e){return!!e?.expr}function Fe(e){const t=x(e||{}),n={};for(const i of t)n[i]=Le(e[i]);return n}function du(e){const{anchor:t,frame:n,offset:i,orient:r,angle:s,limit:o,color:a,subtitleColor:c,subtitleFont:l,subtitleFontSize:u,subtitleFontStyle:f,subtitleFontWeight:d,subtitleLineHeight:g,subtitlePadding:p,...h}=e,m={...h,...a?{fill:a}:{}},y={...t?{anchor:t}:{},...n?{frame:n}:{},...i?{offset:i}:{},...r?{orient:r}:{},...s!==void 0?{angle:s}:{},...o!==void 0?{limit:o}:{}},b={...c?{subtitleColor:c}:{},...l?{subtitleFont:l}:{},...u?{subtitleFontSize:u}:{},...f?{subtitleFontStyle:f}:{},...d?{subtitleFontWeight:d}:{},...g?{subtitleLineHeight:g}:{},...p?{subtitlePadding:p}:{}},C=Qn(e,["align","baseline","dx","dy","limit"]);return{titleMarkConfig:m,subtitleMarkConfig:C,nonMarkTitleProperties:y,subtitle:b}}function Mt(e){return I(e)||N(e)&&I(e[0])}function k(e){return!!e?.signal}function nn(e){return!!e.step}function Fg(e){return N(e)?!1:"fields"in e&&!("data"in e)}function kg(e){return N(e)?!1:"fields"in e&&"data"in e}function Ct(e){return N(e)?!1:"field"in e&&"data"in e}const Tg={aria:1,description:1,ariaRole:1,ariaRoleDescription:1,blend:1,opacity:1,fill:1,fillOpacity:1,stroke:1,strokeCap:1,strokeWidth:1,strokeOpacity:1,strokeDash:1,strokeDashOffset:1,strokeJoin:1,strokeOffset:1,strokeMiterLimit:1,startAngle:1,endAngle:1,padAngle:1,innerRadius:1,outerRadius:1,size:1,shape:1,interpolate:1,tension:1,orient:1,align:1,baseline:1,text:1,dir:1,dx:1,dy:1,ellipsis:1,limit:1,radius:1,theta:1,angle:1,font:1,fontSize:1,fontWeight:1,fontStyle:1,lineBreak:1,lineHeight:1,cursor:1,href:1,tooltip:1,cornerRadius:1,cornerRadiusTopLeft:1,cornerRadiusTopRight:1,cornerRadiusBottomLeft:1,cornerRadiusBottomRight:1,aspect:1,width:1,height:1,url:1,smooth:1},Ag=x(Tg),Og={arc:1,area:1,group:1,image:1,line:1,path:1,rect:1,rule:1,shape:1,symbol:1,text:1,trail:1},Ks=["cornerRadius","cornerRadiusTopLeft","cornerRadiusTopRight","cornerRadiusBottomLeft","cornerRadiusBottomRight"];function pu(e){const t=N(e.condition)?e.condition.map(ic):ic(e.condition);return{...Le(e),condition:t}}function Le(e){if(Vi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return e}function ic(e){if(Vi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return e}function ne(e){if(Vi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return k(e)?e:e!==void 0?{value:e}:void 0}function _g(e){return k(e)?e.signal:B(e)}function rc(e){return k(e)?e.signal:B(e.value)}function Xe(e){return k(e)?e.signal:e==null?null:B(e)}function Rg(e,t,n){for(const i of n){const r=Ot(i,t.markDef,t.config);r!==void 0&&(e[i]=ne(r))}return e}function gu(e){return[].concat(e.type,e.style??[])}function V(e,t,n,i={}){const{vgChannel:r,ignoreVgConfig:s}=i;return r&&t[r]!==void 0?t[r]:t[e]!==void 0?t[e]:s&&(!r||r===e)?void 0:Ot(e,t,n,i)}function Ot(e,t,n,{vgChannel:i}={}){return le(i?mr(e,t,n.style):void 0,mr(e,t,n.style),i?n[t.type][i]:void 0,n[t.type][e],i?n.mark[i]:n.mark[e])}function mr(e,t,n){return hu(e,gu(t),n)}function hu(e,t,n){t=ce(t);let i;for(const r of t){const s=n[r];s&&s[e]!==void 0&&(i=s[e])}return i}function mu(e,t){return ce(e).reduce((n,i)=>(n.field.push(E(i,t)),n.order.push(i.sort??"ascending"),n),{field:[],order:[]})}function yu(e,t){const n=[...e];return t.forEach(i=>{for(const r of n)if(Pe(r,i))return;n.push(i)}),n}function bu(e,t){return Pe(e,t)||!t?e:e?[...ce(e),...ce(t)].join(", "):t}function xu(e,t){const n=e.value,i=t.value;if(n==null||i===null)return{explicit:e.explicit,value:null};if((Mt(n)||k(n))&&(Mt(i)||k(i)))return{explicit:e.explicit,value:bu(n,i)};if(Mt(n)||k(n))return{explicit:e.explicit,value:n};if(Mt(i)||k(i))return{explicit:e.explicit,value:i};if(!Mt(n)&&!k(n)&&!Mt(i)&&!k(i))return{explicit:e.explicit,value:yu(n,i)};throw new Error("It should never reach here")}function Po(e){return`Invalid specification ${Q(e)}. Make sure the specification includes at least one of the following properties: "mark", "layer", "facet", "hconcat", "vconcat", "concat", or "repeat".`}const Ig='Autosize "fit" only works for single views and layered views.';function sc(e){return`${e=="width"?"Width":"Height"} "container" only works for single views and layered views.`}function oc(e){const t=e=="width"?"Width":"Height",n=e=="width"?"x":"y";return`${t} "container" only works well with autosize "fit" or "fit-${n}".`}function ac(e){return e?`Dropping "fit-${e}" because spec has discrete ${_e(e)}.`:'Dropping "fit" because spec has discrete size.'}function zo(e){return`Unknown field for ${e}. Cannot calculate view size.`}function cc(e){return`Cannot project a selection on encoding channel "${e}", which has no field.`}function Lg(e,t){return`Cannot project a selection on encoding channel "${e}" as it uses an aggregate function ("${t}").`}function Pg(e){return`The "nearest" transform is not supported for ${e} marks.`}function vu(e){return`Selection not supported for ${e} yet.`}function zg(e){return`Cannot find a selection named "${e}".`}const Dg="Scale bindings are currently only supported for scales with unbinned, continuous domains.",jg="Sequntial scales are deprecated. The available quantitative scale type values are linear, log, pow, sqrt, symlog, time and utc",Mg="Legend bindings are only supported for selections over an individual field or encoding channel.";function Ug(e){return`Lookups can only be performed on selection parameters. "${e}" is a variable parameter.`}function Bg(e){return`Cannot define and lookup the "${e}" selection in the same view. Try moving the lookup into a second, layered view?`}const Wg="The same selection must be used to override scale domains in a layered view.",Gg='Interval selections should be initialized using "x", "y", "longitude", or "latitude" keys.';function Hg(e){return`Unknown repeated value "${e}".`}function lc(e){return`The "columns" property cannot be used when "${e}" has nested row/column.`}const qg="Axes cannot be shared in concatenated or repeated views yet (https://github.com/vega/vega-lite/issues/2415).";function Vg(e){return`Unrecognized parse "${e}".`}function uc(e,t,n){return`An ancestor parsed field "${e}" as ${n} but a child wants to parse the field as ${t}.`}const Xg="Attempt to add the same child twice.";function Yg(e){return`Ignoring an invalid transform: ${Q(e)}.`}const Kg='If "from.fields" is not specified, "as" has to be a string that specifies the key to be used for the data from the secondary source.';function fc(e){return`Config.customFormatTypes is not true, thus custom format type and format for channel ${e} are dropped.`}function Qg(e){const{parentProjection:t,projection:n}=e;return`Layer's shared projection ${Q(t)} is overridden by a child projection ${Q(n)}.`}const Jg="Arc marks uses theta channel rather than angle, replacing angle with theta.";function Zg(e){return`${e}Offset dropped because ${e} is continuous`}function eh(e,t,n){return`Channel ${e} is a ${t}. Converted to {value: ${Q(n)}}.`}function Su(e){return`Invalid field type "${e}".`}function th(e,t){return`Invalid field type "${e}" for aggregate: "${t}", using "quantitative" instead.`}function nh(e){return`Invalid aggregation operator "${e}".`}function Eu(e,t){const{fill:n,stroke:i}=t;return`Dropping color ${e} as the plot also has ${n&&i?"fill and stroke":n?"fill":"stroke"}.`}function ih(e){return`Position range does not support relative band size for ${e}.`}function Qs(e,t){return`Dropping ${Q(e)} from channel "${t}" since it does not contain any data field, datum, value, or signal.`}const rh="Line marks cannot encode size with a non-groupby field. You may want to use trail marks instead.";function Wr(e,t,n){return`${e} dropped as it is incompatible with "${t}".`}function sh(e){return`${e}-encoding is dropped as ${e} is not a valid encoding channel.`}function oh(e){return`${e} encoding should be discrete (ordinal / nominal / binned).`}function ah(e){return`${e} encoding should be discrete (ordinal / nominal / binned) or use a discretizing scale (e.g. threshold).`}function ch(e){return`Facet encoding dropped as ${e.join(" and ")} ${e.length>1?"are":"is"} also specified.`}function ks(e,t){return`Using discrete channel "${e}" to encode "${t}" field can be misleading as it does not encode ${t==="ordinal"?"order":"magnitude"}.`}function lh(e){return`The ${e} for range marks cannot be an expression`}function uh(e,t){return`Line mark is for continuous lines and thus cannot be used with ${e&&t?"x2 and y2":e?"x2":"y2"}. We will use the rule mark (line segments) instead.`}function fh(e,t){return`Specified orient "${e}" overridden with "${t}".`}function dh(e){return`Cannot use the scale property "${e}" with non-color channel.`}function ph(e){return`Cannot use the relative band size with ${e} scale.`}function gh(e){return`Using unaggregated domain with raw field has no effect (${Q(e)}).`}function hh(e){return`Unaggregated domain not applicable for "${e}" since it produces values outside the origin domain of the source data.`}function mh(e){return`Unaggregated domain is currently unsupported for log scale (${Q(e)}).`}function yh(e){return`Cannot apply size to non-oriented mark "${e}".`}function bh(e,t,n){return`Channel "${e}" does not work with "${t}" scale. We are using "${n}" scale instead.`}function xh(e,t){return`FieldDef does not work with "${e}" scale. We are using "${t}" scale instead.`}function $u(e,t,n){return`${n}-scale's "${t}" is dropped as it does not work with ${e} scale.`}function wu(e){return`The step for "${e}" is dropped because the ${e==="width"?"x":"y"} is continuous.`}function vh(e,t,n,i){return`Conflicting ${t.toString()} property "${e.toString()}" (${Q(n)} and ${Q(i)}). Using ${Q(n)}.`}function Sh(e,t,n,i){return`Conflicting ${t.toString()} property "${e.toString()}" (${Q(n)} and ${Q(i)}). Using the union of the two domains.`}function Eh(e){return`Setting the scale to be independent for "${e}" means we also have to set the guide (axis or legend) to be independent.`}function $h(e){return`Dropping sort property ${Q(e)} as unioned domains only support boolean or op "count", "min", and "max".`}const dc="Domains that should be unioned has conflicting sort properties. Sort will be set to true.",wh="Detected faceted independent scales that union domain of multiple fields from different data sources. We will use the first field. The result view size may be incorrect.",Ch="Detected faceted independent scales that union domain of the same fields from different source. We will assume that this is the same field from a different fork of the same data source. However, if this is not the case, the result view size may be incorrect.",Nh="Detected faceted independent scales that union domain of multiple fields from the same data source. We will use the first field. The result view size may be incorrect.";function Fh(e){return`Cannot stack "${e}" if there is already "${e}2".`}function kh(e){return`Cannot stack non-linear scale (${e}).`}function Th(e){return`Stacking is applied even though the aggregate function is non-summative ("${e}").`}function yr(e,t){return`Invalid ${e}: ${Q(t)}.`}function Ah(e){return`Dropping day from datetime ${Q(e)} as day cannot be combined with other units.`}function Oh(e,t){return`${t?"extent ":""}${t&&e?"and ":""}${e?"center ":""}${t&&e?"are ":"is "}not needed when data are aggregated.`}function _h(e,t,n){return`${e} is not usually used with ${t} for ${n}.`}function Rh(e,t){return`Continuous axis should not have customized aggregation function ${e}; ${t} already agregates the axis.`}function pc(e){return`1D error band does not support ${e}.`}function Cu(e){return`Channel ${e} is required for "binned" bin.`}function Ih(e){return`Channel ${e} should not be used with "binned" bin.`}function Lh(e){return`Domain for ${e} is required for threshold scale.`}const Nu=Ep($p);let ei=Nu;function Ph(e){return ei=e,ei}function zh(){return ei=Nu,ei}function v(...e){ei.warn(...e)}function Dh(...e){ei.debug(...e)}function In(e){if(e&&X(e)){for(const t of jo)if(t in e)return!0}return!1}const Fu=["january","february","march","april","may","june","july","august","september","october","november","december"],jh=Fu.map(e=>e.substr(0,3)),ku=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"],Mh=ku.map(e=>e.substr(0,3));function Uh(e){if(Ir(e)&&(e=+e),ie(e))return e>4&&v(yr("quarter",e)),e-1;throw new Error(yr("quarter",e))}function Bh(e){if(Ir(e)&&(e=+e),ie(e))return e-1;{const t=e.toLowerCase(),n=Fu.indexOf(t);if(n!==-1)return n;const i=t.substr(0,3),r=jh.indexOf(i);if(r!==-1)return r;throw new Error(yr("month",e))}}function Wh(e){if(Ir(e)&&(e=+e),ie(e))return e%7;{const t=e.toLowerCase(),n=ku.indexOf(t);if(n!==-1)return n;const i=t.substr(0,3),r=Mh.indexOf(i);if(r!==-1)return r;throw new Error(yr("day",e))}}function Do(e,t){const n=[];if(t&&e.day!==void 0&&x(e).length>1&&(v(Ah(e)),e=z(e),delete e.day),e.year!==void 0?n.push(e.year):n.push(2012),e.month!==void 0){const i=t?Bh(e.month):e.month;n.push(i)}else if(e.quarter!==void 0){const i=t?Uh(e.quarter):e.quarter;n.push(ie(i)?i*3:`${i}*3`)}else n.push(0);if(e.date!==void 0)n.push(e.date);else if(e.day!==void 0){const i=t?Wh(e.day):e.day;n.push(ie(i)?i+1:`${i}+1`)}else n.push(1);for(const i of["hours","minutes","seconds","milliseconds"]){const r=e[i];n.push(typeof r>"u"?0:r)}return n}function $n(e){const n=Do(e,!0).join(", ");return e.utc?`utc(${n})`:`datetime(${n})`}function Gh(e){const n=Do(e,!1).join(", ");return e.utc?`utc(${n})`:`datetime(${n})`}function Hh(e){const t=Do(e,!0);return e.utc?+new Date(Date.UTC(...t)):+new Date(...t)}const Tu={year:1,quarter:1,month:1,week:1,day:1,dayofyear:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1},jo=x(Tu);function qh(e){return!!Tu[e]}function Ln(e){return X(e)?e.binned:Au(e)}function Au(e){return e&&e.startsWith("binned")}function Mo(e){return e.startsWith("utc")}function Vh(e){return e.substring(3)}const Xh={"year-month":"%b %Y ","year-month-date":"%b %d, %Y "};function Gr(e){return jo.filter(t=>_u(e,t))}function Ou(e){const t=Gr(e);return t[t.length-1]}function _u(e,t){const n=e.indexOf(t);return!(n<0||n>0&&t==="seconds"&&e.charAt(n-1)==="i"||e.length>n+3&&t==="day"&&e.charAt(n+3)==="o"||n>0&&t==="year"&&e.charAt(n-1)==="f")}function Yh(e,t,{end:n}={end:!1}){const i=No(t),r=Mo(e)?"utc":"";function s(c){return c==="quarter"?`(${r}quarter(${i})-1)`:`${r}${c}(${i})`}let o;const a={};for(const c of jo)_u(e,c)&&(a[c]=s(c),o=c);return n&&(a[o]+="+1"),Gh(a)}function Ru(e){if(!e)return;const t=Gr(e);return`timeUnitSpecifier(${Q(t)}, ${Q(Xh)})`}function Kh(e,t,n){if(!e)return;const i=Ru(e);return`${n||Mo(e)?"utc":"time"}Format(${t}, ${i})`}function me(e){if(!e)return;let t;return I(e)?Au(e)?t={unit:e.substring(6),binned:!0}:t={unit:e}:X(e)&&(t={...e,...e.unit?{unit:e.unit}:{}}),Mo(t.unit)&&(t.utc=!0,t.unit=Vh(t.unit)),t}function Qh(e){const{utc:t,...n}=me(e);return n.unit?(t?"utc":"")+x(n).map(i=>re(`${i==="unit"?"":`_${i}_`}${n[i]}`)).join(""):(t?"utc":"")+"timeunit"+x(n).map(i=>re(`_${i}_${n[i]}`)).join("")}function Iu(e,t=n=>n){const n=me(e),i=Ou(n.unit);if(i&&i!=="day"){const r={year:2001,month:1,date:1,hours:0,minutes:0,seconds:0,milliseconds:0},{step:s,part:o}=Lu(i,n.step),a={...r,[o]:+r[o]+s};return`${t($n(a))} - ${t($n(r))}`}}const Jh={year:1,month:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1};function Zh(e){return!!Jh[e]}function Lu(e,t=1){if(Zh(e))return{part:e,step:t};switch(e){case"day":case"dayofyear":return{part:"date",step:t};case"quarter":return{part:"month",step:t*3};case"week":return{part:"date",step:t*7}}}function em(e){return e?.param}function Uo(e){return!!e?.field&&e.equal!==void 0}function Bo(e){return!!e?.field&&e.lt!==void 0}function Wo(e){return!!e?.field&&e.lte!==void 0}function Go(e){return!!e?.field&&e.gt!==void 0}function Ho(e){return!!e?.field&&e.gte!==void 0}function qo(e){if(e?.field){if(N(e.range)&&e.range.length===2)return!0;if(k(e.range))return!0}return!1}function Vo(e){return!!e?.field&&(N(e.oneOf)||N(e.in))}function tm(e){return!!e?.field&&e.valid!==void 0}function Pu(e){return Vo(e)||Uo(e)||qo(e)||Bo(e)||Go(e)||Wo(e)||Ho(e)}function st(e,t){return es(e,{timeUnit:t,wrapTime:!0})}function nm(e,t){return e.map(n=>st(n,t))}function zu(e,t=!0){const{field:n}=e,i=me(e.timeUnit),{unit:r,binned:s}=i||{},o=E(e,{expr:"datum"}),a=r?`time(${s?o:Yh(r,n)})`:o;if(Uo(e))return`${a}===${st(e.equal,r)}`;if(Bo(e)){const c=e.lt;return`${a}<${st(c,r)}`}else if(Go(e)){const c=e.gt;return`${a}>${st(c,r)}`}else if(Wo(e)){const c=e.lte;return`${a}<=${st(c,r)}`}else if(Ho(e)){const c=e.gte;return`${a}>=${st(c,r)}`}else{if(Vo(e))return`indexof([${nm(e.oneOf,r).join(",")}], ${a}) !== -1`;if(tm(e))return Xo(a,e.valid);if(qo(e)){const{range:c}=e,l=k(c)?{signal:`${c.signal}[0]`}:c[0],u=k(c)?{signal:`${c.signal}[1]`}:c[1];if(l!==null&&u!==null&&t)return"inrange("+a+", ["+st(l,r)+", "+st(u,r)+"])";const f=[];return l!==null&&f.push(`${a} >= ${st(l,r)}`),u!==null&&f.push(`${a} <= ${st(u,r)}`),f.length>0?f.join(" && "):"true"}}throw new Error(`Invalid field predicate: ${Q(e)}`)}function Xo(e,t=!0){return t?`isValid(${e}) && isFinite(+${e})`:`!isValid(${e}) || !isFinite(+${e})`}function im(e){return Pu(e)&&e.timeUnit?{...e,timeUnit:me(e.timeUnit)}:e}const Xi={quantitative:"quantitative",ordinal:"ordinal",temporal:"temporal",nominal:"nominal",geojson:"geojson"};function rm(e){return e==="quantitative"||e==="temporal"}function Du(e){return e==="ordinal"||e==="nominal"}const wn=Xi.quantitative,Yo=Xi.ordinal,ti=Xi.temporal,Ko=Xi.nominal,hi=Xi.geojson;function sm(e){if(e)switch(e=e.toLowerCase(),e){case"q":case wn:return"quantitative";case"t":case ti:return"temporal";case"o":case Yo:return"ordinal";case"n":case Ko:return"nominal";case hi:return"geojson"}}const Te={LINEAR:"linear",LOG:"log",POW:"pow",SQRT:"sqrt",SYMLOG:"symlog",IDENTITY:"identity",SEQUENTIAL:"sequential",TIME:"time",UTC:"utc",QUANTILE:"quantile",QUANTIZE:"quantize",THRESHOLD:"threshold",BIN_ORDINAL:"bin-ordinal",ORDINAL:"ordinal",POINT:"point",BAND:"band"},Js={linear:"numeric",log:"numeric",pow:"numeric",sqrt:"numeric",symlog:"numeric",identity:"numeric",sequential:"numeric",time:"time",utc:"time",ordinal:"ordinal","bin-ordinal":"bin-ordinal",point:"ordinal-position",band:"ordinal-position",quantile:"discretizing",quantize:"discretizing",threshold:"discretizing"};function om(e,t){const n=Js[e],i=Js[t];return n===i||n==="ordinal-position"&&i==="time"||i==="ordinal-position"&&n==="time"}const am={linear:0,log:1,pow:1,sqrt:1,symlog:1,identity:1,sequential:1,time:0,utc:0,point:10,band:11,ordinal:0,"bin-ordinal":0,quantile:0,quantize:0,threshold:0};function gc(e){return am[e]}const ju=new Set(["linear","log","pow","sqrt","symlog"]),Mu=new Set([...ju,"time","utc"]);function Uu(e){return ju.has(e)}const Bu=new Set(["quantile","quantize","threshold"]),cm=new Set([...Mu,...Bu,"sequential","identity"]),lm=new Set(["ordinal","bin-ordinal","point","band"]);function ye(e){return lm.has(e)}function De(e){return cm.has(e)}function Ye(e){return Mu.has(e)}function ni(e){return Bu.has(e)}const um={pointPadding:.5,barBandPaddingInner:.1,rectBandPaddingInner:0,bandWithNestedOffsetPaddingInner:.2,bandWithNestedOffsetPaddingOuter:.2,minBandSize:2,minFontSize:8,maxFontSize:40,minOpacity:.3,maxOpacity:.8,minSize:9,minStrokeWidth:1,maxStrokeWidth:4,quantileCount:4,quantizeCount:4,zero:!0};function fm(e){return!I(e)&&!!e.name}function Wu(e){return e?.param}function dm(e){return e?.unionWith}function pm(e){return X(e)&&"field"in e}const gm={type:1,domain:1,domainMax:1,domainMin:1,domainMid:1,domainRaw:1,align:1,range:1,rangeMax:1,rangeMin:1,scheme:1,bins:1,reverse:1,round:1,clamp:1,nice:1,base:1,exponent:1,constant:1,interpolate:1,zero:1,padding:1,paddingInner:1,paddingOuter:1},{type:Xw,domain:Yw,range:Kw,rangeMax:Qw,rangeMin:Jw,scheme:Zw,...hm}=gm,mm=x(hm);function Zs(e,t){switch(t){case"type":case"domain":case"reverse":case"range":return!0;case"scheme":case"interpolate":return!["point","band","identity"].includes(e);case"bins":return!["point","band","identity","ordinal"].includes(e);case"round":return Ye(e)||e==="band"||e==="point";case"padding":case"rangeMin":case"rangeMax":return Ye(e)||["point","band"].includes(e);case"paddingOuter":case"align":return["point","band"].includes(e);case"paddingInner":return e==="band";case"domainMax":case"domainMid":case"domainMin":case"domainRaw":case"clamp":return Ye(e);case"nice":return Ye(e)||e==="quantize"||e==="threshold";case"exponent":return e==="pow";case"base":return e==="log";case"constant":return e==="symlog";case"zero":return De(e)&&!G(["log","time","utc","threshold","quantile"],e)}}function Gu(e,t){switch(t){case"interpolate":case"scheme":case"domainMid":return qn(e)?void 0:dh(t);case"align":case"type":case"bins":case"domain":case"domainMax":case"domainMin":case"domainRaw":case"range":case"base":case"exponent":case"constant":case"nice":case"padding":case"paddingInner":case"paddingOuter":case"rangeMax":case"rangeMin":case"reverse":case"round":case"clamp":case"zero":return}}function ym(e,t){return G([Yo,Ko],t)?e===void 0||ye(e):t===ti?G([Te.TIME,Te.UTC,void 0],e):t===wn?Uu(e)||ni(e)||e===void 0:!0}function bm(e,t,n=!1){if(!Pt(e))return!1;switch(e){case se:case be:case Kt:case gi:case We:case tt:return Ye(t)||t==="band"?!0:t==="point"?!n:!1;case It:case Zt:case Lt:case Qt:case Jt:case On:return Ye(t)||ni(t)||G(["band","point","ordinal"],t);case Ae:case yt:case bt:return t!=="band";case en:case Oe:return t==="ordinal"||ni(t)}}const Ne={arc:"arc",area:"area",bar:"bar",image:"image",line:"line",point:"point",rect:"rect",rule:"rule",text:"text",tick:"tick",trail:"trail",circle:"circle",square:"square",geoshape:"geoshape"},Hu=Ne.arc,Hr=Ne.area,qr=Ne.bar,xm=Ne.image,Vr=Ne.line,Xr=Ne.point,vm=Ne.rect,br=Ne.rule,qu=Ne.text,Qo=Ne.tick,Sm=Ne.trail,Jo=Ne.circle,Zo=Ne.square,Vu=Ne.geoshape;function rn(e){return["line","area","trail"].includes(e)}function zi(e){return["rect","bar","image","arc"].includes(e)}const Em=new Set(x(Ne));function pt(e){return e.type}const $m=["stroke","strokeWidth","strokeDash","strokeDashOffset","strokeOpacity","strokeJoin","strokeMiterLimit"],wm=["fill","fillOpacity"],Cm=[...$m,...wm],Nm={color:1,filled:1,invalid:1,order:1,radius2:1,theta2:1,timeUnitBandSize:1,timeUnitBandPosition:1},hc=x(Nm),Fm={area:["line","point"],bar:["binSpacing","continuousBandSize","discreteBandSize","minBandSize"],rect:["binSpacing","continuousBandSize","discreteBandSize","minBandSize"],line:["point"],tick:["bandSize","thickness"]},km={color:"#4c78a8",invalid:"filter",timeUnitBandSize:1},Tm={mark:1,arc:1,area:1,bar:1,circle:1,image:1,line:1,point:1,rect:1,rule:1,square:1,text:1,tick:1,trail:1,geoshape:1},Xu=x(Tm);function Cn(e){return e&&e.band!=null}const Am={horizontal:["cornerRadiusTopRight","cornerRadiusBottomRight"],vertical:["cornerRadiusTopLeft","cornerRadiusTopRight"]},Yu=5,Om={binSpacing:1,continuousBandSize:Yu,minBandSize:.25,timeUnitBandPosition:.5},_m={binSpacing:0,continuousBandSize:Yu,minBandSize:.25,timeUnitBandPosition:.5},Rm={thickness:1};function Im(e){return pt(e)?e.type:e}function ea(e){const{channel:t,channelDef:n,markDef:i,scale:r,config:s}=e,o=na(e);return S(n)&&!uu(n.aggregate)&&r&&Ye(r.get("type"))?Lm({fieldDef:n,channel:t,markDef:i,ref:o,config:s}):o}function Lm({fieldDef:e,channel:t,markDef:n,ref:i,config:r}){return rn(n.type)?i:V("invalid",n,r)===null?[Pm(e,t),i]:i}function Pm(e,t){const n=ta(e,!0),r=_n(t)==="y"?{field:{group:"height"}}:{value:0};return{test:n,...r}}function ta(e,t=!0){return Xo(I(e)?e:E(e,{expr:"datum"}),!t)}function zm(e){const{datum:t}=e;return In(t)?$n(t):`${Q(t)}`}function mn(e,t,n,i){const r={};if(t&&(r.scale=t),St(e)){const{datum:s}=e;In(s)?r.signal=$n(s):k(s)?r.signal=s.signal:Vi(s)?r.signal=s.expr:r.value=s}else r.field=E(e,n);if(i){const{offset:s,band:o}=i;s&&(r.offset=s),o&&(r.band=o)}return r}function xr({scaleName:e,fieldOrDatumDef:t,fieldOrDatumDef2:n,offset:i,startSuffix:r,endSuffix:s="end",bandPosition:o=.5}){const a=!k(o)&&0<o&&o<1?"datum":void 0,c=E(t,{expr:a,suffix:r}),l=n!==void 0?E(n,{expr:a}):E(t,{suffix:s,expr:a}),u={};if(o===0||o===1){u.scale=e;const f=o===0?c:l;u.field=f}else{const f=k(o)?`(1-${o.signal}) * ${c} + ${o.signal} * ${l}`:`${1-o} * ${c} + ${o} * ${l}`;u.signal=`scale("${e}", ${f})`}return i&&(u.offset=i),u}function Dm({scaleName:e,fieldDef:t}){const n=E(t,{expr:"datum"}),i=E(t,{expr:"datum",suffix:"end"});return`abs(scale("${e}", ${i}) - scale("${e}", ${n}))`}function na({channel:e,channelDef:t,channel2Def:n,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:c,defaultRef:l,bandPosition:u}){if(t){if(D(t)){const f=o?.get("type");if(Ce(t)){u??(u=Ht({fieldDef:t,fieldDef2:n,markDef:i,config:r}));const{bin:d,timeUnit:g,type:p}=t;if(ee(d)||u&&g&&p===ti)return a?.impute?mn(t,s,{binSuffix:"mid"},{offset:c}):u&&!ye(f)?xr({scaleName:s,fieldOrDatumDef:t,bandPosition:u,offset:c}):mn(t,s,Ji(t,e)?{binSuffix:"range"}:{},{offset:c});if(xe(d)){if(S(n))return xr({scaleName:s,fieldOrDatumDef:t,fieldOrDatumDef2:n,bandPosition:u,offset:c});v(Cu(e===se?et:mt))}}return mn(t,s,ye(f)?{binSuffix:"range"}:{},{offset:c,band:f==="band"?u??t.bandPosition??.5:void 0})}else if(Je(t)){const f=t.value,d=c?{offset:c}:{};return{..._i(e,f),...d}}}return wp(l)&&(l=l()),l&&{...l,...c?{offset:c}:{}}}function _i(e,t){return G(["x","x2"],e)&&t==="width"?{field:{group:"width"}}:G(["y","y2"],e)&&t==="height"?{field:{group:"height"}}:ne(t)}function Nn(e){return e&&e!=="number"&&e!=="time"}function Ku(e,t,n){return`${e}(${t}${n?`, ${Q(n)}`:""})`}const jm=" – ";function ia({fieldOrDatumDef:e,format:t,formatType:n,expr:i,normalizeStack:r,config:s}){if(Nn(n))return Ke({fieldOrDatumDef:e,format:t,formatType:n,expr:i,config:s});const o=Qu(e,i,r),a=ii(e);if(t===void 0&&n===void 0&&s.customFormatTypes){if(a==="quantitative"){if(r&&s.normalizedNumberFormatType)return Ke({fieldOrDatumDef:e,format:s.normalizedNumberFormat,formatType:s.normalizedNumberFormatType,expr:i,config:s});if(s.numberFormatType)return Ke({fieldOrDatumDef:e,format:s.numberFormat,formatType:s.numberFormatType,expr:i,config:s})}if(a==="temporal"&&s.timeFormatType&&S(e)&&e.timeUnit===void 0)return Ke({fieldOrDatumDef:e,format:s.timeFormat,formatType:s.timeFormatType,expr:i,config:s})}if(si(e)){const c=Um({field:o,timeUnit:S(e)?me(e.timeUnit)?.unit:void 0,format:t,formatType:s.timeFormatType,rawTimeFormat:s.timeFormat,isUTCScale:Pn(e)&&e.scale?.type===Te.UTC});return c?{signal:c}:void 0}if(t=eo({type:a,specifiedFormat:t,config:s,normalizeStack:r}),S(e)&&ee(e.bin)){const c=E(e,{expr:i,binSuffix:"end"});return{signal:Yi(o,c,t,n,s)}}else return t||ii(e)==="quantitative"?{signal:`${ef(o,t)}`}:{signal:`isValid(${o}) ? ${o} : ""+${o}`}}function Qu(e,t,n){return S(e)?n?`${E(e,{expr:t,suffix:"end"})}-${E(e,{expr:t,suffix:"start"})}`:E(e,{expr:t}):zm(e)}function Ke({fieldOrDatumDef:e,format:t,formatType:n,expr:i,normalizeStack:r,config:s,field:o}){if(o??(o=Qu(e,i,r)),o!=="datum.value"&&S(e)&&ee(e.bin)){const a=E(e,{expr:i,binSuffix:"end"});return{signal:Yi(o,a,t,n,s)}}return{signal:Ku(n,o,t)}}function Ju(e,t,n,i,r,s){if(!(I(i)&&Nn(i))&&!(n===void 0&&i===void 0&&r.customFormatTypes&&ii(e)==="quantitative"&&(r.normalizedNumberFormatType&&ri(e)&&e.stack==="normalize"||r.numberFormatType))){if(ri(e)&&e.stack==="normalize"&&r.normalizedNumberFormat)return eo({type:"quantitative",config:r,normalizeStack:!0});if(si(e)){const o=S(e)?me(e.timeUnit)?.unit:void 0;return o===void 0&&r.customFormatTypes&&r.timeFormatType?void 0:Mm({specifiedFormat:n,timeUnit:o,config:r,omitTimeFormatConfig:s})}return eo({type:t,specifiedFormat:n,config:r})}}function Zu(e,t,n){if(e&&(k(e)||e==="number"||e==="time"))return e;if(si(t)&&n!=="time"&&n!=="utc")return S(t)&&me(t?.timeUnit)?.utc?"utc":"time"}function eo({type:e,specifiedFormat:t,config:n,normalizeStack:i}){if(I(t))return t;if(e===wn)return i?n.normalizedNumberFormat:n.numberFormat}function Mm({specifiedFormat:e,timeUnit:t,config:n,omitTimeFormatConfig:i}){return e||(t?{signal:Ru(t)}:i?void 0:n.timeFormat)}function ef(e,t){return`format(${e}, "${t||""}")`}function mc(e,t,n,i){return Nn(n)?Ku(n,e,t):ef(e,(I(t)?t:void 0)??i.numberFormat)}function Yi(e,t,n,i,r){if(n===void 0&&i===void 0&&r.customFormatTypes&&r.numberFormatType)return Yi(e,t,r.numberFormat,r.numberFormatType,r);const s=mc(e,n,i,r),o=mc(t,n,i,r);return`${Xo(e,!1)} ? "null" : ${s} + "${jm}" + ${o}`}function Um({field:e,timeUnit:t,format:n,formatType:i,rawTimeFormat:r,isUTCScale:s}){return!t||n?!t&&i?`${i}(${e}, '${n}')`:(n=I(n)?n:r,`${s?"utc":"time"}Format(${e}, '${n}')`):Kh(t,e,s)}const Yr="min",Bm={x:1,y:1,color:1,fill:1,stroke:1,strokeWidth:1,size:1,shape:1,fillOpacity:1,strokeOpacity:1,opacity:1,text:1};function yc(e){return e in Bm}function tf(e){return!!e?.encoding}function ut(e){return e&&(e.op==="count"||!!e.field)}function nf(e){return e&&N(e)}function Ki(e){return"row"in e||"column"in e}function ra(e){return!!e&&"header"in e}function Kr(e){return"facet"in e}function Wm(e){return e.param}function Gm(e){return e&&!I(e)&&"repeat"in e}function bc(e){const{field:t,timeUnit:n,bin:i,aggregate:r}=e;return{...n?{timeUnit:n}:{},...i?{bin:i}:{},...r?{aggregate:r}:{},field:t}}function sa(e){return"sort"in e}function Ht({fieldDef:e,fieldDef2:t,markDef:n,config:i}){if(D(e)&&e.bandPosition!==void 0)return e.bandPosition;if(S(e)){const{timeUnit:r,bin:s}=e;if(r&&!t)return Ot("timeUnitBandPosition",n,i);if(ee(s))return .5}}function rf({channel:e,fieldDef:t,fieldDef2:n,markDef:i,config:r,scaleType:s,useVlSizeChannel:o}){const a=_e(e),c=V(o?"size":a,i,r,{vgChannel:a});if(c!==void 0)return c;if(S(t)){const{timeUnit:l,bin:u}=t;if(l&&!n)return{band:Ot("timeUnitBandSize",i,r)};if(ee(u)&&!ye(s))return{band:1}}if(zi(i.type))return s?ye(s)?r[i.type]?.discreteBandSize||{band:1}:r[i.type]?.continuousBandSize:r[i.type]?.discreteBandSize}function sf(e,t,n,i){return ee(e.bin)||e.timeUnit&&Ce(e)&&e.type==="temporal"?Ht({fieldDef:e,fieldDef2:t,markDef:n,config:i})!==void 0:!1}function of(e){return e&&!!e.sort&&!e.field}function Qr(e){return e&&"condition"in e}function Jr(e){const t=e?.condition;return!!t&&!N(t)&&S(t)}function Qi(e){const t=e?.condition;return!!t&&!N(t)&&D(t)}function Hm(e){const t=e?.condition;return!!t&&(N(t)||Je(t))}function S(e){return e&&(!!e.field||e.aggregate==="count")}function ii(e){return e?.type}function St(e){return e&&"datum"in e}function Ut(e){return Ce(e)&&!Sr(e)||vr(e)}function xc(e){return Ce(e)&&e.type==="quantitative"&&!e.bin||vr(e)}function vr(e){return St(e)&&ie(e.datum)}function D(e){return S(e)||St(e)}function Ce(e){return e&&("field"in e||e.aggregate==="count")&&"type"in e}function Je(e){return e&&"value"in e&&"value"in e}function Pn(e){return e&&("scale"in e||"sort"in e)}function ri(e){return e&&("axis"in e||"stack"in e||"impute"in e)}function af(e){return e&&"legend"in e}function cf(e){return e&&("format"in e||"formatType"in e)}function qm(e){return ke(e,["legend","axis","header","scale"])}function Vm(e){return"op"in e}function E(e,t={}){let n=e.field;const i=t.prefix;let r=t.suffix,s="";if(Ym(e))n=Kl("count");else{let o;if(!t.nofn)if(Vm(e))o=e.op;else{const{bin:a,aggregate:c,timeUnit:l}=e;ee(a)?(o=fu(a),r=(t.binSuffix??"")+(t.suffix??"")):c?tn(c)?(s=`["${n}"]`,n=`argmax_${c.argmax}`):At(c)?(s=`["${n}"]`,n=`argmin_${c.argmin}`):o=String(c):l&&!Ln(l)&&(o=Qh(l),r=(!["range","mid"].includes(t.binSuffix)&&t.binSuffix||"")+(t.suffix??""))}o&&(n=n?`${o}_${n}`:o)}return r&&(n=`${n}_${r}`),i&&(n=`${i}_${n}`),t.forAs?Fo(n):t.expr?Vl(n,t.expr)+s:Ue(n)+s}function Sr(e){switch(e.type){case"nominal":case"ordinal":case"geojson":return!0;case"quantitative":return S(e)&&!!e.bin;case"temporal":return!1}throw new Error(Su(e.type))}function Xm(e){return Pn(e)&&ni(e.scale?.type)}function Ym(e){return e.aggregate==="count"}function Km(e,t){const{field:n,bin:i,timeUnit:r,aggregate:s}=e;if(s==="count")return t.countTitle;if(ee(i))return`${n} (binned)`;if(r&&!Ln(r)){const o=me(r)?.unit;if(o)return`${n} (${Gr(o).join("-")})`}else if(s)return tn(s)?`${n} for max ${s.argmax}`:At(s)?`${n} for min ${s.argmin}`:`${Wi(s)} of ${n}`;return n}function Qm(e){const{aggregate:t,bin:n,timeUnit:i,field:r}=e;if(tn(t))return`${r} for argmax(${t.argmax})`;if(At(t))return`${r} for argmin(${t.argmin})`;const s=i&&!Ln(i)?me(i):void 0,o=t||s?.unit||s?.maxbins&&"timeunit"||ee(n)&&"bin";return o?`${o.toUpperCase()}(${r})`:r}const lf=(e,t)=>{switch(t.fieldTitle){case"plain":return e.field;case"functional":return Qm(e);default:return Km(e,t)}};let uf=lf;function ff(e){uf=e}function Jm(){ff(lf)}function Vn(e,t,{allowDisabling:n,includeDefault:i=!0}){const r=oa(e)?.title;if(!S(e))return r??e.title;const s=e,o=i?aa(s,t):void 0;return n?le(r,s.title,o):r??s.title??o}function oa(e){if(ri(e)&&e.axis)return e.axis;if(af(e)&&e.legend)return e.legend;if(ra(e)&&e.header)return e.header}function aa(e,t){return uf(e,t)}function Er(e){if(cf(e)){const{format:t,formatType:n}=e;return{format:t,formatType:n}}else{const t=oa(e)??{},{format:n,formatType:i}=t;return{format:n,formatType:i}}}function Zm(e,t){switch(t){case"latitude":case"longitude":return"quantitative";case"row":case"column":case"facet":case"shape":case"strokeDash":return"nominal";case"order":return"ordinal"}if(sa(e)&&N(e.sort))return"ordinal";const{aggregate:n,bin:i,timeUnit:r}=e;if(r)return"temporal";if(i||n&&!tn(n)&&!At(n))return"quantitative";if(Pn(e)&&e.scale?.type)switch(Js[e.scale.type]){case"numeric":case"discretizing":return"quantitative";case"time":return"temporal"}return"nominal"}function gt(e){if(S(e))return e;if(Jr(e))return e.condition}function fe(e){if(D(e))return e;if(Qi(e))return e.condition}function df(e,t,n,i={}){if(I(e)||ie(e)||di(e)){const r=I(e)?"string":ie(e)?"number":"boolean";return v(eh(t,r,e)),{value:e}}return D(e)?$r(e,t,n,i):Qi(e)?{...e,condition:$r(e.condition,t,n,i)}:e}function $r(e,t,n,i){if(cf(e)){const{format:r,formatType:s,...o}=e;if(Nn(s)&&!n.customFormatTypes)return v(fc(t)),$r(o,t,n,i)}else{const r=ri(e)?"axis":af(e)?"legend":ra(e)?"header":null;if(r&&e[r]){const{format:s,formatType:o,...a}=e[r];if(Nn(o)&&!n.customFormatTypes)return v(fc(t)),$r({...e,[r]:a},t,n,i)}}return S(e)?ca(e,t,i):ey(e)}function ey(e){let t=e.type;if(t)return e;const{datum:n}=e;return t=ie(n)?"quantitative":I(n)?"nominal":In(n)?"temporal":void 0,{...e,type:t}}function ca(e,t,{compositeMark:n=!1}={}){const{aggregate:i,timeUnit:r,bin:s,field:o}=e,a={...e};if(!n&&i&&!Lo(i)&&!tn(i)&&!At(i)&&(v(nh(i)),delete a.aggregate),r&&(a.timeUnit=me(r)),o&&(a.field=`${o}`),ee(s)&&(a.bin=Zr(s,t)),xe(s)&&!ue(t)&&v(Ih(t)),Ce(a)){const{type:c}=a,l=sm(c);c!==l&&(a.type=l),c!=="quantitative"&&uu(i)&&(v(th(c,i)),a.type="quantitative")}else if(!ru(t)){const c=Zm(a,t);a.type=c}if(Ce(a)){const{compatible:c,warning:l}=ty(a,t)||{};c===!1&&v(l)}if(sa(a)&&I(a.sort)){const{sort:c}=a;if(yc(c))return{...a,sort:{encoding:c}};const l=c.substr(1);if(c.charAt(0)==="-"&&yc(l))return{...a,sort:{encoding:l,order:"descending"}}}if(ra(a)){const{header:c}=a;if(c){const{orient:l,...u}=c;if(l)return{...a,header:{...u,labelOrient:c.labelOrient||l,titleOrient:c.titleOrient||l}}}}return a}function Zr(e,t){return di(e)?{maxbins:nc(t)}:e==="binned"?{binned:!0}:!e.maxbins&&!e.step?{...e,maxbins:nc(t)}:e}const jn={compatible:!0};function ty(e,t){const n=e.type;if(n==="geojson"&&t!=="shape")return{compatible:!1,warning:`Channel ${t} should not be used with a geojson data.`};switch(t){case Nt:case Ft:case Lr:return Sr(e)?jn:{compatible:!1,warning:oh(t)};case se:case be:case Kt:case gi:case Ae:case yt:case bt:case Gi:case Hi:case Pr:case En:case zr:case Dr:case On:case We:case tt:case jr:return jn;case it:case Be:case nt:case rt:return n!==wn?{compatible:!1,warning:`Channel ${t} should be used with a quantitative field only, not ${e.type} field.`}:jn;case Lt:case Qt:case Jt:case Zt:case It:case Rt:case _t:case et:case mt:return n==="nominal"&&!e.sort?{compatible:!1,warning:`Channel ${t} should not be used with an unsorted discrete field.`}:jn;case Oe:case en:return!Sr(e)&&!Xm(e)?{compatible:!1,warning:ah(t)}:jn;case Zn:return e.type==="nominal"&&!("sort"in e)?{compatible:!1,warning:"Channel order is inappropriate for nominal field, which has no inherent order."}:jn}}function si(e){const{formatType:t}=Er(e);return t==="time"||!t&&ny(e)}function ny(e){return e&&(e.type==="temporal"||S(e)&&!!e.timeUnit)}function es(e,{timeUnit:t,type:n,wrapTime:i,undefinedIfExprNotRequired:r}){const s=t&&me(t)?.unit;let o=s||n==="temporal",a;return Vi(e)?a=e.expr:k(e)?a=e.signal:In(e)?(o=!0,a=$n(e)):(I(e)||ie(e))&&o&&(a=`datetime(${Q(e)})`,qh(s)&&(ie(e)&&e<1e4||I(e)&&isNaN(Date.parse(e)))&&(a=$n({[s]:e}))),a?i&&o?`time(${a})`:a:r?void 0:Q(e)}function pf(e,t){const{type:n}=e;return t.map(i=>{const r=S(e)&&!Ln(e.timeUnit)?e.timeUnit:void 0,s=es(i,{timeUnit:r,type:n,undefinedIfExprNotRequired:!0});return s!==void 0?{signal:s}:i})}function Ji(e,t){return ee(e.bin)?Pt(t)&&["ordinal","nominal"].includes(e.type):(console.warn("Only call this method for binned field defs."),!1)}const vc={labelAlign:{part:"labels",vgProp:"align"},labelBaseline:{part:"labels",vgProp:"baseline"},labelColor:{part:"labels",vgProp:"fill"},labelFont:{part:"labels",vgProp:"font"},labelFontSize:{part:"labels",vgProp:"fontSize"},labelFontStyle:{part:"labels",vgProp:"fontStyle"},labelFontWeight:{part:"labels",vgProp:"fontWeight"},labelOpacity:{part:"labels",vgProp:"opacity"},labelOffset:null,labelPadding:null,gridColor:{part:"grid",vgProp:"stroke"},gridDash:{part:"grid",vgProp:"strokeDash"},gridDashOffset:{part:"grid",vgProp:"strokeDashOffset"},gridOpacity:{part:"grid",vgProp:"opacity"},gridWidth:{part:"grid",vgProp:"strokeWidth"},tickColor:{part:"ticks",vgProp:"stroke"},tickDash:{part:"ticks",vgProp:"strokeDash"},tickDashOffset:{part:"ticks",vgProp:"strokeDashOffset"},tickOpacity:{part:"ticks",vgProp:"opacity"},tickSize:null,tickWidth:{part:"ticks",vgProp:"strokeWidth"}};function Zi(e){return e?.condition}const gf=["domain","grid","labels","ticks","title"],iy={grid:"grid",gridCap:"grid",gridColor:"grid",gridDash:"grid",gridDashOffset:"grid",gridOpacity:"grid",gridScale:"grid",gridWidth:"grid",orient:"main",bandPosition:"both",aria:"main",description:"main",domain:"main",domainCap:"main",domainColor:"main",domainDash:"main",domainDashOffset:"main",domainOpacity:"main",domainWidth:"main",format:"main",formatType:"main",labelAlign:"main",labelAngle:"main",labelBaseline:"main",labelBound:"main",labelColor:"main",labelFlush:"main",labelFlushOffset:"main",labelFont:"main",labelFontSize:"main",labelFontStyle:"main",labelFontWeight:"main",labelLimit:"main",labelLineHeight:"main",labelOffset:"main",labelOpacity:"main",labelOverlap:"main",labelPadding:"main",labels:"main",labelSeparation:"main",maxExtent:"main",minExtent:"main",offset:"both",position:"main",tickCap:"main",tickColor:"main",tickDash:"main",tickDashOffset:"main",tickMinStep:"both",tickOffset:"both",tickOpacity:"main",tickRound:"both",ticks:"main",tickSize:"main",tickWidth:"both",title:"main",titleAlign:"main",titleAnchor:"main",titleAngle:"main",titleBaseline:"main",titleColor:"main",titleFont:"main",titleFontSize:"main",titleFontStyle:"main",titleFontWeight:"main",titleLimit:"main",titleLineHeight:"main",titleOpacity:"main",titlePadding:"main",titleX:"main",titleY:"main",encode:"both",scale:"both",tickBand:"both",tickCount:"both",tickExtra:"both",translate:"both",values:"both",zindex:"both"},hf={orient:1,aria:1,bandPosition:1,description:1,domain:1,domainCap:1,domainColor:1,domainDash:1,domainDashOffset:1,domainOpacity:1,domainWidth:1,format:1,formatType:1,grid:1,gridCap:1,gridColor:1,gridDash:1,gridDashOffset:1,gridOpacity:1,gridWidth:1,labelAlign:1,labelAngle:1,labelBaseline:1,labelBound:1,labelColor:1,labelFlush:1,labelFlushOffset:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelLineHeight:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labels:1,labelSeparation:1,maxExtent:1,minExtent:1,offset:1,position:1,tickBand:1,tickCap:1,tickColor:1,tickCount:1,tickDash:1,tickDashOffset:1,tickExtra:1,tickMinStep:1,tickOffset:1,tickOpacity:1,tickRound:1,ticks:1,tickSize:1,tickWidth:1,title:1,titleAlign:1,titleAnchor:1,titleAngle:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titlePadding:1,titleX:1,titleY:1,translate:1,values:1,zindex:1},ry={...hf,style:1,labelExpr:1,encoding:1};function Sc(e){return!!ry[e]}const sy={axis:1,axisBand:1,axisBottom:1,axisDiscrete:1,axisLeft:1,axisPoint:1,axisQuantitative:1,axisRight:1,axisTemporal:1,axisTop:1,axisX:1,axisXBand:1,axisXDiscrete:1,axisXPoint:1,axisXQuantitative:1,axisXTemporal:1,axisY:1,axisYBand:1,axisYDiscrete:1,axisYPoint:1,axisYQuantitative:1,axisYTemporal:1},mf=x(sy);function zt(e){return"mark"in e}class ts{constructor(t,n){this.name=t,this.run=n}hasMatchingType(t){return zt(t)?Im(t.mark)===this.name:!1}}function yn(e,t){const n=e&&e[t];return n?N(n)?vn(n,i=>!!i.field):S(n)||Jr(n):!1}function yf(e,t){const n=e&&e[t];return n?N(n)?vn(n,i=>!!i.field):S(n)||St(n)||Qi(n):!1}function bf(e,t){if(ue(t)){const n=e[t];if((S(n)||St(n))&&(Du(n.type)||S(n)&&n.timeUnit)){const i=Oo(t);return yf(e,i)}}return!1}function la(e){return vn(og,t=>{if(yn(e,t)){const n=e[t];if(N(n))return vn(n,i=>!!i.aggregate);{const i=gt(n);return i&&!!i.aggregate}}return!1})}function xf(e,t){const n=[],i=[],r=[],s=[],o={};return ua(e,(a,c)=>{if(S(a)){const{field:l,aggregate:u,bin:f,timeUnit:d,...g}=a;if(u||d||f){const h=oa(a)?.title;let m=E(a,{forAs:!0});const y={...h?[]:{title:Vn(a,t,{allowDisabling:!0})},...g,field:m};if(u){let b;if(tn(u)?(b="argmax",m=E({op:"argmax",field:u.argmax},{forAs:!0}),y.field=`${m}.${l}`):At(u)?(b="argmin",m=E({op:"argmin",field:u.argmin},{forAs:!0}),y.field=`${m}.${l}`):u!=="boxplot"&&u!=="errorbar"&&u!=="errorband"&&(b=u),b){const C={op:b,as:m};l&&(C.field=l),s.push(C)}}else if(n.push(m),Ce(a)&&ee(f)){if(i.push({bin:f,field:l,as:m}),n.push(E(a,{binSuffix:"end"})),Ji(a,c)&&n.push(E(a,{binSuffix:"range"})),ue(c)){const b={field:`${m}_end`};o[`${c}2`]=b}y.bin="binned",ru(c)||(y.type=wn)}else if(d&&!Ln(d)){r.push({timeUnit:d,field:l,as:m});const b=Ce(a)&&a.type!==ti&&"time";b&&(c===Gi||c===En?y.formatType=b:mg(c)?y.legend={formatType:b,...y.legend}:ue(c)&&(y.axis={formatType:b,...y.axis}))}o[c]=y}else n.push(l),o[c]=e[c]}else o[c]=e[c]}),{bins:i,timeUnits:r,aggregate:s,groupby:n,encoding:o}}function oy(e,t,n){const i=bg(t,n);if(i){if(i==="binned"){const r=e[t===et?se:be];return!!(S(r)&&S(e[t])&&xe(r.bin))}}else return!1;return!0}function ay(e,t,n,i){const r={};for(const s of x(e))iu(s)||v(sh(s));for(let s of dg){if(!e[s])continue;const o=e[s];if(qi(s)){const a=fg(s),c=r[a];if(S(c)&&rm(c.type)&&S(o)&&!c.timeUnit){v(Zg(a));continue}}if(s==="angle"&&t==="arc"&&!e.theta&&(v(Jg),s=We),!oy(e,s,t)){v(Wr(s,t));continue}if(s===It&&t==="line"&&gt(e[s])?.aggregate){v(rh);continue}if(s===Ae&&(n?"fill"in e:"stroke"in e)){v(Eu("encoding",{fill:"fill"in e,stroke:"stroke"in e}));continue}if(s===Hi||s===Zn&&!N(o)&&!Je(o)||s===En&&N(o)){if(o){if(s===Zn){const a=e[s];if(of(a)){r[s]=a;continue}}r[s]=ce(o).reduce((a,c)=>(S(c)?a.push(ca(c,s)):v(Qs(c,s)),a),[])}}else{if(s===En&&o===null)r[s]=null;else if(!S(o)&&!St(o)&&!Je(o)&&!Qr(o)&&!k(o)){v(Qs(o,s));continue}r[s]=df(o,s,i)}}return r}function ns(e,t){const n={};for(const i of x(e)){const r=df(e[i],i,t,{compositeMark:!0});n[i]=r}return n}function cy(e){const t=[];for(const n of x(e))if(yn(e,n)){const i=e[n],r=ce(i);for(const s of r)S(s)?t.push(s):Jr(s)&&t.push(s.condition)}return t}function ua(e,t,n){if(e)for(const i of x(e)){const r=e[i];if(N(r))for(const s of r)t.call(n,s,i);else t.call(n,r,i)}}function ly(e,t,n,i){return e?x(e).reduce((r,s)=>{const o=e[s];return N(o)?o.reduce((a,c)=>t.call(i,a,c,s),r):t.call(i,r,o,s)},n):n}function vf(e,t){return x(t).reduce((n,i)=>{switch(i){case se:case be:case zr:case jr:case Dr:case et:case mt:case Kt:case gi:case We:case Rt:case tt:case _t:case nt:case it:case rt:case Be:case Gi:case Oe:case On:case En:return n;case Zn:if(e==="line"||e==="trail")return n;case Hi:case Pr:{const r=t[i];if(N(r)||S(r))for(const s of ce(r))s.aggregate||n.push(E(s,{}));return n}case It:if(e==="trail")return n;case Ae:case yt:case bt:case Lt:case Qt:case Jt:case en:case Zt:{const r=gt(t[i]);return r&&!r.aggregate&&n.push(E(r,{})),n}}},[])}function uy(e){const{tooltip:t,...n}=e;if(!t)return{filteredEncoding:n};let i,r;if(N(t)){for(const s of t)s.aggregate?(i||(i=[]),i.push(s)):(r||(r=[]),r.push(s));i&&(n.tooltip=i)}else t.aggregate?n.tooltip=t:r=t;return N(r)&&r.length===1&&(r=r[0]),{customTooltipWithoutAggregatedField:r,filteredEncoding:n}}function to(e,t,n,i=!0){if("tooltip"in n)return{tooltip:n.tooltip};const r=e.map(({fieldPrefix:o,titlePrefix:a})=>{const c=i?` of ${fa(t)}`:"";return{field:o+t.field,type:t.type,title:k(a)?{signal:`${a}"${escape(c)}"`}:a+c}}),s=cy(n).map(qm);return{tooltip:[...r,...lt(s,W)]}}function fa(e){const{title:t,field:n}=e;return le(t,n)}function da(e,t,n,i,r){const{scale:s,axis:o}=n;return({partName:a,mark:c,positionPrefix:l,endPositionPrefix:u=void 0,extraEncoding:f={}})=>{const d=fa(n);return Sf(e,a,r,{mark:c,encoding:{[t]:{field:`${l}_${n.field}`,type:n.type,...d!==void 0?{title:d}:{},...s!==void 0?{scale:s}:{},...o!==void 0?{axis:o}:{}},...I(u)?{[`${t}2`]:{field:`${u}_${n.field}`}}:{},...i,...f}})}}function Sf(e,t,n,i){const{clip:r,color:s,opacity:o}=e,a=e.type;return e[t]||e[t]===void 0&&n[t]?[{...i,mark:{...n[t],...r?{clip:r}:{},...s?{color:s}:{},...o?{opacity:o}:{},...pt(i.mark)?i.mark:{type:i.mark},style:`${a}-${String(t)}`,...di(e[t])?{}:e[t]}}]:[]}function Ef(e,t,n){const{encoding:i}=e,r=t==="vertical"?"y":"x",s=i[r],o=i[`${r}2`],a=i[`${r}Error`],c=i[`${r}Error2`];return{continuousAxisChannelDef:rr(s,n),continuousAxisChannelDef2:rr(o,n),continuousAxisChannelDefError:rr(a,n),continuousAxisChannelDefError2:rr(c,n),continuousAxis:r}}function rr(e,t){if(e?.aggregate){const{aggregate:n,...i}=e;return n!==t&&v(Rh(n,t)),i}else return e}function $f(e,t){const{mark:n,encoding:i}=e,{x:r,y:s}=i;if(pt(n)&&n.orient)return n.orient;if(Ut(r)){if(Ut(s)){const o=S(r)&&r.aggregate,a=S(s)&&s.aggregate;if(!o&&a===t)return"vertical";if(!a&&o===t)return"horizontal";if(o===t&&a===t)throw new Error("Both x and y cannot have aggregate");return si(s)&&!si(r)?"horizontal":"vertical"}return"horizontal"}else{if(Ut(s))return"vertical";throw new Error(`Need a valid continuous axis for ${t}s`)}}const wr="boxplot",fy=["box","median","outliers","rule","ticks"],dy=new ts(wr,Cf);function wf(e){return ie(e)?"tukey":e}function Cf(e,{config:t}){e={...e,encoding:ns(e.encoding,t)};const{mark:n,encoding:i,params:r,projection:s,...o}=e,a=pt(n)?n:{type:n};r&&v(vu("boxplot"));const c=a.extent??t.boxplot.extent,l=V("size",a,t),u=a.invalid,f=wf(c),{bins:d,timeUnits:g,transform:p,continuousAxisChannelDef:h,continuousAxis:m,groupby:y,aggregate:b,encodingWithoutContinuousAxis:C,ticksOrient:O,boxOrient:F,customTooltipWithoutAggregatedField:_}=py(e,c,t),{color:P,size:U,...oe}=C,de=Sp=>da(a,m,h,Sp,t.boxplot),Re=de(oe),sn=de(C),w=de({...oe,...U?{size:U}:{}}),$=to([{fieldPrefix:f==="min-max"?"upper_whisker_":"max_",titlePrefix:"Max"},{fieldPrefix:"upper_box_",titlePrefix:"Q3"},{fieldPrefix:"mid_box_",titlePrefix:"Median"},{fieldPrefix:"lower_box_",titlePrefix:"Q1"},{fieldPrefix:f==="min-max"?"lower_whisker_":"min_",titlePrefix:"Min"}],h,C),R={type:"tick",color:"black",opacity:1,orient:O,invalid:u,aria:!1},A=f==="min-max"?$:to([{fieldPrefix:"upper_whisker_",titlePrefix:"Upper Whisker"},{fieldPrefix:"lower_whisker_",titlePrefix:"Lower Whisker"}],h,C),L=[...Re({partName:"rule",mark:{type:"rule",invalid:u,aria:!1},positionPrefix:"lower_whisker",endPositionPrefix:"lower_box",extraEncoding:A}),...Re({partName:"rule",mark:{type:"rule",invalid:u,aria:!1},positionPrefix:"upper_box",endPositionPrefix:"upper_whisker",extraEncoding:A}),...Re({partName:"ticks",mark:R,positionPrefix:"lower_whisker",extraEncoding:A}),...Re({partName:"ticks",mark:R,positionPrefix:"upper_whisker",extraEncoding:A})],T=[...f!=="tukey"?L:[],...sn({partName:"box",mark:{type:"bar",...l?{size:l}:{},orient:F,invalid:u,ariaRoleDescription:"box"},positionPrefix:"lower_box",endPositionPrefix:"upper_box",extraEncoding:$}),...w({partName:"median",mark:{type:"tick",invalid:u,...X(t.boxplot.median)&&t.boxplot.median.color?{color:t.boxplot.median.color}:{},...l?{size:l}:{},orient:O,aria:!1},positionPrefix:"mid_box",extraEncoding:$})];if(f==="min-max")return{...o,transform:(o.transform??[]).concat(p),layer:T};const j=`datum["lower_box_${h.field}"]`,q=`datum["upper_box_${h.field}"]`,M=`(${q} - ${j})`,J=`${j} - ${c} * ${M}`,Se=`${q} + ${c} * ${M}`,te=`datum["${h.field}"]`,on={joinaggregate:Nf(h.field),groupby:y},Cs={transform:[{filter:`(${J} <= ${te}) && (${te} <= ${Se})`},{aggregate:[{op:"min",field:h.field,as:`lower_whisker_${h.field}`},{op:"max",field:h.field,as:`upper_whisker_${h.field}`},{op:"min",field:`lower_box_${h.field}`,as:`lower_box_${h.field}`},{op:"max",field:`upper_box_${h.field}`,as:`upper_box_${h.field}`},...b],groupby:y}],layer:L},{tooltip:ow,...xp}=oe,{scale:Ka,axis:vp}=h,Qa=fa(h),Ja=ke(vp,["title"]),Za=Sf(a,"outliers",t.boxplot,{transform:[{filter:`(${te} < ${J}) || (${te} > ${Se})`}],mark:"point",encoding:{[m]:{field:h.field,type:h.type,...Qa!==void 0?{title:Qa}:{},...Ka!==void 0?{scale:Ka}:{},...K(Ja)?{}:{axis:Ja}},...xp,...P?{color:P}:{},..._?{tooltip:_}:{}}})[0];let ir;const ec=[...d,...g,on];return Za?ir={transform:ec,layer:[Za,Cs]}:(ir=Cs,ir.transform.unshift(...ec)),{...o,layer:[ir,{transform:p,layer:T}]}}function Nf(e){return[{op:"q1",field:e,as:`lower_box_${e}`},{op:"q3",field:e,as:`upper_box_${e}`}]}function py(e,t,n){const i=$f(e,wr),{continuousAxisChannelDef:r,continuousAxis:s}=Ef(e,i,wr),o=r.field,a=wf(t),c=[...Nf(o),{op:"median",field:o,as:`mid_box_${o}`},{op:"min",field:o,as:(a==="min-max"?"lower_whisker_":"min_")+o},{op:"max",field:o,as:(a==="min-max"?"upper_whisker_":"max_")+o}],l=a==="min-max"||a==="tukey"?[]:[{calculate:`datum["upper_box_${o}"] - datum["lower_box_${o}"]`,as:`iqr_${o}`},{calculate:`min(datum["upper_box_${o}"] + datum["iqr_${o}"] * ${t}, datum["max_${o}"])`,as:`upper_whisker_${o}`},{calculate:`max(datum["lower_box_${o}"] - datum["iqr_${o}"] * ${t}, datum["min_${o}"])`,as:`lower_whisker_${o}`}],{[s]:u,...f}=e.encoding,{customTooltipWithoutAggregatedField:d,filteredEncoding:g}=uy(f),{bins:p,timeUnits:h,aggregate:m,groupby:y,encoding:b}=xf(g,n),C=i==="vertical"?"horizontal":"vertical",O=i,F=[...p,...h,{aggregate:[...m,...c],groupby:y},...l];return{bins:p,timeUnits:h,transform:F,groupby:y,aggregate:m,continuousAxisChannelDef:r,continuousAxis:s,encodingWithoutContinuousAxis:b,ticksOrient:C,boxOrient:O,customTooltipWithoutAggregatedField:d}}const pa="errorbar",gy=["ticks","rule"],hy=new ts(pa,Ff);function Ff(e,{config:t}){e={...e,encoding:ns(e.encoding,t)};const{transform:n,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,ticksOrient:o,markDef:a,outerSpec:c,tooltipEncoding:l}=kf(e,pa,t);delete s.size;const u=da(a,r,i,s,t.errorbar),f=a.thickness,d=a.size,g={type:"tick",orient:o,aria:!1,...f!==void 0?{thickness:f}:{},...d!==void 0?{size:d}:{}},p=[...u({partName:"ticks",mark:g,positionPrefix:"lower",extraEncoding:l}),...u({partName:"ticks",mark:g,positionPrefix:"upper",extraEncoding:l}),...u({partName:"rule",mark:{type:"rule",ariaRoleDescription:"errorbar",...f!==void 0?{size:f}:{}},positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:l})];return{...c,transform:n,...p.length>1?{layer:p}:{...p[0]}}}function my(e,t){const{encoding:n}=e;if(yy(n))return{orient:$f(e,t),inputType:"raw"};const i=by(n),r=xy(n),s=n.x,o=n.y;if(i){if(r)throw new Error(`${t} cannot be both type aggregated-upper-lower and aggregated-error`);const a=n.x2,c=n.y2;if(D(a)&&D(c))throw new Error(`${t} cannot have both x2 and y2`);if(D(a)){if(Ut(s))return{orient:"horizontal",inputType:"aggregated-upper-lower"};throw new Error(`Both x and x2 have to be quantitative in ${t}`)}else if(D(c)){if(Ut(o))return{orient:"vertical",inputType:"aggregated-upper-lower"};throw new Error(`Both y and y2 have to be quantitative in ${t}`)}throw new Error("No ranged axis")}else{const a=n.xError,c=n.xError2,l=n.yError,u=n.yError2;if(D(c)&&!D(a))throw new Error(`${t} cannot have xError2 without xError`);if(D(u)&&!D(l))throw new Error(`${t} cannot have yError2 without yError`);if(D(a)&&D(l))throw new Error(`${t} cannot have both xError and yError with both are quantiative`);if(D(a)){if(Ut(s))return{orient:"horizontal",inputType:"aggregated-error"};throw new Error("All x, xError, and xError2 (if exist) have to be quantitative")}else if(D(l)){if(Ut(o))return{orient:"vertical",inputType:"aggregated-error"};throw new Error("All y, yError, and yError2 (if exist) have to be quantitative")}throw new Error("No ranged axis")}}function yy(e){return(D(e.x)||D(e.y))&&!D(e.x2)&&!D(e.y2)&&!D(e.xError)&&!D(e.xError2)&&!D(e.yError)&&!D(e.yError2)}function by(e){return D(e.x2)||D(e.y2)}function xy(e){return D(e.xError)||D(e.xError2)||D(e.yError)||D(e.yError2)}function kf(e,t,n){const{mark:i,encoding:r,params:s,projection:o,...a}=e,c=pt(i)?i:{type:i};s&&v(vu(t));const{orient:l,inputType:u}=my(e,t),{continuousAxisChannelDef:f,continuousAxisChannelDef2:d,continuousAxisChannelDefError:g,continuousAxisChannelDefError2:p,continuousAxis:h}=Ef(e,l,t),{errorBarSpecificAggregate:m,postAggregateCalculates:y,tooltipSummary:b,tooltipTitleWithFieldName:C}=vy(c,f,d,g,p,u,t,n),{[h]:O,[h==="x"?"x2":"y2"]:F,[h==="x"?"xError":"yError"]:_,[h==="x"?"xError2":"yError2"]:P,...U}=r,{bins:oe,timeUnits:de,aggregate:Re,groupby:sn,encoding:w}=xf(U,n),$=[...Re,...m],R=u!=="raw"?[]:sn,A=to(b,f,w,C);return{transform:[...a.transform??[],...oe,...de,...$.length===0?[]:[{aggregate:$,groupby:R}],...y],groupby:R,continuousAxisChannelDef:f,continuousAxis:h,encodingWithoutContinuousAxis:w,ticksOrient:l==="vertical"?"horizontal":"vertical",markDef:c,outerSpec:a,tooltipEncoding:A}}function vy(e,t,n,i,r,s,o,a){let c=[],l=[];const u=t.field;let f,d=!1;if(s==="raw"){const g=e.center?e.center:e.extent?e.extent==="iqr"?"median":"mean":a.errorbar.center,p=e.extent?e.extent:g==="mean"?"stderr":"iqr";if(g==="median"!=(p==="iqr")&&v(_h(g,p,o)),p==="stderr"||p==="stdev")c=[{op:p,field:u,as:`extent_${u}`},{op:g,field:u,as:`center_${u}`}],l=[{calculate:`datum["center_${u}"] + datum["extent_${u}"]`,as:`upper_${u}`},{calculate:`datum["center_${u}"] - datum["extent_${u}"]`,as:`lower_${u}`}],f=[{fieldPrefix:"center_",titlePrefix:Wi(g)},{fieldPrefix:"upper_",titlePrefix:Ec(g,p,"+")},{fieldPrefix:"lower_",titlePrefix:Ec(g,p,"-")}],d=!0;else{let h,m,y;p==="ci"?(h="mean",m="ci0",y="ci1"):(h="median",m="q1",y="q3"),c=[{op:m,field:u,as:`lower_${u}`},{op:y,field:u,as:`upper_${u}`},{op:h,field:u,as:`center_${u}`}],f=[{fieldPrefix:"upper_",titlePrefix:Vn({field:u,aggregate:y,type:"quantitative"},a,{allowDisabling:!1})},{fieldPrefix:"lower_",titlePrefix:Vn({field:u,aggregate:m,type:"quantitative"},a,{allowDisabling:!1})},{fieldPrefix:"center_",titlePrefix:Vn({field:u,aggregate:h,type:"quantitative"},a,{allowDisabling:!1})}]}}else{(e.center||e.extent)&&v(Oh(e.center,e.extent)),s==="aggregated-upper-lower"?(f=[],l=[{calculate:`datum["${n.field}"]`,as:`upper_${u}`},{calculate:`datum["${u}"]`,as:`lower_${u}`}]):s==="aggregated-error"&&(f=[{fieldPrefix:"",titlePrefix:u}],l=[{calculate:`datum["${u}"] + datum["${i.field}"]`,as:`upper_${u}`}],r?l.push({calculate:`datum["${u}"] + datum["${r.field}"]`,as:`lower_${u}`}):l.push({calculate:`datum["${u}"] - datum["${i.field}"]`,as:`lower_${u}`}));for(const g of l)f.push({fieldPrefix:g.as.substring(0,6),titlePrefix:Sn(Sn(g.calculate,'datum["',""),'"]',"")})}return{postAggregateCalculates:l,errorBarSpecificAggregate:c,tooltipSummary:f,tooltipTitleWithFieldName:d}}function Ec(e,t,n){return`${Wi(e)} ${n} ${t}`}const ga="errorband",Sy=["band","borders"],Ey=new ts(ga,Tf);function Tf(e,{config:t}){e={...e,encoding:ns(e.encoding,t)};const{transform:n,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,markDef:o,outerSpec:a,tooltipEncoding:c}=kf(e,ga,t),l=o,u=da(l,r,i,s,t.errorband),f=e.encoding.x!==void 0&&e.encoding.y!==void 0;let d={type:f?"area":"rect"},g={type:f?"line":"rule"};const p={...l.interpolate?{interpolate:l.interpolate}:{},...l.tension&&l.interpolate?{tension:l.tension}:{}};return f?(d={...d,...p,ariaRoleDescription:"errorband"},g={...g,...p,aria:!1}):l.interpolate?v(pc("interpolate")):l.tension&&v(pc("tension")),{...a,transform:n,layer:[...u({partName:"band",mark:d,positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:c}),...u({partName:"borders",mark:g,positionPrefix:"lower",extraEncoding:c}),...u({partName:"borders",mark:g,positionPrefix:"upper",extraEncoding:c})]}}const Af={};function ha(e,t,n){const i=new ts(e,t);Af[e]={normalizer:i,parts:n}}function $y(){return x(Af)}ha(wr,Cf,fy);ha(pa,Ff,gy);ha(ga,Tf,Sy);const wy=["gradientHorizontalMaxLength","gradientHorizontalMinLength","gradientVerticalMaxLength","gradientVerticalMinLength","unselectedOpacity"],Of={titleAlign:"align",titleAnchor:"anchor",titleAngle:"angle",titleBaseline:"baseline",titleColor:"color",titleFont:"font",titleFontSize:"fontSize",titleFontStyle:"fontStyle",titleFontWeight:"fontWeight",titleLimit:"limit",titleLineHeight:"lineHeight",titleOrient:"orient",titlePadding:"offset"},_f={labelAlign:"align",labelAnchor:"anchor",labelAngle:"angle",labelBaseline:"baseline",labelColor:"color",labelFont:"font",labelFontSize:"fontSize",labelFontStyle:"fontStyle",labelFontWeight:"fontWeight",labelLimit:"limit",labelLineHeight:"lineHeight",labelOrient:"orient",labelPadding:"offset"},Cy=x(Of),Ny=x(_f),Fy={header:1,headerRow:1,headerColumn:1,headerFacet:1},Rf=x(Fy),If=["size","shape","fill","stroke","strokeDash","strokeWidth","opacity"],ky={gradientHorizontalMaxLength:200,gradientHorizontalMinLength:100,gradientVerticalMaxLength:200,gradientVerticalMinLength:64,unselectedOpacity:.35},Ty={aria:1,clipHeight:1,columnPadding:1,columns:1,cornerRadius:1,description:1,direction:1,fillColor:1,format:1,formatType:1,gradientLength:1,gradientOpacity:1,gradientStrokeColor:1,gradientStrokeWidth:1,gradientThickness:1,gridAlign:1,labelAlign:1,labelBaseline:1,labelColor:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labelSeparation:1,legendX:1,legendY:1,offset:1,orient:1,padding:1,rowPadding:1,strokeColor:1,symbolDash:1,symbolDashOffset:1,symbolFillColor:1,symbolLimit:1,symbolOffset:1,symbolOpacity:1,symbolSize:1,symbolStrokeColor:1,symbolStrokeWidth:1,symbolType:1,tickCount:1,tickMinStep:1,title:1,titleAlign:1,titleAnchor:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titleOrient:1,titlePadding:1,type:1,values:1,zindex:1},Ze="_vgsid_",Ay={point:{on:"click",fields:[Ze],toggle:"event.shiftKey",resolve:"global",clear:"dblclick"},interval:{on:"[pointerdown, window:pointerup] > window:pointermove!",encodings:["x","y"],translate:"[pointerdown, window:pointerup] > window:pointermove!",zoom:"wheel!",mark:{fill:"#333",fillOpacity:.125,stroke:"white"},resolve:"global",clear:"dblclick"}};function ma(e){return e==="legend"||!!e?.legend}function Ts(e){return ma(e)&&X(e)}function ya(e){return!!e?.select}function Lf(e){const t=[];for(const n of e||[]){if(ya(n))continue;const{expr:i,bind:r,...s}=n;if(r&&i){const o={...s,bind:r,init:i};t.push(o)}else{const o={...s,...i?{update:i}:{},...r?{bind:r}:{}};t.push(o)}}return t}function Oy(e){return is(e)||xa(e)||ba(e)}function ba(e){return"concat"in e}function is(e){return"vconcat"in e}function xa(e){return"hconcat"in e}function Pf({step:e,offsetIsDiscrete:t}){return t?e.for??"offset":"position"}function ht(e){return X(e)&&e.step!==void 0}function $c(e){return e.view||e.width||e.height}const wc=20,_y={align:1,bounds:1,center:1,columns:1,spacing:1},Ry=x(_y);function Iy(e,t,n){const i=n[t],r={},{spacing:s,columns:o}=i;s!==void 0&&(r.spacing=s),o!==void 0&&(Kr(e)&&!Ki(e.facet)||ba(e))&&(r.columns=o),is(e)&&(r.columns=1);for(const a of Ry)if(e[a]!==void 0)if(a==="spacing"){const c=e[a];r[a]=ie(c)?c:{row:c.row??s,column:c.column??s}}else r[a]=e[a];return r}function no(e,t){return e[t]??e[t==="width"?"continuousWidth":"continuousHeight"]}function Cr(e,t){const n=Nr(e,t);return ht(n)?n.step:zf}function Nr(e,t){const n=e[t]??e[t==="width"?"discreteWidth":"discreteHeight"];return le(n,{step:e.step})}const zf=20,Ly={continuousWidth:200,continuousHeight:200,step:zf},Py={background:"white",padding:5,timeFormat:"%b %d, %Y",countTitle:"Count of Records",view:Ly,mark:km,arc:{},area:{},bar:Om,circle:{},geoshape:{},image:{},line:{},point:{},rect:_m,rule:{color:"black"},square:{},text:{color:"black"},tick:Rm,trail:{},boxplot:{size:14,extent:1.5,box:{},median:{color:"white"},outliers:{},rule:{},ticks:null},errorbar:{center:"mean",rule:!0,ticks:!1},errorband:{band:{opacity:.3},borders:!1},scale:um,projection:{},legend:ky,header:{titlePadding:10,labelPadding:10},headerColumn:{},headerRow:{},headerFacet:{},selection:Ay,style:{},title:{},facet:{spacing:wc},concat:{spacing:wc},normalizedNumberFormat:".0%"},Et=["#4c78a8","#f58518","#e45756","#72b7b2","#54a24b","#eeca3b","#b279a2","#ff9da6","#9d755d","#bab0ac"],Cc={text:11,guideLabel:10,guideTitle:11,groupTitle:13,groupSubtitle:12},Nc={blue:Et[0],orange:Et[1],red:Et[2],teal:Et[3],green:Et[4],yellow:Et[5],purple:Et[6],pink:Et[7],brown:Et[8],gray0:"#000",gray1:"#111",gray2:"#222",gray3:"#333",gray4:"#444",gray5:"#555",gray6:"#666",gray7:"#777",gray8:"#888",gray9:"#999",gray10:"#aaa",gray11:"#bbb",gray12:"#ccc",gray13:"#ddd",gray14:"#eee",gray15:"#fff"};function zy(e={}){return{signals:[{name:"color",value:X(e)?{...Nc,...e}:Nc}],mark:{color:{signal:"color.blue"}},rule:{color:{signal:"color.gray0"}},text:{color:{signal:"color.gray0"}},style:{"guide-label":{fill:{signal:"color.gray0"}},"guide-title":{fill:{signal:"color.gray0"}},"group-title":{fill:{signal:"color.gray0"}},"group-subtitle":{fill:{signal:"color.gray0"}},cell:{stroke:{signal:"color.gray8"}}},axis:{domainColor:{signal:"color.gray13"},gridColor:{signal:"color.gray8"},tickColor:{signal:"color.gray13"}},range:{category:[{signal:"color.blue"},{signal:"color.orange"},{signal:"color.red"},{signal:"color.teal"},{signal:"color.green"},{signal:"color.yellow"},{signal:"color.purple"},{signal:"color.pink"},{signal:"color.brown"},{signal:"color.grey8"}]}}}function Dy(e){return{signals:[{name:"fontSize",value:X(e)?{...Cc,...e}:Cc}],text:{fontSize:{signal:"fontSize.text"}},style:{"guide-label":{fontSize:{signal:"fontSize.guideLabel"}},"guide-title":{fontSize:{signal:"fontSize.guideTitle"}},"group-title":{fontSize:{signal:"fontSize.groupTitle"}},"group-subtitle":{fontSize:{signal:"fontSize.groupSubtitle"}}}}}function jy(e){return{text:{font:e},style:{"guide-label":{font:e},"guide-title":{font:e},"group-title":{font:e},"group-subtitle":{font:e}}}}function Df(e){const t=x(e||{}),n={};for(const i of t){const r=e[i];n[i]=Zi(r)?pu(r):Le(r)}return n}function My(e){const t=x(e),n={};for(const i of t)n[i]=Df(e[i]);return n}const Uy=[...Xu,...mf,...Rf,"background","padding","legend","lineBreak","scale","style","title","view"];function jf(e={}){const{color:t,font:n,fontSize:i,selection:r,...s}=e,o=Rr({},z(Py),n?jy(n):{},t?zy(t):{},i?Dy(i):{},s||{});r&&xo(o,"selection",r,!0);const a=ke(o,Uy);for(const c of["background","lineBreak","padding"])o[c]&&(a[c]=Le(o[c]));for(const c of Xu)o[c]&&(a[c]=Fe(o[c]));for(const c of mf)o[c]&&(a[c]=Df(o[c]));for(const c of Rf)o[c]&&(a[c]=Fe(o[c]));return o.legend&&(a.legend=Fe(o.legend)),o.scale&&(a.scale=Fe(o.scale)),o.style&&(a.style=My(o.style)),o.title&&(a.title=Fe(o.title)),o.view&&(a.view=Fe(o.view)),a}const By=new Set(["view",...Em]),Wy=["color","fontSize","background","padding","facet","concat","numberFormat","numberFormatType","normalizedNumberFormat","normalizedNumberFormatType","timeFormat","countTitle","header","axisQuantitative","axisTemporal","axisDiscrete","axisPoint","axisXBand","axisXPoint","axisXDiscrete","axisXQuantitative","axisXTemporal","axisYBand","axisYPoint","axisYDiscrete","axisYQuantitative","axisYTemporal","scale","selection","overlay"],Gy={view:["continuousWidth","continuousHeight","discreteWidth","discreteHeight","step"],...Fm};function Hy(e){e=z(e);for(const t of Wy)delete e[t];if(e.axis)for(const t in e.axis)Zi(e.axis[t])&&delete e.axis[t];if(e.legend)for(const t of wy)delete e.legend[t];if(e.mark){for(const t of hc)delete e.mark[t];e.mark.tooltip&&X(e.mark.tooltip)&&delete e.mark.tooltip}e.params&&(e.signals=(e.signals||[]).concat(Lf(e.params)),delete e.params);for(const t of By){for(const i of hc)delete e[t][i];const n=Gy[t];if(n)for(const i of n)delete e[t][i];Vy(e,t)}for(const t of $y())delete e[t];qy(e);for(const t in e)X(e[t])&&K(e[t])&&delete e[t];return K(e)?void 0:e}function qy(e){const{titleMarkConfig:t,subtitleMarkConfig:n,subtitle:i}=du(e.title);K(t)||(e.style["group-title"]={...e.style["group-title"],...t}),K(n)||(e.style["group-subtitle"]={...e.style["group-subtitle"],...n}),K(i)?delete e.title:e.title=i}function Vy(e,t,n,i){const r=e[t];t==="view"&&(n="cell");const s={...r,...e.style[n??t]};K(s)||(e.style[n??t]=s),delete e[t]}function rs(e){return"layer"in e}function Xy(e){return"repeat"in e}function Yy(e){return!N(e.repeat)&&e.repeat.layer}class va{map(t,n){return Kr(t)?this.mapFacet(t,n):Xy(t)?this.mapRepeat(t,n):xa(t)?this.mapHConcat(t,n):is(t)?this.mapVConcat(t,n):ba(t)?this.mapConcat(t,n):this.mapLayerOrUnit(t,n)}mapLayerOrUnit(t,n){if(rs(t))return this.mapLayer(t,n);if(zt(t))return this.mapUnit(t,n);throw new Error(Po(t))}mapLayer(t,n){return{...t,layer:t.layer.map(i=>this.mapLayerOrUnit(i,n))}}mapHConcat(t,n){return{...t,hconcat:t.hconcat.map(i=>this.map(i,n))}}mapVConcat(t,n){return{...t,vconcat:t.vconcat.map(i=>this.map(i,n))}}mapConcat(t,n){const{concat:i,...r}=t;return{...r,concat:i.map(s=>this.map(s,n))}}mapFacet(t,n){return{...t,spec:this.map(t.spec,n)}}mapRepeat(t,n){return{...t,spec:this.map(t.spec,n)}}}const Ky={zero:1,center:1,normalize:1};function Qy(e){return e in Ky}const Jy=new Set([Hu,qr,Hr,br,Xr,Jo,Zo,Vr,qu,Qo]),Zy=new Set([qr,Hr,Hu]);function Mn(e){return S(e)&&ii(e)==="quantitative"&&!e.bin}function Fc(e,t,{orient:n,type:i}){const r=t==="x"?"y":"radius",s=t==="x"&&["bar","area"].includes(i),o=e[t],a=e[r];if(S(o)&&S(a))if(Mn(o)&&Mn(a)){if(o.stack)return t;if(a.stack)return r;const c=S(o)&&!!o.aggregate,l=S(a)&&!!a.aggregate;if(c!==l)return c?t:r;if(s){if(n==="vertical")return r;if(n==="horizontal")return t}}else{if(Mn(o))return t;if(Mn(a))return r}else{if(Mn(o))return s&&n==="vertical"?void 0:t;if(Mn(a))return s&&n==="horizontal"?void 0:r}}function eb(e){switch(e){case"x":return"y";case"y":return"x";case"theta":return"radius";case"radius":return"theta"}}function Mf(e,t){const n=pt(e)?e:{type:e},i=n.type;if(!Jy.has(i))return null;const r=Fc(t,"x",n)||Fc(t,"theta",n);if(!r)return null;const s=t[r],o=S(s)?E(s,{}):void 0,a=eb(r),c=[],l=new Set;if(t[a]){const h=t[a],m=S(h)?E(h,{}):void 0;m&&m!==o&&(c.push(a),l.add(m))}const u=a==="x"?"xOffset":"yOffset",f=t[u],d=S(f)?E(f,{}):void 0;d&&d!==o&&(c.push(u),l.add(d));const g=pg.reduce((h,m)=>{if(m!=="tooltip"&&yn(t,m)){const y=t[m];for(const b of ce(y)){const C=gt(b);if(C.aggregate)continue;const O=E(C,{});(!O||!l.has(O))&&h.push({channel:m,fieldDef:C})}}return h},[]);let p;return s.stack!==void 0?di(s.stack)?p=s.stack?"zero":null:p=s.stack:Zy.has(i)&&(p="zero"),!p||!Qy(p)||la(t)&&g.length===0?null:s?.scale?.type&&s?.scale?.type!==Te.LINEAR?(s?.stack&&v(kh(s.scale.type)),null):D(t[xt(r)])?(s.stack!==void 0&&v(Fh(r)),null):(S(s)&&s.aggregate&&!Cg.has(s.aggregate)&&v(Th(s.aggregate)),{groupbyChannels:c,groupbyFields:l,fieldChannel:r,impute:s.impute===null?!1:rn(i),stackBy:g,offset:p})}function Uf(e,t,n){const i=Fe(e),r=V("orient",i,n);if(i.orient=rb(i.type,t,r),r!==void 0&&r!==i.orient&&v(fh(i.orient,r)),i.type==="bar"&&i.orient){const c=V("cornerRadiusEnd",i,n);if(c!==void 0){const l=i.orient==="horizontal"&&t.x2||i.orient==="vertical"&&t.y2?["cornerRadius"]:Am[i.orient];for(const u of l)i[u]=c;i.cornerRadiusEnd!==void 0&&delete i.cornerRadiusEnd}}const s=V("opacity",i,n),o=V("fillOpacity",i,n);return s===void 0&&o===void 0&&(i.opacity=nb(i.type,t)),V("cursor",i,n)===void 0&&(i.cursor=tb(i,t,n)),i}function tb(e,t,n){return t.href||e.href||V("href",e,n)?"pointer":e.cursor}function nb(e,t){if(G([Xr,Qo,Jo,Zo],e)&&!la(t))return .7}function ib(e,t,{graticule:n}){if(n)return!1;const i=Ot("filled",e,t),r=e.type;return le(i,r!==Xr&&r!==Vr&&r!==br)}function rb(e,t,n){switch(e){case Xr:case Jo:case Zo:case qu:case vm:case xm:return}const{x:i,y:r,x2:s,y2:o}=t;switch(e){case qr:if(S(i)&&(xe(i.bin)||S(r)&&r.aggregate&&!i.aggregate))return"vertical";if(S(r)&&(xe(r.bin)||S(i)&&i.aggregate&&!r.aggregate))return"horizontal";if(o||s){if(n)return n;if(!s)return(S(i)&&i.type===wn&&!ee(i.bin)||vr(i))&&S(r)&&xe(r.bin)?"horizontal":"vertical";if(!o)return(S(r)&&r.type===wn&&!ee(r.bin)||vr(r))&&S(i)&&xe(i.bin)?"vertical":"horizontal"}case br:if(s&&!(S(i)&&xe(i.bin))&&o&&!(S(r)&&xe(r.bin)))return;case Hr:if(o)return S(r)&&xe(r.bin)?"horizontal":"vertical";if(s)return S(i)&&xe(i.bin)?"vertical":"horizontal";if(e===br){if(i&&!r)return"vertical";if(r&&!i)return"horizontal"}case Vr:case Qo:{const a=xc(i),c=xc(r);if(n)return n;if(a&&!c)return e!=="tick"?"horizontal":"vertical";if(!a&&c)return e!=="tick"?"vertical":"horizontal";if(a&&c)return"vertical";{const l=Ce(i)&&i.type===ti,u=Ce(r)&&r.type===ti;if(l&&!u)return"vertical";if(!l&&u)return"horizontal"}return}}return"vertical"}function sb(e){const{point:t,line:n,...i}=e;return x(i).length>1?i:i.type}function ob(e){for(const t of["line","area","rule","trail"])e[t]&&(e={...e,[t]:ke(e[t],["point","line"])});return e}function As(e,t={},n){return e.point==="transparent"?{opacity:0}:e.point?X(e.point)?e.point:{}:e.point!==void 0?null:t.point||n.shape?X(t.point)?t.point:{}:void 0}function kc(e,t={}){return e.line?e.line===!0?{}:e.line:e.line!==void 0?null:t.line?t.line===!0?{}:t.line:void 0}class ab{constructor(){this.name="path-overlay"}hasMatchingType(t,n){if(zt(t)){const{mark:i,encoding:r}=t,s=pt(i)?i:{type:i};switch(s.type){case"line":case"rule":case"trail":return!!As(s,n[s.type],r);case"area":return!!As(s,n[s.type],r)||!!kc(s,n[s.type])}}return!1}run(t,n,i){const{config:r}=n,{params:s,projection:o,mark:a,name:c,encoding:l,...u}=t,f=ns(l,r),d=pt(a)?a:{type:a},g=As(d,r[d.type],f),p=d.type==="area"&&kc(d,r[d.type]),h=[{name:c,...s?{params:s}:{},mark:sb({...d.type==="area"&&d.opacity===void 0&&d.fillOpacity===void 0?{opacity:.7}:{},...d}),encoding:ke(f,["shape"])}],m=Mf(Uf(d,f,r),f);let y=f;if(m){const{fieldChannel:b,offset:C}=m;y={...f,[b]:{...f[b],...C?{stack:C}:{}}}}return y=ke(y,["y2","x2"]),p&&h.push({...o?{projection:o}:{},mark:{type:"line",...Qn(d,["clip","interpolate","tension","tooltip"]),...p},encoding:y}),g&&h.push({...o?{projection:o}:{},mark:{type:"point",opacity:1,filled:!0,...Qn(d,["clip","tooltip"]),...g},encoding:y}),i({...u,layer:h},{...n,config:ob(r)})}}function cb(e,t){return t?Ki(e)?Wf(e,t):Bf(e,t):e}function Os(e,t){return t?Wf(e,t):e}function io(e,t,n){const i=t[e];if(Gm(i)){if(i.repeat in n)return{...t,[e]:n[i.repeat]};v(Hg(i.repeat));return}return t}function Bf(e,t){if(e=io("field",e,t),e!==void 0){if(e===null)return null;if(sa(e)&&ut(e.sort)){const n=io("field",e.sort,t);e={...e,...n?{sort:n}:{}}}return e}}function Tc(e,t){if(S(e))return Bf(e,t);{const n=io("datum",e,t);return n!==e&&!n.type&&(n.type="nominal"),n}}function Ac(e,t){if(D(e)){const n=Tc(e,t);if(n)return n;if(Qr(e))return{condition:e.condition}}else{if(Qi(e)){const n=Tc(e.condition,t);if(n)return{...e,condition:n};{const{condition:i,...r}=e;return r}}return e}}function Wf(e,t){const n={};for(const i in e)if(Gn(e,i)){const r=e[i];if(N(r))n[i]=r.map(s=>Ac(s,t)).filter(s=>s);else{const s=Ac(r,t);s!==void 0&&(n[i]=s)}}return n}class lb{constructor(){this.name="RuleForRangedLine"}hasMatchingType(t){if(zt(t)){const{encoding:n,mark:i}=t;if(i==="line"||pt(i)&&i.type==="line")for(const r of ug){const s=_n(r),o=n[s];if(n[r]&&(S(o)&&!xe(o.bin)||St(o)))return!0}}return!1}run(t,n,i){const{encoding:r,mark:s}=t;return v(uh(!!r.x2,!!r.y2)),i({...t,mark:X(s)?{...s,type:"rule"}:"rule"},n)}}class ub extends va{constructor(){super(...arguments),this.nonFacetUnitNormalizers=[dy,hy,Ey,new ab,new lb]}map(t,n){if(zt(t)){const i=yn(t.encoding,Nt),r=yn(t.encoding,Ft),s=yn(t.encoding,Lr);if(i||r||s)return this.mapFacetedUnit(t,n)}return super.map(t,n)}mapUnit(t,n){const{parentEncoding:i,parentProjection:r}=n,s=Os(t.encoding,n.repeater),o={...t,...t.name?{name:[n.repeaterPrefix,t.name].filter(c=>c).join("_")}:{},...s?{encoding:s}:{}};if(i||r)return this.mapUnitWithParentEncodingOrProjection(o,n);const a=this.mapLayerOrUnit.bind(this);for(const c of this.nonFacetUnitNormalizers)if(c.hasMatchingType(o,n.config))return c.run(o,n,a);return o}mapRepeat(t,n){return Yy(t)?this.mapLayerRepeat(t,n):this.mapNonLayerRepeat(t,n)}mapLayerRepeat(t,n){const{repeat:i,spec:r,...s}=t,{row:o,column:a,layer:c}=i,{repeater:l={},repeaterPrefix:u=""}=n;return o||a?this.mapRepeat({...t,repeat:{...o?{row:o}:{},...a?{column:a}:{}},spec:{repeat:{layer:c},spec:r}},n):{...s,layer:c.map(f=>{const d={...l,layer:f},g=`${(r.name?`${r.name}_`:"")+u}child__layer_${re(f)}`,p=this.mapLayerOrUnit(r,{...n,repeater:d,repeaterPrefix:g});return p.name=g,p})}}mapNonLayerRepeat(t,n){const{repeat:i,spec:r,data:s,...o}=t;!N(i)&&t.columns&&(t=ke(t,["columns"]),v(lc("repeat")));const a=[],{repeater:c={},repeaterPrefix:l=""}=n,u=!N(i)&&i.row||[c?c.row:null],f=!N(i)&&i.column||[c?c.column:null],d=N(i)&&i||[c?c.repeat:null];for(const p of d)for(const h of u)for(const m of f){const y={repeat:p,row:h,column:m,layer:c.layer},b=(r.name?`${r.name}_`:"")+l+"child__"+(N(i)?`${re(p)}`:(i.row?`row_${re(h)}`:"")+(i.column?`column_${re(m)}`:"")),C=this.map(r,{...n,repeater:y,repeaterPrefix:b});C.name=b,a.push(ke(C,["data"]))}const g=N(i)?t.columns:i.column?i.column.length:1;return{data:r.data??s,align:"all",...o,columns:g,concat:a}}mapFacet(t,n){const{facet:i}=t;return Ki(i)&&t.columns&&(t=ke(t,["columns"]),v(lc("facet"))),super.mapFacet(t,n)}mapUnitWithParentEncodingOrProjection(t,n){const{encoding:i,projection:r}=t,{parentEncoding:s,parentProjection:o,config:a}=n,c=_c({parentProjection:o,projection:r}),l=Oc({parentEncoding:s,encoding:Os(i,n.repeater)});return this.mapUnit({...t,...c?{projection:c}:{},...l?{encoding:l}:{}},{config:a})}mapFacetedUnit(t,n){const{row:i,column:r,facet:s,...o}=t.encoding,{mark:a,width:c,projection:l,height:u,view:f,params:d,encoding:g,...p}=t,{facetMapping:h,layout:m}=this.getFacetMappingAndLayout({row:i,column:r,facet:s},n),y=Os(o,n.repeater);return this.mapFacet({...p,...m,facet:h,spec:{...c?{width:c}:{},...u?{height:u}:{},...f?{view:f}:{},...l?{projection:l}:{},mark:a,encoding:y,...d?{params:d}:{}}},n)}getFacetMappingAndLayout(t,n){const{row:i,column:r,facet:s}=t;if(i||r){s&&v(ch([...i?[Nt]:[],...r?[Ft]:[]]));const o={},a={};for(const c of[Nt,Ft]){const l=t[c];if(l){const{align:u,center:f,spacing:d,columns:g,...p}=l;o[c]=p;for(const h of["align","center","spacing"])l[h]!==void 0&&(a[h]??(a[h]={}),a[h][c]=l[h])}}return{facetMapping:o,layout:a}}else{const{align:o,center:a,spacing:c,columns:l,...u}=s;return{facetMapping:cb(u,n.repeater),layout:{...o?{align:o}:{},...a?{center:a}:{},...c?{spacing:c}:{},...l?{columns:l}:{}}}}}mapLayer(t,{parentEncoding:n,parentProjection:i,...r}){const{encoding:s,projection:o,...a}=t,c={...r,parentEncoding:Oc({parentEncoding:n,encoding:s,layer:!0}),parentProjection:_c({parentProjection:i,projection:o})};return super.mapLayer({...a,...t.name?{name:[c.repeaterPrefix,t.name].filter(l=>l).join("_")}:{}},c)}}function Oc({parentEncoding:e,encoding:t={},layer:n}){let i={};if(e){const r=new Set([...x(e),...x(t)]);for(const s of r){const o=t[s],a=e[s];if(D(o)){const c={...a,...o};i[s]=c}else Qi(o)?i[s]={...o,condition:{...a,...o.condition}}:o||o===null?i[s]=o:(n||Je(a)||k(a)||D(a)||N(a))&&(i[s]=a)}}else i=t;return!i||K(i)?void 0:i}function _c(e){const{parentProjection:t,projection:n}=e;return t&&n&&v(Qg({parentProjection:t,projection:n})),n??t}function Sa(e){return"filter"in e}function fb(e){return e?.stop!==void 0}function Gf(e){return"lookup"in e}function db(e){return"data"in e}function pb(e){return"param"in e}function gb(e){return"pivot"in e}function hb(e){return"density"in e}function mb(e){return"quantile"in e}function yb(e){return"regression"in e}function bb(e){return"loess"in e}function xb(e){return"sample"in e}function vb(e){return"window"in e}function Sb(e){return"joinaggregate"in e}function Eb(e){return"flatten"in e}function $b(e){return"calculate"in e}function Hf(e){return"bin"in e}function wb(e){return"impute"in e}function Cb(e){return"timeUnit"in e}function Nb(e){return"aggregate"in e}function Fb(e){return"stack"in e}function kb(e){return"fold"in e}function Tb(e){return"extent"in e&&!("density"in e)}function Ab(e){return e.map(t=>Sa(t)?{filter:Hn(t.filter,im)}:t)}class Ob extends va{map(t,n){return n.emptySelections??(n.emptySelections={}),n.selectionPredicates??(n.selectionPredicates={}),t=Rc(t,n),super.map(t,n)}mapLayerOrUnit(t,n){if(t=Rc(t,n),t.encoding){const i={};for(const[r,s]of Wt(t.encoding))i[r]=qf(s,n);t={...t,encoding:i}}return super.mapLayerOrUnit(t,n)}mapUnit(t,n){const{selection:i,...r}=t;return i?{...r,params:Wt(i).map(([s,o])=>{const{init:a,bind:c,empty:l,...u}=o;u.type==="single"?(u.type="point",u.toggle=!1):u.type==="multi"&&(u.type="point"),n.emptySelections[s]=l!=="none";for(const f of ve(n.selectionPredicates[s]??{}))f.empty=l!=="none";return{name:s,value:a,select:u,bind:c}})}:t}}function Rc(e,t){const{transform:n,...i}=e;if(n){const r=n.map(s=>{if(Sa(s))return{filter:ro(s,t)};if(Hf(s)&&Rn(s.bin))return{...s,bin:Vf(s.bin)};if(Gf(s)){const{selection:o,...a}=s.from;return o?{...s,from:{param:o,...a}}:s}return s});return{...i,transform:r}}return e}function qf(e,t){const n=z(e);if(S(n)&&Rn(n.bin)&&(n.bin=Vf(n.bin)),Pn(n)&&n.scale?.domain?.selection){const{selection:i,...r}=n.scale.domain;n.scale.domain={...r,...i?{param:i}:{}}}if(Qr(n))if(N(n.condition))n.condition=n.condition.map(i=>{const{selection:r,param:s,test:o,...a}=i;return s?i:{...a,test:ro(i,t)}});else{const{selection:i,param:r,test:s,...o}=qf(n.condition,t);n.condition=r?n.condition:{...o,test:ro(n.condition,t)}}return n}function Vf(e){const t=e.extent;if(t?.selection){const{selection:n,...i}=t;return{...e,extent:{...i,param:n}}}return e}function ro(e,t){const n=i=>Hn(i,r=>{var s;const o=t.emptySelections[r]??!0,a={param:r,empty:o};return(s=t.selectionPredicates)[r]??(s[r]=[]),t.selectionPredicates[r].push(a),a});return e.selection?n(e.selection):Hn(e.test||e.filter,i=>i.selection?n(i.selection):i)}class so extends va{map(t,n){const i=n.selections??[];if(t.params&&!zt(t)){const r=[];for(const s of t.params)ya(s)?i.push(s):r.push(s);t.params=r}return n.selections=i,super.map(t,n)}mapUnit(t,n){const i=n.selections;if(!i||!i.length)return t;const r=(n.path??[]).concat(t.name),s=[];for(const o of i)if(!o.views||!o.views.length)s.push(o);else for(const a of o.views)(I(a)&&(a===t.name||r.includes(a))||N(a)&&a.map(c=>r.indexOf(c)).every((c,l,u)=>c!==-1&&(l===0||c>u[l-1])))&&s.push(o);return s.length&&(t.params=s),t}}for(const e of["mapFacet","mapRepeat","mapHConcat","mapVConcat","mapLayer"]){const t=so.prototype[e];so.prototype[e]=function(n,i){return t.call(this,n,_b(n,i))}}function _b(e,t){return e.name?{...t,path:(t.path??[]).concat(e.name)}:t}function Xf(e,t){t===void 0&&(t=jf(e.config));const n=Pb(e,t),{width:i,height:r}=e,s=zb(n,{width:i,height:r,autosize:e.autosize},t);return{...n,...s?{autosize:s}:{}}}const Rb=new ub,Ib=new Ob,Lb=new so;function Pb(e,t={}){const n={config:t};return Lb.map(Rb.map(Ib.map(e,n),n),n)}function Ic(e){return I(e)?{type:e}:e??{}}function zb(e,t,n){let{width:i,height:r}=t;const s=zt(e)||rs(e),o={};s?i=="container"&&r=="container"?(o.type="fit",o.contains="padding"):i=="container"?(o.type="fit-x",o.contains="padding"):r=="container"&&(o.type="fit-y",o.contains="padding"):(i=="container"&&(v(sc("width")),i=void 0),r=="container"&&(v(sc("height")),r=void 0));const a={type:"pad",...o,...n?Ic(n.autosize):{},...Ic(e.autosize)};if(a.type==="fit"&&!s&&(v(Ig),a.type="pad"),i=="container"&&!(a.type=="fit"||a.type=="fit-x")&&v(oc("width")),r=="container"&&!(a.type=="fit"||a.type=="fit-y")&&v(oc("height")),!Pe(a,{type:"pad"}))return a}function Db(e){return e==="fit"||e==="fit-x"||e==="fit-y"}function jb(e){return e?`fit-${Mr(e)}`:"fit"}const Mb=["background","padding"];function Lc(e,t){const n={};for(const i of Mb)e&&e[i]!==void 0&&(n[i]=Le(e[i]));return t&&(n.params=e.params),n}class Dt{constructor(t={},n={}){this.explicit=t,this.implicit=n}clone(){return new Dt(z(this.explicit),z(this.implicit))}combine(){return{...this.explicit,...this.implicit}}get(t){return le(this.explicit[t],this.implicit[t])}getWithExplicit(t){return this.explicit[t]!==void 0?{explicit:!0,value:this.explicit[t]}:this.implicit[t]!==void 0?{explicit:!1,value:this.implicit[t]}:{explicit:!1,value:void 0}}setWithExplicit(t,{value:n,explicit:i}){n!==void 0&&this.set(t,n,i)}set(t,n,i){return delete this[i?"implicit":"explicit"][t],this[i?"explicit":"implicit"][t]=n,this}copyKeyFromSplit(t,{explicit:n,implicit:i}){n[t]!==void 0?this.set(t,n[t],!0):i[t]!==void 0&&this.set(t,i[t],!1)}copyKeyFromObject(t,n){n[t]!==void 0&&this.set(t,n[t],!0)}copyAll(t){for(const n of x(t.combine())){const i=t.getWithExplicit(n);this.setWithExplicit(n,i)}}}function ct(e){return{explicit:!0,value:e}}function Ie(e){return{explicit:!1,value:e}}function Yf(e){return(t,n,i,r)=>{const s=e(t.value,n.value);return s>0?t:s<0?n:ss(t,n,i,r)}}function ss(e,t,n,i){return e.explicit&&t.explicit&&v(vh(n,i,e.value,t.value)),e}function qt(e,t,n,i,r=ss){return e===void 0||e.value===void 0?t:e.explicit&&!t.explicit?e:t.explicit&&!e.explicit?t:Pe(e.value,t.value)?e:r(e,t,n,i)}class Ub extends Dt{constructor(t={},n={},i=!1){super(t,n),this.explicit=t,this.implicit=n,this.parseNothing=i}clone(){const t=super.clone();return t.parseNothing=this.parseNothing,t}}function oi(e){return"url"in e}function Di(e){return"values"in e}function Kf(e){return"name"in e&&!oi(e)&&!Di(e)&&!Bt(e)}function Bt(e){return e&&(Qf(e)||Jf(e)||Ea(e))}function Qf(e){return"sequence"in e}function Jf(e){return"sphere"in e}function Ea(e){return"graticule"in e}var Z;(function(e){e[e.Raw=0]="Raw",e[e.Main=1]="Main",e[e.Row=2]="Row",e[e.Column=3]="Column",e[e.Lookup=4]="Lookup"})(Z||(Z={}));function Zf(e){const{signals:t,hasLegend:n,index:i,...r}=e;return r.field=Ue(r.field),r}function Fn(e,t=!0,n=Cp){if(N(e)){const i=e.map(r=>Fn(r,t,n));return t?`[${i.join(", ")}]`:i}else if(In(e))return n(t?$n(e):Hh(e));return t?n(Q(e)):e}function Bb(e,t){for(const n of ve(e.component.selection??{})){const i=n.name;let r=`${i}${Xt}, ${n.resolve==="global"?"true":`{unit: ${bn(e)}}`}`;for(const s of ls)s.defined(n)&&(s.signals&&(t=s.signals(e,n,t)),s.modifyExpr&&(r=s.modifyExpr(e,n,r)));t.push({name:i+vx,on:[{events:{signal:n.name+Xt},update:`modify(${B(n.name+kn)}, ${r})`}]})}return $a(t)}function Wb(e,t){if(e.component.selection&&x(e.component.selection).length){const n=B(e.getName("cell"));t.unshift({name:"facet",value:{},on:[{events:pi("pointermove","scope"),update:`isTuple(facet) ? facet : group(${n}).datum`}]})}return $a(t)}function Gb(e,t){let n=!1;for(const i of ve(e.component.selection??{})){const r=i.name,s=B(r+kn);if(t.filter(a=>a.name===r).length===0){const a=i.resolve==="global"?"union":i.resolve,c=i.type==="point"?", true, true)":")";t.push({name:i.name,update:`${yd}(${s}, ${B(a)}${c}`})}n=!0;for(const a of ls)a.defined(i)&&a.topLevelSignals&&(t=a.topLevelSignals(e,i,t))}return n&&t.filter(r=>r.name==="unit").length===0&&t.unshift({name:"unit",value:{},on:[{events:"pointermove",update:"isTuple(group()) ? group() : unit"}]}),$a(t)}function Hb(e,t){const n=[...t],i=bn(e,{escape:!1});for(const r of ve(e.component.selection??{})){const s={name:r.name+kn};if(r.project.hasSelectionId&&(s.transform=[{type:"collect",sort:{field:Ze}}]),r.init){const a=r.project.items.map(Zf);s.values=r.project.hasSelectionId?r.init.map(c=>({unit:i,[Ze]:Fn(c,!1)[0]})):r.init.map(c=>({unit:i,fields:a,values:Fn(c,!1)}))}n.filter(a=>a.name===r.name+kn).length||n.push(s)}return n}function ed(e,t){for(const n of ve(e.component.selection??{}))for(const i of ls)i.defined(n)&&i.marks&&(t=i.marks(e,n,t));return t}function qb(e,t){for(const n of e.children)ae(n)&&(t=ed(n,t));return t}function Vb(e,t,n,i){const r=Ed(e,t.param,t);return{signal:De(n.get("type"))&&N(i)&&i[0]>i[1]?`isValid(${r}) && reverse(${r})`:r}}function $a(e){return e.map(t=>(t.on&&!t.on.length&&delete t.on,t))}class Y{constructor(t,n){this.debugName=n,this._children=[],this._parent=null,t&&(this.parent=t)}clone(){throw new Error("Cannot clone node")}get parent(){return this._parent}set parent(t){this._parent=t,t&&t.addChild(this)}get children(){return this._children}numChildren(){return this._children.length}addChild(t,n){if(this._children.includes(t)){v(Xg);return}n!==void 0?this._children.splice(n,0,t):this._children.push(t)}removeChild(t){const n=this._children.indexOf(t);return this._children.splice(n,1),n}remove(){let t=this._parent.removeChild(this);for(const n of this._children)n._parent=this._parent,this._parent.addChild(n,t++)}insertAsParentOf(t){const n=t.parent;n.removeChild(this),this.parent=n,t.parent=this}swapWithParent(){const t=this._parent,n=t.parent;for(const r of this._children)r.parent=t;this._children=[],t.removeChild(this);const i=t.parent.removeChild(t);this._parent=n,n.addChild(this,i),t.parent=this}}class we extends Y{clone(){const t=new this.constructor;return t.debugName=`clone_${this.debugName}`,t._source=this._source,t._name=`clone_${this._name}`,t.type=this.type,t.refCounts=this.refCounts,t.refCounts[t._name]=0,t}constructor(t,n,i,r){super(t,n),this.type=i,this.refCounts=r,this._source=this._name=n,this.refCounts&&!(this._name in this.refCounts)&&(this.refCounts[this._name]=0)}dependentFields(){return new Set}producedFields(){return new Set}hash(){return this._hash===void 0&&(this._hash=`Output ${Yl()}`),this._hash}getSource(){return this.refCounts[this._name]++,this._source}isRequired(){return!!this.refCounts[this._name]}setSource(t){this._source=t}}function _s(e){return e.as!==void 0}function Pc(e){return`${e}_end`}class ft extends Y{clone(){return new ft(null,z(this.timeUnits))}constructor(t,n){super(t),this.timeUnits=n}static makeFromEncoding(t,n){const i=n.reduceFieldDef((r,s,o)=>{const{field:a,timeUnit:c}=s;if(c){let l;if(Ln(c)){if(ae(n)){const{mark:u,markDef:f,config:d}=n,g=Ht({fieldDef:s,markDef:f,config:d});(zi(u)||g)&&(l={timeUnit:me(c),field:a})}}else l={as:E(s,{forAs:!0}),field:a,timeUnit:c};if(ae(n)){const{mark:u,markDef:f,config:d}=n,g=Ht({fieldDef:s,markDef:f,config:d});zi(u)&&ue(o)&&g!==.5&&(l.rectBandPosition=g)}l&&(r[W(l)]=l)}return r},{});return K(i)?null:new ft(t,i)}static makeFromTransform(t,n){const{timeUnit:i,...r}={...n},s=me(i),o={...r,timeUnit:s};return new ft(t,{[W(o)]:o})}merge(t){this.timeUnits={...this.timeUnits};for(const n in t.timeUnits)this.timeUnits[n]||(this.timeUnits[n]=t.timeUnits[n]);for(const n of t.children)t.removeChild(n),n.parent=this;t.remove()}removeFormulas(t){const n={};for(const[i,r]of Wt(this.timeUnits)){const s=_s(r)?r.as:`${r.field}_end`;t.has(s)||(n[i]=r)}this.timeUnits=n}producedFields(){return new Set(ve(this.timeUnits).map(t=>_s(t)?t.as:Pc(t.field)))}dependentFields(){return new Set(ve(this.timeUnits).map(t=>t.field))}hash(){return`TimeUnit ${W(this.timeUnits)}`}assemble(){const t=[];for(const n of ve(this.timeUnits)){const{rectBandPosition:i}=n,r=me(n.timeUnit);if(_s(n)){const{field:s,as:o}=n,{unit:a,utc:c,...l}=r,u=[o,`${o}_end`];t.push({field:Ue(s),type:"timeunit",...a?{units:Gr(a)}:{},...c?{timezone:"utc"}:{},...l,as:u}),t.push(...zc(u,i,r))}else if(n){const{field:s}=n,o=s.replaceAll("\\.","."),a=td({timeUnit:r,field:o}),c=Pc(o);t.push({type:"formula",expr:a,as:c}),t.push(...zc([o,c],i,r))}}return t}}const os="offsetted_rect_start",as="offsetted_rect_end";function td({timeUnit:e,field:t,reverse:n}){const{unit:i,utc:r}=e,s=Ou(i),{part:o,step:a}=Lu(s,e.step);return`${r?"utcOffset":"timeOffset"}('${o}', datum['${t}'], ${n?-a:a})`}function zc([e,t],n,i){if(n!==void 0&&n!==.5){const r=`datum['${e}']`,s=`datum['${t}']`;return[{type:"formula",expr:Dc([td({timeUnit:i,field:e,reverse:!0}),r],n+.5),as:`${e}_${os}`},{type:"formula",expr:Dc([r,s],n+.5),as:`${e}_${as}`}]}return[]}function Dc([e,t],n){return`${1-n} * ${e} + ${n} * ${t}`}const er="_tuple_fields";class Xb{constructor(...t){this.items=t,this.hasChannel={},this.hasField={},this.hasSelectionId=!1}}const Yb={defined:()=>!0,parse:(e,t,n)=>{const i=t.name,r=t.project??(t.project=new Xb),s={},o={},a=new Set,c=(p,h)=>{const m=h==="visual"?p.channel:p.field;let y=re(`${i}_${m}`);for(let b=1;a.has(y);b++)y=re(`${i}_${m}_${b}`);return a.add(y),{[h]:y}},l=t.type,u=e.config.selection[l],f=n.value!==void 0?ce(n.value):null;let{fields:d,encodings:g}=X(n.select)?n.select:{};if(!d&&!g&&f){for(const p of f)if(X(p))for(const h of x(p))lg(h)?(g||(g=[])).push(h):l==="interval"?(v(Gg),g=u.encodings):(d??(d=[])).push(h)}!d&&!g&&(g=u.encodings,"fields"in u&&(d=u.fields));for(const p of g??[]){const h=e.fieldDef(p);if(h){let m=h.field;if(h.aggregate){v(Lg(p,h.aggregate));continue}else if(!m){v(cc(p));continue}if(h.timeUnit&&!Ln(h.timeUnit)){m=e.vgField(p);const y={timeUnit:h.timeUnit,as:m,field:h.field};o[W(y)]=y}if(!s[m]){const y=l==="interval"&&Pt(p)&&De(e.getScaleComponent(p).get("type"))?"R":h.bin?"R-RE":"E",b={field:m,channel:p,type:y,index:r.items.length};b.signals={...c(b,"data"),...c(b,"visual")},r.items.push(s[m]=b),r.hasField[m]=s[m],r.hasSelectionId=r.hasSelectionId||m===Ze,tu(p)?(b.geoChannel=p,b.channel=eu(p),r.hasChannel[b.channel]=s[m]):r.hasChannel[p]=s[m]}}else v(cc(p))}for(const p of d??[]){if(r.hasField[p])continue;const h={type:"E",field:p,index:r.items.length};h.signals={...c(h,"data")},r.items.push(h),r.hasField[p]=h,r.hasSelectionId=r.hasSelectionId||p===Ze}f&&(t.init=f.map(p=>r.items.map(h=>X(p)?p[h.geoChannel||h.channel]!==void 0?p[h.geoChannel||h.channel]:p[h.field]:p))),K(o)||(r.timeUnit=new ft(null,o))},signals:(e,t,n)=>{const i=t.name+er;return n.filter(s=>s.name===i).length>0||t.project.hasSelectionId?n:n.concat({name:i,value:t.project.items.map(Zf)})}},kt={defined:e=>e.type==="interval"&&e.resolve==="global"&&e.bind&&e.bind==="scales",parse:(e,t)=>{const n=t.scales=[];for(const i of t.project.items){const r=i.channel;if(!Pt(r))continue;const s=e.getScaleComponent(r),o=s?s.get("type"):void 0;if(o=="sequential"&&v(jg),!s||!De(o)){v(Dg);continue}s.set("selectionExtent",{param:t.name,field:i.field},!0),n.push(i)}},topLevelSignals:(e,t,n)=>{const i=t.scales.filter(o=>n.filter(a=>a.name===o.signals.data).length===0);if(!e.parent||jc(e)||i.length===0)return n;const r=n.filter(o=>o.name===t.name)[0];let s=r.update;if(s.indexOf(yd)>=0)r.update=`{${i.map(o=>`${B(Ue(o.field))}: ${o.signals.data}`).join(", ")}}`;else{for(const o of i){const a=`${B(Ue(o.field))}: ${o.signals.data}`;s.includes(a)||(s=`${s.substring(0,s.length-1)}, ${a}}`)}r.update=s}return n.concat(i.map(o=>({name:o.signals.data})))},signals:(e,t,n)=>{if(e.parent&&!jc(e))for(const i of t.scales){const r=n.find(s=>s.name===i.signals.data);r.push="outer",delete r.value,delete r.update}return n}};function oo(e,t){return`domain(${B(e.scaleName(t))})`}function jc(e){return e.parent&&vi(e.parent)&&!e.parent.parent}const Xn="_brush",nd="_scale_trigger",Ei="geo_interval_init_tick",id="_init",Kb="_center",Qb={defined:e=>e.type==="interval",parse:(e,t,n)=>{var i;if(e.hasProjection){const r={...X(n.select)?n.select:{}};r.fields=[Ze],r.encodings||(r.encodings=n.value?x(n.value):[it,nt]),n.select={type:"interval",...r}}if(t.translate&&!kt.defined(t)){const r=`!event.item || event.item.mark.name !== ${B(t.name+Xn)}`;for(const s of t.events){if(!s.between){v(`${s} is not an ordered event stream for interval selections.`);continue}const o=ce((i=s.between[0]).filter??(i.filter=[]));o.indexOf(r)<0&&o.push(r)}}},signals:(e,t,n)=>{const i=t.name,r=i+Xt,s=ve(t.project.hasChannel).filter(a=>a.channel===se||a.channel===be),o=t.init?t.init[0]:null;if(n.push(...s.reduce((a,c)=>a.concat(Jb(e,t,c,o&&o[c.index])),[])),e.hasProjection){const a=B(e.projectionName()),c=e.projectionName()+Kb,{x:l,y:u}=t.project.hasChannel,f=l&&l.signals.visual,d=u&&u.signals.visual,g=l?o&&o[l.index]:`${c}[0]`,p=u?o&&o[u.index]:`${c}[1]`,h=F=>e.getSizeSignalRef(F).signal,m=`[[${f?f+"[0]":"0"}, ${d?d+"[0]":"0"}],[${f?f+"[1]":h("width")}, ${d?d+"[1]":h("height")}]]`;o&&(n.unshift({name:i+id,init:`[scale(${a}, [${l?g[0]:g}, ${u?p[0]:p}]), scale(${a}, [${l?g[1]:g}, ${u?p[1]:p}])]`}),(!l||!u)&&(n.find(_=>_.name===c)||n.unshift({name:c,update:`invert(${a}, [${h("width")}/2, ${h("height")}/2])`})));const y=`intersect(${m}, {markname: ${B(e.getName("marks"))}}, unit.mark)`,b=`{unit: ${bn(e)}}`,C=`vlSelectionTuples(${y}, ${b})`,O=s.map(F=>F.signals.visual);return n.concat({name:r,on:[{events:[...O.length?[{signal:O.join(" || ")}]:[],...o?[{signal:Ei}]:[]],update:C}]})}else{if(!kt.defined(t)){const l=i+nd,u=s.map(f=>{const d=f.channel,{data:g,visual:p}=f.signals,h=B(e.scaleName(d)),m=e.getScaleComponent(d).get("type"),y=De(m)?"+":"";return`(!isArray(${g}) || (${y}invert(${h}, ${p})[0] === ${y}${g}[0] && ${y}invert(${h}, ${p})[1] === ${y}${g}[1]))`});u.length&&n.push({name:l,value:{},on:[{events:s.map(f=>({scale:e.scaleName(f.channel)})),update:u.join(" && ")+` ? ${l} : {}`}]})}const a=s.map(l=>l.signals.data),c=`unit: ${bn(e)}, fields: ${i+er}, values`;return n.concat({name:r,...o?{init:`{${c}: ${Fn(o)}}`}:{},...a.length?{on:[{events:[{signal:a.join(" || ")}],update:`${a.join(" && ")} ? {${c}: [${a}]} : null`}]}:{}})}},topLevelSignals:(e,t,n)=>(ae(e)&&e.hasProjection&&t.init&&(n.filter(r=>r.name===Ei).length||n.unshift({name:Ei,value:null,on:[{events:"timer{1}",update:`${Ei} === null ? {} : ${Ei}`}]})),n),marks:(e,t,n)=>{const i=t.name,{x:r,y:s}=t.project.hasChannel,o=r?.signals.visual,a=s?.signals.visual,c=`data(${B(t.name+kn)})`;if(kt.defined(t)||!r&&!s)return n;const l={x:r!==void 0?{signal:`${o}[0]`}:{value:0},y:s!==void 0?{signal:`${a}[0]`}:{value:0},x2:r!==void 0?{signal:`${o}[1]`}:{field:{group:"width"}},y2:s!==void 0?{signal:`${a}[1]`}:{field:{group:"height"}}};if(t.resolve==="global")for(const m of x(l))l[m]=[{test:`${c}.length && ${c}[0].unit === ${bn(e)}`,...l[m]},{value:0}];const{fill:u,fillOpacity:f,cursor:d,...g}=t.mark,p=x(g).reduce((m,y)=>(m[y]=[{test:[r!==void 0&&`${o}[0] !== ${o}[1]`,s!==void 0&&`${a}[0] !== ${a}[1]`].filter(b=>b).join(" && "),value:g[y]},{value:null}],m),{}),h=d??(t.translate?"move":null);return[{name:`${i+Xn}_bg`,type:"rect",clip:!0,encode:{enter:{fill:{value:u},fillOpacity:{value:f}},update:l}},...n,{name:i+Xn,type:"rect",clip:!0,encode:{enter:{...h?{cursor:{value:h}}:{},fill:{value:"transparent"}},update:{...l,...p}}}]}};function Jb(e,t,n,i){const r=!e.hasProjection,s=n.channel,o=n.signals.visual,a=B(r?e.scaleName(s):e.projectionName()),c=d=>`scale(${a}, ${d})`,l=e.getSizeSignalRef(s===se?"width":"height").signal,u=`${s}(unit)`,f=t.events.reduce((d,g)=>[...d,{events:g.between[0],update:`[${u}, ${u}]`},{events:g,update:`[${o}[0], clamp(${u}, 0, ${l})]`}],[]);if(r){const d=n.signals.data,g=kt.defined(t),p=e.getScaleComponent(s),h=p?p.get("type"):void 0,m=i?{init:Fn(i,!0,c)}:{value:[]};return f.push({events:{signal:t.name+nd},update:De(h)?`[${c(`${d}[0]`)}, ${c(`${d}[1]`)}]`:"[0, 0]"}),g?[{name:d,on:[]}]:[{name:o,...m,on:f},{name:d,...i?{init:Fn(i)}:{},on:[{events:{signal:o},update:`${o}[0] === ${o}[1] ? null : invert(${a}, ${o})`}]}]}else{const d=s===se?0:1,g=t.name+id,p=i?{init:`[${g}[0][${d}], ${g}[1][${d}]]`}:{value:[]};return[{name:o,...p,on:f}]}}const Zb={defined:e=>e.type==="point",signals:(e,t,n)=>{const i=t.name,r=i+er,s=t.project,o="(item().isVoronoi ? datum.datum : datum)",a=ve(e.component.selection??{}).reduce((f,d)=>d.type==="interval"?f.concat(d.name+Xn):f,[]).map(f=>`indexof(item().mark.name, '${f}') < 0`).join(" && "),c=`datum && item().mark.marktype !== 'group' && indexof(item().mark.role, 'legend') < 0${a?` && ${a}`:""}`;let l=`unit: ${bn(e)}, `;if(t.project.hasSelectionId)l+=`${Ze}: ${o}[${B(Ze)}]`;else{const f=s.items.map(d=>e.fieldDef(d.channel)?.bin?`[${o}[${B(e.vgField(d.channel,{}))}], ${o}[${B(e.vgField(d.channel,{binSuffix:"end"}))}]]`:`${o}[${B(d.field)}]`).join(", ");l+=`fields: ${r}, values: [${f}]`}const u=t.events;return n.concat([{name:i+Xt,on:u?[{events:u,update:`${c} ? {${l}} : null`,force:!0}]:[]}])}};function mi(e,t,n,i){const r=Qr(t)&&t.condition,s=i(t);if(r){const a=ce(r).map(c=>{const l=i(c);if(Wm(c)){const{param:u,empty:f}=c;return{test:Sd(e,{param:u,empty:f}),...l}}else return{test:Tr(e,c.test),...l}});return{[n]:[...a,...s!==void 0?[s]:[]]}}else return s!==void 0?{[n]:s}:{}}function wa(e,t="text"){const n=e.encoding[t];return mi(e,n,t,i=>cs(i,e.config))}function cs(e,t,n="datum"){if(e){if(Je(e))return ne(e.value);if(D(e)){const{format:i,formatType:r}=Er(e);return ia({fieldOrDatumDef:e,format:i,formatType:r,expr:n,config:t})}}}function rd(e,t={}){const{encoding:n,markDef:i,config:r,stack:s}=e,o=n.tooltip;if(N(o))return{tooltip:Mc({tooltip:o},s,r,t)};{const a=t.reactiveGeom?"datum.datum":"datum";return mi(e,o,"tooltip",c=>{const l=cs(c,r,a);if(l)return l;if(c===null)return;let u=V("tooltip",i,r);if(u===!0&&(u={content:"encoding"}),I(u))return{value:u};if(X(u))return k(u)?u:u.content==="encoding"?Mc(n,s,r,t):{signal:a}})}}function sd(e,t,n,{reactiveGeom:i}={}){const r={...n,...n.tooltipFormat},s={},o=i?"datum.datum":"datum",a=[];function c(u,f){const d=_n(f),g=Ce(u)?u:{...u,type:e[d].type},p=g.title||aa(g,r),h=ce(p).join(", ").replaceAll(/"/g,'\\"');let m;if(ue(f)){const y=f==="x"?"x2":"y2",b=gt(e[y]);if(xe(g.bin)&&b){const C=E(g,{expr:o}),O=E(b,{expr:o}),{format:F,formatType:_}=Er(g);m=Yi(C,O,F,_,r),s[y]=!0}}if((ue(f)||f===We||f===tt)&&t&&t.fieldChannel===f&&t.offset==="normalize"){const{format:y,formatType:b}=Er(g);m=ia({fieldOrDatumDef:g,format:y,formatType:b,expr:o,config:r,normalizeStack:!0}).signal}m??(m=cs(g,r,o).signal),a.push({channel:f,key:h,value:m})}ua(e,(u,f)=>{S(u)?c(u,f):Jr(u)&&c(u.condition,f)});const l={};for(const{channel:u,key:f,value:d}of a)!s[u]&&!l[f]&&(l[f]=d);return l}function Mc(e,t,n,{reactiveGeom:i}={}){const r=sd(e,t,n,{reactiveGeom:i}),s=Wt(r).map(([o,a])=>`"${o}": ${a}`);return s.length>0?{signal:`{${s.join(", ")}}`}:void 0}function ex(e){const{markDef:t,config:n}=e,i=V("aria",t,n);return i===!1?{}:{...i?{aria:i}:{},...tx(e),...nx(e)}}function tx(e){const{mark:t,markDef:n,config:i}=e;if(i.aria===!1)return{};const r=V("ariaRoleDescription",n,i);return r!=null?{ariaRoleDescription:{value:r}}:t in Og?{}:{ariaRoleDescription:{value:t}}}function nx(e){const{encoding:t,markDef:n,config:i,stack:r}=e,s=t.description;if(s)return mi(e,s,"description",c=>cs(c,e.config));const o=V("description",n,i);if(o!=null)return{description:ne(o)};if(i.aria===!1)return{};const a=sd(t,r,i);if(!K(a))return{description:{signal:Wt(a).map(([c,l],u)=>`"${u>0?"; ":""}${c}: " + (${l})`).join(" + ")}}}function he(e,t,n={}){const{markDef:i,encoding:r,config:s}=t,{vgChannel:o}=n;let{defaultRef:a,defaultValue:c}=n;a===void 0&&(c??(c=V(e,i,s,{vgChannel:o,ignoreVgConfig:!0})),c!==void 0&&(a=ne(c)));const l=r[e];return mi(t,l,o??e,u=>na({channel:e,channelDef:u,markDef:i,config:s,scaleName:t.scaleName(e),scale:t.getScaleComponent(e),stack:null,defaultRef:a}))}function od(e,t={filled:void 0}){const{markDef:n,encoding:i,config:r}=e,{type:s}=n,o=t.filled??V("filled",n,r),a=G(["bar","point","circle","square","geoshape"],s)?"transparent":void 0,c=V(o===!0?"color":void 0,n,r,{vgChannel:"fill"})??r.mark[o===!0&&"color"]??a,l=V(o===!1?"color":void 0,n,r,{vgChannel:"stroke"})??r.mark[o===!1&&"color"],u=o?"fill":"stroke",f={...c?{fill:ne(c)}:{},...l?{stroke:ne(l)}:{}};return n.color&&(o?n.fill:n.stroke)&&v(Eu("property",{fill:"fill"in n,stroke:"stroke"in n})),{...f,...he("color",e,{vgChannel:u,defaultValue:o?c:l}),...he("fill",e,{defaultValue:i.fill?c:void 0}),...he("stroke",e,{defaultValue:i.stroke?l:void 0})}}function ix(e){const{encoding:t,mark:n}=e,i=t.order;return!rn(n)&&Je(i)?mi(e,i,"zindex",r=>ne(r.value)):{}}function ai({channel:e,markDef:t,encoding:n={},model:i,bandPosition:r}){const s=`${e}Offset`,o=t[s],a=n[s];if((s==="xOffset"||s==="yOffset")&&a)return{offsetType:"encoding",offset:na({channel:s,channelDef:a,markDef:t,config:i?.config,scaleName:i.scaleName(s),scale:i.getScaleComponent(s),stack:null,defaultRef:ne(o),bandPosition:r})};const c=t[s];return c?{offsetType:"visual",offset:c}:{}}function Ee(e,t,{defaultPos:n,vgChannel:i}){const{encoding:r,markDef:s,config:o,stack:a}=t,c=r[e],l=r[xt(e)],u=t.scaleName(e),f=t.getScaleComponent(e),{offset:d,offsetType:g}=ai({channel:e,markDef:s,encoding:r,model:t,bandPosition:.5}),p=Ca({model:t,defaultPos:n,channel:e,scaleName:u,scale:f}),h=!c&&ue(e)&&(r.latitude||r.longitude)?{field:t.getName(e)}:rx({channel:e,channelDef:c,channel2Def:l,markDef:s,config:o,scaleName:u,scale:f,stack:a,offset:d,defaultRef:p,bandPosition:g==="encoding"?0:void 0});return h?{[i||e]:h}:void 0}function rx(e){const{channel:t,channelDef:n,scaleName:i,stack:r,offset:s,markDef:o}=e;if(D(n)&&r&&t===r.fieldChannel){if(S(n)){let a=n.bandPosition;if(a===void 0&&o.type==="text"&&(t==="radius"||t==="theta")&&(a=.5),a!==void 0)return xr({scaleName:i,fieldOrDatumDef:n,startSuffix:"start",bandPosition:a,offset:s})}return mn(n,i,{suffix:"end"},{offset:s})}return ea(e)}function Ca({model:e,defaultPos:t,channel:n,scaleName:i,scale:r}){const{markDef:s,config:o}=e;return()=>{const a=_n(n),c=Gt(n),l=V(n,s,o,{vgChannel:c});if(l!==void 0)return _i(n,l);switch(t){case"zeroOrMin":case"zeroOrMax":if(i){const u=r.get("type");if(!G([Te.LOG,Te.TIME,Te.UTC],u)){if(r.domainDefinitelyIncludesZero())return{scale:i,value:0}}}if(t==="zeroOrMin")return a==="y"?{field:{group:"height"}}:{value:0};switch(a){case"radius":return{signal:`min(${e.width.signal},${e.height.signal})/2`};case"theta":return{signal:"2*PI"};case"x":return{field:{group:"width"}};case"y":return{value:0}}break;case"mid":return{...e[_e(n)],mult:.5}}}}const sx={left:"x",center:"xc",right:"x2"},ox={top:"y",middle:"yc",bottom:"y2"};function ad(e,t,n,i="middle"){if(e==="radius"||e==="theta")return Gt(e);const r=e==="x"?"align":"baseline",s=V(r,t,n);let o;return k(s)?(v(lh(r)),o=void 0):o=s,e==="x"?sx[o||(i==="top"?"left":"center")]:ox[o||i]}function Fr(e,t,{defaultPos:n,defaultPos2:i,range:r}){return r?cd(e,t,{defaultPos:n,defaultPos2:i}):Ee(e,t,{defaultPos:n})}function cd(e,t,{defaultPos:n,defaultPos2:i}){const{markDef:r,config:s}=t,o=xt(e),a=_e(e),c=ax(t,i,o),l=c[a]?ad(e,r,s):Gt(e);return{...Ee(e,t,{defaultPos:n,vgChannel:l}),...c}}function ax(e,t,n){const{encoding:i,mark:r,markDef:s,stack:o,config:a}=e,c=_n(n),l=_e(n),u=Gt(n),f=i[c],d=e.scaleName(c),g=e.getScaleComponent(c),{offset:p}=n in i||n in s?ai({channel:n,markDef:s,encoding:i,model:e}):ai({channel:c,markDef:s,encoding:i,model:e});if(!f&&(n==="x2"||n==="y2")&&(i.latitude||i.longitude)){const m=_e(n),y=e.markDef[m];return y!=null?{[m]:{value:y}}:{[u]:{field:e.getName(n)}}}const h=cx({channel:n,channelDef:f,channel2Def:i[n],markDef:s,config:a,scaleName:d,scale:g,stack:o,offset:p,defaultRef:void 0});return h!==void 0?{[u]:h}:sr(n,s)||sr(n,{[n]:mr(n,s,a.style),[l]:mr(l,s,a.style)})||sr(n,a[r])||sr(n,a.mark)||{[u]:Ca({model:e,defaultPos:t,channel:n,scaleName:d,scale:g})()}}function cx({channel:e,channelDef:t,channel2Def:n,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:c,defaultRef:l}){return D(t)&&a&&e.charAt(0)===a.fieldChannel.charAt(0)?mn(t,s,{suffix:"start"},{offset:c}):ea({channel:e,channelDef:n,scaleName:s,scale:o,stack:a,markDef:i,config:r,offset:c,defaultRef:l})}function sr(e,t){const n=_e(e),i=Gt(e);if(t[i]!==void 0)return{[i]:_i(e,t[i])};if(t[e]!==void 0)return{[i]:_i(e,t[e])};if(t[n]){const r=t[n];if(Cn(r))v(ih(n));else return{[n]:_i(e,r)}}}function Vt(e,t){const{config:n,encoding:i,markDef:r}=e,s=r.type,o=xt(t),a=_e(t),c=i[t],l=i[o],u=e.getScaleComponent(t),f=u?u.get("type"):void 0,d=r.orient,g=i[a]??i.size??V("size",r,n,{vgChannel:a}),p=su(t),h=s==="bar"&&(t==="x"?d==="vertical":d==="horizontal");return S(c)&&(ee(c.bin)||xe(c.bin)||c.timeUnit&&!l)&&!(g&&!Cn(g))&&!i[p]&&!ye(f)?fx({fieldDef:c,fieldDef2:l,channel:t,model:e}):(D(c)&&ye(f)||h)&&!l?ux(c,t,e):cd(t,e,{defaultPos:"zeroOrMax",defaultPos2:"zeroOrMin"})}function lx(e,t,n,i,r,s,o){if(Cn(r))if(n){const c=n.get("type");if(c==="band"){let l=`bandwidth('${t}')`;r.band!==1&&(l=`${r.band} * ${l}`);const u=Ot("minBandSize",{type:o},i);return{signal:u?`max(${Xe(u)}, ${l})`:l}}else r.band!==1&&(v(ph(c)),r=void 0)}else return{mult:r.band,field:{group:e}};else{if(k(r))return r;if(r)return{value:r}}if(n){const c=n.get("range");if(nn(c)&&ie(c.step))return{value:c.step-2}}if(!s){const{bandPaddingInner:c,barBandPaddingInner:l,rectBandPaddingInner:u}=i.scale,f=le(c,o==="bar"?l:u);if(k(f))return{signal:`(1 - (${f.signal})) * ${e}`};if(ie(f))return{signal:`${1-f} * ${e}`}}return{value:Cr(i.view,e)-2}}function ux(e,t,n){const{markDef:i,encoding:r,config:s,stack:o}=n,a=i.orient,c=n.scaleName(t),l=n.getScaleComponent(t),u=_e(t),f=xt(t),d=su(t),g=n.scaleName(d),p=n.getScaleComponent(Oo(t)),h=a==="horizontal"&&t==="y"||a==="vertical"&&t==="x";let m;(r.size||i.size)&&(h?m=he("size",n,{vgChannel:u,defaultRef:ne(i.size)}):v(yh(i.type)));const y=!!m,b=rf({channel:t,fieldDef:e,markDef:i,config:s,scaleType:(l||p)?.get("type"),useVlSizeChannel:h});m=m||{[u]:lx(u,g||c,p||l,s,b,!!e,i.type)};const C=(l||p)?.get("type")==="band"&&Cn(b)&&!y?"top":"middle",O=ad(t,i,s,C),F=O==="xc"||O==="yc",{offset:_,offsetType:P}=ai({channel:t,markDef:i,encoding:r,model:n,bandPosition:F?.5:0}),U=ea({channel:t,channelDef:e,markDef:i,config:s,scaleName:c,scale:l,stack:o,offset:_,defaultRef:Ca({model:n,defaultPos:"mid",channel:t,scaleName:c,scale:l}),bandPosition:F?P==="encoding"?0:.5:k(b)?{signal:`(1-${b})/2`}:Cn(b)?(1-b.band)/2:0});if(u)return{[O]:U,...m};{const oe=Gt(f),de=m[u],Re=_?{...de,offset:_}:de;return{[O]:U,[oe]:N(U)?[U[0],{...U[1],offset:Re}]:{...U,offset:Re}}}}function Uc(e,t,n,i,r,s,o){if(Zl(e))return 0;const a=e==="x"||e==="y2",c=a?-t/2:t/2;if(k(n)||k(r)||k(i)||s){const l=Xe(n),u=Xe(r),f=Xe(i),d=Xe(s),p=s?`(${o} < ${d} ? ${a?"":"-"}0.5 * (${d} - (${o})) : ${c})`:c,h=f?`${f} + `:"",m=l?`(${l} ? -1 : 1) * `:"",y=u?`(${u} + ${p})`:p;return{signal:h+m+y}}else return r=r||0,i+(n?-r-c:+r+c)}function fx({fieldDef:e,fieldDef2:t,channel:n,model:i}){const{config:r,markDef:s,encoding:o}=i,a=i.getScaleComponent(n),c=i.scaleName(n),l=a?a.get("type"):void 0,u=a.get("reverse"),f=rf({channel:n,fieldDef:e,markDef:s,config:r,scaleType:l}),g=i.component.axes[n]?.[0]?.get("translate")??.5,p=ue(n)?V("binSpacing",s,r)??0:0,h=xt(n),m=Gt(n),y=Gt(h),b=Ot("minBandSize",s,r),{offset:C}=ai({channel:n,markDef:s,encoding:o,model:i,bandPosition:0}),{offset:O}=ai({channel:h,markDef:s,encoding:o,model:i,bandPosition:0}),F=Dm({fieldDef:e,scaleName:c}),_=Uc(n,p,u,g,C,b,F),P=Uc(h,p,u,g,O??C,b,F),U=k(f)?{signal:`(1-${f.signal})/2`}:Cn(f)?(1-f.band)/2:.5,oe=Ht({fieldDef:e,fieldDef2:t,markDef:s,config:r});if(ee(e.bin)||e.timeUnit){const de=e.timeUnit&&oe!==.5;return{[y]:Bc({fieldDef:e,scaleName:c,bandPosition:U,offset:P,useRectOffsetField:de}),[m]:Bc({fieldDef:e,scaleName:c,bandPosition:k(U)?{signal:`1-${U.signal}`}:1-U,offset:_,useRectOffsetField:de})}}else if(xe(e.bin)){const de=mn(e,c,{},{offset:P});if(S(t))return{[y]:de,[m]:mn(t,c,{},{offset:_})};if(Rn(e.bin)&&e.bin.step)return{[y]:de,[m]:{signal:`scale("${c}", ${E(e,{expr:"datum"})} + ${e.bin.step})`,offset:_}}}v(Cu(h))}function Bc({fieldDef:e,scaleName:t,bandPosition:n,offset:i,useRectOffsetField:r}){return xr({scaleName:t,fieldOrDatumDef:e,bandPosition:n,offset:i,...r?{startSuffix:os,endSuffix:as}:{}})}const dx=new Set(["aria","width","height"]);function Ge(e,t){const{fill:n=void 0,stroke:i=void 0}=t.color==="include"?od(e):{};return{...px(e.markDef,t),...Wc(e,"fill",n),...Wc(e,"stroke",i),...he("opacity",e),...he("fillOpacity",e),...he("strokeOpacity",e),...he("strokeWidth",e),...he("strokeDash",e),...ix(e),...rd(e),...wa(e,"href"),...ex(e)}}function Wc(e,t,n){const{config:i,mark:r,markDef:s}=e;if(V("invalid",s,i)==="hide"&&n&&!rn(r)){const a=gx(e,{invalid:!0,channels:Ur});if(a)return{[t]:[{test:a,value:null},...ce(n)]}}return n?{[t]:n}:{}}function px(e,t){return Ag.reduce((n,i)=>(!dx.has(i)&&e[i]!==void 0&&t[i]!=="ignore"&&(n[i]=ne(e[i])),n),{})}function gx(e,{invalid:t=!1,channels:n}){const i=n.reduce((s,o)=>{const a=e.getScaleComponent(o);if(a){const c=a.get("type"),l=e.vgField(o,{expr:"datum"});l&&De(c)&&(s[l]=!0)}return s},{}),r=x(i);if(r.length>0){const s=t?"||":"&&";return r.map(o=>ta(o,t)).join(` ${s} `)}}function Na(e){const{config:t,markDef:n}=e;if(V("invalid",n,t)){const r=hx(e,{channels:vt});if(r)return{defined:{signal:r}}}return{}}function hx(e,{invalid:t=!1,channels:n}){const i=n.reduce((s,o)=>{const a=e.getScaleComponent(o);if(a){const c=a.get("type"),l=e.vgField(o,{expr:"datum",binSuffix:e.stack?.impute?"mid":void 0});l&&De(c)&&(s[l]=!0)}return s},{}),r=x(i);if(r.length>0){const s=t?"||":"&&";return r.map(o=>ta(o,t)).join(` ${s} `)}}function Gc(e,t){if(t!==void 0)return{[e]:ne(t)}}const Rs="voronoi",ld={defined:e=>e.type==="point"&&e.nearest,parse:(e,t)=>{if(t.events)for(const n of t.events)n.markname=e.getName(Rs)},marks:(e,t,n)=>{const{x:i,y:r}=t.project.hasChannel,s=e.mark;if(rn(s))return v(Pg(s)),n;const o={name:e.getName(Rs),type:"path",interactive:!0,from:{data:e.getName("marks")},encode:{update:{fill:{value:"transparent"},strokeWidth:{value:.35},stroke:{value:"transparent"},isVoronoi:{value:!0},...rd(e,{reactiveGeom:!0})}},transform:[{type:"voronoi",x:{expr:i||!r?"datum.datum.x || 0":"0"},y:{expr:r||!i?"datum.datum.y || 0":"0"},size:[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]}]};let a=0,c=!1;return n.forEach((l,u)=>{const f=l.name??"";f===e.component.mark[0].name?a=u:f.indexOf(Rs)>=0&&(c=!0)}),c||n.splice(a+1,0,o),n}},ud={defined:e=>e.type==="point"&&e.resolve==="global"&&e.bind&&e.bind!=="scales"&&!ma(e.bind),parse:(e,t,n)=>bd(t,n),topLevelSignals:(e,t,n)=>{const i=t.name,r=t.project,s=t.bind,o=t.init&&t.init[0],a=ld.defined(t)?"(item().isVoronoi ? datum.datum : datum)":"datum";return r.items.forEach((c,l)=>{const u=re(`${i}_${c.field}`);n.filter(d=>d.name===u).length||n.unshift({name:u,...o?{init:Fn(o[l])}:{value:null},on:t.events?[{events:t.events,update:`datum && item().mark.marktype !== 'group' ? ${a}[${B(c.field)}] : null`}]:[],bind:s[c.field]??s[c.channel]??s})}),n},signals:(e,t,n)=>{const i=t.name,r=t.project,s=n.filter(l=>l.name===i+Xt)[0],o=i+er,a=r.items.map(l=>re(`${i}_${l.field}`)),c=a.map(l=>`${l} !== null`).join(" && ");return a.length&&(s.update=`${c} ? {fields: ${o}, values: [${a.join(", ")}]} : null`),delete s.value,delete s.on,n}},kr="_toggle",fd={defined:e=>e.type==="point"&&!!e.toggle,signals:(e,t,n)=>n.concat({name:t.name+kr,value:!1,on:[{events:t.events,update:t.toggle}]}),modifyExpr:(e,t)=>{const n=t.name+Xt,i=t.name+kr;return`${i} ? null : ${n}, `+(t.resolve==="global"?`${i} ? null : true, `:`${i} ? null : {unit: ${bn(e)}}, `)+`${i} ? ${n} : null`}},mx={defined:e=>e.clear!==void 0&&e.clear!==!1,parse:(e,t)=>{t.clear&&(t.clear=I(t.clear)?pi(t.clear,"view"):t.clear)},topLevelSignals:(e,t,n)=>{if(ud.defined(t))for(const i of t.project.items){const r=n.findIndex(s=>s.name===re(`${t.name}_${i.field}`));r!==-1&&n[r].on.push({events:t.clear,update:"null"})}return n},signals:(e,t,n)=>{function i(r,s){r!==-1&&n[r].on&&n[r].on.push({events:t.clear,update:s})}if(t.type==="interval")for(const r of t.project.items){const s=n.findIndex(o=>o.name===r.signals.visual);if(i(s,"[0, 0]"),s===-1){const o=n.findIndex(a=>a.name===r.signals.data);i(o,"null")}}else{let r=n.findIndex(s=>s.name===t.name+Xt);i(r,"null"),fd.defined(t)&&(r=n.findIndex(s=>s.name===t.name+kr),i(r,"false"))}return n}},dd={defined:e=>{const t=e.resolve==="global"&&e.bind&&ma(e.bind),n=e.project.items.length===1&&e.project.items[0].field!==Ze;return t&&!n&&v(Mg),t&&n},parse:(e,t,n)=>{const i=z(n);if(i.select=I(i.select)?{type:i.select,toggle:t.toggle}:{...i.select,toggle:t.toggle},bd(t,i),X(n.select)&&(n.select.on||n.select.clear)){const o='event.item && indexof(event.item.mark.role, "legend") < 0';for(const a of t.events)a.filter=ce(a.filter??[]),a.filter.includes(o)||a.filter.push(o)}const r=Ts(t.bind)?t.bind.legend:"click",s=I(r)?pi(r,"view"):ce(r);t.bind={legend:{merge:s}}},topLevelSignals:(e,t,n)=>{const i=t.name,r=Ts(t.bind)&&t.bind.legend,s=o=>a=>{const c=z(a);return c.markname=o,c};for(const o of t.project.items){if(!o.hasLegend)continue;const a=`${re(o.field)}_legend`,c=`${i}_${a}`;if(n.filter(u=>u.name===c).length===0){const u=r.merge.map(s(`${a}_symbols`)).concat(r.merge.map(s(`${a}_labels`))).concat(r.merge.map(s(`${a}_entries`)));n.unshift({name:c,...t.init?{}:{value:null},on:[{events:u,update:"isDefined(datum.value) ? datum.value : item().items[0].items[0].datum.value",force:!0},{events:r.merge,update:`!event.item || !datum ? null : ${c}`,force:!0}]})}}return n},signals:(e,t,n)=>{const i=t.name,r=t.project,s=n.find(d=>d.name===i+Xt),o=i+er,a=r.items.filter(d=>d.hasLegend).map(d=>re(`${i}_${re(d.field)}_legend`)),l=`${a.map(d=>`${d} !== null`).join(" && ")} ? {fields: ${o}, values: [${a.join(", ")}]} : null`;t.events&&a.length>0?s.on.push({events:a.map(d=>({signal:d})),update:l}):a.length>0&&(s.update=l,delete s.value,delete s.on);const u=n.find(d=>d.name===i+kr),f=Ts(t.bind)&&t.bind.legend;return u&&(t.events?u.on.push({...u.on[0],events:f}):u.on[0].events=f),n}};function yx(e,t,n){const i=e.fieldDef(t)?.field;for(const r of ve(e.component.selection??{})){const s=r.project.hasField[i]??r.project.hasChannel[t];if(s&&dd.defined(r)){const o=n.get("selections")??[];o.push(r.name),n.set("selections",o,!1),s.hasLegend=!0}}}const pd="_translate_anchor",gd="_translate_delta",bx={defined:e=>e.type==="interval"&&e.translate,signals:(e,t,n)=>{const i=t.name,r=kt.defined(t),s=i+pd,{x:o,y:a}=t.project.hasChannel;let c=pi(t.translate,"scope");return r||(c=c.map(l=>(l.between[0].markname=i+Xn,l))),n.push({name:s,value:{},on:[{events:c.map(l=>l.between[0]),update:"{x: x(unit), y: y(unit)"+(o!==void 0?`, extent_x: ${r?oo(e,se):`slice(${o.signals.visual})`}`:"")+(a!==void 0?`, extent_y: ${r?oo(e,be):`slice(${a.signals.visual})`}`:"")+"}"}]},{name:i+gd,value:{},on:[{events:c,update:`{x: ${s}.x - x(unit), y: ${s}.y - y(unit)}`}]}),o!==void 0&&Hc(e,t,o,"width",n),a!==void 0&&Hc(e,t,a,"height",n),n}};function Hc(e,t,n,i,r){const s=t.name,o=s+pd,a=s+gd,c=n.channel,l=kt.defined(t),u=r.filter(F=>F.name===n.signals[l?"data":"visual"])[0],f=e.getSizeSignalRef(i).signal,d=e.getScaleComponent(c),g=d&&d.get("type"),p=d&&d.get("reverse"),h=l?c===se?p?"":"-":p?"-":"":"",m=`${o}.extent_${c}`,y=`${h}${a}.${c} / ${l?`${f}`:`span(${m})`}`,b=!l||!d?"panLinear":g==="log"?"panLog":g==="symlog"?"panSymlog":g==="pow"?"panPow":"panLinear",C=l?g==="pow"?`, ${d.get("exponent")??1}`:g==="symlog"?`, ${d.get("constant")??1}`:"":"",O=`${b}(${m}, ${y}${C})`;u.on.push({events:{signal:a},update:l?O:`clampRange(${O}, 0, ${f})`})}const hd="_zoom_anchor",md="_zoom_delta",xx={defined:e=>e.type==="interval"&&e.zoom,signals:(e,t,n)=>{const i=t.name,r=kt.defined(t),s=i+md,{x:o,y:a}=t.project.hasChannel,c=B(e.scaleName(se)),l=B(e.scaleName(be));let u=pi(t.zoom,"scope");return r||(u=u.map(f=>(f.markname=i+Xn,f))),n.push({name:i+hd,on:[{events:u,update:r?"{"+[c?`x: invert(${c}, x(unit))`:"",l?`y: invert(${l}, y(unit))`:""].filter(f=>f).join(", ")+"}":"{x: x(unit), y: y(unit)}"}]},{name:s,on:[{events:u,force:!0,update:"pow(1.001, event.deltaY * pow(16, event.deltaMode))"}]}),o!==void 0&&qc(e,t,o,"width",n),a!==void 0&&qc(e,t,a,"height",n),n}};function qc(e,t,n,i,r){const s=t.name,o=n.channel,a=kt.defined(t),c=r.filter(b=>b.name===n.signals[a?"data":"visual"])[0],l=e.getSizeSignalRef(i).signal,u=e.getScaleComponent(o),f=u&&u.get("type"),d=a?oo(e,o):c.name,g=s+md,p=`${s}${hd}.${o}`,h=!a||!u?"zoomLinear":f==="log"?"zoomLog":f==="symlog"?"zoomSymlog":f==="pow"?"zoomPow":"zoomLinear",m=a?f==="pow"?`, ${u.get("exponent")??1}`:f==="symlog"?`, ${u.get("constant")??1}`:"":"",y=`${h}(${d}, ${p}, ${g}${m})`;c.on.push({events:{signal:g},update:a?y:`clampRange(${y}, 0, ${l})`})}const kn="_store",Xt="_tuple",vx="_modify",yd="vlSelectionResolve",ls=[Zb,Qb,Yb,fd,ud,kt,dd,mx,bx,xx,ld];function Sx(e){let t=e.parent;for(;t&&!ze(t);)t=t.parent;return t}function bn(e,{escape:t}={escape:!0}){let n=t?B(e.name):e.name;const i=Sx(e);if(i){const{facet:r}=i;for(const s of Me)r[s]&&(n+=` + '__facet_${s}_' + (facet[${B(i.vgField(s))}])`)}return n}function Fa(e){return ve(e.component.selection??{}).reduce((t,n)=>t||n.project.hasSelectionId,!1)}function bd(e,t){(I(t.select)||!t.select.on)&&delete e.events,(I(t.select)||!t.select.clear)&&delete e.clear,(I(t.select)||!t.select.toggle)&&delete e.toggle}function ao(e){const t=[];return e.type==="Identifier"?[e.name]:e.type==="Literal"?[e.value]:(e.type==="MemberExpression"&&(t.push(...ao(e.object)),t.push(...ao(e.property))),t)}function xd(e){return e.object.type==="MemberExpression"?xd(e.object):e.object.name==="datum"}function vd(e){const t=Np(e),n=new Set;return t.visit(i=>{i.type==="MemberExpression"&&xd(i)&&n.add(ao(i).slice(1).join("."))}),n}class yi extends Y{clone(){return new yi(null,this.model,z(this.filter))}constructor(t,n,i){super(t),this.model=n,this.filter=i,this.expr=Tr(this.model,this.filter,this),this._dependentFields=vd(this.expr)}dependentFields(){return this._dependentFields}producedFields(){return new Set}assemble(){return{type:"filter",expr:this.expr}}hash(){return`Filter ${this.expr}`}}function Ex(e,t){const n={},i=e.config.selection;if(!t||!t.length)return n;for(const r of t){const s=re(r.name),o=r.select,a=I(o)?o:o.type,c=X(o)?z(o):{type:a},l=i[a];for(const d in l)d==="fields"||d==="encodings"||(d==="mark"&&(c[d]={...l[d],...c[d]}),(c[d]===void 0||c[d]===!0)&&(c[d]=z(l[d]??c[d])));const u=n[s]={...c,name:s,type:a,init:r.value,bind:r.bind,events:I(c.on)?pi(c.on,"scope"):ce(z(c.on))},f=z(r);for(const d of ls)d.defined(u)&&d.parse&&d.parse(e,u,f)}return n}function Sd(e,t,n,i="datum"){const r=I(t)?t:t.param,s=re(r),o=B(s+kn);let a;try{a=e.getSelectionComponent(s,r)}catch{return`!!${s}`}if(a.project.timeUnit){const d=n??e.component.data.raw,g=a.project.timeUnit.clone();d.parent?g.insertAsParentOf(d):d.parent=g}const c=a.project.hasSelectionId?"vlSelectionIdTest(":"vlSelectionTest(",l=a.resolve==="global"?")":`, ${B(a.resolve)})`,u=`${c}${o}, ${i}${l}`,f=`length(data(${o}))`;return t.empty===!1?`${f} && ${u}`:`!${f} || ${u}`}function Ed(e,t,n){const i=re(t),r=n.encoding;let s=n.field,o;try{o=e.getSelectionComponent(i,t)}catch{return i}if(!r&&!s)s=o.project.items[0].field,o.project.items.length>1&&v(`A "field" or "encoding" must be specified when using a selection as a scale domain. Using "field": ${B(s)}.`);else if(r&&!s){const a=o.project.items.filter(c=>c.channel===r);!a.length||a.length>1?(s=o.project.items[0].field,v((a.length?"Multiple ":"No ")+`matching ${B(r)} encoding found for selection ${B(n.param)}. Using "field": ${B(s)}.`)):s=a[0].field}return`${o.name}[${B(Ue(s))}]`}function $x(e,t){for(const[n,i]of Wt(e.component.selection??{})){const r=e.getName(`lookup_${n}`);e.component.data.outputNodes[r]=i.materialized=new we(new yi(t,e,{param:n}),r,Z.Lookup,e.component.data.outputNodeRefCounts)}}function Tr(e,t,n){return Oi(t,i=>I(i)?i:em(i)?Sd(e,i,n):zu(i))}function wx(e,t){if(e)return N(e)&&!Mt(e)?e.map(n=>aa(n,t)).join(", "):e}function Is(e,t,n,i){var r,s;e.encode??(e.encode={}),(r=e.encode)[t]??(r[t]={}),(s=e.encode[t]).update??(s.update={}),e.encode[t].update[n]=i}function ki(e,t,n,i={header:!1}){const{disable:r,orient:s,scale:o,labelExpr:a,title:c,zindex:l,...u}=e.combine();if(!r){for(const f in u){const d=iy[f],g=u[f];if(d&&d!==t&&d!=="both")delete u[f];else if(Zi(g)){const{condition:p,...h}=g,m=ce(p),y=vc[f];if(y){const{vgProp:b,part:C}=y,O=[...m.map(F=>{const{test:_,...P}=F;return{test:Tr(null,_),...P}}),h];Is(u,C,b,O),delete u[f]}else if(y===null){const b={signal:m.map(C=>{const{test:O,...F}=C;return`${Tr(null,O)} ? ${rc(F)} : `}).join("")+rc(h)};u[f]=b}}else if(k(g)){const p=vc[f];if(p){const{vgProp:h,part:m}=p;Is(u,m,h,g),delete u[f]}}G(["labelAlign","labelBaseline"],f)&&u[f]===null&&delete u[f]}if(t==="grid"){if(!u.grid)return;if(u.encode){const{grid:f}=u.encode;u.encode={...f?{grid:f}:{}},K(u.encode)&&delete u.encode}return{scale:o,orient:s,...u,domain:!1,labels:!1,aria:!1,maxExtent:0,minExtent:0,ticks:!1,zindex:le(l,0)}}else{if(!i.header&&e.mainExtracted)return;if(a!==void 0){let d=a;u.encode?.labels?.update&&k(u.encode.labels.update.text)&&(d=Sn(a,"datum.label",u.encode.labels.update.text.signal)),Is(u,"labels","text",{signal:d})}if(u.labelAlign===null&&delete u.labelAlign,u.encode){for(const d of gf)e.hasAxisPart(d)||delete u.encode[d];K(u.encode)&&delete u.encode}const f=wx(c,n);return{scale:o,orient:s,grid:!1,...f?{title:f}:{},...u,...n.aria===!1?{aria:!1}:{},zindex:le(l,0)}}}}function $d(e){const{axes:t}=e.component,n=[];for(const i of vt)if(t[i]){for(const r of t[i])if(!r.get("disable")&&!r.get("gridScale")){const s=i==="x"?"height":"width",o=e.getSizeSignalRef(s).signal;s!==o&&n.push({name:s,update:o})}}return n}function Cx(e,t){const{x:n=[],y:i=[]}=e;return[...n.map(r=>ki(r,"grid",t)),...i.map(r=>ki(r,"grid",t)),...n.map(r=>ki(r,"main",t)),...i.map(r=>ki(r,"main",t))].filter(r=>r)}function Vc(e,t,n,i){return Object.assign.apply(null,[{},...e.map(r=>{if(r==="axisOrient"){const s=n==="x"?"bottom":"left",o=t[n==="x"?"axisBottom":"axisLeft"]||{},a=t[n==="x"?"axisTop":"axisRight"]||{},c=new Set([...x(o),...x(a)]),l={};for(const u of c.values())l[u]={signal:`${i.signal} === "${s}" ? ${Xe(o[u])} : ${Xe(a[u])}`};return l}return t[r]})])}function Nx(e,t,n,i){const r=t==="band"?["axisDiscrete","axisBand"]:t==="point"?["axisDiscrete","axisPoint"]:Uu(t)?["axisQuantitative"]:t==="time"||t==="utc"?["axisTemporal"]:[],s=e==="x"?"axisX":"axisY",o=k(n)?"axisOrient":`axis${Wi(n)}`,a=[...r,...r.map(l=>s+l.substr(4))],c=["axis",o,s];return{vlOnlyAxisConfig:Vc(a,i,e,n),vgAxisConfig:Vc(c,i,e,n),axisConfigStyle:Fx([...c,...a],i)}}function Fx(e,t){const n=[{}];for(const i of e){let r=t[i]?.style;if(r){r=ce(r);for(const s of r)n.push(t.style[s])}}return Object.assign.apply(null,n)}function co(e,t,n,i={}){const r=hu(e,n,t);if(r!==void 0)return{configFrom:"style",configValue:r};for(const s of["vlOnlyAxisConfig","vgAxisConfig","axisConfigStyle"])if(i[s]?.[e]!==void 0)return{configFrom:s,configValue:i[s][e]};return{}}const Xc={scale:({model:e,channel:t})=>e.scaleName(t),format:({format:e})=>e,formatType:({formatType:e})=>e,grid:({fieldOrDatumDef:e,axis:t,scaleType:n})=>t.grid??kx(n,e),gridScale:({model:e,channel:t})=>Tx(e,t),labelAlign:({axis:e,labelAngle:t,orient:n,channel:i})=>e.labelAlign||Cd(t,n,i),labelAngle:({labelAngle:e})=>e,labelBaseline:({axis:e,labelAngle:t,orient:n,channel:i})=>e.labelBaseline||wd(t,n,i),labelFlush:({axis:e,fieldOrDatumDef:t,channel:n})=>e.labelFlush??Ox(t.type,n),labelOverlap:({axis:e,fieldOrDatumDef:t,scaleType:n})=>e.labelOverlap??_x(t.type,n,S(t)&&!!t.timeUnit,S(t)?t.sort:void 0),orient:({orient:e})=>e,tickCount:({channel:e,model:t,axis:n,fieldOrDatumDef:i,scaleType:r})=>{const s=e==="x"?"width":e==="y"?"height":void 0,o=s?t.getSizeSignalRef(s):void 0;return n.tickCount??Ix({fieldOrDatumDef:i,scaleType:r,size:o,values:n.values})},tickMinStep:Lx,title:({axis:e,model:t,channel:n})=>{if(e.title!==void 0)return e.title;const i=Nd(t,n);if(i!==void 0)return i;const r=t.typedFieldDef(n),s=n==="x"?"x2":"y2",o=t.fieldDef(s);return yu(r?[bc(r)]:[],S(o)?[bc(o)]:[])},values:({axis:e,fieldOrDatumDef:t})=>Px(e,t),zindex:({axis:e,fieldOrDatumDef:t,mark:n})=>e.zindex??zx(n,t)};function kx(e,t){return!ye(e)&&S(t)&&!ee(t?.bin)&&!xe(t?.bin)}function Tx(e,t){const n=t==="x"?"y":"x";if(e.getScaleComponent(n))return e.scaleName(n)}function Ax(e,t,n,i,r){const s=t?.labelAngle;if(s!==void 0)return k(s)?s:Pi(s);{const{configValue:o}=co("labelAngle",i,t?.style,r);return o!==void 0?Pi(o):n===se&&G([Ko,Yo],e.type)&&!(S(e)&&e.timeUnit)?270:void 0}}function lo(e){return`(((${e.signal} % 360) + 360) % 360)`}function wd(e,t,n,i){if(e!==void 0)if(n==="x"){if(k(e)){const r=lo(e),s=k(t)?`(${t.signal} === "top")`:t==="top";return{signal:`(45 < ${r} && ${r} < 135) || (225 < ${r} && ${r} < 315) ? "middle" :(${r} <= 45 || 315 <= ${r}) === ${s} ? "bottom" : "top"`}}if(45<e&&e<135||225<e&&e<315)return"middle";if(k(t)){const r=e<=45||315<=e?"===":"!==";return{signal:`${t.signal} ${r} "top" ? "bottom" : "top"`}}return(e<=45||315<=e)==(t==="top")?"bottom":"top"}else{if(k(e)){const r=lo(e),s=k(t)?`(${t.signal} === "left")`:t==="left";return{signal:`${r} <= 45 || 315 <= ${r} || (135 <= ${r} && ${r} <= 225) ? ${i?'"middle"':"null"} : (45 <= ${r} && ${r} <= 135) === ${s} ? "top" : "bottom"`}}if(e<=45||315<=e||135<=e&&e<=225)return i?"middle":null;if(k(t)){const r=45<=e&&e<=135?"===":"!==";return{signal:`${t.signal} ${r} "left" ? "top" : "bottom"`}}return(45<=e&&e<=135)==(t==="left")?"top":"bottom"}}function Cd(e,t,n){if(e===void 0)return;const i=n==="x",r=i?0:90,s=i?"bottom":"left";if(k(e)){const o=lo(e),a=k(t)?`(${t.signal} === "${s}")`:t===s;return{signal:`(${r?`(${o} + 90)`:o} % 180 === 0) ? ${i?null:'"center"'} :(${r} < ${o} && ${o} < ${180+r}) === ${a} ? "left" : "right"`}}if((e+r)%180===0)return i?null:"center";if(k(t)){const o=r<e&&e<180+r?"===":"!==";return{signal:`${`${t.signal} ${o} "${s}"`} ? "left" : "right"`}}return(r<e&&e<180+r)==(t===s)?"left":"right"}function Ox(e,t){if(t==="x"&&G(["quantitative","temporal"],e))return!0}function _x(e,t,n,i){if(n&&!X(i)||e!=="nominal"&&e!=="ordinal")return t==="log"||t==="symlog"?"greedy":!0}function Rx(e){return e==="x"?"bottom":"left"}function Ix({fieldOrDatumDef:e,scaleType:t,size:n,values:i}){if(!i&&!ye(t)&&t!=="log"){if(S(e)){if(ee(e.bin))return{signal:`ceil(${n.signal}/10)`};if(e.timeUnit&&G(["month","hours","day","quarter"],me(e.timeUnit)?.unit))return}return{signal:`ceil(${n.signal}/40)`}}}function Lx({format:e,fieldOrDatumDef:t}){if(e==="d")return 1;if(S(t)){const{timeUnit:n}=t;if(n){const i=Iu(n);if(i)return{signal:i}}}}function Nd(e,t){const n=t==="x"?"x2":"y2",i=e.fieldDef(t),r=e.fieldDef(n),s=i?i.title:void 0,o=r?r.title:void 0;if(s&&o)return bu(s,o);if(s)return s;if(o)return o;if(s!==void 0)return s;if(o!==void 0)return o}function Px(e,t){const n=e.values;if(N(n))return pf(t,n);if(k(n))return n}function zx(e,t){return e==="rect"&&Sr(t)?1:0}class ci extends Y{clone(){return new ci(null,z(this.transform))}constructor(t,n){super(t),this.transform=n,this._dependentFields=vd(this.transform.calculate)}static parseAllForSortIndex(t,n){return n.forEachFieldDef((i,r)=>{if(Pn(i)&&nf(i.sort)){const{field:s,timeUnit:o}=i,a=i.sort,c=a.map((l,u)=>`${zu({field:s,timeUnit:o,equal:l})} ? ${u} : `).join("")+a.length;t=new ci(t,{calculate:c,as:li(i,r,{forAs:!0})})}}),t}producedFields(){return new Set([this.transform.as])}dependentFields(){return this._dependentFields}assemble(){return{type:"formula",expr:this.transform.calculate,as:this.transform.as}}hash(){return`Calculate ${W(this.transform)}`}}function li(e,t,n){return E(e,{prefix:t,suffix:"sort_index",...n})}function us(e,t){return G(["top","bottom"],t)?"column":G(["left","right"],t)||e==="row"?"row":"column"}function ui(e,t,n,i){const r=i==="row"?n.headerRow:i==="column"?n.headerColumn:n.headerFacet;return le((t||{})[e],r[e],n.header[e])}function fs(e,t,n,i){const r={};for(const s of e){const o=ui(s,t||{},n,i);o!==void 0&&(r[s]=o)}return r}const ka=["row","column"],Ta=["header","footer"];function Dx(e,t){const n=e.component.layoutHeaders[t].title,i=e.config?e.config:void 0,r=e.component.layoutHeaders[t].facetFieldDef?e.component.layoutHeaders[t].facetFieldDef:void 0,{titleAnchor:s,titleAngle:o,titleOrient:a}=fs(["titleAnchor","titleAngle","titleOrient"],r.header,i,t),c=us(t,a),l=Pi(o);return{name:`${t}-title`,type:"group",role:`${c}-title`,title:{text:n,...t==="row"?{orient:"left"}:{},style:"guide-title",...kd(l,c),...Fd(c,l,s),...Td(i,r,t,Cy,Of)}}}function Fd(e,t,n="middle"){switch(n){case"start":return{align:"left"};case"end":return{align:"right"}}const i=Cd(t,e==="row"?"left":"top",e==="row"?"y":"x");return i?{align:i}:{}}function kd(e,t){const n=wd(e,t==="row"?"left":"top",t==="row"?"y":"x",!0);return n?{baseline:n}:{}}function jx(e,t){const n=e.component.layoutHeaders[t],i=[];for(const r of Ta)if(n[r])for(const s of n[r]){const o=Ux(e,t,r,n,s);o!=null&&i.push(o)}return i}function Mx(e,t){const{sort:n}=e;return ut(n)?{field:E(n,{expr:"datum"}),order:n.order??"ascending"}:N(n)?{field:li(e,t,{expr:"datum"}),order:"ascending"}:{field:E(e,{expr:"datum"}),order:n??"ascending"}}function uo(e,t,n){const{format:i,formatType:r,labelAngle:s,labelAnchor:o,labelOrient:a,labelExpr:c}=fs(["format","formatType","labelAngle","labelAnchor","labelOrient","labelExpr"],e.header,n,t),l=ia({fieldOrDatumDef:e,format:i,formatType:r,expr:"parent",config:n}).signal,u=us(t,a);return{text:{signal:c?Sn(Sn(c,"datum.label",l),"datum.value",E(e,{expr:"parent"})):l},...t==="row"?{orient:"left"}:{},style:"guide-label",frame:"group",...kd(s,u),...Fd(u,s,o),...Td(n,e,t,Ny,_f)}}function Ux(e,t,n,i,r){if(r){let s=null;const{facetFieldDef:o}=i,a=e.config?e.config:void 0;if(o&&r.labels){const{labelOrient:f}=fs(["labelOrient"],o.header,a,t);(t==="row"&&!G(["top","bottom"],f)||t==="column"&&!G(["left","right"],f))&&(s=uo(o,t,a))}const c=ze(e)&&!Ki(e.facet),l=r.axes,u=l?.length>0;if(s||u){const f=t==="row"?"height":"width";return{name:e.getName(`${t}_${n}`),type:"group",role:`${t}-${n}`,...i.facetFieldDef?{from:{data:e.getName(`${t}_domain`)},sort:Mx(o,t)}:{},...u&&c?{from:{data:e.getName(`facet_domain_${t}`)}}:{},...s?{title:s}:{},...r.sizeSignal?{encode:{update:{[f]:r.sizeSignal}}}:{},...u?{axes:l}:{}}}}return null}const Bx={column:{start:0,end:1},row:{start:1,end:0}};function Wx(e,t){return Bx[t][e]}function Gx(e,t){const n={};for(const i of Me){const r=e[i];if(r?.facetFieldDef){const{titleAnchor:s,titleOrient:o}=fs(["titleAnchor","titleOrient"],r.facetFieldDef.header,t,i),a=us(i,o),c=Wx(s,a);c!==void 0&&(n[a]=c)}}return K(n)?void 0:n}function Td(e,t,n,i,r){const s={};for(const o of i){if(!r[o])continue;const a=ui(o,t?.header,e,n);a!==void 0&&(s[r[o]]=a)}return s}function Aa(e){return[...or(e,"width"),...or(e,"height"),...or(e,"childWidth"),...or(e,"childHeight")]}function or(e,t){const n=t==="width"?"x":"y",i=e.component.layoutSize.get(t);if(!i||i==="merged")return[];const r=e.getSizeSignalRef(t).signal;if(i==="step"){const s=e.getScaleComponent(n);if(s){const o=s.get("type"),a=s.get("range");if(ye(o)&&nn(a)){const c=e.scaleName(n);return ze(e.parent)&&e.parent.component.resolve.scale[n]==="independent"?[Yc(c,a)]:[Yc(c,a),{name:r,update:Ad(c,s,`domain('${c}').length`)}]}}throw new Error("layout size is step although width/height is not step.")}else if(i=="container"){const s=r.endsWith("width"),o=s?"containerSize()[0]":"containerSize()[1]",a=no(e.config.view,s?"width":"height"),c=`isFinite(${o}) ? ${o} : ${a}`;return[{name:r,init:c,on:[{update:c,events:"window:resize"}]}]}else return[{name:r,value:i}]}function Yc(e,t){const n=`${e}_step`;return k(t.step)?{name:n,update:t.step.signal}:{name:n,value:t.step}}function Ad(e,t,n){const i=t.get("type"),r=t.get("padding"),s=le(t.get("paddingOuter"),r);let o=t.get("paddingInner");return o=i==="band"?o!==void 0?o:r:1,`bandspace(${n}, ${Xe(o)}, ${Xe(s)}) * ${e}_step`}function Od(e){return e==="childWidth"?"width":e==="childHeight"?"height":e}function _d(e,t){return x(e).reduce((n,i)=>{const r=e[i];return{...n,...mi(t,r,i,s=>ne(s.value))}},{})}function Rd(e,t){if(ze(t))return e==="theta"?"independent":"shared";if(vi(t))return"shared";if(za(t))return ue(e)||e==="theta"||e==="radius"?"independent":"shared";throw new Error("invalid model type for resolve")}function Oa(e,t){const n=e.scale[t],i=ue(t)?"axis":"legend";return n==="independent"?(e[i][t]==="shared"&&v(Eh(t)),"independent"):e[i][t]||"shared"}const Hx={...Ty,disable:1,labelExpr:1,selections:1,opacity:1,shape:1,stroke:1,fill:1,size:1,strokeWidth:1,strokeDash:1,encode:1},Id=x(Hx);class qx extends Dt{}const Kc={symbols:Vx,gradient:Xx,labels:Yx,entries:Kx};function Vx(e,{fieldOrDatumDef:t,model:n,channel:i,legendCmpt:r,legendType:s}){if(s!=="symbol")return;const{markDef:o,encoding:a,config:c,mark:l}=n,u=o.filled&&l!=="trail";let f={...Rg({},n,Cm),...od(n,{filled:u})};const d=r.get("symbolOpacity")??c.legend.symbolOpacity,g=r.get("symbolFillColor")??c.legend.symbolFillColor,p=r.get("symbolStrokeColor")??c.legend.symbolStrokeColor,h=d===void 0?Ld(a.opacity)??o.opacity:void 0;if(f.fill){if(i==="fill"||u&&i===Ae)delete f.fill;else if(f.fill.field)g?delete f.fill:(f.fill=ne(c.legend.symbolBaseFillColor??"black"),f.fillOpacity=ne(h??1));else if(N(f.fill)){const m=fo(a.fill??a.color)??o.fill??(u&&o.color);m&&(f.fill=ne(m))}}if(f.stroke){if(i==="stroke"||!u&&i===Ae)delete f.stroke;else if(f.stroke.field||p)delete f.stroke;else if(N(f.stroke)){const m=le(fo(a.stroke||a.color),o.stroke,u?o.color:void 0);m&&(f.stroke={value:m})}}if(i!==Lt){const m=S(t)&&zd(n,r,t);m?f.opacity=[{test:m,...ne(h??1)},ne(c.legend.unselectedOpacity)]:h&&(f.opacity=ne(h))}return f={...f,...e},K(f)?void 0:f}function Xx(e,{model:t,legendType:n,legendCmpt:i}){if(n!=="gradient")return;const{config:r,markDef:s,encoding:o}=t;let a={};const l=(i.get("gradientOpacity")??r.legend.gradientOpacity)===void 0?Ld(o.opacity)||s.opacity:void 0;return l&&(a.opacity=ne(l)),a={...a,...e},K(a)?void 0:a}function Yx(e,{fieldOrDatumDef:t,model:n,channel:i,legendCmpt:r}){const s=n.legend(i)||{},o=n.config,a=S(t)?zd(n,r,t):void 0,c=a?[{test:a,value:1},{value:o.legend.unselectedOpacity}]:void 0,{format:l,formatType:u}=s;let f;Nn(u)?f=Ke({fieldOrDatumDef:t,field:"datum.value",format:l,formatType:u,config:o}):l===void 0&&u===void 0&&o.customFormatTypes&&(t.type==="quantitative"&&o.numberFormatType?f=Ke({fieldOrDatumDef:t,field:"datum.value",format:o.numberFormat,formatType:o.numberFormatType,config:o}):t.type==="temporal"&&o.timeFormatType&&S(t)&&t.timeUnit===void 0&&(f=Ke({fieldOrDatumDef:t,field:"datum.value",format:o.timeFormat,formatType:o.timeFormatType,config:o})));const d={...c?{opacity:c}:{},...f?{text:f}:{},...e};return K(d)?void 0:d}function Kx(e,{legendCmpt:t}){return t.get("selections")?.length?{...e,fill:{value:"transparent"}}:e}function Ld(e){return Pd(e,(t,n)=>Math.max(t,n.value))}function fo(e){return Pd(e,(t,n)=>le(t,n.value))}function Pd(e,t){if(Hm(e))return ce(e.condition).reduce(t,e.value);if(Je(e))return e.value}function zd(e,t,n){const i=t.get("selections");if(!i?.length)return;const r=B(n.field);return i.map(s=>`(!length(data(${B(re(s)+kn)})) || (${s}[${r}] && indexof(${s}[${r}], datum.value) >= 0))`).join(" || ")}const Qc={direction:({direction:e})=>e,format:({fieldOrDatumDef:e,legend:t,config:n})=>{const{format:i,formatType:r}=t;return Ju(e,e.type,i,r,n,!1)},formatType:({legend:e,fieldOrDatumDef:t,scaleType:n})=>{const{formatType:i}=e;return Zu(i,t,n)},gradientLength:e=>{const{legend:t,legendConfig:n}=e;return t.gradientLength??n.gradientLength??iv(e)},labelOverlap:({legend:e,legendConfig:t,scaleType:n})=>e.labelOverlap??t.labelOverlap??rv(n),symbolType:({legend:e,markDef:t,channel:n,encoding:i})=>e.symbolType??Jx(t.type,n,i.shape,t.shape),title:({fieldOrDatumDef:e,config:t})=>Vn(e,t,{allowDisabling:!0}),type:({legendType:e,scaleType:t,channel:n})=>{if(qn(n)&&Ye(t)){if(e==="gradient")return}else if(e==="symbol")return;return e},values:({fieldOrDatumDef:e,legend:t})=>Qx(t,e)};function Qx(e,t){const n=e.values;if(N(n))return pf(t,n);if(k(n))return n}function Jx(e,t,n,i){if(t!=="shape"){const r=fo(n)??i;if(r)return r}switch(e){case"bar":case"rect":case"image":case"square":return"square";case"line":case"trail":case"rule":return"stroke";case"arc":case"point":case"circle":case"tick":case"geoshape":case"area":case"text":return"circle"}}function Zx(e){const{legend:t}=e;return le(t.type,ev(e))}function ev({channel:e,timeUnit:t,scaleType:n}){if(qn(e)){if(G(["quarter","month","day"],t))return"symbol";if(Ye(n))return"gradient"}return"symbol"}function tv({legendConfig:e,legendType:t,orient:n,legend:i}){return i.direction??e[t?"gradientDirection":"symbolDirection"]??nv(n,t)}function nv(e,t){switch(e){case"top":case"bottom":return"horizontal";case"left":case"right":case"none":case void 0:return;default:return t==="gradient"?"horizontal":void 0}}function iv({legendConfig:e,model:t,direction:n,orient:i,scaleType:r}){const{gradientHorizontalMaxLength:s,gradientHorizontalMinLength:o,gradientVerticalMaxLength:a,gradientVerticalMinLength:c}=e;if(Ye(r))return n==="horizontal"?i==="top"||i==="bottom"?Jc(t,"width",o,s):o:Jc(t,"height",c,a)}function Jc(e,t,n,i){return{signal:`clamp(${e.getSizeSignalRef(t).signal}, ${n}, ${i})`}}function rv(e){if(G(["quantile","threshold","log","symlog"],e))return"greedy"}function Dd(e){const t=ae(e)?sv(e):lv(e);return e.component.legends=t,t}function sv(e){const{encoding:t}=e,n={};for(const i of[Ae,...If]){const r=fe(t[i]);!r||!e.getScaleComponent(i)||i===Oe&&S(r)&&r.type===hi||(n[i]=cv(e,i))}return n}function ov(e,t){const n=e.scaleName(t);if(e.mark==="trail"){if(t==="color")return{stroke:n};if(t==="size")return{strokeWidth:n}}return t==="color"?e.markDef.filled?{fill:n}:{stroke:n}:{[t]:n}}function av(e,t,n,i){switch(t){case"disable":return n!==void 0;case"values":return!!n?.values;case"title":if(t==="title"&&e===i?.title)return!0}return e===(n||{})[t]}function cv(e,t){let n=e.legend(t);const{markDef:i,encoding:r,config:s}=e,o=s.legend,a=new qx({},ov(e,t));yx(e,t,a);const c=n!==void 0?!n:o.disable;if(a.set("disable",c,n!==void 0),c)return a;n=n||{};const l=e.getScaleComponent(t).get("type"),u=fe(r[t]),f=S(u)?me(u.timeUnit)?.unit:void 0,d=n.orient||s.legend.orient||"right",g=Zx({legend:n,channel:t,timeUnit:f,scaleType:l}),p=tv({legend:n,legendType:g,orient:d,legendConfig:o}),h={legend:n,channel:t,model:e,markDef:i,encoding:r,fieldOrDatumDef:u,legendConfig:o,config:s,scaleType:l,orient:d,legendType:g,direction:p};for(const O of Id){if(g==="gradient"&&O.startsWith("symbol")||g==="symbol"&&O.startsWith("gradient"))continue;const F=O in Qc?Qc[O](h):n[O];if(F!==void 0){const _=av(F,O,n,e.fieldDef(t));(_||s.legend[O]===void 0)&&a.set(O,F,_)}}const m=n?.encoding??{},y=a.get("selections"),b={},C={fieldOrDatumDef:u,model:e,channel:t,legendCmpt:a,legendType:g};for(const O of["labels","legend","title","symbols","gradient","entries"]){const F=_d(m[O]??{},e),_=O in Kc?Kc[O](F,C):F;_!==void 0&&!K(_)&&(b[O]={...y?.length&&S(u)?{name:`${re(u.field)}_legend_${O}`}:{},...y?.length?{interactive:!!y}:{},update:_})}return K(b)||a.set("encode",b,!!n?.encoding),a}function lv(e){const{legends:t,resolve:n}=e.component;for(const i of e.children){Dd(i);for(const r of x(i.component.legends))n.legend[r]=Oa(e.component.resolve,r),n.legend[r]==="shared"&&(t[r]=jd(t[r],i.component.legends[r]),t[r]||(n.legend[r]="independent",delete t[r]))}for(const i of x(t))for(const r of e.children)r.component.legends[i]&&n.legend[i]==="shared"&&delete r.component.legends[i];return t}function jd(e,t){if(!e)return t.clone();const n=e.getWithExplicit("orient"),i=t.getWithExplicit("orient");if(n.explicit&&i.explicit&&n.value!==i.value)return;let r=!1;for(const s of Id){const o=qt(e.getWithExplicit(s),t.getWithExplicit(s),s,"legend",(a,c)=>{switch(s){case"symbolType":return uv(a,c);case"title":return xu(a,c);case"type":return r=!0,Ie("symbol")}return ss(a,c,s,"legend")});e.setWithExplicit(s,o)}return r&&(e.implicit?.encode?.gradient&&hr(e.implicit,["encode","gradient"]),e.explicit?.encode?.gradient&&hr(e.explicit,["encode","gradient"])),e}function uv(e,t){return t.value==="circle"?t:e}function fv(e,t,n,i){var r,s;e.encode??(e.encode={}),(r=e.encode)[t]??(r[t]={}),(s=e.encode[t]).update??(s.update={}),e.encode[t].update[n]=i}function Md(e){const t=e.component.legends,n={};for(const r of x(t)){const s=e.getScaleComponent(r),o=Q(s.get("domains"));if(n[o])for(const a of n[o])jd(a,t[r])||n[o].push(t[r]);else n[o]=[t[r].clone()]}return ve(n).flat().map(r=>dv(r,e.config)).filter(r=>r!==void 0)}function dv(e,t){const{disable:n,labelExpr:i,selections:r,...s}=e.combine();if(!n){if(t.aria===!1&&s.aria==null&&(s.aria=!1),s.encode?.symbols){const o=s.encode.symbols.update;o.fill&&o.fill.value!=="transparent"&&!o.stroke&&!s.stroke&&(o.stroke={value:"transparent"});for(const a of If)s[a]&&delete o[a]}if(s.title||delete s.title,i!==void 0){let o=i;s.encode?.labels?.update&&k(s.encode.labels.update.text)&&(o=Sn(i,"datum.label",s.encode.labels.update.text.signal)),fv(s,"labels","text",{signal:o})}return s}}function pv(e){return vi(e)||za(e)?gv(e):Ud(e)}function gv(e){return e.children.reduce((t,n)=>t.concat(n.assembleProjections()),Ud(e))}function Ud(e){const t=e.component.projection;if(!t||t.merged)return[];const n=t.combine(),{name:i}=n;if(t.data){const r={signal:`[${t.size.map(o=>o.signal).join(", ")}]`},s=t.data.reduce((o,a)=>{const c=k(a)?a.signal:`data('${e.lookupDataSource(a)}')`;return G(o,c)||o.push(c),o},[]);if(s.length<=0)throw new Error("Projection's fit didn't find any data sources");return[{name:i,size:r,fit:{signal:s.length>1?`[${s.join(", ")}]`:s[0]},...n}]}else return[{name:i,translate:{signal:"[width / 2, height / 2]"},...n}]}const hv=["type","clipAngle","clipExtent","center","rotate","precision","reflectX","reflectY","coefficient","distance","fraction","lobes","parallel","radius","ratio","spacing","tilt"];class Bd extends Dt{constructor(t,n,i,r){super({...n},{name:t}),this.specifiedProjection=n,this.size=i,this.data=r,this.merged=!1}get isFit(){return!!this.data}}function Wd(e){e.component.projection=ae(e)?mv(e):xv(e)}function mv(e){if(e.hasProjection){const t=Fe(e.specifiedProjection),n=!(t&&(t.scale!=null||t.translate!=null)),i=n?[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]:void 0,r=n?yv(e):void 0,s=new Bd(e.projectionName(!0),{...Fe(e.config.projection),...t},i,r);return s.get("type")||s.set("type","equalEarth",!1),s}}function yv(e){const t=[],{encoding:n}=e;for(const i of[[it,nt],[Be,rt]])(fe(n[i[0]])||fe(n[i[1]]))&&t.push({signal:e.getName(`geojson_${t.length}`)});return e.channelHasField(Oe)&&e.typedFieldDef(Oe).type===hi&&t.push({signal:e.getName(`geojson_${t.length}`)}),t.length===0&&t.push(e.requestDataName(Z.Main)),t}function bv(e,t){const n=$o(hv,r=>!!(!Gn(e.explicit,r)&&!Gn(t.explicit,r)||Gn(e.explicit,r)&&Gn(t.explicit,r)&&Pe(e.get(r),t.get(r))));if(Pe(e.size,t.size)){if(n)return e;if(Pe(e.explicit,{}))return t;if(Pe(t.explicit,{}))return e}return null}function xv(e){if(e.children.length===0)return;let t;for(const i of e.children)Wd(i);const n=$o(e.children,i=>{const r=i.component.projection;if(r)if(t){const s=bv(t,r);return s&&(t=s),!!s}else return t=r,!0;else return!0});if(t&&n){const i=e.projectionName(!0),r=new Bd(i,t.specifiedProjection,t.size,z(t.data));for(const s of e.children){const o=s.component.projection;o&&(o.isFit&&r.data.push(...s.component.projection.data),s.renameProjection(o.get("name"),i),o.merged=!0)}return r}}function vv(e,t,n,i){if(Ji(t,n)){const r=ae(e)?e.axis(n)??e.legend(n)??{}:{},s=E(t,{expr:"datum"}),o=E(t,{expr:"datum",binSuffix:"end"});return{formulaAs:E(t,{binSuffix:"range",forAs:!0}),formula:Yi(s,o,r.format,r.formatType,i)}}return{}}function Gd(e,t){return`${fu(e)}_${t}`}function Sv(e,t){return{signal:e.getName(`${t}_bins`),extentSignal:e.getName(`${t}_extent`)}}function _a(e,t,n){const i=Zr(n,void 0)??{},r=Gd(i,t);return e.getName(`${r}_bins`)}function Ev(e){return"as"in e}function Zc(e,t,n){let i,r;Ev(e)?i=I(e.as)?[e.as,`${e.as}_end`]:[e.as[0],e.as[1]]:i=[E(e,{forAs:!0}),E(e,{binSuffix:"end",forAs:!0})];const s={...Zr(t,void 0)},o=Gd(s,e.field),{signal:a,extentSignal:c}=Sv(n,o);if(Br(s.extent)){const u=s.extent;r=Ed(n,u.param,u),delete s.extent}const l={bin:s,field:e.field,as:[i],...a?{signal:a}:{},...c?{extentSignal:c}:{},...r?{span:r}:{}};return{key:o,binComponent:l}}class dt extends Y{clone(){return new dt(null,z(this.bins))}constructor(t,n){super(t),this.bins=n}static makeFromEncoding(t,n){const i=n.reduceFieldDef((r,s,o)=>{if(Ce(s)&&ee(s.bin)){const{key:a,binComponent:c}=Zc(s,s.bin,n);r[a]={...c,...r[a],...vv(n,s,o,n.config)}}return r},{});return K(i)?null:new dt(t,i)}static makeFromTransform(t,n,i){const{key:r,binComponent:s}=Zc(n,n.bin,i);return new dt(t,{[r]:s})}merge(t,n){for(const i of x(t.bins))i in this.bins?(n(t.bins[i].signal,this.bins[i].signal),this.bins[i].as=lt([...this.bins[i].as,...t.bins[i].as],W)):this.bins[i]=t.bins[i];for(const i of t.children)t.removeChild(i),i.parent=this;t.remove()}producedFields(){return new Set(ve(this.bins).map(t=>t.as).flat(2))}dependentFields(){return new Set(ve(this.bins).map(t=>t.field))}hash(){return`Bin ${W(this.bins)}`}assemble(){return ve(this.bins).flatMap(t=>{const n=[],[i,...r]=t.as,{extent:s,...o}=t.bin,a={type:"bin",field:Ue(t.field),as:i,signal:t.signal,...Br(s)?{extent:null}:{extent:s},...t.span?{span:{signal:`span(${t.span})`}}:{},...o};!s&&t.extentSignal&&(n.push({type:"extent",field:Ue(t.field),signal:t.extentSignal}),a.extent={signal:t.extentSignal}),n.push(a);for(const c of r)for(let l=0;l<2;l++)n.push({type:"formula",expr:E({field:i[l]},{expr:"datum"}),as:c[l]});return t.formula&&n.push({type:"formula",expr:t.formula,as:t.formulaAs}),n})}}function $v(e,t,n,i){const r=ae(i)?i.encoding[xt(t)]:void 0;if(Ce(n)&&ae(i)&&sf(n,r,i.markDef,i.config)){e.add(E(n,{})),e.add(E(n,{suffix:"end"}));const{mark:s,markDef:o,config:a}=i,c=Ht({fieldDef:n,markDef:o,config:a});zi(s)&&c!==.5&&ue(t)&&(e.add(E(n,{suffix:os})),e.add(E(n,{suffix:as}))),n.bin&&Ji(n,t)&&e.add(E(n,{binSuffix:"range"}))}else if(tu(t)){const s=eu(t);e.add(i.getName(s))}else e.add(E(n));return Pn(n)&&pm(n.scale?.range)&&e.add(n.scale.range.field),e}function wv(e,t){for(const n of x(t)){const i=t[n];for(const r of x(i))n in e?e[n][r]=new Set([...e[n][r]??[],...i[r]]):e[n]={[r]:i[r]}}}class Qe extends Y{clone(){return new Qe(null,new Set(this.dimensions),z(this.measures))}constructor(t,n,i){super(t),this.dimensions=n,this.measures=i}get groupBy(){return this.dimensions}static makeFromEncoding(t,n){let i=!1;n.forEachFieldDef(o=>{o.aggregate&&(i=!0)});const r={},s=new Set;return!i||(n.forEachFieldDef((o,a)=>{const{aggregate:c,field:l}=o;if(c)if(c==="count")r["*"]??(r["*"]={}),r["*"].count=new Set([E(o,{forAs:!0})]);else{if(At(c)||tn(c)){const u=At(c)?"argmin":"argmax",f=c[u];r[f]??(r[f]={}),r[f][u]=new Set([E({op:u,field:f},{forAs:!0})])}else r[l]??(r[l]={}),r[l][c]=new Set([E(o,{forAs:!0})]);Pt(a)&&n.scaleDomain(a)==="unaggregated"&&(r[l]??(r[l]={}),r[l].min=new Set([E({field:l,aggregate:"min"},{forAs:!0})]),r[l].max=new Set([E({field:l,aggregate:"max"},{forAs:!0})]))}else $v(s,a,o,n)}),s.size+x(r).length===0)?null:new Qe(t,s,r)}static makeFromTransform(t,n){const i=new Set,r={};for(const s of n.aggregate){const{op:o,field:a,as:c}=s;o&&(o==="count"?(r["*"]??(r["*"]={}),r["*"].count=new Set([c||E(s,{forAs:!0})])):(r[a]??(r[a]={}),r[a][o]=new Set([c||E(s,{forAs:!0})])))}for(const s of n.groupby??[])i.add(s);return i.size+x(r).length===0?null:new Qe(t,i,r)}merge(t){return ql(this.dimensions,t.dimensions)?(wv(this.measures,t.measures),!0):(Dh("different dimensions, cannot merge"),!1)}addDimensions(t){t.forEach(this.dimensions.add,this.dimensions)}dependentFields(){return new Set([...this.dimensions,...x(this.measures)])}producedFields(){const t=new Set;for(const n of x(this.measures))for(const i of x(this.measures[n])){const r=this.measures[n][i];r.size===0?t.add(`${i}_${n}`):r.forEach(t.add,t)}return t}hash(){return`Aggregate ${W({dimensions:this.dimensions,measures:this.measures})}`}assemble(){const t=[],n=[],i=[];for(const s of x(this.measures))for(const o of x(this.measures[s]))for(const a of this.measures[s][o])i.push(a),t.push(o),n.push(s==="*"?null:Ue(s));return{type:"aggregate",groupby:[...this.dimensions].map(Ue),ops:t,fields:n,as:i}}}class bi extends Y{constructor(t,n,i,r){super(t),this.model=n,this.name=i,this.data=r;for(const s of Me){const o=n.facet[s];if(o){const{bin:a,sort:c}=o;this[s]={name:n.getName(`${s}_domain`),fields:[E(o),...ee(a)?[E(o,{binSuffix:"end"})]:[]],...ut(c)?{sortField:c}:N(c)?{sortIndexField:li(o,s)}:{}}}}this.childModel=n.child}hash(){let t="Facet";for(const n of Me)this[n]&&(t+=` ${n.charAt(0)}:${W(this[n])}`);return t}get fields(){const t=[];for(const n of Me)this[n]?.fields&&t.push(...this[n].fields);return t}dependentFields(){const t=new Set(this.fields);for(const n of Me)this[n]&&(this[n].sortField&&t.add(this[n].sortField.field),this[n].sortIndexField&&t.add(this[n].sortIndexField));return t}producedFields(){return new Set}getSource(){return this.name}getChildIndependentFieldsWithStep(){const t={};for(const n of vt){const i=this.childModel.component.scales[n];if(i&&!i.merged){const r=i.get("type"),s=i.get("range");if(ye(r)&&nn(s)){const o=ds(this.childModel,n),a=Pa(o);a?t[n]=a:v(zo(n))}}}return t}assembleRowColumnHeaderData(t,n,i){const r={row:"y",column:"x",facet:void 0}[t],s=[],o=[],a=[];r&&i&&i[r]&&(n?(s.push(`distinct_${i[r]}`),o.push("max")):(s.push(i[r]),o.push("distinct")),a.push(`distinct_${i[r]}`));const{sortField:c,sortIndexField:l}=this[t];if(c){const{op:u=Yr,field:f}=c;s.push(f),o.push(u),a.push(E(c,{forAs:!0}))}else l&&(s.push(l),o.push("max"),a.push(l));return{name:this[t].name,source:n??this.data,transform:[{type:"aggregate",groupby:this[t].fields,...s.length?{fields:s,ops:o,as:a}:{}}]}}assembleFacetHeaderData(t){const{columns:n}=this.model.layout,{layoutHeaders:i}=this.model.component,r=[],s={};for(const c of ka){for(const l of Ta){const u=(i[c]&&i[c][l])??[];for(const f of u)if(f.axes?.length>0){s[c]=!0;break}}if(s[c]){const l=`length(data("${this.facet.name}"))`,u=c==="row"?n?{signal:`ceil(${l} / ${n})`}:1:n?{signal:`min(${l}, ${n})`}:{signal:l};r.push({name:`${this.facet.name}_${c}`,transform:[{type:"sequence",start:0,stop:u}]})}}const{row:o,column:a}=s;return(o||a)&&r.unshift(this.assembleRowColumnHeaderData("facet",null,t)),r}assemble(){const t=[];let n=null;const i=this.getChildIndependentFieldsWithStep(),{column:r,row:s,facet:o}=this;if(r&&s&&(i.x||i.y)){n=`cross_${this.column.name}_${this.row.name}`;const a=[].concat(i.x??[],i.y??[]),c=a.map(()=>"distinct");t.push({name:n,source:this.data,transform:[{type:"aggregate",groupby:this.fields,fields:a,ops:c}]})}for(const a of[Ft,Nt])this[a]&&t.push(this.assembleRowColumnHeaderData(a,n,i));if(o){const a=this.assembleFacetHeaderData(i);a&&t.push(...a)}return t}}function el(e){return e.startsWith("'")&&e.endsWith("'")||e.startsWith('"')&&e.endsWith('"')?e.slice(1,-1):e}function Cv(e,t){const n=No(e);if(t==="number")return`toNumber(${n})`;if(t==="boolean")return`toBoolean(${n})`;if(t==="string")return`toString(${n})`;if(t==="date")return`toDate(${n})`;if(t==="flatten")return n;if(t.startsWith("date:")){const i=el(t.slice(5,t.length));return`timeParse(${n},'${i}')`}else if(t.startsWith("utc:")){const i=el(t.slice(4,t.length));return`utcParse(${n},'${i}')`}else return v(Vg(t)),null}function Nv(e){const t={};return gr(e.filter,n=>{if(Pu(n)){let i=null;Uo(n)?i=Le(n.equal):Wo(n)?i=Le(n.lte):Bo(n)?i=Le(n.lt):Go(n)?i=Le(n.gt):Ho(n)?i=Le(n.gte):qo(n)?i=n.range[0]:Vo(n)&&(i=(n.oneOf??n.in)[0]),i&&(In(i)?t[n.field]="date":ie(i)?t[n.field]="number":I(i)&&(t[n.field]="string")),n.timeUnit&&(t[n.field]="date")}}),t}function Fv(e){const t={};function n(i){si(i)?t[i.field]="date":i.type==="quantitative"&&wg(i.aggregate)?t[i.field]="number":Jn(i.field)>1?i.field in t||(t[i.field]="flatten"):Pn(i)&&ut(i.sort)&&Jn(i.sort.field)>1&&(i.sort.field in t||(t[i.sort.field]="flatten"))}if((ae(e)||ze(e))&&e.forEachFieldDef((i,r)=>{if(Ce(i))n(i);else{const s=_n(r),o=e.fieldDef(s);n({...i,type:o.type})}}),ae(e)){const{mark:i,markDef:r,encoding:s}=e;if(rn(i)&&!e.encoding.order){const o=r.orient==="horizontal"?"y":"x",a=s[o];S(a)&&a.type==="quantitative"&&!(a.field in t)&&(t[a.field]="number")}}return t}function kv(e){const t={};if(ae(e)&&e.component.selection)for(const n of x(e.component.selection)){const i=e.component.selection[n];for(const r of i.project.items)!r.channel&&Jn(r.field)>1&&(t[r.field]="flatten")}return t}class $e extends Y{clone(){return new $e(null,z(this._parse))}constructor(t,n){super(t),this._parse=n}hash(){return`Parse ${W(this._parse)}`}static makeExplicit(t,n,i){let r={};const s=n.data;return!Bt(s)&&s?.format?.parse&&(r=s.format.parse),this.makeWithAncestors(t,r,{},i)}static makeWithAncestors(t,n,i,r){for(const a of x(i)){const c=r.getWithExplicit(a);c.value!==void 0&&(c.explicit||c.value===i[a]||c.value==="derived"||i[a]==="flatten"?delete i[a]:v(uc(a,i[a],c.value)))}for(const a of x(n)){const c=r.get(a);c!==void 0&&(c===n[a]?delete n[a]:v(uc(a,n[a],c)))}const s=new Dt(n,i);r.copyAll(s);const o={};for(const a of x(s.combine())){const c=s.get(a);c!==null&&(o[a]=c)}return x(o).length===0||r.parseNothing?null:new $e(t,o)}get parse(){return this._parse}merge(t){this._parse={...this._parse,...t.parse},t.remove()}assembleFormatParse(){const t={};for(const n of x(this._parse)){const i=this._parse[n];Jn(n)===1&&(t[n]=i)}return t}producedFields(){return new Set(x(this._parse))}dependentFields(){return new Set(x(this._parse))}assembleTransforms(t=!1){return x(this._parse).filter(n=>t?Jn(n)>1:!0).map(n=>{const i=Cv(n,this._parse[n]);return i?{type:"formula",expr:i,as:Fo(n)}:null}).filter(n=>n!==null)}}class Yt extends Y{clone(){return new Yt(null)}constructor(t){super(t)}dependentFields(){return new Set}producedFields(){return new Set([Ze])}hash(){return"Identifier"}assemble(){return{type:"identifier",as:Ze}}}class tr extends Y{clone(){return new tr(null,this.params)}constructor(t,n){super(t),this.params=n}dependentFields(){return new Set}producedFields(){}hash(){return`Graticule ${W(this.params)}`}assemble(){return{type:"graticule",...this.params===!0?{}:this.params}}}class nr extends Y{clone(){return new nr(null,this.params)}constructor(t,n){super(t),this.params=n}dependentFields(){return new Set}producedFields(){return new Set([this.params.as??"data"])}hash(){return`Hash ${W(this.params)}`}assemble(){return{type:"sequence",...this.params}}}class Tn extends Y{constructor(t){super(null),t??(t={name:"source"});let n;if(Bt(t)||(n=t.format?{...ke(t.format,["parse"])}:{}),Di(t))this._data={values:t.values};else if(oi(t)){if(this._data={url:t.url},!n.type){let i=/(?:\.([^.]+))?$/.exec(t.url)[1];G(["json","csv","tsv","dsv","topojson"],i)||(i="json"),n.type=i}}else Jf(t)?this._data={values:[{type:"Sphere"}]}:(Kf(t)||Bt(t))&&(this._data={});this._generator=Bt(t),t.name&&(this._name=t.name),n&&!K(n)&&(this._data.format=n)}dependentFields(){return new Set}producedFields(){}get data(){return this._data}hasName(){return!!this._name}get isGenerator(){return this._generator}get dataName(){return this._name}set dataName(t){this._name=t}set parent(t){throw new Error("Source nodes have to be roots.")}remove(){throw new Error("Source nodes are roots and cannot be removed.")}hash(){throw new Error("Cannot hash sources")}assemble(){return{name:this._name,...this._data,transform:[]}}}var tl=function(e,t,n,i,r){if(i==="m")throw new TypeError("Private method is not writable");if(i==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i==="a"?r.call(e,n):r?r.value=n:t.set(e,n),n},Tv=function(e,t,n,i){if(n==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?i:n==="a"?i.call(e):i?i.value:t.get(e)},Ti;function Ra(e){return e instanceof Tn||e instanceof tr||e instanceof nr}class Ia{constructor(){Ti.set(this,void 0),tl(this,Ti,!1,"f")}setModified(){tl(this,Ti,!0,"f")}get modifiedFlag(){return Tv(this,Ti,"f")}}Ti=new WeakMap;class zn extends Ia{getNodeDepths(t,n,i){i.set(t,n);for(const r of t.children)this.getNodeDepths(r,n+1,i);return i}optimize(t){const i=[...this.getNodeDepths(t,0,new Map).entries()].sort((r,s)=>s[1]-r[1]);for(const r of i)this.run(r[0]);return this.modifiedFlag}}class La extends Ia{optimize(t){this.run(t);for(const n of t.children)this.optimize(n);return this.modifiedFlag}}class Av extends La{mergeNodes(t,n){const i=n.shift();for(const r of n)t.removeChild(r),r.parent=i,r.remove()}run(t){const n=t.children.map(r=>r.hash()),i={};for(let r=0;r<n.length;r++)i[n[r]]===void 0?i[n[r]]=[t.children[r]]:i[n[r]].push(t.children[r]);for(const r of x(i))i[r].length>1&&(this.setModified(),this.mergeNodes(t,i[r]))}}class Ov extends La{constructor(t){super(),this.requiresSelectionId=t&&Fa(t)}run(t){t instanceof Yt&&(this.requiresSelectionId&&(Ra(t.parent)||t.parent instanceof Qe||t.parent instanceof $e)||(this.setModified(),t.remove()))}}class _v extends Ia{optimize(t){return this.run(t,new Set),this.modifiedFlag}run(t,n){let i=new Set;t instanceof ft&&(i=t.producedFields(),wo(i,n)&&(this.setModified(),t.removeFormulas(n),t.producedFields.length===0&&t.remove()));for(const r of t.children)this.run(r,new Set([...n,...i]))}}class Rv extends La{constructor(){super()}run(t){t instanceof we&&!t.isRequired()&&(this.setModified(),t.remove())}}class Iv extends zn{run(t){if(!Ra(t)&&!(t.numChildren()>1)){for(const n of t.children)if(n instanceof $e)if(t instanceof $e)this.setModified(),t.merge(n);else{if(Co(t.producedFields(),n.dependentFields()))continue;this.setModified(),n.swapWithParent()}}}}class Lv extends zn{run(t){const n=[...t.children],i=t.children.filter(r=>r instanceof $e);if(t.numChildren()>1&&i.length>=1){const r={},s=new Set;for(const o of i){const a=o.parse;for(const c of x(a))c in r?r[c]!==a[c]&&s.add(c):r[c]=a[c]}for(const o of s)delete r[o];if(!K(r)){this.setModified();const o=new $e(t,r);for(const a of n){if(a instanceof $e)for(const c of x(r))delete a.parse[c];t.removeChild(a),a.parent=o,a instanceof $e&&x(a.parse).length===0&&a.remove()}}}}}class Pv extends zn{run(t){t instanceof we||t.numChildren()>0||t instanceof bi||t instanceof Tn||(this.setModified(),t.remove())}}class zv extends zn{run(t){const n=t.children.filter(r=>r instanceof ft),i=n.pop();for(const r of n)this.setModified(),i.merge(r)}}class Dv extends zn{run(t){const n=t.children.filter(r=>r instanceof Qe),i={};for(const r of n){const s=W(r.groupBy);s in i||(i[s]=[]),i[s].push(r)}for(const r of x(i)){const s=i[r];if(s.length>1){const o=s.pop();for(const a of s)o.merge(a)&&(t.removeChild(a),a.parent=o,a.remove(),this.setModified())}}}}class jv extends zn{constructor(t){super(),this.model=t}run(t){const n=!(Ra(t)||t instanceof yi||t instanceof $e||t instanceof Yt),i=[],r=[];for(const s of t.children)s instanceof dt&&(n&&!Co(t.producedFields(),s.dependentFields())?i.push(s):r.push(s));if(i.length>0){const s=i.pop();for(const o of i)s.merge(o,this.model.renameSignal.bind(this.model));this.setModified(),t instanceof dt?t.merge(s,this.model.renameSignal.bind(this.model)):s.swapWithParent()}if(r.length>1){const s=r.pop();for(const o of r)s.merge(o,this.model.renameSignal.bind(this.model));this.setModified()}}}class Mv extends zn{run(t){const n=[...t.children];if(!vn(n,o=>o instanceof we)||t.numChildren()<=1)return;const r=[];let s;for(const o of n)if(o instanceof we){let a=o;for(;a.numChildren()===1;){const[c]=a.children;if(c instanceof we)a=c;else break}r.push(...a.children),s?(t.removeChild(o),o.parent=s.parent,s.parent.removeChild(s),s.parent=a,this.setModified()):s=a}else r.push(o);if(r.length){this.setModified();for(const o of r)o.parent.removeChild(o),o.parent=s}}}class Dn extends Y{clone(){return new Dn(null,z(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=lt(this.transform.groupby.concat(t),n=>n)}dependentFields(){const t=new Set;return this.transform.groupby&&this.transform.groupby.forEach(t.add,t),this.transform.joinaggregate.map(n=>n.field).filter(n=>n!==void 0).forEach(t.add,t),t}producedFields(){return new Set(this.transform.joinaggregate.map(this.getDefaultName))}getDefaultName(t){return t.as??E(t)}hash(){return`JoinAggregateTransform ${W(this.transform)}`}assemble(){const t=[],n=[],i=[];for(const s of this.transform.joinaggregate)n.push(s.op),i.push(this.getDefaultName(s)),t.push(s.field===void 0?null:s.field);const r=this.transform.groupby;return{type:"joinaggregate",as:i,ops:n,fields:t,...r!==void 0?{groupby:r}:{}}}}function Uv(e){return e.stack.stackBy.reduce((t,n)=>{const i=n.fieldDef,r=E(i);return r&&t.push(r),t},[])}function Bv(e){return N(e)&&e.every(t=>I(t))&&e.length>1}class Tt extends Y{clone(){return new Tt(null,z(this._stack))}constructor(t,n){super(t),this._stack=n}static makeFromTransform(t,n){const{stack:i,groupby:r,as:s,offset:o="zero"}=n,a=[],c=[];if(n.sort!==void 0)for(const f of n.sort)a.push(f.field),c.push(le(f.order,"ascending"));const l={field:a,order:c};let u;return Bv(s)?u=s:I(s)?u=[s,`${s}_end`]:u=[`${n.stack}_start`,`${n.stack}_end`],new Tt(t,{dimensionFieldDefs:[],stackField:i,groupby:r,offset:o,sort:l,facetby:[],as:u})}static makeFromEncoding(t,n){const i=n.stack,{encoding:r}=n;if(!i)return null;const{groupbyChannels:s,fieldChannel:o,offset:a,impute:c}=i,l=s.map(g=>{const p=r[g];return gt(p)}).filter(g=>!!g),u=Uv(n),f=n.encoding.order;let d;if(N(f)||S(f))d=mu(f);else{const g=of(f)?f.sort:o==="y"?"descending":"ascending";d=u.reduce((p,h)=>(p.field.includes(h)||(p.field.push(h),p.order.push(g)),p),{field:[],order:[]})}return new Tt(t,{dimensionFieldDefs:l,stackField:n.vgField(o),facetby:[],stackby:u,sort:d,offset:a,impute:c,as:[n.vgField(o,{suffix:"start",forAs:!0}),n.vgField(o,{suffix:"end",forAs:!0})]})}get stack(){return this._stack}addDimensions(t){this._stack.facetby.push(...t)}dependentFields(){const t=new Set;return t.add(this._stack.stackField),this.getGroupbyFields().forEach(t.add,t),this._stack.facetby.forEach(t.add,t),this._stack.sort.field.forEach(t.add,t),t}producedFields(){return new Set(this._stack.as)}hash(){return`Stack ${W(this._stack)}`}getGroupbyFields(){const{dimensionFieldDefs:t,impute:n,groupby:i}=this._stack;return t.length>0?t.map(r=>r.bin?n?[E(r,{binSuffix:"mid"})]:[E(r,{}),E(r,{binSuffix:"end"})]:[E(r)]).flat():i??[]}assemble(){const t=[],{facetby:n,dimensionFieldDefs:i,stackField:r,stackby:s,sort:o,offset:a,impute:c,as:l}=this._stack;if(c)for(const u of i){const{bandPosition:f=.5,bin:d}=u;if(d){const g=E(u,{expr:"datum"}),p=E(u,{expr:"datum",binSuffix:"end"});t.push({type:"formula",expr:`${f}*${g}+${1-f}*${p}`,as:E(u,{binSuffix:"mid",forAs:!0})})}t.push({type:"impute",field:r,groupby:[...s,...n],key:E(u,{binSuffix:"mid"}),method:"value",value:0})}return t.push({type:"stack",groupby:[...this.getGroupbyFields(),...n],field:r,sort:o,as:l,offset:a}),t}}class xi extends Y{clone(){return new xi(null,z(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=lt(this.transform.groupby.concat(t),n=>n)}dependentFields(){const t=new Set;return(this.transform.groupby??[]).forEach(t.add,t),(this.transform.sort??[]).forEach(n=>t.add(n.field)),this.transform.window.map(n=>n.field).filter(n=>n!==void 0).forEach(t.add,t),t}producedFields(){return new Set(this.transform.window.map(this.getDefaultName))}getDefaultName(t){return t.as??E(t)}hash(){return`WindowTransform ${W(this.transform)}`}assemble(){const t=[],n=[],i=[],r=[];for(const f of this.transform.window)n.push(f.op),i.push(this.getDefaultName(f)),r.push(f.param===void 0?null:f.param),t.push(f.field===void 0?null:f.field);const s=this.transform.frame,o=this.transform.groupby;if(s&&s[0]===null&&s[1]===null&&n.every(f=>Lo(f)))return{type:"joinaggregate",as:i,ops:n,fields:t,...o!==void 0?{groupby:o}:{}};const a=[],c=[];if(this.transform.sort!==void 0)for(const f of this.transform.sort)a.push(f.field),c.push(f.order??"ascending");const l={field:a,order:c},u=this.transform.ignorePeers;return{type:"window",params:r,as:i,ops:n,fields:t,sort:l,...u!==void 0?{ignorePeers:u}:{},...o!==void 0?{groupby:o}:{},...s!==void 0?{frame:s}:{}}}}function Wv(e){function t(n){if(!(n instanceof bi)){const i=n.clone();if(i instanceof we){const r=go+i.getSource();i.setSource(r),e.model.component.data.outputNodes[r]=i}else(i instanceof Qe||i instanceof Tt||i instanceof xi||i instanceof Dn)&&i.addDimensions(e.fields);for(const r of n.children.flatMap(t))r.parent=i;return[i]}return n.children.flatMap(t)}return t}function po(e){if(e instanceof bi)if(e.numChildren()===1&&!(e.children[0]instanceof we)){const t=e.children[0];(t instanceof Qe||t instanceof Tt||t instanceof xi||t instanceof Dn)&&t.addDimensions(e.fields),t.swapWithParent(),po(e)}else{const t=e.model.component.data.main;Hd(t);const n=Wv(e),i=e.children.map(n).flat();for(const r of i)r.parent=t}else e.children.map(po)}function Hd(e){if(e instanceof we&&e.type===Z.Main&&e.numChildren()===1){const t=e.children[0];t instanceof bi||(t.swapWithParent(),Hd(e))}}const go="scale_",ar=5;function ho(e){for(const t of e){for(const n of t.children)if(n.parent!==t)return!1;if(!ho(t.children))return!1}return!0}function He(e,t){let n=!1;for(const i of t)n=e.optimize(i)||n;return n}function nl(e,t,n){let i=e.sources,r=!1;return r=He(new Rv,i)||r,r=He(new Ov(t),i)||r,i=i.filter(s=>s.numChildren()>0),r=He(new Pv,i)||r,i=i.filter(s=>s.numChildren()>0),n||(r=He(new Iv,i)||r,r=He(new jv(t),i)||r,r=He(new _v,i)||r,r=He(new Lv,i)||r,r=He(new Dv,i)||r,r=He(new zv,i)||r,r=He(new Av,i)||r,r=He(new Mv,i)||r),e.sources=i,r}function Gv(e,t){ho(e.sources);let n=0,i=0;for(let r=0;r<ar&&nl(e,t,!0);r++)n++;e.sources.map(po);for(let r=0;r<ar&&nl(e,t,!1);r++)i++;ho(e.sources),Math.max(n,i)===ar&&v(`Maximum optimization runs(${ar}) reached.`)}class ge{constructor(t){Object.defineProperty(this,"signal",{enumerable:!0,get:t})}static fromName(t,n){return new ge(()=>t(n))}}function qd(e){ae(e)?Hv(e):qv(e)}function Hv(e){const t=e.component.scales;for(const n of x(t)){const i=Xv(e,n);if(t[n].setWithExplicit("domains",i),Kv(e,n),e.component.data.isFaceted){let s=e;for(;!ze(s)&&s.parent;)s=s.parent;if(s.component.resolve.scale[n]==="shared")for(const a of i.value)Ct(a)&&(a.data=go+a.data.replace(go,""))}}}function qv(e){for(const n of e.children)qd(n);const t=e.component.scales;for(const n of x(t)){let i,r=null;for(const s of e.children){const o=s.component.scales[n];if(o){i===void 0?i=o.getWithExplicit("domains"):i=qt(i,o.getWithExplicit("domains"),"domains","scale",mo);const a=o.get("selectionExtent");r&&a&&r.param!==a.param&&v(Wg),r=a}}t[n].setWithExplicit("domains",i),r&&t[n].set("selectionExtent",r,!0)}}function Vv(e,t,n,i){if(e==="unaggregated"){const{valid:r,reason:s}=il(t,n);if(!r){v(s);return}}else if(e===void 0&&i.useUnaggregatedDomain){const{valid:r}=il(t,n);if(r)return"unaggregated"}return e}function Xv(e,t){const n=e.getScaleComponent(t).get("type"),{encoding:i}=e,r=Vv(e.scaleDomain(t),e.typedFieldDef(t),n,e.config.scale);return r!==e.scaleDomain(t)&&(e.specifiedScales[t]={...e.specifiedScales[t],domain:r}),t==="x"&&fe(i.x2)?fe(i.x)?qt(jt(n,r,e,"x"),jt(n,r,e,"x2"),"domain","scale",mo):jt(n,r,e,"x2"):t==="y"&&fe(i.y2)?fe(i.y)?qt(jt(n,r,e,"y"),jt(n,r,e,"y2"),"domain","scale",mo):jt(n,r,e,"y2"):jt(n,r,e,t)}function Yv(e,t,n){return e.map(i=>({signal:`{data: ${es(i,{timeUnit:n,type:t})}}`}))}function Ls(e,t,n){const i=me(n)?.unit;return t==="temporal"||i?Yv(e,t,i):[e]}function jt(e,t,n,i){const{encoding:r,markDef:s,mark:o,config:a,stack:c}=n,l=fe(r[i]),{type:u}=l,f=l.timeUnit;if(dm(t)){const p=jt(e,void 0,n,i),h=Ls(t.unionWith,u,f);return ct([...h,...p.value])}else{if(k(t))return ct([t]);if(t&&t!=="unaggregated"&&!Wu(t))return ct(Ls(t,u,f))}if(c&&i===c.fieldChannel){if(c.offset==="normalize")return Ie([[0,1]]);const p=n.requestDataName(Z.Main);return Ie([{data:p,field:n.vgField(i,{suffix:"start"})},{data:p,field:n.vgField(i,{suffix:"end"})}])}const d=Pt(i)&&S(l)?Qv(n,i,e):void 0;if(St(l)){const p=Ls([l.datum],u,f);return Ie(p)}const g=l;if(t==="unaggregated"){const p=n.requestDataName(Z.Main),{field:h}=l;return Ie([{data:p,field:E({field:h,aggregate:"min"})},{data:p,field:E({field:h,aggregate:"max"})}])}else if(ee(g.bin)){if(ye(e))return Ie(e==="bin-ordinal"?[]:[{data:Li(d)?n.requestDataName(Z.Main):n.requestDataName(Z.Raw),field:n.vgField(i,Ji(g,i)?{binSuffix:"range"}:{}),sort:d===!0||!X(d)?{field:n.vgField(i,{}),op:"min"}:d}]);{const{bin:p}=g;if(ee(p)){const h=_a(n,g.field,p);return Ie([new ge(()=>{const m=n.getSignalName(h);return`[${m}.start, ${m}.stop]`})])}else return Ie([{data:n.requestDataName(Z.Main),field:n.vgField(i,{})}])}}else if(g.timeUnit&&G(["time","utc"],e)){const p=r[xt(i)];if(sf(g,p,s,a)){const h=n.requestDataName(Z.Main),m=Ht({fieldDef:g,fieldDef2:p,markDef:s,config:a}),y=zi(o)&&m!==.5&&ue(i);return Ie([{data:h,field:n.vgField(i,y?{suffix:os}:{})},{data:h,field:n.vgField(i,{suffix:y?as:"end"})}])}}return Ie(d?[{data:Li(d)?n.requestDataName(Z.Main):n.requestDataName(Z.Raw),field:n.vgField(i),sort:d}]:[{data:n.requestDataName(Z.Main),field:n.vgField(i)}])}function Ps(e,t){const{op:n,field:i,order:r}=e;return{op:n??(t?"sum":Yr),...i?{field:Ue(i)}:{},...r?{order:r}:{}}}function Kv(e,t){const n=e.component.scales[t],i=e.specifiedScales[t].domain,r=e.fieldDef(t)?.bin,s=Wu(i)&&i,o=Rn(r)&&Br(r.extent)&&r.extent;(s||o)&&n.set("selectionExtent",s??o,!0)}function Qv(e,t,n){if(!ye(n))return;const i=e.fieldDef(t),r=i.sort;if(nf(r))return{op:"min",field:li(i,t),order:"ascending"};const{stack:s}=e,o=s?new Set([...s.groupbyFields,...s.stackBy.map(a=>a.fieldDef.field)]):void 0;if(ut(r)){const a=s&&!o.has(r.field);return Ps(r,a)}else if(tf(r)){const{encoding:a,order:c}=r,l=e.fieldDef(a),{aggregate:u,field:f}=l,d=s&&!o.has(f);if(At(u)||tn(u))return Ps({field:E(l),order:c},d);if(Lo(u)||!u)return Ps({op:u,field:f,order:c},d)}else{if(r==="descending")return{op:"min",field:e.vgField(t),order:"descending"};if(G(["ascending",void 0],r))return!0}}function il(e,t){const{aggregate:n,type:i}=e;return n?I(n)&&!Ng.has(n)?{valid:!1,reason:hh(n)}:i==="quantitative"&&t==="log"?{valid:!1,reason:mh(e)}:{valid:!0}:{valid:!1,reason:gh(e)}}function mo(e,t,n,i){return e.explicit&&t.explicit&&v(Sh(n,i,e.value,t.value)),{explicit:e.explicit,value:[...e.value,...t.value]}}function Jv(e){const t=lt(e.map(o=>{if(Ct(o)){const{sort:a,...c}=o;return c}return o}),W),n=lt(e.map(o=>{if(Ct(o)){const a=o.sort;return a!==void 0&&!Li(a)&&("op"in a&&a.op==="count"&&delete a.field,a.order==="ascending"&&delete a.order),a}}).filter(o=>o!==void 0),W);if(t.length===0)return;if(t.length===1){const o=e[0];if(Ct(o)&&n.length>0){let a=n[0];if(n.length>1){v(dc);const c=n.filter(l=>X(l)&&"op"in l&&l.op!=="min");n.every(l=>X(l)&&"op"in l)&&c.length===1?a=c[0]:a=!0}else if(X(a)&&"field"in a){const c=a.field;o.field===c&&(a=a.order?{order:a.order}:!0)}return{...o,sort:a}}return o}const i=lt(n.map(o=>Li(o)||!("op"in o)||I(o.op)&&o.op in Eg?o:(v($h(o)),!0)),W);let r;i.length===1?r=i[0]:i.length>1&&(v(dc),r=!0);const s=lt(e.map(o=>Ct(o)?o.data:null),o=>o);return s.length===1&&s[0]!==null?{data:s[0],fields:t.map(a=>a.field),...r?{sort:r}:{}}:{fields:t,...r?{sort:r}:{}}}function Pa(e){if(Ct(e)&&I(e.field))return e.field;if(Fg(e)){let t;for(const n of e.fields)if(Ct(n)&&I(n.field)){if(!t)t=n.field;else if(t!==n.field)return v(wh),t}return v(Ch),t}else if(kg(e)){v(Nh);const t=e.fields[0];return I(t)?t:void 0}}function ds(e,t){const i=e.component.scales[t].get("domains").map(r=>(Ct(r)&&(r.data=e.lookupDataSource(r.data)),r));return Jv(i)}function Vd(e){return vi(e)||za(e)?e.children.reduce((t,n)=>t.concat(Vd(n)),rl(e)):rl(e)}function rl(e){return x(e.component.scales).reduce((t,n)=>{const i=e.component.scales[n];if(i.merged)return t;const r=i.combine(),{name:s,type:o,selectionExtent:a,domains:c,range:l,reverse:u,...f}=r,d=Zv(r.range,s,n,e),g=ds(e,n),p=a?Vb(e,a,i,g):null;return t.push({name:s,type:o,...g?{domain:g}:{},...p?{domainRaw:p}:{},range:d,...u!==void 0?{reverse:u}:{},...f}),t},[])}function Zv(e,t,n,i){if(ue(n)){if(nn(e))return{step:{signal:`${t}_step`}}}else if(X(e)&&Ct(e))return{...e,data:i.lookupDataSource(e.data)};return e}class Xd extends Dt{constructor(t,n){super({},{name:t}),this.merged=!1,this.setWithExplicit("type",n)}domainDefinitelyIncludesZero(){return this.get("zero")!==!1?!0:vn(this.get("domains"),t=>N(t)&&t.length===2&&ie(t[0])&&t[0]<=0&&ie(t[1])&&t[1]>=0)}}const eS=["range","scheme"];function tS(e){const t=e.component.scales;for(const n of Ur){const i=t[n];if(!i)continue;const r=nS(n,e);i.setWithExplicit("range",r)}}function sl(e,t){const n=e.fieldDef(t);if(n?.bin){const{bin:i,field:r}=n,s=_e(t),o=e.getName(s);if(X(i)&&i.binned&&i.step!==void 0)return new ge(()=>{const a=e.scaleName(t),c=`(domain("${a}")[1] - domain("${a}")[0]) / ${i.step}`;return`${e.getSignalName(o)} / (${c})`});if(ee(i)){const a=_a(e,r,i);return new ge(()=>{const c=e.getSignalName(a),l=`(${c}.stop - ${c}.start) / ${c}.step`;return`${e.getSignalName(o)} / (${l})`})}}}function nS(e,t){const n=t.specifiedScales[e],{size:i}=t,s=t.getScaleComponent(e).get("type");for(const f of eS)if(n[f]!==void 0){const d=Zs(s,f),g=Gu(e,f);if(!d)v($u(s,f,e));else if(g)v(g);else switch(f){case"range":{const p=n.range;if(N(p)){if(ue(e))return ct(p.map(h=>{if(h==="width"||h==="height"){const m=t.getName(h),y=t.getSignalName.bind(t);return ge.fromName(y,m)}return h}))}else if(X(p))return ct({data:t.requestDataName(Z.Main),field:p.field,sort:{op:"min",field:t.vgField(e)}});return ct(p)}case"scheme":return ct(iS(n[f]))}}const o=e===se||e==="xOffset"?"width":"height",a=i[o];if(ht(a)){if(ue(e))if(ye(s)){const f=Kd(a,t,e);if(f)return ct({step:f})}else v(wu(o));else if(qi(e)){const f=e===Kt?"x":"y";if(t.getScaleComponent(f).get("type")==="band"){const p=Qd(a,s);if(p)return ct(p)}}}const{rangeMin:c,rangeMax:l}=n,u=rS(e,t);return(c!==void 0||l!==void 0)&&Zs(s,"rangeMin")&&N(u)&&u.length===2?ct([c??u[0],l??u[1]]):Ie(u)}function iS(e){return fm(e)?{scheme:e.name,...ke(e,["name"])}:{scheme:e}}function Yd(e,t,n,{center:i}={}){const r=_e(e),s=t.getName(r),o=t.getSignalName.bind(t);return e===be&&De(n)?i?[ge.fromName(a=>`${o(a)}/2`,s),ge.fromName(a=>`-${o(a)}/2`,s)]:[ge.fromName(o,s),0]:i?[ge.fromName(a=>`-${o(a)}/2`,s),ge.fromName(a=>`${o(a)}/2`,s)]:[0,ge.fromName(o,s)]}function rS(e,t){const{size:n,config:i,mark:r,encoding:s}=t,{type:o}=fe(s[e]),c=t.getScaleComponent(e).get("type"),{domain:l,domainMid:u}=t.specifiedScales[e];switch(e){case se:case be:{if(G(["point","band"],c)){const f=Jd(e,n,i.view);if(ht(f))return{step:Kd(f,t,e)}}return Yd(e,t,c)}case Kt:case gi:return sS(e,t,c);case It:{const f=t.component.scales[e].get("zero"),d=Zd(r,f,i),g=cS(r,n,t,i);return ni(c)?aS(d,g,oS(c,i,l,e)):[d,g]}case We:return[0,Math.PI*2];case On:return[0,360];case tt:return[0,new ge(()=>{const f=t.getSignalName(ze(t.parent)?"child_width":"width"),d=t.getSignalName(ze(t.parent)?"child_height":"height");return`min(${f},${d})/2`})];case Zt:return[i.scale.minStrokeWidth,i.scale.maxStrokeWidth];case en:return[[1,0],[4,2],[2,1],[1,1],[1,2,4,2]];case Oe:return"symbol";case Ae:case yt:case bt:return c==="ordinal"?o==="nominal"?"category":"ordinal":u!==void 0?"diverging":r==="rect"||r==="geoshape"?"heatmap":"ramp";case Lt:case Qt:case Jt:return[i.scale.minOpacity,i.scale.maxOpacity]}}function Kd(e,t,n){const{encoding:i}=t,r=t.getScaleComponent(n),s=Oo(n),o=i[s];if(Pf({step:e,offsetIsDiscrete:D(o)&&Du(o.type)})==="offset"&&yf(i,s)){const c=t.getScaleComponent(s);let u=`domain('${t.scaleName(s)}').length`;if(c.get("type")==="band"){const d=c.get("paddingInner")??c.get("padding")??0,g=c.get("paddingOuter")??c.get("padding")??0;u=`bandspace(${u}, ${d}, ${g})`}const f=r.get("paddingInner")??r.get("padding");return{signal:`${e.step} * ${u} / (1-${_g(f)})`}}else return e.step}function Qd(e,t){if(Pf({step:e,offsetIsDiscrete:ye(t)})==="offset")return{step:e.step}}function sS(e,t,n){const i=e===Kt?"x":"y",r=t.getScaleComponent(i);if(!r)return Yd(i,t,n,{center:!0});const s=r.get("type"),o=t.scaleName(i),{markDef:a,config:c}=t;if(s==="band"){const l=Jd(i,t.size,t.config.view);if(ht(l)){const u=Qd(l,n);if(u)return u}return[0,{signal:`bandwidth('${o}')`}]}else{const l=t.encoding[i];if(S(l)&&l.timeUnit){const u=Iu(l.timeUnit,p=>`scale('${o}', ${p})`),f=t.config.scale.bandWithNestedOffsetPaddingInner,d=Ht({fieldDef:l,markDef:a,config:c})-.5,g=d!==0?` + ${d}`:"";if(f){const p=k(f)?`${f.signal}/2`+g:`${f/2+d}`,h=k(f)?`(1 - ${f.signal}/2)`+g:`${1-f/2+d}`;return[{signal:`${p} * (${u})`},{signal:`${h} * (${u})`}]}return[0,{signal:u}]}return Gl(`Cannot use ${e} scale if ${i} scale is not discrete.`)}}function Jd(e,t,n){const i=e===se?"width":"height",r=t[i];return r||Nr(n,i)}function oS(e,t,n,i){switch(e){case"quantile":return t.scale.quantileCount;case"quantize":return t.scale.quantizeCount;case"threshold":return n!==void 0&&N(n)?n.length+1:(v(Lh(i)),3)}}function aS(e,t,n){const i=()=>{const r=Xe(t),s=Xe(e),o=`(${r} - ${s}) / (${n} - 1)`;return`sequence(${s}, ${r} + ${o}, ${o})`};return k(t)?new ge(i):{signal:i()}}function Zd(e,t,n){if(t)return k(t)?{signal:`${t.signal} ? 0 : ${Zd(e,!1,n)}`}:0;switch(e){case"bar":case"tick":return n.scale.minBandSize;case"line":case"trail":case"rule":return n.scale.minStrokeWidth;case"text":return n.scale.minFontSize;case"point":case"square":case"circle":return n.scale.minSize}throw new Error(Wr("size",e))}const ol=.95;function cS(e,t,n,i){const r={x:sl(n,"x"),y:sl(n,"y")};switch(e){case"bar":case"tick":{if(i.scale.maxBandSize!==void 0)return i.scale.maxBandSize;const s=al(t,r,i.view);return ie(s)?s-1:new ge(()=>`${s.signal} - 1`)}case"line":case"trail":case"rule":return i.scale.maxStrokeWidth;case"text":return i.scale.maxFontSize;case"point":case"square":case"circle":{if(i.scale.maxSize)return i.scale.maxSize;const s=al(t,r,i.view);return ie(s)?Math.pow(ol*s,2):new ge(()=>`pow(${ol} * ${s.signal}, 2)`)}}throw new Error(Wr("size",e))}function al(e,t,n){const i=ht(e.width)?e.width.step:Cr(n,"width"),r=ht(e.height)?e.height.step:Cr(n,"height");return t.x||t.y?new ge(()=>`min(${[t.x?t.x.signal:i,t.y?t.y.signal:r].join(", ")})`):Math.min(i,r)}function ep(e,t){ae(e)?lS(e,t):np(e,t)}function lS(e,t){const n=e.component.scales,{config:i,encoding:r,markDef:s,specifiedScales:o}=e;for(const a of x(n)){const c=o[a],l=n[a],u=e.getScaleComponent(a),f=fe(r[a]),d=c[t],g=u.get("type"),p=u.get("padding"),h=u.get("paddingInner"),m=Zs(g,t),y=Gu(a,t);if(d!==void 0&&(m?y&&v(y):v($u(g,t,a))),m&&y===void 0)if(d!==void 0){const b=f.timeUnit,C=f.type;switch(t){case"domainMax":case"domainMin":In(c[t])||C==="temporal"||b?l.set(t,{signal:es(c[t],{type:C,timeUnit:b})},!0):l.set(t,c[t],!0);break;default:l.copyKeyFromObject(t,c)}}else{const b=t in cl?cl[t]({model:e,channel:a,fieldOrDatumDef:f,scaleType:g,scalePadding:p,scalePaddingInner:h,domain:c.domain,domainMin:c.domainMin,domainMax:c.domainMax,markDef:s,config:i,hasNestedOffsetScale:bf(r,a),hasSecondaryRangeChannel:!!r[xt(a)]}):i.scale[t];b!==void 0&&l.set(t,b,!1)}}}const cl={bins:({model:e,fieldOrDatumDef:t})=>S(t)?uS(e,t):void 0,interpolate:({channel:e,fieldOrDatumDef:t})=>fS(e,t.type),nice:({scaleType:e,channel:t,domain:n,domainMin:i,domainMax:r,fieldOrDatumDef:s})=>dS(e,t,n,i,r,s),padding:({channel:e,scaleType:t,fieldOrDatumDef:n,markDef:i,config:r})=>pS(e,t,r.scale,n,i,r.bar),paddingInner:({scalePadding:e,channel:t,markDef:n,scaleType:i,config:r,hasNestedOffsetScale:s})=>gS(e,t,n.type,i,r.scale,s),paddingOuter:({scalePadding:e,channel:t,scaleType:n,scalePaddingInner:i,config:r,hasNestedOffsetScale:s})=>hS(e,t,n,i,r.scale,s),reverse:({fieldOrDatumDef:e,scaleType:t,channel:n,config:i})=>{const r=S(e)?e.sort:void 0;return mS(t,r,n,i.scale)},zero:({channel:e,fieldOrDatumDef:t,domain:n,markDef:i,scaleType:r,config:s,hasSecondaryRangeChannel:o})=>yS(e,t,n,i,r,s.scale,o)};function tp(e){ae(e)?tS(e):np(e,"range")}function np(e,t){const n=e.component.scales;for(const i of e.children)t==="range"?tp(i):ep(i,t);for(const i of x(n)){let r;for(const s of e.children){const o=s.component.scales[i];if(o){const a=o.getWithExplicit(t);r=qt(r,a,t,"scale",Yf((c,l)=>{switch(t){case"range":return c.step&&l.step?c.step-l.step:0}return 0}))}}n[i].setWithExplicit(t,r)}}function uS(e,t){const n=t.bin;if(ee(n)){const i=_a(e,t.field,n);return new ge(()=>e.getSignalName(i))}else if(xe(n)&&Rn(n)&&n.step!==void 0)return{step:n.step}}function fS(e,t){if(G([Ae,yt,bt],e)&&t!=="nominal")return"hcl"}function dS(e,t,n,i,r,s){if(!(gt(s)?.bin||N(n)||r!=null||i!=null||G([Te.TIME,Te.UTC],e)))return ue(t)?!0:void 0}function pS(e,t,n,i,r,s){if(ue(e)){if(Ye(t)){if(n.continuousPadding!==void 0)return n.continuousPadding;const{type:o,orient:a}=r;if(o==="bar"&&!(S(i)&&(i.bin||i.timeUnit))&&(a==="vertical"&&e==="x"||a==="horizontal"&&e==="y"))return s.continuousBandSize}if(t===Te.POINT)return n.pointPadding}}function gS(e,t,n,i,r,s=!1){if(e===void 0){if(ue(t)){const{bandPaddingInner:o,barBandPaddingInner:a,rectBandPaddingInner:c,bandWithNestedOffsetPaddingInner:l}=r;return s?l:le(o,n==="bar"?a:c)}else if(qi(t)&&i===Te.BAND)return r.offsetBandPaddingInner}}function hS(e,t,n,i,r,s=!1){if(e===void 0){if(ue(t)){const{bandPaddingOuter:o,bandWithNestedOffsetPaddingOuter:a}=r;if(s)return a;if(n===Te.BAND)return le(o,k(i)?{signal:`${i.signal}/2`}:i/2)}else if(qi(t)){if(n===Te.POINT)return .5;if(n===Te.BAND)return r.offsetBandPaddingOuter}}}function mS(e,t,n,i){if(n==="x"&&i.xReverse!==void 0)return De(e)&&t==="descending"?k(i.xReverse)?{signal:`!${i.xReverse.signal}`}:!i.xReverse:i.xReverse;if(De(e)&&t==="descending")return!0}function yS(e,t,n,i,r,s,o){if(!!n&&n!=="unaggregated"&&De(r)){if(N(n)){const c=n[0],l=n[n.length-1];if(ie(c)&&c<=0&&ie(l)&&l>=0)return!0}return!1}if(e==="size"&&t.type==="quantitative"&&!ni(r))return!0;if(!(S(t)&&t.bin)&&G([...vt,...gg],e)){const{orient:c,type:l}=i;return G(["bar","area","line","trail"],l)&&(c==="horizontal"&&e==="y"||c==="vertical"&&e==="x")?!1:G(["bar","area"],l)&&!o?!0:s?.zero}return!1}function bS(e,t,n,i,r=!1){const s=xS(t,n,i,r),{type:o}=e;return Pt(t)?o!==void 0?bm(t,o)?S(n)&&!ym(o,n.type)?(v(xh(o,s)),s):o:(v(bh(t,o,s)),s):s:null}function xS(e,t,n,i){switch(t.type){case"nominal":case"ordinal":{if(qn(e)||Fs(e)==="discrete")return e==="shape"&&t.type==="ordinal"&&v(ks(e,"ordinal")),"ordinal";if(ue(e)||qi(e)){if(G(["rect","bar","image","rule"],n.type)||i)return"band"}else if(n.type==="arc"&&e in Io)return"band";const r=n[_e(e)];return Cn(r)||ri(t)&&t.axis?.tickBand?"band":"point"}case"temporal":return qn(e)?"time":Fs(e)==="discrete"?(v(ks(e,"temporal")),"ordinal"):S(t)&&t.timeUnit&&me(t.timeUnit).utc?"utc":"time";case"quantitative":return qn(e)?S(t)&&ee(t.bin)?"bin-ordinal":"linear":Fs(e)==="discrete"?(v(ks(e,"quantitative")),"ordinal"):"linear";case"geojson":return}throw new Error(Su(t.type))}function vS(e,{ignoreRange:t}={}){ip(e),qd(e);for(const n of mm)ep(e,n);t||tp(e)}function ip(e){ae(e)?e.component.scales=SS(e):e.component.scales=$S(e)}function SS(e){const{encoding:t,mark:n,markDef:i}=e,r={};for(const s of Ur){const o=fe(t[s]);if(o&&n===Vu&&s===Oe&&o.type===hi)continue;let a=o&&o.scale;if(o&&a!==null&&a!==!1){a??(a={});const c=bf(t,s),l=bS(a,s,o,i,c);r[s]=new Xd(e.scaleName(`${s}`,!0),{value:l,explicit:a.type===l})}}return r}const ES=Yf((e,t)=>gc(e)-gc(t));function $S(e){var t;const n=e.component.scales={},i={},r=e.component.resolve;for(const s of e.children){ip(s);for(const o of x(s.component.scales))if((t=r.scale)[o]??(t[o]=Rd(o,e)),r.scale[o]==="shared"){const a=i[o],c=s.component.scales[o].getWithExplicit("type");a?om(a.value,c.value)?i[o]=qt(a,c,"type","scale",ES):(r.scale[o]="independent",delete i[o]):i[o]=c}}for(const s of x(i)){const o=e.scaleName(s,!0),a=i[s];n[s]=new Xd(o,a);for(const c of e.children){const l=c.component.scales[s];l&&(c.renameScale(l.get("name"),o),l.merged=!0)}}return n}class zs{constructor(){this.nameMap={}}rename(t,n){this.nameMap[t]=n}has(t){return this.nameMap[t]!==void 0}get(t){for(;this.nameMap[t]&&t!==this.nameMap[t];)t=this.nameMap[t];return t}}function ae(e){return e?.type==="unit"}function ze(e){return e?.type==="facet"}function za(e){return e?.type==="concat"}function vi(e){return e?.type==="layer"}class Da{constructor(t,n,i,r,s,o,a){this.type=n,this.parent=i,this.config=s,this.correctDataNames=c=>(c.from?.data&&(c.from.data=this.lookupDataSource(c.from.data)),c.from?.facet?.data&&(c.from.facet.data=this.lookupDataSource(c.from.facet.data)),c),this.parent=i,this.config=s,this.view=Fe(a),this.name=t.name??r,this.title=Mt(t.title)?{text:t.title}:t.title?Fe(t.title):void 0,this.scaleNameMap=i?i.scaleNameMap:new zs,this.projectionNameMap=i?i.projectionNameMap:new zs,this.signalNameMap=i?i.signalNameMap:new zs,this.data=t.data,this.description=t.description,this.transforms=Ab(t.transform??[]),this.layout=n==="layer"||n==="unit"?{}:Iy(t,n,s),this.component={data:{sources:i?i.component.data.sources:[],outputNodes:i?i.component.data.outputNodes:{},outputNodeRefCounts:i?i.component.data.outputNodeRefCounts:{},isFaceted:Kr(t)||i?.component.data.isFaceted&&t.data===void 0},layoutSize:new Dt,layoutHeaders:{row:{},column:{},facet:{}},mark:null,resolve:{scale:{},axis:{},legend:{},...o?z(o):{}},selection:null,scales:null,projection:null,axes:{},legends:{}}}get width(){return this.getSizeSignalRef("width")}get height(){return this.getSizeSignalRef("height")}parse(){this.parseScale(),this.parseLayoutSize(),this.renameTopLevelLayoutSizeSignal(),this.parseSelections(),this.parseProjection(),this.parseData(),this.parseAxesAndHeaders(),this.parseLegends(),this.parseMarkGroup()}parseScale(){vS(this)}parseProjection(){Wd(this)}renameTopLevelLayoutSizeSignal(){this.getName("width")!=="width"&&this.renameSignal(this.getName("width"),"width"),this.getName("height")!=="height"&&this.renameSignal(this.getName("height"),"height")}parseLegends(){Dd(this)}assembleEncodeFromView(t){const{style:n,...i}=t,r={};for(const s of x(i)){const o=i[s];o!==void 0&&(r[s]=ne(o))}return r}assembleGroupEncodeEntry(t){let n={};return this.view&&(n=this.assembleEncodeFromView(this.view)),!t&&(this.description&&(n.description=ne(this.description)),this.type==="unit"||this.type==="layer")?{width:this.getSizeSignalRef("width"),height:this.getSizeSignalRef("height"),...n}:K(n)?void 0:n}assembleLayout(){if(!this.layout)return;const{spacing:t,...n}=this.layout,{component:i,config:r}=this,s=Gx(i.layoutHeaders,r);return{padding:t,...this.assembleDefaultLayout(),...n,...s?{titleBand:s}:{}}}assembleDefaultLayout(){return{}}assembleHeaderMarks(){const{layoutHeaders:t}=this.component;let n=[];for(const i of Me)t[i].title&&n.push(Dx(this,i));for(const i of ka)n=n.concat(jx(this,i));return n}assembleAxes(){return Cx(this.component.axes,this.config)}assembleLegends(){return Md(this)}assembleProjections(){return pv(this)}assembleTitle(){const{encoding:t,...n}=this.title??{},i={...du(this.config.title).nonMarkTitleProperties,...n,...t?{encode:{update:t}}:{}};if(i.text)return G(["unit","layer"],this.type)?G(["middle",void 0],i.anchor)&&(i.frame??(i.frame="group")):i.anchor??(i.anchor="start"),K(i)?void 0:i}assembleGroup(t=[]){const n={};t=t.concat(this.assembleSignals()),t.length>0&&(n.signals=t);const i=this.assembleLayout();i&&(n.layout=i),n.marks=[].concat(this.assembleHeaderMarks(),this.assembleMarks());const r=!this.parent||ze(this.parent)?Vd(this):[];r.length>0&&(n.scales=r);const s=this.assembleAxes();s.length>0&&(n.axes=s);const o=this.assembleLegends();return o.length>0&&(n.legends=o),n}getName(t){return re((this.name?`${this.name}_`:"")+t)}getDataName(t){return this.getName(Z[t].toLowerCase())}requestDataName(t){const n=this.getDataName(t),i=this.component.data.outputNodeRefCounts;return i[n]=(i[n]||0)+1,n}getSizeSignalRef(t){if(ze(this.parent)){const n=Od(t),i=Mr(n),r=this.component.scales[i];if(r&&!r.merged){const s=r.get("type"),o=r.get("range");if(ye(s)&&nn(o)){const a=r.get("name"),c=ds(this,i),l=Pa(c);if(l){const u=E({aggregate:"distinct",field:l},{expr:"datum"});return{signal:Ad(a,r,u)}}else return v(zo(i)),null}}}return{signal:this.signalNameMap.get(this.getName(t))}}lookupDataSource(t){const n=this.component.data.outputNodes[t];return n?n.getSource():t}getSignalName(t){return this.signalNameMap.get(t)}renameSignal(t,n){this.signalNameMap.rename(t,n)}renameScale(t,n){this.scaleNameMap.rename(t,n)}renameProjection(t,n){this.projectionNameMap.rename(t,n)}scaleName(t,n){if(n)return this.getName(t);if(iu(t)&&Pt(t)&&this.component.scales[t]||this.scaleNameMap.has(this.getName(t)))return this.scaleNameMap.get(this.getName(t))}projectionName(t){if(t)return this.getName("projection");if(this.component.projection&&!this.component.projection.merged||this.projectionNameMap.has(this.getName("projection")))return this.projectionNameMap.get(this.getName("projection"))}getScaleComponent(t){if(!this.component.scales)throw new Error("getScaleComponent cannot be called before parseScale(). Make sure you have called parseScale or use parseUnitModelWithScale().");const n=this.component.scales[t];return n&&!n.merged?n:this.parent?this.parent.getScaleComponent(t):void 0}getSelectionComponent(t,n){let i=this.component.selection[t];if(!i&&this.parent&&(i=this.parent.getSelectionComponent(t,n)),!i)throw new Error(zg(n));return i}hasAxisOrientSignalRef(){return this.component.axes.x?.some(t=>t.hasOrientSignalRef())||this.component.axes.y?.some(t=>t.hasOrientSignalRef())}}class rp extends Da{vgField(t,n={}){const i=this.fieldDef(t);if(i)return E(i,n)}reduceFieldDef(t,n){return ly(this.getMapping(),(i,r,s)=>{const o=gt(r);return o?t(i,o,s):i},n)}forEachFieldDef(t,n){ua(this.getMapping(),(i,r)=>{const s=gt(i);s&&t(s,r)},n)}}class ps extends Y{clone(){return new ps(null,z(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=z(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"value",i[1]??"density"]}dependentFields(){return new Set([this.transform.density,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`DensityTransform ${W(this.transform)}`}assemble(){const{density:t,...n}=this.transform,i={type:"kde",field:t,...n};return this.transform.groupby&&(i.resolve="shared"),i}}class gs extends Y{clone(){return new gs(null,z(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=z(n)}dependentFields(){return new Set([this.transform.extent])}producedFields(){return new Set([])}hash(){return`ExtentTransform ${W(this.transform)}`}assemble(){const{extent:t,param:n}=this.transform;return{type:"extent",field:t,signal:n}}}class ji extends Y{clone(){return new ji(null,{...this.filter})}constructor(t,n){super(t),this.filter=n}static make(t,n){const{config:i,mark:r,markDef:s}=n;if(V("invalid",s,i)!=="filter")return null;const a=n.reduceFieldDef((c,l,u)=>{const f=Pt(u)&&n.getScaleComponent(u);if(f){const d=f.get("type");De(d)&&l.aggregate!=="count"&&!rn(r)&&(c[l.field]=l)}return c},{});return x(a).length?new ji(t,a):null}dependentFields(){return new Set(x(this.filter))}producedFields(){return new Set}hash(){return`FilterInvalid ${W(this.filter)}`}assemble(){const t=x(this.filter).reduce((n,i)=>{const r=this.filter[i],s=E(r,{expr:"datum"});return r!==null&&(r.type==="temporal"?n.push(`(isDate(${s}) || (isValid(${s}) && isFinite(+${s})))`):r.type==="quantitative"&&(n.push(`isValid(${s})`),n.push(`isFinite(+${s})`))),n},[]);return t.length>0?{type:"filter",expr:t.join(" && ")}:null}}class hs extends Y{clone(){return new hs(this.parent,z(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=z(n);const{flatten:i,as:r=[]}=this.transform;this.transform.as=i.map((s,o)=>r[o]??s)}dependentFields(){return new Set(this.transform.flatten)}producedFields(){return new Set(this.transform.as)}hash(){return`FlattenTransform ${W(this.transform)}`}assemble(){const{flatten:t,as:n}=this.transform;return{type:"flatten",fields:t,as:n}}}class ms extends Y{clone(){return new ms(null,z(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=z(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"key",i[1]??"value"]}dependentFields(){return new Set(this.transform.fold)}producedFields(){return new Set(this.transform.as)}hash(){return`FoldTransform ${W(this.transform)}`}assemble(){const{fold:t,as:n}=this.transform;return{type:"fold",fields:t,as:n}}}class Yn extends Y{clone(){return new Yn(null,z(this.fields),this.geojson,this.signal)}static parseAll(t,n){if(n.component.projection&&!n.component.projection.isFit)return t;let i=0;for(const r of[[it,nt],[Be,rt]]){const s=r.map(o=>{const a=fe(n.encoding[o]);return S(a)?a.field:St(a)?{expr:`${a.datum}`}:Je(a)?{expr:`${a.value}`}:void 0});(s[0]||s[1])&&(t=new Yn(t,s,null,n.getName(`geojson_${i++}`)))}if(n.channelHasField(Oe)){const r=n.typedFieldDef(Oe);r.type===hi&&(t=new Yn(t,null,r.field,n.getName(`geojson_${i++}`)))}return t}constructor(t,n,i,r){super(t),this.fields=n,this.geojson=i,this.signal=r}dependentFields(){const t=(this.fields??[]).filter(I);return new Set([...this.geojson?[this.geojson]:[],...t])}producedFields(){return new Set}hash(){return`GeoJSON ${this.geojson} ${this.signal} ${W(this.fields)}`}assemble(){return[...this.geojson?[{type:"filter",expr:`isValid(datum["${this.geojson}"])`}]:[],{type:"geojson",...this.fields?{fields:this.fields}:{},...this.geojson?{geojson:this.geojson}:{},signal:this.signal}]}}class Mi extends Y{clone(){return new Mi(null,this.projection,z(this.fields),z(this.as))}constructor(t,n,i,r){super(t),this.projection=n,this.fields=i,this.as=r}static parseAll(t,n){if(!n.projectionName())return t;for(const i of[[it,nt],[Be,rt]]){const r=i.map(o=>{const a=fe(n.encoding[o]);return S(a)?a.field:St(a)?{expr:`${a.datum}`}:Je(a)?{expr:`${a.value}`}:void 0}),s=i[0]===Be?"2":"";(r[0]||r[1])&&(t=new Mi(t,n.projectionName(),r,[n.getName(`x${s}`),n.getName(`y${s}`)]))}return t}dependentFields(){return new Set(this.fields.filter(I))}producedFields(){return new Set(this.as)}hash(){return`Geopoint ${this.projection} ${W(this.fields)} ${W(this.as)}`}assemble(){return{type:"geopoint",projection:this.projection,fields:this.fields,as:this.as}}}class xn extends Y{clone(){return new xn(null,z(this.transform))}constructor(t,n){super(t),this.transform=n}dependentFields(){return new Set([this.transform.impute,this.transform.key,...this.transform.groupby??[]])}producedFields(){return new Set([this.transform.impute])}processSequence(t){const{start:n=0,stop:i,step:r}=t;return{signal:`sequence(${[n,i,...r?[r]:[]].join(",")})`}}static makeFromTransform(t,n){return new xn(t,n)}static makeFromEncoding(t,n){const i=n.encoding,r=i.x,s=i.y;if(S(r)&&S(s)){const o=r.impute?r:s.impute?s:void 0;if(o===void 0)return;const a=r.impute?s:s.impute?r:void 0,{method:c,value:l,frame:u,keyvals:f}=o.impute,d=vf(n.mark,i);return new xn(t,{impute:o.field,key:a.field,...c?{method:c}:{},...l!==void 0?{value:l}:{},...u?{frame:u}:{},...f!==void 0?{keyvals:f}:{},...d.length?{groupby:d}:{}})}return null}hash(){return`Impute ${W(this.transform)}`}assemble(){const{impute:t,key:n,keyvals:i,method:r,groupby:s,value:o,frame:a=[null,null]}=this.transform,c={type:"impute",field:t,key:n,...i?{keyvals:fb(i)?this.processSequence(i):i}:{},method:"value",...s?{groupby:s}:{},value:!r||r==="value"?o:null};if(r&&r!=="value"){const l={type:"window",as:[`imputed_${t}_value`],ops:[r],fields:[t],frame:a,ignorePeers:!1,...s?{groupby:s}:{}},u={type:"formula",expr:`datum.${t} === null ? datum.imputed_${t}_value : datum.${t}`,as:t};return[c,l,u]}else return[c]}}class ys extends Y{clone(){return new ys(null,z(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=z(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??n.on,i[1]??n.loess]}dependentFields(){return new Set([this.transform.loess,this.transform.on,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`LoessTransform ${W(this.transform)}`}assemble(){const{loess:t,on:n,...i}=this.transform;return{type:"loess",x:n,y:t,...i}}}class Ui extends Y{clone(){return new Ui(null,z(this.transform),this.secondary)}constructor(t,n,i){super(t),this.transform=n,this.secondary=i}static make(t,n,i,r){const s=n.component.data.sources,{from:o}=i;let a=null;if(db(o)){let c=ap(o.data,s);c||(c=new Tn(o.data),s.push(c));const l=n.getName(`lookup_${r}`);a=new we(c,l,Z.Lookup,n.component.data.outputNodeRefCounts),n.component.data.outputNodes[l]=a}else if(pb(o)){const c=o.param;i={as:c,...i};let l;try{l=n.getSelectionComponent(re(c),c)}catch{throw new Error(Ug(c))}if(a=l.materialized,!a)throw new Error(Bg(c))}return new Ui(t,i,a.getSource())}dependentFields(){return new Set([this.transform.lookup])}producedFields(){return new Set(this.transform.as?ce(this.transform.as):this.transform.from.fields)}hash(){return`Lookup ${W({transform:this.transform,secondary:this.secondary})}`}assemble(){let t;if(this.transform.from.fields)t={values:this.transform.from.fields,...this.transform.as?{as:ce(this.transform.as)}:{}};else{let n=this.transform.as;I(n)||(v(Kg),n="_lookup"),t={as:[n]}}return{type:"lookup",from:this.secondary,key:this.transform.from.key,fields:[this.transform.lookup],...t,...this.transform.default?{default:this.transform.default}:{}}}}class bs extends Y{clone(){return new bs(null,z(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=z(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"prob",i[1]??"value"]}dependentFields(){return new Set([this.transform.quantile,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`QuantileTransform ${W(this.transform)}`}assemble(){const{quantile:t,...n}=this.transform;return{type:"quantile",field:t,...n}}}class xs extends Y{clone(){return new xs(null,z(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=z(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??n.on,i[1]??n.regression]}dependentFields(){return new Set([this.transform.regression,this.transform.on,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`RegressionTransform ${W(this.transform)}`}assemble(){const{regression:t,on:n,...i}=this.transform;return{type:"regression",x:n,y:t,...i}}}class vs extends Y{clone(){return new vs(null,z(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=lt((this.transform.groupby??[]).concat(t),n=>n)}producedFields(){}dependentFields(){return new Set([this.transform.pivot,this.transform.value,...this.transform.groupby??[]])}hash(){return`PivotTransform ${W(this.transform)}`}assemble(){const{pivot:t,value:n,groupby:i,limit:r,op:s}=this.transform;return{type:"pivot",field:t,value:n,...r!==void 0?{limit:r}:{},...s!==void 0?{op:s}:{},...i!==void 0?{groupby:i}:{}}}}class Ss extends Y{clone(){return new Ss(null,z(this.transform))}constructor(t,n){super(t),this.transform=n}dependentFields(){return new Set}producedFields(){return new Set}hash(){return`SampleTransform ${W(this.transform)}`}assemble(){return{type:"sample",size:this.transform.sample}}}function sp(e){let t=0;function n(i,r){if(i instanceof Tn&&!i.isGenerator&&!oi(i.data)&&(e.push(r),r={name:null,source:r.name,transform:[]}),i instanceof $e&&(i.parent instanceof Tn&&!r.source?(r.format={...r.format,parse:i.assembleFormatParse()},r.transform.push(...i.assembleTransforms(!0))):r.transform.push(...i.assembleTransforms())),i instanceof bi){r.name||(r.name=`data_${t++}`),!r.source||r.transform.length>0?(e.push(r),i.data=r.name):i.data=r.source,e.push(...i.assemble());return}switch((i instanceof tr||i instanceof nr||i instanceof ji||i instanceof yi||i instanceof ci||i instanceof Mi||i instanceof Qe||i instanceof Ui||i instanceof xi||i instanceof Dn||i instanceof ms||i instanceof hs||i instanceof ps||i instanceof ys||i instanceof bs||i instanceof xs||i instanceof Yt||i instanceof Ss||i instanceof vs||i instanceof gs)&&r.transform.push(i.assemble()),(i instanceof dt||i instanceof ft||i instanceof xn||i instanceof Tt||i instanceof Yn)&&r.transform.push(...i.assemble()),i instanceof we&&(r.source&&r.transform.length===0?i.setSource(r.source):i.parent instanceof we?i.setSource(r.name):(r.name||(r.name=`data_${t++}`),i.setSource(r.name),i.numChildren()===1&&(e.push(r),r={name:null,source:r.name,transform:[]}))),i.numChildren()){case 0:i instanceof we&&(!r.source||r.transform.length>0)&&e.push(r);break;case 1:n(i.children[0],r);break;default:{r.name||(r.name=`data_${t++}`);let s=r.name;!r.source||r.transform.length>0?e.push(r):s=r.source;for(const o of i.children)n(o,{name:null,source:s,transform:[]});break}}}return n}function wS(e){const t=[],n=sp(t);for(const i of e.children)n(i,{source:e.name,name:null,transform:[]});return t}function CS(e,t){const n=[],i=sp(n);let r=0;for(const o of e.sources){o.hasName()||(o.dataName=`source_${r++}`);const a=o.assemble();i(o,a)}for(const o of n)o.transform.length===0&&delete o.transform;let s=0;for(const[o,a]of n.entries())(a.transform??[]).length===0&&!a.source&&n.splice(s++,0,n.splice(o,1)[0]);for(const o of n)for(const a of o.transform??[])a.type==="lookup"&&(a.from=e.outputNodes[a.from].getSource());for(const o of n)o.name in t&&(o.values=t[o.name]);return n}function NS(e){return e==="top"||e==="left"||k(e)?"header":"footer"}function FS(e){for(const t of Me)kS(e,t);ll(e,"x"),ll(e,"y")}function kS(e,t){const{facet:n,config:i,child:r,component:s}=e;if(e.channelHasField(t)){const o=n[t],a=ui("title",null,i,t);let c=Vn(o,i,{allowDisabling:!0,includeDefault:a===void 0||!!a});r.component.layoutHeaders[t].title&&(c=N(c)?c.join(", "):c,c+=` / ${r.component.layoutHeaders[t].title}`,r.component.layoutHeaders[t].title=null);const l=ui("labelOrient",o.header,i,t),u=o.header!==null?le(o.header?.labels,i.header.labels,!0):!1,f=G(["bottom","right"],l)?"footer":"header";s.layoutHeaders[t]={title:o.header!==null?c:null,facetFieldDef:o,[f]:t==="facet"?[]:[op(e,t,u)]}}}function op(e,t,n){const i=t==="row"?"height":"width";return{labels:n,sizeSignal:e.child.component.layoutSize.get(i)?e.child.getSizeSignalRef(i):void 0,axes:[]}}function ll(e,t){const{child:n}=e;if(n.component.axes[t]){const{layoutHeaders:i,resolve:r}=e.component;if(r.axis[t]=Oa(r,t),r.axis[t]==="shared"){const s=t==="x"?"column":"row",o=i[s];for(const a of n.component.axes[t]){const c=NS(a.get("orient"));o[c]??(o[c]=[op(e,s,!1)]);const l=ki(a,"main",e.config,{header:!0});l&&o[c][0].axes.push(l),a.mainExtracted=!0}}}}function TS(e){ja(e),Ar(e,"width"),Ar(e,"height")}function AS(e){ja(e);const t=e.layout.columns===1?"width":"childWidth",n=e.layout.columns===void 0?"height":"childHeight";Ar(e,t),Ar(e,n)}function ja(e){for(const t of e.children)t.parseLayoutSize()}function Ar(e,t){const n=Od(t),i=Mr(n),r=e.component.resolve,s=e.component.layoutSize;let o;for(const a of e.children){const c=a.component.layoutSize.getWithExplicit(n),l=r.scale[i]??Rd(i,e);if(l==="independent"&&c.value==="step"){o=void 0;break}if(o){if(l==="independent"&&o.value!==c.value){o=void 0;break}o=qt(o,c,n,"")}else o=c}if(o){for(const a of e.children)e.renameSignal(a.getName(n),e.getName(t)),a.component.layoutSize.set(n,"merged",!1);s.setWithExplicit(t,o)}else s.setWithExplicit(t,{explicit:!1,value:void 0})}function OS(e){const{size:t,component:n}=e;for(const i of vt){const r=_e(i);if(t[r]){const s=t[r];n.layoutSize.set(r,ht(s)?"step":s,!0)}else{const s=_S(e,r);n.layoutSize.set(r,s,!1)}}}function _S(e,t){const n=t==="width"?"x":"y",i=e.config,r=e.getScaleComponent(n);if(r){const s=r.get("type"),o=r.get("range");if(ye(s)){const a=Nr(i.view,t);return nn(o)||ht(a)?"step":a}else return no(i.view,t)}else{if(e.hasProjection||e.mark==="arc")return no(i.view,t);{const s=Nr(i.view,t);return ht(s)?s.step:s}}}function yo(e,t,n){return E(t,{suffix:`by_${E(e)}`,...n})}class Ri extends rp{constructor(t,n,i,r){super(t,"facet",n,i,r,t.resolve),this.child=Ga(t.spec,this,this.getName("child"),void 0,r),this.children=[this.child],this.facet=this.initFacet(t.facet)}initFacet(t){if(!Ki(t))return{facet:this.initFacetFieldDef(t,"facet")};const n=x(t),i={};for(const r of n){if(![Nt,Ft].includes(r)){v(Wr(r,"facet"));break}const s=t[r];if(s.field===void 0){v(Qs(s,r));break}i[r]=this.initFacetFieldDef(s,r)}return i}initFacetFieldDef(t,n){const i=ca(t,n);return i.header?i.header=Fe(i.header):i.header===null&&(i.header=null),i}channelHasField(t){return!!this.facet[t]}fieldDef(t){return this.facet[t]}parseData(){this.component.data=Es(this),this.child.parseData()}parseLayoutSize(){ja(this)}parseSelections(){this.child.parseSelections(),this.component.selection=this.child.component.selection}parseMarkGroup(){this.child.parseMarkGroup()}parseAxesAndHeaders(){this.child.parseAxesAndHeaders(),FS(this)}assembleSelectionTopLevelSignals(t){return this.child.assembleSelectionTopLevelSignals(t)}assembleSignals(){return this.child.assembleSignals(),[]}assembleSelectionData(t){return this.child.assembleSelectionData(t)}getHeaderLayoutMixins(){const t={};for(const n of Me)for(const i of Ta){const r=this.component.layoutHeaders[n],s=r[i],{facetFieldDef:o}=r;if(o){const a=ui("titleOrient",o.header,this.config,n);if(["right","bottom"].includes(a)){const c=us(n,a);t.titleAnchor??(t.titleAnchor={}),t.titleAnchor[c]="end"}}if(s?.[0]){const a=n==="row"?"height":"width",c=i==="header"?"headerBand":"footerBand";n!=="facet"&&!this.child.component.layoutSize.get(a)&&(t[c]??(t[c]={}),t[c][n]=.5),r.title&&(t.offset??(t.offset={}),t.offset[n==="row"?"rowTitle":"columnTitle"]=10)}}return t}assembleDefaultLayout(){const{column:t,row:n}=this.facet,i=t?this.columnDistinctSignal():n?1:void 0;let r="all";return(!n&&this.component.resolve.scale.x==="independent"||!t&&this.component.resolve.scale.y==="independent")&&(r="none"),{...this.getHeaderLayoutMixins(),...i?{columns:i}:{},bounds:"full",align:r}}assembleLayoutSignals(){return this.child.assembleLayoutSignals()}columnDistinctSignal(){if(!(this.parent&&this.parent instanceof Ri))return{signal:`length(data('${this.getName("column_domain")}'))`}}assembleGroupStyle(){}assembleGroup(t){return this.parent&&this.parent instanceof Ri?{...this.channelHasField("column")?{encode:{update:{columns:{field:E(this.facet.column,{prefix:"distinct"})}}}}:{},...super.assembleGroup(t)}:super.assembleGroup(t)}getCardinalityAggregateForChild(){const t=[],n=[],i=[];if(this.child instanceof Ri){if(this.child.channelHasField("column")){const r=E(this.child.facet.column);t.push(r),n.push("distinct"),i.push(`distinct_${r}`)}}else for(const r of vt){const s=this.child.component.scales[r];if(s&&!s.merged){const o=s.get("type"),a=s.get("range");if(ye(o)&&nn(a)){const c=ds(this.child,r),l=Pa(c);l?(t.push(l),n.push("distinct"),i.push(`distinct_${l}`)):v(zo(r))}}}return{fields:t,ops:n,as:i}}assembleFacet(){const{name:t,data:n}=this.component.data.facetRoot,{row:i,column:r}=this.facet,{fields:s,ops:o,as:a}=this.getCardinalityAggregateForChild(),c=[];for(const u of Me){const f=this.facet[u];if(f){c.push(E(f));const{bin:d,sort:g}=f;if(ee(d)&&c.push(E(f,{binSuffix:"end"})),ut(g)){const{field:p,op:h=Yr}=g,m=yo(f,g);i&&r?(s.push(m),o.push("max"),a.push(m)):(s.push(p),o.push(h),a.push(m))}else if(N(g)){const p=li(f,u);s.push(p),o.push("max"),a.push(p)}}}const l=!!i&&!!r;return{name:t,data:n,groupby:c,...l||s.length>0?{aggregate:{...l?{cross:l}:{},...s.length?{fields:s,ops:o,as:a}:{}}}:{}}}facetSortFields(t){const{facet:n}=this,i=n[t];return i?ut(i.sort)?[yo(i,i.sort,{expr:"datum"})]:N(i.sort)?[li(i,t,{expr:"datum"})]:[E(i,{expr:"datum"})]:[]}facetSortOrder(t){const{facet:n}=this,i=n[t];if(i){const{sort:r}=i;return[(ut(r)?r.order:!N(r)&&r)||"ascending"]}return[]}assembleLabelTitle(){const{facet:t,config:n}=this;if(t.facet)return uo(t.facet,"facet",n);const i={row:["top","bottom"],column:["left","right"]};for(const r of ka)if(t[r]){const s=ui("labelOrient",t[r]?.header,n,r);if(i[r].includes(s))return uo(t[r],r,n)}}assembleMarks(){const{child:t}=this,n=this.component.data.facetRoot,i=wS(n),r=t.assembleGroupEncodeEntry(!1),s=this.assembleLabelTitle()||t.assembleTitle(),o=t.assembleGroupStyle();return[{name:this.getName("cell"),type:"group",...s?{title:s}:{},...o?{style:o}:{},from:{facet:this.assembleFacet()},sort:{field:Me.map(c=>this.facetSortFields(c)).flat(),order:Me.map(c=>this.facetSortOrder(c)).flat()},...i.length>0?{data:i}:{},...r?{encode:{update:r}}:{},...t.assembleGroup(Wb(this,[]))}]}getMapping(){return this.facet}}function RS(e,t){const{row:n,column:i}=t;if(n&&i){let r=null;for(const s of[n,i])if(ut(s.sort)){const{field:o,op:a=Yr}=s.sort;e=r=new Dn(e,{joinaggregate:[{op:a,field:o,as:yo(s,s.sort,{forAs:!0})}],groupby:[E(s)]})}return r}return null}function ap(e,t){for(const n of t){const i=n.data;if(e.name&&n.hasName()&&e.name!==n.dataName)continue;const r=e.format?.mesh,s=i.format?.feature;if(r&&s)continue;const o=e.format?.feature;if((o||s)&&o!==s)continue;const a=i.format?.mesh;if(!((r||a)&&r!==a)){if(Di(e)&&Di(i)){if(Pe(e.values,i.values))return n}else if(oi(e)&&oi(i)){if(e.url===i.url)return n}else if(Kf(e)&&e.name===n.dataName)return n}}return null}function IS(e,t){if(e.data||!e.parent){if(e.data===null){const i=new Tn({values:[]});return t.push(i),i}const n=ap(e.data,t);if(n)return Bt(e.data)||(n.data.format=Hl({},e.data.format,n.data.format)),!n.hasName()&&e.data.name&&(n.dataName=e.data.name),n;{const i=new Tn(e.data);return t.push(i),i}}else return e.parent.component.data.facetRoot?e.parent.component.data.facetRoot:e.parent.component.data.main}function LS(e,t,n){let i=0;for(const r of t.transforms){let s,o;if($b(r))o=e=new ci(e,r),s="derived";else if(Sa(r)){const a=Nv(r);o=e=$e.makeWithAncestors(e,{},a,n)??e,e=new yi(e,t,r.filter)}else if(Hf(r))o=e=dt.makeFromTransform(e,r,t),s="number";else if(Cb(r))s="date",n.getWithExplicit(r.field).value===void 0&&(e=new $e(e,{[r.field]:s}),n.set(r.field,s,!1)),o=e=ft.makeFromTransform(e,r);else if(Nb(r))o=e=Qe.makeFromTransform(e,r),s="number",Fa(t)&&(e=new Yt(e));else if(Gf(r))o=e=Ui.make(e,t,r,i++),s="derived";else if(vb(r))o=e=new xi(e,r),s="number";else if(Sb(r))o=e=new Dn(e,r),s="number";else if(Fb(r))o=e=Tt.makeFromTransform(e,r),s="derived";else if(kb(r))o=e=new ms(e,r),s="derived";else if(Tb(r))o=e=new gs(e,r),s="derived";else if(Eb(r))o=e=new hs(e,r),s="derived";else if(gb(r))o=e=new vs(e,r),s="derived";else if(xb(r))e=new Ss(e,r);else if(wb(r))o=e=xn.makeFromTransform(e,r),s="derived";else if(hb(r))o=e=new ps(e,r),s="derived";else if(mb(r))o=e=new bs(e,r),s="derived";else if(yb(r))o=e=new xs(e,r),s="derived";else if(bb(r))o=e=new ys(e,r),s="derived";else{v(Yg(r));continue}if(o&&s!==void 0)for(const a of o.producedFields()??[])n.set(a,s,!1)}return e}function Es(e){let t=IS(e,e.component.data.sources);const{outputNodes:n,outputNodeRefCounts:i}=e.component.data,r=e.data,o=!(r&&(Bt(r)||oi(r)||Di(r)))&&e.parent?e.parent.component.data.ancestorParse.clone():new Ub;Bt(r)?(Qf(r)?t=new nr(t,r.sequence):Ea(r)&&(t=new tr(t,r.graticule)),o.parseNothing=!0):r?.format?.parse===null&&(o.parseNothing=!0),t=$e.makeExplicit(t,e,o)??t,t=new Yt(t);const a=e.parent&&vi(e.parent);(ae(e)||ze(e))&&a&&(t=dt.makeFromEncoding(t,e)??t),e.transforms.length>0&&(t=LS(t,e,o));const c=kv(e),l=Fv(e);t=$e.makeWithAncestors(t,{},{...c,...l},o)??t,ae(e)&&(t=Yn.parseAll(t,e),t=Mi.parseAll(t,e)),(ae(e)||ze(e))&&(a||(t=dt.makeFromEncoding(t,e)??t),t=ft.makeFromEncoding(t,e)??t,t=ci.parseAllForSortIndex(t,e));const u=e.getDataName(Z.Raw),f=new we(t,u,Z.Raw,i);if(n[u]=f,t=f,ae(e)){const h=Qe.makeFromEncoding(t,e);h&&(t=h,Fa(e)&&(t=new Yt(t))),t=xn.makeFromEncoding(t,e)??t,t=Tt.makeFromEncoding(t,e)??t}ae(e)&&(t=ji.make(t,e)??t);const d=e.getDataName(Z.Main),g=new we(t,d,Z.Main,i);n[d]=g,t=g,ae(e)&&$x(e,g);let p=null;if(ze(e)){const h=e.getName("facet");t=RS(t,e.facet)??t,p=new bi(t,e,h,g.getSource()),n[h]=p}return{...e.component.data,outputNodes:n,outputNodeRefCounts:i,raw:f,main:g,facetRoot:p,ancestorParse:o}}class PS extends Da{constructor(t,n,i,r){super(t,"concat",n,i,r,t.resolve),(t.resolve?.axis?.x==="shared"||t.resolve?.axis?.y==="shared")&&v(qg),this.children=this.getChildren(t).map((s,o)=>Ga(s,this,this.getName(`concat_${o}`),void 0,r))}parseData(){this.component.data=Es(this);for(const t of this.children)t.parseData()}parseSelections(){this.component.selection={};for(const t of this.children){t.parseSelections();for(const n of x(t.component.selection))this.component.selection[n]=t.component.selection[n]}}parseMarkGroup(){for(const t of this.children)t.parseMarkGroup()}parseAxesAndHeaders(){for(const t of this.children)t.parseAxesAndHeaders()}getChildren(t){return is(t)?t.vconcat:xa(t)?t.hconcat:t.concat}parseLayoutSize(){AS(this)}parseAxisGroup(){return null}assembleSelectionTopLevelSignals(t){return this.children.reduce((n,i)=>i.assembleSelectionTopLevelSignals(n),t)}assembleSignals(){return this.children.forEach(t=>t.assembleSignals()),[]}assembleLayoutSignals(){const t=Aa(this);for(const n of this.children)t.push(...n.assembleLayoutSignals());return t}assembleSelectionData(t){return this.children.reduce((n,i)=>i.assembleSelectionData(n),t)}assembleMarks(){return this.children.map(t=>{const n=t.assembleTitle(),i=t.assembleGroupStyle(),r=t.assembleGroupEncodeEntry(!1);return{type:"group",name:t.getName("group"),...n?{title:n}:{},...i?{style:i}:{},...r?{encode:{update:r}}:{},...t.assembleGroup()}})}assembleGroupStyle(){}assembleDefaultLayout(){const t=this.layout.columns;return{...t!=null?{columns:t}:{},bounds:"full",align:"each"}}}function zS(e){return e===!1||e===null}const DS={disable:1,gridScale:1,scale:1,...hf,labelExpr:1,encode:1},cp=x(DS);class Ma extends Dt{constructor(t={},n={},i=!1){super(),this.explicit=t,this.implicit=n,this.mainExtracted=i}clone(){return new Ma(z(this.explicit),z(this.implicit),this.mainExtracted)}hasAxisPart(t){return t==="axis"?!0:t==="grid"||t==="title"?!!this.get(t):!zS(this.get(t))}hasOrientSignalRef(){return k(this.explicit.orient)}}function jS(e,t,n){const{encoding:i,config:r}=e,s=fe(i[t])??fe(i[xt(t)]),o=e.axis(t)||{},{format:a,formatType:c}=o;if(Nn(c))return{text:Ke({fieldOrDatumDef:s,field:"datum.value",format:a,formatType:c,config:r}),...n};if(a===void 0&&c===void 0&&r.customFormatTypes){if(ii(s)==="quantitative"){if(ri(s)&&s.stack==="normalize"&&r.normalizedNumberFormatType)return{text:Ke({fieldOrDatumDef:s,field:"datum.value",format:r.normalizedNumberFormat,formatType:r.normalizedNumberFormatType,config:r}),...n};if(r.numberFormatType)return{text:Ke({fieldOrDatumDef:s,field:"datum.value",format:r.numberFormat,formatType:r.numberFormatType,config:r}),...n}}if(ii(s)==="temporal"&&r.timeFormatType&&S(s)&&!s.timeUnit)return{text:Ke({fieldOrDatumDef:s,field:"datum.value",format:r.timeFormat,formatType:r.timeFormatType,config:r}),...n}}return n}function MS(e){return vt.reduce((t,n)=>(e.component.scales[n]&&(t[n]=[VS(n,e)]),t),{})}const US={bottom:"top",top:"bottom",left:"right",right:"left"};function BS(e){const{axes:t,resolve:n}=e.component,i={top:0,bottom:0,right:0,left:0};for(const r of e.children){r.parseAxesAndHeaders();for(const s of x(r.component.axes))n.axis[s]=Oa(e.component.resolve,s),n.axis[s]==="shared"&&(t[s]=WS(t[s],r.component.axes[s]),t[s]||(n.axis[s]="independent",delete t[s]))}for(const r of vt){for(const s of e.children)if(s.component.axes[r]){if(n.axis[r]==="independent"){t[r]=(t[r]??[]).concat(s.component.axes[r]);for(const o of s.component.axes[r]){const{value:a,explicit:c}=o.getWithExplicit("orient");if(!k(a)){if(i[a]>0&&!c){const l=US[a];i[a]>i[l]&&o.set("orient",l,!1)}i[a]++}}}delete s.component.axes[r]}if(n.axis[r]==="independent"&&t[r]&&t[r].length>1)for(const[s,o]of(t[r]||[]).entries())s>0&&o.get("grid")&&!o.explicit.grid&&(o.implicit.grid=!1)}}function WS(e,t){if(e){if(e.length!==t.length)return;const n=e.length;for(let i=0;i<n;i++){const r=e[i],s=t[i];if(!!r!=!!s)return;if(r&&s){const o=r.getWithExplicit("orient"),a=s.getWithExplicit("orient");if(o.explicit&&a.explicit&&o.value!==a.value)return;e[i]=GS(r,s)}}}else return t.map(n=>n.clone());return e}function GS(e,t){for(const n of cp){const i=qt(e.getWithExplicit(n),t.getWithExplicit(n),n,"axis",(r,s)=>{switch(n){case"title":return xu(r,s);case"gridScale":return{explicit:r.explicit,value:le(r.value,s.value)}}return ss(r,s,n,"axis")});e.setWithExplicit(n,i)}return e}function HS(e,t,n,i,r){if(t==="disable")return n!==void 0;switch(n=n||{},t){case"titleAngle":case"labelAngle":return e===(k(n.labelAngle)?n.labelAngle:Pi(n.labelAngle));case"values":return!!n.values;case"encode":return!!n.encoding||!!n.labelAngle;case"title":if(e===Nd(i,r))return!0}return e===n[t]}const qS=new Set(["grid","translate","format","formatType","orient","labelExpr","tickCount","position","tickMinStep"]);function VS(e,t){let n=t.axis(e);const i=new Ma,r=fe(t.encoding[e]),{mark:s,config:o}=t,a=n?.orient||o[e==="x"?"axisX":"axisY"]?.orient||o.axis?.orient||Rx(e),c=t.getScaleComponent(e).get("type"),l=Nx(e,c,a,t.config),u=n!==void 0?!n:co("disable",o.style,n?.style,l).configValue;if(i.set("disable",u,n!==void 0),u)return i;n=n||{};const f=Ax(r,n,e,o.style,l),d=Zu(n.formatType,r,c),g=Ju(r,r.type,n.format,n.formatType,o,!0),p={fieldOrDatumDef:r,axis:n,channel:e,model:t,scaleType:c,orient:a,labelAngle:f,format:g,formatType:d,mark:s,config:o};for(const y of cp){const b=y in Xc?Xc[y](p):Sc(y)?n[y]:void 0,C=b!==void 0,O=HS(b,y,n,t,e);if(C&&O)i.set(y,b,O);else{const{configValue:F=void 0,configFrom:_=void 0}=Sc(y)&&y!=="values"?co(y,o.style,n.style,l):{},P=F!==void 0;C&&!P?i.set(y,b,O):(_!=="vgAxisConfig"||qS.has(y)&&P||Zi(F)||k(F))&&i.set(y,F,!1)}}const h=n.encoding??{},m=gf.reduce((y,b)=>{if(!i.hasAxisPart(b))return y;const C=_d(h[b]??{},t),O=b==="labels"?jS(t,e,C):C;return O!==void 0&&!K(O)&&(y[b]={update:O}),y},{});return K(m)||i.set("encode",m,!!n.encoding||n.labelAngle!==void 0),i}function XS({encoding:e,size:t}){for(const n of vt){const i=_e(n);ht(t[i])&&Ut(e[n])&&(delete t[i],v(wu(i)))}return t}const YS={vgMark:"arc",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...Vt(e,"radius"),...Vt(e,"theta")})},KS={vgMark:"area",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",orient:"include",size:"ignore",theta:"ignore"}),...Fr("x",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="horizontal"}),...Fr("y",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="vertical"}),...Na(e)})},QS={vgMark:"rect",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Vt(e,"x"),...Vt(e,"y")})},JS={vgMark:"shape",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"})}),postEncodingTransform:e=>{const{encoding:t}=e,n=t.shape;return[{type:"geoshape",projection:e.projectionName(),...n&&S(n)&&n.type===hi?{field:E(n,{expr:"datum"})}:{}}]}},ZS={vgMark:"image",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"ignore",orient:"ignore",size:"ignore",theta:"ignore"}),...Vt(e,"x"),...Vt(e,"y"),...wa(e,"url")})},eE={vgMark:"line",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...he("size",e,{vgChannel:"strokeWidth"}),...Na(e)})},tE={vgMark:"trail",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...he("size",e),...Na(e)})};function Ua(e,t){const{config:n}=e;return{...Ge(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...he("size",e),...he("angle",e),...nE(e,n,t)}}function nE(e,t,n){return n?{shape:{value:n}}:he("shape",e)}const iE={vgMark:"symbol",encodeEntry:e=>Ua(e)},rE={vgMark:"symbol",encodeEntry:e=>Ua(e,"circle")},sE={vgMark:"symbol",encodeEntry:e=>Ua(e,"square")},oE={vgMark:"rect",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Vt(e,"x"),...Vt(e,"y")})},aE={vgMark:"rule",encodeEntry:e=>{const{markDef:t}=e,n=t.orient;return!e.encoding.x&&!e.encoding.y&&!e.encoding.latitude&&!e.encoding.longitude?{}:{...Ge(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Fr("x",e,{defaultPos:n==="horizontal"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:n!=="vertical"}),...Fr("y",e,{defaultPos:n==="vertical"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:n!=="horizontal"}),...he("size",e,{vgChannel:"strokeWidth"})}}},cE={vgMark:"text",encodeEntry:e=>{const{config:t,encoding:n}=e;return{...Ge(e,{align:"include",baseline:"include",color:"include",size:"ignore",orient:"ignore",theta:"include"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...wa(e),...he("size",e,{vgChannel:"fontSize"}),...he("angle",e),...Gc("align",lE(e.markDef,n,t)),...Gc("baseline",uE(e.markDef,n,t)),...Ee("radius",e,{defaultPos:null}),...Ee("theta",e,{defaultPos:null})}}};function lE(e,t,n){if(V("align",e,n)===void 0)return"center"}function uE(e,t,n){if(V("baseline",e,n)===void 0)return"middle"}const fE={vgMark:"rect",encodeEntry:e=>{const{config:t,markDef:n}=e,i=n.orient,r=i==="horizontal"?"width":"height",s=i==="horizontal"?"height":"width";return{...Ge(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid",vgChannel:"xc"}),...Ee("y",e,{defaultPos:"mid",vgChannel:"yc"}),...he("size",e,{defaultValue:dE(e),vgChannel:r}),[s]:ne(V("thickness",n,t))}}};function dE(e){const{config:t,markDef:n}=e,{orient:i}=n,r=i==="horizontal"?"width":"height",s=e.getScaleComponent(i==="horizontal"?"x":"y"),o=V("size",n,t,{vgChannel:r})??t.tick.bandSize;if(o!==void 0)return o;{const a=s?s.get("range"):void 0;return a&&nn(a)&&ie(a.step)?a.step*3/4:Cr(t.view,r)*3/4}}const cr={arc:YS,area:KS,bar:QS,circle:rE,geoshape:JS,image:ZS,line:eE,point:iE,rect:oE,rule:aE,square:sE,text:cE,tick:fE,trail:tE};function pE(e){if(G([Vr,Hr,Sm],e.mark)){const t=vf(e.mark,e.encoding);if(t.length>0)return gE(e,t)}else if(e.mark===qr){const t=Ks.some(n=>V(n,e.markDef,e.config));if(e.stack&&!e.fieldDef("size")&&t)return hE(e)}return Ba(e)}const ul="faceted_path_";function gE(e,t){return[{name:e.getName("pathgroup"),type:"group",from:{facet:{name:ul+e.requestDataName(Z.Main),data:e.requestDataName(Z.Main),groupby:t}},encode:{update:{width:{field:{group:"width"}},height:{field:{group:"height"}}}},marks:Ba(e,{fromPrefix:ul})}]}const fl="stack_group_";function hE(e){const[t]=Ba(e,{fromPrefix:fl}),n=e.scaleName(e.stack.fieldChannel),i=(l={})=>e.vgField(e.stack.fieldChannel,l),r=(l,u)=>{const f=[i({prefix:"min",suffix:"start",expr:u}),i({prefix:"max",suffix:"start",expr:u}),i({prefix:"min",suffix:"end",expr:u}),i({prefix:"max",suffix:"end",expr:u})];return`${l}(${f.map(d=>`scale('${n}',${d})`).join(",")})`};let s,o;e.stack.fieldChannel==="x"?(s={...Qn(t.encode.update,["y","yc","y2","height",...Ks]),x:{signal:r("min","datum")},x2:{signal:r("max","datum")},clip:{value:!0}},o={x:{field:{group:"x"},mult:-1},height:{field:{group:"height"}}},t.encode.update={...ke(t.encode.update,["y","yc","y2"]),height:{field:{group:"height"}}}):(s={...Qn(t.encode.update,["x","xc","x2","width"]),y:{signal:r("min","datum")},y2:{signal:r("max","datum")},clip:{value:!0}},o={y:{field:{group:"y"},mult:-1},width:{field:{group:"width"}}},t.encode.update={...ke(t.encode.update,["x","xc","x2"]),width:{field:{group:"width"}}});for(const l of Ks){const u=Ot(l,e.markDef,e.config);t.encode.update[l]?(s[l]=t.encode.update[l],delete t.encode.update[l]):u&&(s[l]=ne(u)),u&&(t.encode.update[l]={value:0})}const a=[];if(e.stack.groupbyChannels?.length>0)for(const l of e.stack.groupbyChannels){const u=e.fieldDef(l),f=E(u);f&&a.push(f),(u?.bin||u?.timeUnit)&&a.push(E(u,{binSuffix:"end"}))}return s=["stroke","strokeWidth","strokeJoin","strokeCap","strokeDash","strokeDashOffset","strokeMiterLimit","strokeOpacity"].reduce((l,u)=>{if(t.encode.update[u])return{...l,[u]:t.encode.update[u]};{const f=Ot(u,e.markDef,e.config);return f!==void 0?{...l,[u]:ne(f)}:l}},s),s.stroke&&(s.strokeForeground={value:!0},s.strokeOffset={value:0}),[{type:"group",from:{facet:{data:e.requestDataName(Z.Main),name:fl+e.requestDataName(Z.Main),groupby:a,aggregate:{fields:[i({suffix:"start"}),i({suffix:"start"}),i({suffix:"end"}),i({suffix:"end"})],ops:["min","max","min","max"]}}},encode:{update:s},marks:[{type:"group",encode:{update:o},marks:[t]}]}]}function mE(e){const{encoding:t,stack:n,mark:i,markDef:r,config:s}=e,o=t.order;if(!(!N(o)&&Je(o)&&Xs(o.value)||!o&&Xs(V("order",r,s)))){if((N(o)||S(o))&&!n)return mu(o,{expr:"datum"});if(rn(i)){const a=r.orient==="horizontal"?"y":"x",c=t[a];if(S(c)){const l=c.sort;if(N(l))return{field:E(c,{prefix:a,suffix:"sort_index",expr:"datum"})};if(ut(l))return{field:E({aggregate:la(e.encoding)?l.op:void 0,field:l.field},{expr:"datum"})};if(tf(l)){const u=e.fieldDef(l.encoding);return{field:E(u,{expr:"datum"}),order:l.order}}else return l===null?void 0:{field:E(c,{binSuffix:e.stack?.impute?"mid":void 0,expr:"datum"})}}return}}}function Ba(e,t={fromPrefix:""}){const{mark:n,markDef:i,encoding:r,config:s}=e,o=le(i.clip,yE(e),bE(e)),a=gu(i),c=r.key,l=mE(e),u=xE(e),f=V("aria",i,s),d=cr[n].postEncodingTransform?cr[n].postEncodingTransform(e):null;return[{name:e.getName("marks"),type:cr[n].vgMark,...o?{clip:o}:{},...a?{style:a}:{},...c?{key:c.field}:{},...l?{sort:l}:{},...u||{},...f===!1?{aria:f}:{},from:{data:t.fromPrefix+e.requestDataName(Z.Main)},encode:{update:cr[n].encodeEntry(e)},...d?{transform:d}:{}}]}function yE(e){const t=e.getScaleComponent("x"),n=e.getScaleComponent("y");return t?.get("selectionExtent")||n?.get("selectionExtent")?!0:void 0}function bE(e){const t=e.component.projection;return t&&!t.isFit?!0:void 0}function xE(e){if(!e.component.selection)return null;const t=x(e.component.selection).length;let n=t,i=e.parent;for(;i&&n===0;)n=x(i.component.selection).length,i=i.parent;return n?{interactive:t>0||e.mark==="geoshape"||!!e.encoding.tooltip||!!e.markDef.tooltip}:null}class lp extends rp{constructor(t,n,i,r={},s){super(t,"unit",n,i,s,void 0,$c(t)?t.view:void 0),this.specifiedScales={},this.specifiedAxes={},this.specifiedLegends={},this.specifiedProjection={},this.selection=[],this.children=[];const o=pt(t.mark)?{...t.mark}:{type:t.mark},a=o.type;o.filled===void 0&&(o.filled=ib(o,s,{graticule:t.data&&Ea(t.data)}));const c=this.encoding=ay(t.encoding||{},a,o.filled,s);this.markDef=Uf(o,c,s),this.size=XS({encoding:c,size:$c(t)?{...r,...t.width?{width:t.width}:{},...t.height?{height:t.height}:{}}:r}),this.stack=Mf(this.markDef,c),this.specifiedScales=this.initScales(a,c),this.specifiedAxes=this.initAxes(c),this.specifiedLegends=this.initLegends(c),this.specifiedProjection=t.projection,this.selection=(t.params??[]).filter(l=>ya(l))}get hasProjection(){const{encoding:t}=this,n=this.mark===Vu,i=t&&sg.some(r=>D(t[r]));return n||i}scaleDomain(t){const n=this.specifiedScales[t];return n?n.domain:void 0}axis(t){return this.specifiedAxes[t]}legend(t){return this.specifiedLegends[t]}initScales(t,n){return Ur.reduce((i,r)=>{const s=fe(n[r]);return s&&(i[r]=this.initScale(s.scale??{})),i},{})}initScale(t){const{domain:n,range:i}=t,r=Fe(t);return N(n)&&(r.domain=n.map(Le)),N(i)&&(r.range=i.map(Le)),r}initAxes(t){return vt.reduce((n,i)=>{const r=t[i];if(D(r)||i===se&&D(t.x2)||i===be&&D(t.y2)){const s=D(r)?r.axis:void 0;n[i]=s&&this.initAxis({...s})}return n},{})}initAxis(t){const n=x(t),i={};for(const r of n){const s=t[r];i[r]=Zi(s)?pu(s):Le(s)}return i}initLegends(t){return hg.reduce((n,i)=>{const r=fe(t[i]);if(r&&yg(i)){const s=r.legend;n[i]=s&&Fe(s)}return n},{})}parseData(){this.component.data=Es(this)}parseLayoutSize(){OS(this)}parseSelections(){this.component.selection=Ex(this,this.selection)}parseMarkGroup(){this.component.mark=pE(this)}parseAxesAndHeaders(){this.component.axes=MS(this)}assembleSelectionTopLevelSignals(t){return Gb(this,t)}assembleSignals(){return[...$d(this),...Bb(this,[])]}assembleSelectionData(t){return Hb(this,t)}assembleLayout(){return null}assembleLayoutSignals(){return Aa(this)}assembleMarks(){let t=this.component.mark??[];return(!this.parent||!vi(this.parent))&&(t=ed(this,t)),t.map(this.correctDataNames)}assembleGroupStyle(){const{style:t}=this.view||{};return t!==void 0?t:this.encoding.x||this.encoding.y?"cell":"view"}getMapping(){return this.encoding}get mark(){return this.markDef.type}channelHasField(t){return yn(this.encoding,t)}fieldDef(t){const n=this.encoding[t];return gt(n)}typedFieldDef(t){const n=this.fieldDef(t);return Ce(n)?n:null}}class Wa extends Da{constructor(t,n,i,r,s){super(t,"layer",n,i,s,t.resolve,t.view);const o={...r,...t.width?{width:t.width}:{},...t.height?{height:t.height}:{}};this.children=t.layer.map((a,c)=>{if(rs(a))return new Wa(a,this,this.getName(`layer_${c}`),o,s);if(zt(a))return new lp(a,this,this.getName(`layer_${c}`),o,s);throw new Error(Po(a))})}parseData(){this.component.data=Es(this);for(const t of this.children)t.parseData()}parseLayoutSize(){TS(this)}parseSelections(){this.component.selection={};for(const t of this.children){t.parseSelections();for(const n of x(t.component.selection))this.component.selection[n]=t.component.selection[n]}}parseMarkGroup(){for(const t of this.children)t.parseMarkGroup()}parseAxesAndHeaders(){BS(this)}assembleSelectionTopLevelSignals(t){return this.children.reduce((n,i)=>i.assembleSelectionTopLevelSignals(n),t)}assembleSignals(){return this.children.reduce((t,n)=>t.concat(n.assembleSignals()),$d(this))}assembleLayoutSignals(){return this.children.reduce((t,n)=>t.concat(n.assembleLayoutSignals()),Aa(this))}assembleSelectionData(t){return this.children.reduce((n,i)=>i.assembleSelectionData(n),t)}assembleGroupStyle(){const t=new Set;for(const i of this.children)for(const r of ce(i.assembleGroupStyle()))t.add(r);const n=Array.from(t);return n.length>1?n:n.length===1?n[0]:void 0}assembleTitle(){let t=super.assembleTitle();if(t)return t;for(const n of this.children)if(t=n.assembleTitle(),t)return t}assembleLayout(){return null}assembleMarks(){return qb(this,this.children.flatMap(t=>t.assembleMarks()))}assembleLegends(){return this.children.reduce((t,n)=>t.concat(n.assembleLegends()),Md(this))}}function Ga(e,t,n,i,r){if(Kr(e))return new Ri(e,t,n,r);if(rs(e))return new Wa(e,t,n,i,r);if(zt(e))return new lp(e,t,n,i,r);if(Oy(e))return new PS(e,t,n,r);throw new Error(Po(e))}function vE(e,t={}){t.logger&&Ph(t.logger),t.fieldTitle&&ff(t.fieldTitle);try{const n=jf(Rr(t.config,e.config)),i=Xf(e,n),r=Ga(i,null,"",void 0,n);return r.parse(),Gv(r.component.data,r),{spec:EE(r,SE(e,i.autosize,n,r),e.datasets,e.usermeta),normalized:i}}finally{t.logger&&zh(),t.fieldTitle&&Jm()}}function SE(e,t,n,i){const r=i.component.layoutSize.get("width"),s=i.component.layoutSize.get("height");if(t===void 0?(t={type:"pad"},i.hasAxisOrientSignalRef()&&(t.resize=!0)):I(t)&&(t={type:t}),r&&s&&Db(t.type)){if(r==="step"&&s==="step")v(ac()),t.type="pad";else if(r==="step"||s==="step"){const o=r==="step"?"width":"height";v(ac(Mr(o)));const a=o==="width"?"height":"width";t.type=jb(a)}}return{...x(t).length===1&&t.type?t.type==="pad"?{}:{autosize:t.type}:{autosize:t},...Lc(n,!1),...Lc(e,!0)}}function EE(e,t,n={},i){const r=e.config?Hy(e.config):void 0,s=[].concat(e.assembleSelectionData([]),CS(e.component.data,n)),o=e.assembleProjections(),a=e.assembleTitle(),c=e.assembleGroupStyle(),l=e.assembleGroupEncodeEntry(!0);let u=e.assembleLayoutSignals();u=u.filter(g=>(g.name==="width"||g.name==="height")&&g.value!==void 0?(t[g.name]=+g.value,!1):!0);const{params:f,...d}=t;return{$schema:"https://vega.github.io/schema/vega/v5.json",...e.description?{description:e.description}:{},...d,...a?{title:a}:{},...c?{style:c}:{},...l?{encode:{update:l}}:{},data:s,...o.length>0?{projections:o}:{},...e.assembleGroup([...u,...e.assembleSelectionTopLevelSignals([]),...Lf(f)]),...r?{config:r}:{},...i?{usermeta:i}:{}}}const $E=Zp.version,wE=Object.freeze(Object.defineProperty({__proto__:null,accessPathDepth:Jn,accessPathWithDatum:No,compile:vE,contains:G,deepEqual:Pe,deleteNestedProperty:hr,duplicate:z,entries:Wt,every:$o,fieldIntersection:Co,flatAccessWithDatum:Vl,getFirstDefined:le,hasIntersection:wo,hash:W,internalField:Kl,isBoolean:Li,isEmpty:K,isEqual:tg,isInternalField:Ql,isNullOrFalse:Xs,isNumeric:Ir,keys:x,logicalExpr:Oi,mergeDeep:Hl,never:Gl,normalize:Xf,normalizeAngle:Pi,omit:ke,pick:Qn,prefixGenerator:Ys,removePathFromField:Fo,replaceAll:Sn,replacePathInField:Ue,resetIdCounter:ig,setEqual:ql,some:vn,stringify:Q,titleCase:Wi,unique:lt,uniqueId:Yl,vals:ve,varName:re,version:$E},Symbol.toStringTag,{value:"Module"}));var CE="vega-themes",NE="2.14.0",FE="Themes for stylized Vega and Vega-Lite visualizations.",kE=["vega","vega-lite","themes","style"],TE="BSD-3-Clause",AE={name:"UW Interactive Data Lab",url:"https://idl.cs.washington.edu"},OE=[{name:"Emily Gu",url:"https://github.com/emilygu"},{name:"Arvind Satyanarayan",url:"http://arvindsatya.com"},{name:"Jeffrey Heer",url:"https://idl.cs.washington.edu"},{name:"Dominik Moritz",url:"https://www.domoritz.de"}],_E="build/vega-themes.js",RE="build/vega-themes.module.js",IE="build/vega-themes.min.js",LE="build/vega-themes.min.js",PE="build/vega-themes.module.d.ts",zE={type:"git",url:"https://github.com/vega/vega-themes.git"},DE=["src","build"],jE={prebuild:"yarn clean",build:"rollup -c",clean:"rimraf build && rimraf examples/build","copy:data":"rsync -r node_modules/vega-datasets/data/* examples/data","copy:build":"rsync -r build/* examples/build","deploy:gh":"yarn build && mkdir -p examples/build && rsync -r build/* examples/build && gh-pages -d examples",preversion:"yarn lint",serve:"browser-sync start -s -f build examples --serveStatic examples",start:"yarn build && concurrently --kill-others -n Server,Rollup 'yarn serve' 'rollup -c -w'",format:"eslint . --fix",lint:"eslint .",release:"release-it"},ME={"@babel/core":"^7.22.9","@babel/plugin-proposal-async-generator-functions":"^7.20.7","@babel/plugin-proposal-json-strings":"^7.18.6","@babel/plugin-proposal-object-rest-spread":"^7.20.7","@babel/plugin-proposal-optional-catch-binding":"^7.18.6","@babel/plugin-transform-runtime":"^7.22.9","@babel/preset-env":"^7.22.9","@babel/preset-typescript":"^7.22.5","@release-it/conventional-changelog":"^7.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.1.0","@rollup/plugin-terser":"^0.4.3","@typescript-eslint/eslint-plugin":"^6.0.0","@typescript-eslint/parser":"^6.0.0","browser-sync":"^2.29.3",concurrently:"^8.2.0",eslint:"^8.45.0","eslint-config-prettier":"^8.8.0","eslint-plugin-prettier":"^5.0.0","gh-pages":"^5.0.0",prettier:"^3.0.0","release-it":"^16.1.0",rollup:"^3.26.2","rollup-plugin-bundle-size":"^1.0.3","rollup-plugin-ts":"^3.2.0",typescript:"^5.1.6",vega:"^5.25.0","vega-lite":"^5.9.3"},UE={vega:"*","vega-lite":"*"},BE={},WE={name:CE,version:NE,description:FE,keywords:kE,license:TE,author:AE,contributors:OE,main:_E,module:RE,unpkg:IE,jsdelivr:LE,types:PE,repository:zE,files:DE,scripts:jE,devDependencies:ME,peerDependencies:UE,dependencies:BE};const Un="#fff",dl="#888",GE={background:"#333",view:{stroke:dl},title:{color:Un,subtitleColor:Un},style:{"guide-label":{fill:Un},"guide-title":{fill:Un}},axis:{domainColor:Un,gridColor:dl,tickColor:Un}},an="#4572a7",HE={background:"#fff",arc:{fill:an},area:{fill:an},line:{stroke:an,strokeWidth:2},path:{stroke:an},rect:{fill:an},shape:{stroke:an},symbol:{fill:an,strokeWidth:1.5,size:50},axis:{bandPosition:.5,grid:!0,gridColor:"#000000",gridOpacity:1,gridWidth:.5,labelPadding:10,tickSize:5,tickWidth:.5},axisBand:{grid:!1,tickExtra:!0},legend:{labelBaseline:"middle",labelFontSize:11,symbolSize:50,symbolType:"square"},range:{category:["#4572a7","#aa4643","#8aa453","#71598e","#4598ae","#d98445","#94aace","#d09393","#b9cc98","#a99cbc"]}},cn="#30a2da",Ds="#cbcbcb",qE="#999",VE="#333",pl="#f0f0f0",gl="#333",XE={arc:{fill:cn},area:{fill:cn},axis:{domainColor:Ds,grid:!0,gridColor:Ds,gridWidth:1,labelColor:qE,labelFontSize:10,titleColor:VE,tickColor:Ds,tickSize:10,titleFontSize:14,titlePadding:10,labelPadding:4},axisBand:{grid:!1},background:pl,group:{fill:pl},legend:{labelColor:gl,labelFontSize:11,padding:1,symbolSize:30,symbolType:"square",titleColor:gl,titleFontSize:14,titlePadding:10},line:{stroke:cn,strokeWidth:2},path:{stroke:cn,strokeWidth:.5},rect:{fill:cn},range:{category:["#30a2da","#fc4f30","#e5ae38","#6d904f","#8b8b8b","#b96db8","#ff9e27","#56cc60","#52d2ca","#52689e","#545454","#9fe4f8"],diverging:["#cc0020","#e77866","#f6e7e1","#d6e8ed","#91bfd9","#1d78b5"],heatmap:["#d6e8ed","#cee0e5","#91bfd9","#549cc6","#1d78b5"]},point:{filled:!0,shape:"circle"},shape:{stroke:cn},bar:{binSpacing:2,fill:cn,stroke:null},title:{anchor:"start",fontSize:24,fontWeight:600,offset:20}},ln="#000",YE={group:{fill:"#e5e5e5"},arc:{fill:ln},area:{fill:ln},line:{stroke:ln},path:{stroke:ln},rect:{fill:ln},shape:{stroke:ln},symbol:{fill:ln,size:40},axis:{domain:!1,grid:!0,gridColor:"#FFFFFF",gridOpacity:1,labelColor:"#7F7F7F",labelPadding:4,tickColor:"#7F7F7F",tickSize:5.67,titleFontSize:16,titleFontWeight:"normal"},legend:{labelBaseline:"middle",labelFontSize:11,symbolSize:40},range:{category:["#000000","#7F7F7F","#1A1A1A","#999999","#333333","#B0B0B0","#4D4D4D","#C9C9C9","#666666","#DCDCDC"]}},KE=22,QE="normal",hl="Benton Gothic, sans-serif",ml=11.5,JE="normal",un="#82c6df",js="Benton Gothic Bold, sans-serif",yl="normal",bl=13,$i={"category-6":["#ec8431","#829eb1","#c89d29","#3580b1","#adc839","#ab7fb4"],"fire-7":["#fbf2c7","#f9e39c","#f8d36e","#f4bb6a","#e68a4f","#d15a40","#ab4232"],"fireandice-6":["#e68a4f","#f4bb6a","#f9e39c","#dadfe2","#a6b7c6","#849eae"],"ice-7":["#edefee","#dadfe2","#c4ccd2","#a6b7c6","#849eae","#607785","#47525d"]},ZE={background:"#ffffff",title:{anchor:"start",color:"#000000",font:js,fontSize:KE,fontWeight:QE},arc:{fill:un},area:{fill:un},line:{stroke:un,strokeWidth:2},path:{stroke:un},rect:{fill:un},shape:{stroke:un},symbol:{fill:un,size:30},axis:{labelFont:hl,labelFontSize:ml,labelFontWeight:JE,titleFont:js,titleFontSize:bl,titleFontWeight:yl},axisX:{labelAngle:0,labelPadding:4,tickSize:3},axisY:{labelBaseline:"middle",maxExtent:45,minExtent:45,tickSize:2,titleAlign:"left",titleAngle:0,titleX:-45,titleY:-11},legend:{labelFont:hl,labelFontSize:ml,symbolType:"square",titleFont:js,titleFontSize:bl,titleFontWeight:yl},range:{category:$i["category-6"],diverging:$i["fireandice-6"],heatmap:$i["fire-7"],ordinal:$i["fire-7"],ramp:$i["fire-7"]}},fn="#ab5787",lr="#979797",e$={background:"#f9f9f9",arc:{fill:fn},area:{fill:fn},line:{stroke:fn},path:{stroke:fn},rect:{fill:fn},shape:{stroke:fn},symbol:{fill:fn,size:30},axis:{domainColor:lr,domainWidth:.5,gridWidth:.2,labelColor:lr,tickColor:lr,tickWidth:.2,titleColor:lr},axisBand:{grid:!1},axisX:{grid:!0,tickSize:10},axisY:{domain:!1,grid:!0,tickSize:0},legend:{labelFontSize:11,padding:1,symbolSize:30,symbolType:"square"},range:{category:["#ab5787","#51b2e5","#703c5c","#168dd9","#d190b6","#00609f","#d365ba","#154866","#666666","#c4c4c4"]}},dn="#3e5c69",t$={background:"#fff",arc:{fill:dn},area:{fill:dn},line:{stroke:dn},path:{stroke:dn},rect:{fill:dn},shape:{stroke:dn},symbol:{fill:dn},axis:{domainWidth:.5,grid:!0,labelPadding:2,tickSize:5,tickWidth:.5,titleFontWeight:"normal"},axisBand:{grid:!1},axisX:{gridWidth:.2},axisY:{gridDash:[3],gridWidth:.4},legend:{labelFontSize:11,padding:1,symbolType:"square"},range:{category:["#3e5c69","#6793a6","#182429","#0570b0","#3690c0","#74a9cf","#a6bddb","#e2ddf2"]}},je="#1696d2",xl="#000000",n$="#FFFFFF",ur="Lato",Ms="Lato",i$="Lato",r$="#DEDDDD",s$=18,wi={"main-colors":["#1696d2","#d2d2d2","#000000","#fdbf11","#ec008b","#55b748","#5c5859","#db2b27"],"shades-blue":["#CFE8F3","#A2D4EC","#73BFE2","#46ABDB","#1696D2","#12719E","#0A4C6A","#062635"],"shades-gray":["#F5F5F5","#ECECEC","#E3E3E3","#DCDBDB","#D2D2D2","#9D9D9D","#696969","#353535"],"shades-yellow":["#FFF2CF","#FCE39E","#FDD870","#FCCB41","#FDBF11","#E88E2D","#CA5800","#843215"],"shades-magenta":["#F5CBDF","#EB99C2","#E46AA7","#E54096","#EC008B","#AF1F6B","#761548","#351123"],"shades-green":["#DCEDD9","#BCDEB4","#98CF90","#78C26D","#55B748","#408941","#2C5C2D","#1A2E19"],"shades-black":["#D5D5D4","#ADABAC","#848081","#5C5859","#332D2F","#262223","#1A1717","#0E0C0D"],"shades-red":["#F8D5D4","#F1AAA9","#E9807D","#E25552","#DB2B27","#A4201D","#6E1614","#370B0A"],"one-group":["#1696d2","#000000"],"two-groups-cat-1":["#1696d2","#000000"],"two-groups-cat-2":["#1696d2","#fdbf11"],"two-groups-cat-3":["#1696d2","#db2b27"],"two-groups-seq":["#a2d4ec","#1696d2"],"three-groups-cat":["#1696d2","#fdbf11","#000000"],"three-groups-seq":["#a2d4ec","#1696d2","#0a4c6a"],"four-groups-cat-1":["#000000","#d2d2d2","#fdbf11","#1696d2"],"four-groups-cat-2":["#1696d2","#ec0008b","#fdbf11","#5c5859"],"four-groups-seq":["#cfe8f3","#73bf42","#1696d2","#0a4c6a"],"five-groups-cat-1":["#1696d2","#fdbf11","#d2d2d2","#ec008b","#000000"],"five-groups-cat-2":["#1696d2","#0a4c6a","#d2d2d2","#fdbf11","#332d2f"],"five-groups-seq":["#cfe8f3","#73bf42","#1696d2","#0a4c6a","#000000"],"six-groups-cat-1":["#1696d2","#ec008b","#fdbf11","#000000","#d2d2d2","#55b748"],"six-groups-cat-2":["#1696d2","#d2d2d2","#ec008b","#fdbf11","#332d2f","#0a4c6a"],"six-groups-seq":["#cfe8f3","#a2d4ec","#73bfe2","#46abdb","#1696d2","#12719e"],"diverging-colors":["#ca5800","#fdbf11","#fdd870","#fff2cf","#cfe8f3","#73bfe2","#1696d2","#0a4c6a"]},o$={background:n$,title:{anchor:"start",fontSize:s$,font:ur},axisX:{domain:!0,domainColor:xl,domainWidth:1,grid:!1,labelFontSize:12,labelFont:Ms,labelAngle:0,tickColor:xl,tickSize:5,titleFontSize:12,titlePadding:10,titleFont:ur},axisY:{domain:!1,domainWidth:1,grid:!0,gridColor:r$,gridWidth:1,labelFontSize:12,labelFont:Ms,labelPadding:8,ticks:!1,titleFontSize:12,titlePadding:10,titleFont:ur,titleAngle:0,titleY:-10,titleX:18},legend:{labelFontSize:12,labelFont:Ms,symbolSize:100,titleFontSize:12,titlePadding:10,titleFont:ur,orient:"right",offset:10},view:{stroke:"transparent"},range:{category:wi["six-groups-cat-1"],diverging:wi["diverging-colors"],heatmap:wi["diverging-colors"],ordinal:wi["six-groups-seq"],ramp:wi["shades-blue"]},area:{fill:je},rect:{fill:je},line:{color:je,stroke:je,strokeWidth:5},trail:{color:je,stroke:je,strokeWidth:0,size:1},path:{stroke:je,strokeWidth:.5},point:{filled:!0},text:{font:i$,color:je,fontSize:11,align:"center",fontWeight:400,size:11},style:{bar:{fill:je,stroke:null}},arc:{fill:je},shape:{stroke:je},symbol:{fill:je,size:30}},pn="#3366CC",vl="#ccc",fr="Arial, sans-serif",a$={arc:{fill:pn},area:{fill:pn},path:{stroke:pn},rect:{fill:pn},shape:{stroke:pn},symbol:{stroke:pn},circle:{fill:pn},background:"#fff",padding:{top:10,right:10,bottom:10,left:10},style:{"guide-label":{font:fr,fontSize:12},"guide-title":{font:fr,fontSize:12},"group-title":{font:fr,fontSize:12}},title:{font:fr,fontSize:14,fontWeight:"bold",dy:-3,anchor:"start"},axis:{gridColor:vl,tickColor:vl,domain:!1,grid:!0},range:{category:["#4285F4","#DB4437","#F4B400","#0F9D58","#AB47BC","#00ACC1","#FF7043","#9E9D24","#5C6BC0","#F06292","#00796B","#C2185B"],heatmap:["#c6dafc","#5e97f6","#2a56c6"]}},Ha=e=>e*(1/3+1),Sl=Ha(9),El=Ha(10),$l=Ha(12),Ci="Segoe UI",wl="wf_standard-font, helvetica, arial, sans-serif",Cl="#252423",Ni="#605E5C",Nl="transparent",c$="#C8C6C4",qe="#118DFF",l$="#12239E",u$="#E66C37",f$="#6B007B",d$="#E044A7",p$="#744EC2",g$="#D9B300",h$="#D64550",up=qe,fp="#DEEFFF",Fl=[fp,up],m$=[fp,"#c7e4ff","#b0d9ff","#9aceff","#83c3ff","#6cb9ff","#55aeff","#3fa3ff","#2898ff",up],y$={view:{stroke:Nl},background:Nl,font:Ci,header:{titleFont:wl,titleFontSize:$l,titleColor:Cl,labelFont:Ci,labelFontSize:El,labelColor:Ni},axis:{ticks:!1,grid:!1,domain:!1,labelColor:Ni,labelFontSize:Sl,titleFont:wl,titleColor:Cl,titleFontSize:$l,titleFontWeight:"normal"},axisQuantitative:{tickCount:3,grid:!0,gridColor:c$,gridDash:[1,5],labelFlush:!1},axisBand:{tickExtra:!0},axisX:{labelPadding:5},axisY:{labelPadding:10},bar:{fill:qe},line:{stroke:qe,strokeWidth:3,strokeCap:"round",strokeJoin:"round"},text:{font:Ci,fontSize:Sl,fill:Ni},arc:{fill:qe},area:{fill:qe,line:!0,opacity:.6},path:{stroke:qe},rect:{fill:qe},point:{fill:qe,filled:!0,size:75},shape:{stroke:qe},symbol:{fill:qe,strokeWidth:1.5,size:50},legend:{titleFont:Ci,titleFontWeight:"bold",titleColor:Ni,labelFont:Ci,labelFontSize:El,labelColor:Ni,symbolType:"circle",symbolSize:75},range:{category:[qe,l$,u$,f$,d$,p$,g$,h$],diverging:Fl,heatmap:Fl,ordinal:m$}},Us='IBM Plex Sans,system-ui,-apple-system,BlinkMacSystemFont,".sfnstext-regular",sans-serif',kl=400,b$=["#8a3ffc","#33b1ff","#007d79","#ff7eb6","#fa4d56","#fff1f1","#6fdc8c","#4589ff","#d12771","#d2a106","#08bdba","#bae6ff","#ba4e00","#d4bbff"],x$=["#6929c4","#1192e8","#005d5d","#9f1853","#fa4d56","#570408","#198038","#002d9c","#ee538b","#b28600","#009d9a","#012749","#8a3800","#a56eff"];function $s({type:e,background:t}){const n=e==="dark"?"#161616":"#ffffff",i=e==="dark"?"#f4f4f4":"#161616",r=e==="dark"?b$:x$,s=e==="dark"?"#d4bbff":"#6929c4";return{background:t,arc:{fill:s},area:{fill:s},path:{stroke:s},rect:{fill:s},shape:{stroke:s},symbol:{stroke:s},circle:{fill:s},view:{fill:n,stroke:n},group:{fill:n},title:{color:i,anchor:"start",dy:-15,fontSize:16,font:Us,fontWeight:600},axis:{labelColor:i,labelFontSize:12,grid:!0,gridColor:"#525252",titleColor:i,labelAngle:0},style:{"guide-label":{font:Us,fill:i,fontWeight:kl},"guide-title":{font:Us,fill:i,fontWeight:kl}},range:{category:r,diverging:["#750e13","#a2191f","#da1e28","#fa4d56","#ff8389","#ffb3b8","#ffd7d9","#fff1f1","#e5f6ff","#bae6ff","#82cfff","#33b1ff","#1192e8","#0072c3","#00539a","#003a6d"],heatmap:["#f6f2ff","#e8daff","#d4bbff","#be95ff","#a56eff","#8a3ffc","#6929c4","#491d8b","#31135e","#1c0f30"]}}}const v$=$s({type:"light",background:"#ffffff"}),S$=$s({type:"light",background:"#f4f4f4"}),E$=$s({type:"dark",background:"#262626"}),$$=$s({type:"dark",background:"#161616"}),w$=WE.version,C$=Object.freeze(Object.defineProperty({__proto__:null,carbong10:S$,carbong100:$$,carbong90:E$,carbonwhite:v$,dark:GE,excel:HE,fivethirtyeight:XE,ggplot2:YE,googlecharts:a$,latimes:ZE,powerbi:y$,quartz:e$,urbaninstitute:o$,version:w$,vox:t$},Symbol.toStringTag,{value:"Module"}));var Bs={};function N$(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ws,Tl;function F$(){return Tl||(Tl=1,Ws=function(e){e.prototype[Symbol.iterator]=function*(){for(let t=this.head;t;t=t.next)yield t.value}}),Ws}var k$=H;H.Node=An;H.create=H;function H(e){var t=this;if(t instanceof H||(t=new H),t.tail=null,t.head=null,t.length=0,e&&typeof e.forEach=="function")e.forEach(function(r){t.push(r)});else if(arguments.length>0)for(var n=0,i=arguments.length;n<i;n++)t.push(arguments[n]);return t}H.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,n=e.prev;return t&&(t.prev=n),n&&(n.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=n),e.list.length--,e.next=null,e.prev=null,e.list=null,t};H.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}};H.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}};H.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)A$(this,arguments[e]);return this.length};H.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)O$(this,arguments[e]);return this.length};H.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}};H.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}};H.prototype.forEach=function(e,t){t=t||this;for(var n=this.head,i=0;n!==null;i++)e.call(t,n.value,i,this),n=n.next};H.prototype.forEachReverse=function(e,t){t=t||this;for(var n=this.tail,i=this.length-1;n!==null;i--)e.call(t,n.value,i,this),n=n.prev};H.prototype.get=function(e){for(var t=0,n=this.head;n!==null&&t<e;t++)n=n.next;if(t===e&&n!==null)return n.value};H.prototype.getReverse=function(e){for(var t=0,n=this.tail;n!==null&&t<e;t++)n=n.prev;if(t===e&&n!==null)return n.value};H.prototype.map=function(e,t){t=t||this;for(var n=new H,i=this.head;i!==null;)n.push(e.call(t,i.value,this)),i=i.next;return n};H.prototype.mapReverse=function(e,t){t=t||this;for(var n=new H,i=this.tail;i!==null;)n.push(e.call(t,i.value,this)),i=i.prev;return n};H.prototype.reduce=function(e,t){var n,i=this.head;if(arguments.length>1)n=t;else if(this.head)i=this.head.next,n=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var r=0;i!==null;r++)n=e(n,i.value,r),i=i.next;return n};H.prototype.reduceReverse=function(e,t){var n,i=this.tail;if(arguments.length>1)n=t;else if(this.tail)i=this.tail.prev,n=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var r=this.length-1;i!==null;r--)n=e(n,i.value,r),i=i.prev;return n};H.prototype.toArray=function(){for(var e=new Array(this.length),t=0,n=this.head;n!==null;t++)e[t]=n.value,n=n.next;return e};H.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,n=this.tail;n!==null;t++)e[t]=n.value,n=n.prev;return e};H.prototype.slice=function(e,t){t=t||this.length,t<0&&(t+=this.length),e=e||0,e<0&&(e+=this.length);var n=new H;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=0,r=this.head;r!==null&&i<e;i++)r=r.next;for(;r!==null&&i<t;i++,r=r.next)n.push(r.value);return n};H.prototype.sliceReverse=function(e,t){t=t||this.length,t<0&&(t+=this.length),e=e||0,e<0&&(e+=this.length);var n=new H;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=this.length,r=this.tail;r!==null&&i>t;i--)r=r.prev;for(;r!==null&&i>e;i--,r=r.prev)n.push(r.value);return n};H.prototype.splice=function(e,t,...n){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var i=0,r=this.head;r!==null&&i<e;i++)r=r.next;for(var s=[],i=0;r&&i<t;i++)s.push(r.value),r=this.removeNode(r);r===null&&(r=this.tail),r!==this.head&&r!==this.tail&&(r=r.prev);for(var i=0;i<n.length;i++)r=T$(this,r,n[i]);return s};H.prototype.reverse=function(){for(var e=this.head,t=this.tail,n=e;n!==null;n=n.prev){var i=n.prev;n.prev=n.next,n.next=i}return this.head=t,this.tail=e,this};function T$(e,t,n){var i=t===e.head?new An(n,null,t,e):new An(n,t,t.next,e);return i.next===null&&(e.tail=i),i.prev===null&&(e.head=i),e.length++,i}function A$(e,t){e.tail=new An(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++}function O$(e,t){e.head=new An(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++}function An(e,t,n,i){if(!(this instanceof An))return new An(e,t,n,i);this.list=i,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,n?(n.prev=this,this.next=n):this.next=null}try{F$()(H)}catch{}const _$=k$,gn=Symbol("max"),wt=Symbol("length"),Bn=Symbol("lengthCalculator"),Ii=Symbol("allowStale"),hn=Symbol("maxAge"),$t=Symbol("dispose"),Al=Symbol("noDisposeOnSet"),pe=Symbol("lruList"),Ve=Symbol("cache"),dp=Symbol("updateAgeOnGet"),Gs=()=>1;class R${constructor(t){if(typeof t=="number"&&(t={max:t}),t||(t={}),t.max&&(typeof t.max!="number"||t.max<0))throw new TypeError("max must be a non-negative number");this[gn]=t.max||1/0;const n=t.length||Gs;if(this[Bn]=typeof n!="function"?Gs:n,this[Ii]=t.stale||!1,t.maxAge&&typeof t.maxAge!="number")throw new TypeError("maxAge must be a number");this[hn]=t.maxAge||0,this[$t]=t.dispose,this[Al]=t.noDisposeOnSet||!1,this[dp]=t.updateAgeOnGet||!1,this.reset()}set max(t){if(typeof t!="number"||t<0)throw new TypeError("max must be a non-negative number");this[gn]=t||1/0,Fi(this)}get max(){return this[gn]}set allowStale(t){this[Ii]=!!t}get allowStale(){return this[Ii]}set maxAge(t){if(typeof t!="number")throw new TypeError("maxAge must be a non-negative number");this[hn]=t,Fi(this)}get maxAge(){return this[hn]}set lengthCalculator(t){typeof t!="function"&&(t=Gs),t!==this[Bn]&&(this[Bn]=t,this[wt]=0,this[pe].forEach(n=>{n.length=this[Bn](n.value,n.key),this[wt]+=n.length})),Fi(this)}get lengthCalculator(){return this[Bn]}get length(){return this[wt]}get itemCount(){return this[pe].length}rforEach(t,n){n=n||this;for(let i=this[pe].tail;i!==null;){const r=i.prev;Ol(this,t,i,n),i=r}}forEach(t,n){n=n||this;for(let i=this[pe].head;i!==null;){const r=i.next;Ol(this,t,i,n),i=r}}keys(){return this[pe].toArray().map(t=>t.key)}values(){return this[pe].toArray().map(t=>t.value)}reset(){this[$t]&&this[pe]&&this[pe].length&&this[pe].forEach(t=>this[$t](t.key,t.value)),this[Ve]=new Map,this[pe]=new _$,this[wt]=0}dump(){return this[pe].map(t=>Or(this,t)?!1:{k:t.key,v:t.value,e:t.now+(t.maxAge||0)}).toArray().filter(t=>t)}dumpLru(){return this[pe]}set(t,n,i){if(i=i||this[hn],i&&typeof i!="number")throw new TypeError("maxAge must be a number");const r=i?Date.now():0,s=this[Bn](n,t);if(this[Ve].has(t)){if(s>this[gn])return Kn(this,this[Ve].get(t)),!1;const c=this[Ve].get(t).value;return this[$t]&&(this[Al]||this[$t](t,c.value)),c.now=r,c.maxAge=i,c.value=n,this[wt]+=s-c.length,c.length=s,this.get(t),Fi(this),!0}const o=new I$(t,n,s,r,i);return o.length>this[gn]?(this[$t]&&this[$t](t,n),!1):(this[wt]+=o.length,this[pe].unshift(o),this[Ve].set(t,this[pe].head),Fi(this),!0)}has(t){if(!this[Ve].has(t))return!1;const n=this[Ve].get(t).value;return!Or(this,n)}get(t){return Hs(this,t,!0)}peek(t){return Hs(this,t,!1)}pop(){const t=this[pe].tail;return t?(Kn(this,t),t.value):null}del(t){Kn(this,this[Ve].get(t))}load(t){this.reset();const n=Date.now();for(let i=t.length-1;i>=0;i--){const r=t[i],s=r.e||0;if(s===0)this.set(r.k,r.v);else{const o=s-n;o>0&&this.set(r.k,r.v,o)}}}prune(){this[Ve].forEach((t,n)=>Hs(this,n,!1))}}const Hs=(e,t,n)=>{const i=e[Ve].get(t);if(i){const r=i.value;if(Or(e,r)){if(Kn(e,i),!e[Ii])return}else n&&(e[dp]&&(i.value.now=Date.now()),e[pe].unshiftNode(i));return r.value}},Or=(e,t)=>{if(!t||!t.maxAge&&!e[hn])return!1;const n=Date.now()-t.now;return t.maxAge?n>t.maxAge:e[hn]&&n>e[hn]},Fi=e=>{if(e[wt]>e[gn])for(let t=e[pe].tail;e[wt]>e[gn]&&t!==null;){const n=t.prev;Kn(e,t),t=n}},Kn=(e,t)=>{if(t){const n=t.value;e[$t]&&e[$t](n.key,n.value),e[wt]-=n.length,e[Ve].delete(n.key),e[pe].removeNode(t)}};class I${constructor(t,n,i,r,s){this.key=t,this.value=n,this.length=i,this.now=r,this.maxAge=s||0}}const Ol=(e,t,n,i)=>{let r=n.value;Or(e,r)&&(Kn(e,n),e[Ii]||(r=void 0)),r&&t.call(i,r.value,r.key,e)};var L$=R$;const P$=Object.freeze({loose:!0}),z$=Object.freeze({}),D$=e=>e?typeof e!="object"?P$:e:z$;var qa=D$,bo={exports:{}};const j$="2.0.0",pp=256,M$=Number.MAX_SAFE_INTEGER||9007199254740991,U$=16,B$=pp-6,W$=["major","premajor","minor","preminor","patch","prepatch","prerelease"];var Va={MAX_LENGTH:pp,MAX_SAFE_COMPONENT_LENGTH:U$,MAX_SAFE_BUILD_LENGTH:B$,MAX_SAFE_INTEGER:M$,RELEASE_TYPES:W$,SEMVER_SPEC_VERSION:j$,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2};const G$=typeof process=="object"&&Bs&&Bs.NODE_DEBUG&&/\bsemver\b/i.test(Bs.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var ws=G$;(function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:r}=Va,s=ws;t=e.exports={};const o=t.re=[],a=t.safeRe=[],c=t.src=[],l=t.t={};let u=0;const f="[a-zA-Z0-9-]",d=[["\\s",1],["\\d",r],[f,i]],g=h=>{for(const[m,y]of d)h=h.split(`${m}*`).join(`${m}{0,${y}}`).split(`${m}+`).join(`${m}{1,${y}}`);return h},p=(h,m,y)=>{const b=g(m),C=u++;s(h,C,m),l[h]=C,c[C]=m,o[C]=new RegExp(m,y?"g":void 0),a[C]=new RegExp(b,y?"g":void 0)};p("NUMERICIDENTIFIER","0|[1-9]\\d*"),p("NUMERICIDENTIFIERLOOSE","\\d+"),p("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${f}*`),p("MAINVERSION",`(${c[l.NUMERICIDENTIFIER]})\\.(${c[l.NUMERICIDENTIFIER]})\\.(${c[l.NUMERICIDENTIFIER]})`),p("MAINVERSIONLOOSE",`(${c[l.NUMERICIDENTIFIERLOOSE]})\\.(${c[l.NUMERICIDENTIFIERLOOSE]})\\.(${c[l.NUMERICIDENTIFIERLOOSE]})`),p("PRERELEASEIDENTIFIER",`(?:${c[l.NUMERICIDENTIFIER]}|${c[l.NONNUMERICIDENTIFIER]})`),p("PRERELEASEIDENTIFIERLOOSE",`(?:${c[l.NUMERICIDENTIFIERLOOSE]}|${c[l.NONNUMERICIDENTIFIER]})`),p("PRERELEASE",`(?:-(${c[l.PRERELEASEIDENTIFIER]}(?:\\.${c[l.PRERELEASEIDENTIFIER]})*))`),p("PRERELEASELOOSE",`(?:-?(${c[l.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[l.PRERELEASEIDENTIFIERLOOSE]})*))`),p("BUILDIDENTIFIER",`${f}+`),p("BUILD",`(?:\\+(${c[l.BUILDIDENTIFIER]}(?:\\.${c[l.BUILDIDENTIFIER]})*))`),p("FULLPLAIN",`v?${c[l.MAINVERSION]}${c[l.PRERELEASE]}?${c[l.BUILD]}?`),p("FULL",`^${c[l.FULLPLAIN]}$`),p("LOOSEPLAIN",`[v=\\s]*${c[l.MAINVERSIONLOOSE]}${c[l.PRERELEASELOOSE]}?${c[l.BUILD]}?`),p("LOOSE",`^${c[l.LOOSEPLAIN]}$`),p("GTLT","((?:<|>)?=?)"),p("XRANGEIDENTIFIERLOOSE",`${c[l.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),p("XRANGEIDENTIFIER",`${c[l.NUMERICIDENTIFIER]}|x|X|\\*`),p("XRANGEPLAIN",`[v=\\s]*(${c[l.XRANGEIDENTIFIER]})(?:\\.(${c[l.XRANGEIDENTIFIER]})(?:\\.(${c[l.XRANGEIDENTIFIER]})(?:${c[l.PRERELEASE]})?${c[l.BUILD]}?)?)?`),p("XRANGEPLAINLOOSE",`[v=\\s]*(${c[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[l.XRANGEIDENTIFIERLOOSE]})(?:${c[l.PRERELEASELOOSE]})?${c[l.BUILD]}?)?)?`),p("XRANGE",`^${c[l.GTLT]}\\s*${c[l.XRANGEPLAIN]}$`),p("XRANGELOOSE",`^${c[l.GTLT]}\\s*${c[l.XRANGEPLAINLOOSE]}$`),p("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),p("COERCE",`${c[l.COERCEPLAIN]}(?:$|[^\\d])`),p("COERCEFULL",c[l.COERCEPLAIN]+`(?:${c[l.PRERELEASE]})?(?:${c[l.BUILD]})?(?:$|[^\\d])`),p("COERCERTL",c[l.COERCE],!0),p("COERCERTLFULL",c[l.COERCEFULL],!0),p("LONETILDE","(?:~>?)"),p("TILDETRIM",`(\\s*)${c[l.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",p("TILDE",`^${c[l.LONETILDE]}${c[l.XRANGEPLAIN]}$`),p("TILDELOOSE",`^${c[l.LONETILDE]}${c[l.XRANGEPLAINLOOSE]}$`),p("LONECARET","(?:\\^)"),p("CARETTRIM",`(\\s*)${c[l.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",p("CARET",`^${c[l.LONECARET]}${c[l.XRANGEPLAIN]}$`),p("CARETLOOSE",`^${c[l.LONECARET]}${c[l.XRANGEPLAINLOOSE]}$`),p("COMPARATORLOOSE",`^${c[l.GTLT]}\\s*(${c[l.LOOSEPLAIN]})$|^$`),p("COMPARATOR",`^${c[l.GTLT]}\\s*(${c[l.FULLPLAIN]})$|^$`),p("COMPARATORTRIM",`(\\s*)${c[l.GTLT]}\\s*(${c[l.LOOSEPLAIN]}|${c[l.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",p("HYPHENRANGE",`^\\s*(${c[l.XRANGEPLAIN]})\\s+-\\s+(${c[l.XRANGEPLAIN]})\\s*$`),p("HYPHENRANGELOOSE",`^\\s*(${c[l.XRANGEPLAINLOOSE]})\\s+-\\s+(${c[l.XRANGEPLAINLOOSE]})\\s*$`),p("STAR","(<|>)?=?\\s*\\*"),p("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),p("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")})(bo,bo.exports);var Xa=bo.exports;const _l=/^[0-9]+$/,gp=(e,t)=>{const n=_l.test(e),i=_l.test(t);return n&&i&&(e=+e,t=+t),e===t?0:n&&!i?-1:i&&!n?1:e<t?-1:1},H$=(e,t)=>gp(t,e);var q$={compareIdentifiers:gp,rcompareIdentifiers:H$};const dr=ws,{MAX_LENGTH:Rl,MAX_SAFE_INTEGER:pr}=Va,{safeRe:Il,t:Ll}=Xa,V$=qa,{compareIdentifiers:Wn}=q$;let X$=class ot{constructor(t,n){if(n=V$(n),t instanceof ot){if(t.loose===!!n.loose&&t.includePrerelease===!!n.includePrerelease)return t;t=t.version}else if(typeof t!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof t}".`);if(t.length>Rl)throw new TypeError(`version is longer than ${Rl} characters`);dr("SemVer",t,n),this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease;const i=t.trim().match(n.loose?Il[Ll.LOOSE]:Il[Ll.FULL]);if(!i)throw new TypeError(`Invalid Version: ${t}`);if(this.raw=t,this.major=+i[1],this.minor=+i[2],this.patch=+i[3],this.major>pr||this.major<0)throw new TypeError("Invalid major version");if(this.minor>pr||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>pr||this.patch<0)throw new TypeError("Invalid patch version");i[4]?this.prerelease=i[4].split(".").map(r=>{if(/^[0-9]+$/.test(r)){const s=+r;if(s>=0&&s<pr)return s}return r}):this.prerelease=[],this.build=i[5]?i[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(dr("SemVer.compare",this.version,this.options,t),!(t instanceof ot)){if(typeof t=="string"&&t===this.version)return 0;t=new ot(t,this.options)}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(t){return t instanceof ot||(t=new ot(t,this.options)),Wn(this.major,t.major)||Wn(this.minor,t.minor)||Wn(this.patch,t.patch)}comparePre(t){if(t instanceof ot||(t=new ot(t,this.options)),this.prerelease.length&&!t.prerelease.length)return-1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let n=0;do{const i=this.prerelease[n],r=t.prerelease[n];if(dr("prerelease compare",n,i,r),i===void 0&&r===void 0)return 0;if(r===void 0)return 1;if(i===void 0)return-1;if(i===r)continue;return Wn(i,r)}while(++n)}compareBuild(t){t instanceof ot||(t=new ot(t,this.options));let n=0;do{const i=this.build[n],r=t.build[n];if(dr("prerelease compare",n,i,r),i===void 0&&r===void 0)return 0;if(r===void 0)return 1;if(i===void 0)return-1;if(i===r)continue;return Wn(i,r)}while(++n)}inc(t,n,i){switch(t){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",n,i);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",n,i);break;case"prepatch":this.prerelease.length=0,this.inc("patch",n,i),this.inc("pre",n,i);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",n,i),this.inc("pre",n,i);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const r=Number(i)?1:0;if(!n&&i===!1)throw new Error("invalid increment argument: identifier is empty");if(this.prerelease.length===0)this.prerelease=[r];else{let s=this.prerelease.length;for(;--s>=0;)typeof this.prerelease[s]=="number"&&(this.prerelease[s]++,s=-2);if(s===-1){if(n===this.prerelease.join(".")&&i===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(r)}}if(n){let s=[n,r];i===!1&&(s=[n]),Wn(this.prerelease[0],n)===0?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw new Error(`invalid increment argument: ${t}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};var Ya=X$;const Pl=Ya,Y$=(e,t,n)=>new Pl(e,n).compare(new Pl(t,n));var Si=Y$;const K$=Si,Q$=(e,t,n)=>K$(e,t,n)===0;var J$=Q$;const Z$=Si,e1=(e,t,n)=>Z$(e,t,n)!==0;var t1=e1;const n1=Si,i1=(e,t,n)=>n1(e,t,n)>0;var r1=i1;const s1=Si,o1=(e,t,n)=>s1(e,t,n)>=0;var a1=o1;const c1=Si,l1=(e,t,n)=>c1(e,t,n)<0;var u1=l1;const f1=Si,d1=(e,t,n)=>f1(e,t,n)<=0;var p1=d1;const g1=J$,h1=t1,m1=r1,y1=a1,b1=u1,x1=p1,v1=(e,t,n,i)=>{switch(t){case"===":return typeof e=="object"&&(e=e.version),typeof n=="object"&&(n=n.version),e===n;case"!==":return typeof e=="object"&&(e=e.version),typeof n=="object"&&(n=n.version),e!==n;case"":case"=":case"==":return g1(e,n,i);case"!=":return h1(e,n,i);case">":return m1(e,n,i);case">=":return y1(e,n,i);case"<":return b1(e,n,i);case"<=":return x1(e,n,i);default:throw new TypeError(`Invalid operator: ${t}`)}};var S1=v1,qs,zl;function E1(){if(zl)return qs;zl=1;const e=Symbol("SemVer ANY");class t{static get ANY(){return e}constructor(u,f){if(f=n(f),u instanceof t){if(u.loose===!!f.loose)return u;u=u.value}u=u.trim().split(/\s+/).join(" "),o("comparator",u,f),this.options=f,this.loose=!!f.loose,this.parse(u),this.semver===e?this.value="":this.value=this.operator+this.semver.version,o("comp",this)}parse(u){const f=this.options.loose?i[r.COMPARATORLOOSE]:i[r.COMPARATOR],d=u.match(f);if(!d)throw new TypeError(`Invalid comparator: ${u}`);this.operator=d[1]!==void 0?d[1]:"",this.operator==="="&&(this.operator=""),d[2]?this.semver=new a(d[2],this.options.loose):this.semver=e}toString(){return this.value}test(u){if(o("Comparator.test",u,this.options.loose),this.semver===e||u===e)return!0;if(typeof u=="string")try{u=new a(u,this.options)}catch{return!1}return s(u,this.operator,this.semver,this.options)}intersects(u,f){if(!(u instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new c(u.value,f).test(this.value):u.operator===""?u.value===""?!0:new c(this.value,f).test(u.semver):(f=n(f),f.includePrerelease&&(this.value==="<0.0.0-0"||u.value==="<0.0.0-0")||!f.includePrerelease&&(this.value.startsWith("<0.0.0")||u.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&u.operator.startsWith(">")||this.operator.startsWith("<")&&u.operator.startsWith("<")||this.semver.version===u.semver.version&&this.operator.includes("=")&&u.operator.includes("=")||s(this.semver,"<",u.semver,f)&&this.operator.startsWith(">")&&u.operator.startsWith("<")||s(this.semver,">",u.semver,f)&&this.operator.startsWith("<")&&u.operator.startsWith(">")))}}qs=t;const n=qa,{safeRe:i,t:r}=Xa,s=S1,o=ws,a=Ya,c=hp();return qs}var Vs,Dl;function hp(){if(Dl)return Vs;Dl=1;class e{constructor($,R){if(R=i(R),$ instanceof e)return $.loose===!!R.loose&&$.includePrerelease===!!R.includePrerelease?$:new e($.raw,R);if($ instanceof r)return this.raw=$.value,this.set=[[$]],this.format(),this;if(this.options=R,this.loose=!!R.loose,this.includePrerelease=!!R.includePrerelease,this.raw=$.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(A=>this.parseRange(A.trim())).filter(A=>A.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const A=this.set[0];if(this.set=this.set.filter(L=>!p(L[0])),this.set.length===0)this.set=[A];else if(this.set.length>1){for(const L of this.set)if(L.length===1&&h(L[0])){this.set=[L];break}}}this.format()}format(){return this.range=this.set.map($=>$.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange($){const A=((this.options.includePrerelease&&d)|(this.options.loose&&g))+":"+$,L=n.get(A);if(L)return L;const T=this.options.loose,j=T?a[c.HYPHENRANGELOOSE]:a[c.HYPHENRANGE];$=$.replace(j,Re(this.options.includePrerelease)),s("hyphen replace",$),$=$.replace(a[c.COMPARATORTRIM],l),s("comparator trim",$),$=$.replace(a[c.TILDETRIM],u),s("tilde trim",$),$=$.replace(a[c.CARETTRIM],f),s("caret trim",$);let q=$.split(" ").map(te=>y(te,this.options)).join(" ").split(/\s+/).map(te=>de(te,this.options));T&&(q=q.filter(te=>(s("loose invalid filter",te,this.options),!!te.match(a[c.COMPARATORLOOSE])))),s("range list",q);const M=new Map,J=q.map(te=>new r(te,this.options));for(const te of J){if(p(te))return[te];M.set(te.value,te)}M.size>1&&M.has("")&&M.delete("");const Se=[...M.values()];return n.set(A,Se),Se}intersects($,R){if(!($ instanceof e))throw new TypeError("a Range is required");return this.set.some(A=>m(A,R)&&$.set.some(L=>m(L,R)&&A.every(T=>L.every(j=>T.intersects(j,R)))))}test($){if(!$)return!1;if(typeof $=="string")try{$=new o($,this.options)}catch{return!1}for(let R=0;R<this.set.length;R++)if(sn(this.set[R],$,this.options))return!0;return!1}}Vs=e;const t=L$,n=new t({max:1e3}),i=qa,r=E1(),s=ws,o=Ya,{safeRe:a,t:c,comparatorTrimReplace:l,tildeTrimReplace:u,caretTrimReplace:f}=Xa,{FLAG_INCLUDE_PRERELEASE:d,FLAG_LOOSE:g}=Va,p=w=>w.value==="<0.0.0-0",h=w=>w.value==="",m=(w,$)=>{let R=!0;const A=w.slice();let L=A.pop();for(;R&&A.length;)R=A.every(T=>L.intersects(T,$)),L=A.pop();return R},y=(w,$)=>(s("comp",w,$),w=F(w,$),s("caret",w),w=C(w,$),s("tildes",w),w=P(w,$),s("xrange",w),w=oe(w,$),s("stars",w),w),b=w=>!w||w.toLowerCase()==="x"||w==="*",C=(w,$)=>w.trim().split(/\s+/).map(R=>O(R,$)).join(" "),O=(w,$)=>{const R=$.loose?a[c.TILDELOOSE]:a[c.TILDE];return w.replace(R,(A,L,T,j,q)=>{s("tilde",w,A,L,T,j,q);let M;return b(L)?M="":b(T)?M=`>=${L}.0.0 <${+L+1}.0.0-0`:b(j)?M=`>=${L}.${T}.0 <${L}.${+T+1}.0-0`:q?(s("replaceTilde pr",q),M=`>=${L}.${T}.${j}-${q} <${L}.${+T+1}.0-0`):M=`>=${L}.${T}.${j} <${L}.${+T+1}.0-0`,s("tilde return",M),M})},F=(w,$)=>w.trim().split(/\s+/).map(R=>_(R,$)).join(" "),_=(w,$)=>{s("caret",w,$);const R=$.loose?a[c.CARETLOOSE]:a[c.CARET],A=$.includePrerelease?"-0":"";return w.replace(R,(L,T,j,q,M)=>{s("caret",w,L,T,j,q,M);let J;return b(T)?J="":b(j)?J=`>=${T}.0.0${A} <${+T+1}.0.0-0`:b(q)?T==="0"?J=`>=${T}.${j}.0${A} <${T}.${+j+1}.0-0`:J=`>=${T}.${j}.0${A} <${+T+1}.0.0-0`:M?(s("replaceCaret pr",M),T==="0"?j==="0"?J=`>=${T}.${j}.${q}-${M} <${T}.${j}.${+q+1}-0`:J=`>=${T}.${j}.${q}-${M} <${T}.${+j+1}.0-0`:J=`>=${T}.${j}.${q}-${M} <${+T+1}.0.0-0`):(s("no pr"),T==="0"?j==="0"?J=`>=${T}.${j}.${q}${A} <${T}.${j}.${+q+1}-0`:J=`>=${T}.${j}.${q}${A} <${T}.${+j+1}.0-0`:J=`>=${T}.${j}.${q} <${+T+1}.0.0-0`),s("caret return",J),J})},P=(w,$)=>(s("replaceXRanges",w,$),w.split(/\s+/).map(R=>U(R,$)).join(" ")),U=(w,$)=>{w=w.trim();const R=$.loose?a[c.XRANGELOOSE]:a[c.XRANGE];return w.replace(R,(A,L,T,j,q,M)=>{s("xRange",w,A,L,T,j,q,M);const J=b(T),Se=J||b(j),te=Se||b(q),on=te;return L==="="&&on&&(L=""),M=$.includePrerelease?"-0":"",J?L===">"||L==="<"?A="<0.0.0-0":A="*":L&&on?(Se&&(j=0),q=0,L===">"?(L=">=",Se?(T=+T+1,j=0,q=0):(j=+j+1,q=0)):L==="<="&&(L="<",Se?T=+T+1:j=+j+1),L==="<"&&(M="-0"),A=`${L+T}.${j}.${q}${M}`):Se?A=`>=${T}.0.0${M} <${+T+1}.0.0-0`:te&&(A=`>=${T}.${j}.0${M} <${T}.${+j+1}.0-0`),s("xRange return",A),A})},oe=(w,$)=>(s("replaceStars",w,$),w.trim().replace(a[c.STAR],"")),de=(w,$)=>(s("replaceGTE0",w,$),w.trim().replace(a[$.includePrerelease?c.GTE0PRE:c.GTE0],"")),Re=w=>($,R,A,L,T,j,q,M,J,Se,te,on,Cs)=>(b(A)?R="":b(L)?R=`>=${A}.0.0${w?"-0":""}`:b(T)?R=`>=${A}.${L}.0${w?"-0":""}`:j?R=`>=${R}`:R=`>=${R}${w?"-0":""}`,b(J)?M="":b(Se)?M=`<${+J+1}.0.0-0`:b(te)?M=`<${J}.${+Se+1}.0-0`:on?M=`<=${J}.${Se}.${te}-${on}`:w?M=`<${J}.${Se}.${+te+1}-0`:M=`<=${M}`,`${R} ${M}`.trim()),sn=(w,$,R)=>{for(let A=0;A<w.length;A++)if(!w[A].test($))return!1;if($.prerelease.length&&!R.includePrerelease){for(let A=0;A<w.length;A++)if(s(w[A].semver),w[A].semver!==r.ANY&&w[A].semver.prerelease.length>0){const L=w[A].semver;if(L.major===$.major&&L.minor===$.minor&&L.patch===$.patch)return!0}return!1}return!0};return Vs}const $1=hp(),w1=(e,t,n)=>{try{t=new $1(t,n)}catch{return!1}return t.test(e)};var C1=w1,mp=N$(C1);function N1(e,t,n){const i=e.open(t),r=1e4,s=250,{origin:o}=new URL(t);let a=~~(r/s);function c(u){u.source===i&&(a=0,e.removeEventListener("message",c,!1))}e.addEventListener("message",c,!1);function l(){a<=0||(i.postMessage(n,o),setTimeout(l,s),a-=1)}setTimeout(l,s)}var F1=`.vega-embed {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
}
.vega-embed.has-actions {
  padding-right: 38px;
}
.vega-embed details:not([open]) > :not(summary) {
  display: none !important;
}
.vega-embed summary {
  list-style: none;
  position: absolute;
  top: 0;
  right: 0;
  padding: 6px;
  z-index: 1000;
  background: white;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
  color: #1b1e23;
  border: 1px solid #aaa;
  border-radius: 999px;
  opacity: 0.2;
  transition: opacity 0.4s ease-in;
  cursor: pointer;
  line-height: 0px;
}
.vega-embed summary::-webkit-details-marker {
  display: none;
}
.vega-embed summary:active {
  box-shadow: #aaa 0px 0px 0px 1px inset;
}
.vega-embed summary svg {
  width: 14px;
  height: 14px;
}
.vega-embed details[open] summary {
  opacity: 0.7;
}
.vega-embed:hover summary, .vega-embed:focus-within summary {
  opacity: 1 !important;
  transition: opacity 0.2s ease;
}
.vega-embed .vega-actions {
  position: absolute;
  z-index: 1001;
  top: 35px;
  right: -9px;
  display: flex;
  flex-direction: column;
  padding-bottom: 8px;
  padding-top: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid #d9d9d9;
  background: white;
  animation-duration: 0.15s;
  animation-name: scale-in;
  animation-timing-function: cubic-bezier(0.2, 0, 0.13, 1.5);
  text-align: left;
}
.vega-embed .vega-actions a {
  padding: 8px 16px;
  font-family: sans-serif;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  color: #434a56;
  text-decoration: none;
}
.vega-embed .vega-actions a:hover, .vega-embed .vega-actions a:focus {
  background-color: #f7f7f9;
  color: black;
}
.vega-embed .vega-actions::before, .vega-embed .vega-actions::after {
  content: "";
  display: inline-block;
  position: absolute;
}
.vega-embed .vega-actions::before {
  left: auto;
  right: 14px;
  top: -16px;
  border: 8px solid rgba(0, 0, 0, 0);
  border-bottom-color: #d9d9d9;
}
.vega-embed .vega-actions::after {
  left: auto;
  right: 15px;
  top: -14px;
  border: 7px solid rgba(0, 0, 0, 0);
  border-bottom-color: #fff;
}
.vega-embed .chart-wrapper.fit-x {
  width: 100%;
}
.vega-embed .chart-wrapper.fit-y {
  height: 100%;
}

.vega-embed-wrapper {
  max-width: 100%;
  overflow: auto;
  padding-right: 14px;
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.6);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
`;function yp(e,...t){for(const n of t)k1(e,n);return e}function k1(e,t){for(const n of Object.keys(t))xo(e,n,t[n],!0)}var T1="vega-embed",A1="6.25.0",O1="Publish Vega visualizations as embedded web components.",_1=["vega","data","visualization","component","embed"],R1={type:"git",url:"http://github.com/vega/vega-embed.git"},I1={name:"UW Interactive Data Lab",url:"http://idl.cs.washington.edu"},L1=[{name:"Dominik Moritz",url:"https://www.domoritz.de"}],P1={url:"https://github.com/vega/vega-embed/issues"},z1="https://github.com/vega/vega-embed#readme",D1="BSD-3-Clause",j1="build/vega-embed.js",M1="build/vega-embed.module.js",U1="build/vega-embed.min.js",B1="build/vega-embed.min.js",W1="build/vega-embed.module.d.ts",G1=["src","build"],H1={"@babel/core":"^7.24.4","@babel/plugin-transform-runtime":"^7.24.3","@babel/preset-env":"^7.24.4","@babel/preset-typescript":"^7.24.1","@release-it/conventional-changelog":"^8.0.1","@rollup/plugin-commonjs":"25.0.7","@rollup/plugin-json":"^6.1.0","@rollup/plugin-node-resolve":"^15.2.3","@rollup/plugin-terser":"^0.4.4","@types/jest":"^29.5.12","@types/semver":"^7.5.8","@typescript-eslint/eslint-plugin":"^7.6.0","@typescript-eslint/parser":"^7.6.0","browser-sync":"^3.0.2",concurrently:"^8.2.2","del-cli":"^5.1.0",eslint:"^8.56.0","eslint-config-prettier":"^9.1.0","eslint-plugin-jest":"^28.2.0","eslint-plugin-prettier":"^5.1.3",jest:"^29.7.0","jest-canvas-mock":"^2.5.2","jest-environment-jsdom":"^29.7.0","postinstall-postinstall":"^2.1.0",prettier:"^3.2.5","release-it":"^17.1.1",rollup:"4.14.1","rollup-plugin-bundle-size":"^1.0.3","rollup-plugin-ts":"^3.4.5",sass:"^1.74.1",typescript:"^5.4.5",vega:"^5.22.1","vega-lite":"^5.2.0"},q1={vega:"^5.21.0","vega-lite":"*"},V1={"fast-json-patch":"^3.1.1","json-stringify-pretty-compact":"^3.0.0",semver:"^7.6.0",tslib:"^2.6.2","vega-interpreter":"^1.0.5","vega-schema-url-parser":"^2.2.0","vega-themes":"^2.14.0","vega-tooltip":"^0.34.0"},X1={prebuild:"yarn clean && yarn build:style",build:"rollup -c","build:style":"./build-style.sh",clean:"del-cli build src/style.ts",prepublishOnly:"yarn clean && yarn build",preversion:"yarn lint && yarn test",serve:"browser-sync start --directory -s -f build *.html",start:"yarn build && concurrently --kill-others -n Server,Rollup 'yarn serve' 'rollup -c -w'",pretest:"yarn build:style",test:"jest","test:inspect":"node --inspect-brk ./node_modules/.bin/jest --runInBand",prettierbase:"prettier '*.{css,scss,html}'",format:"eslint . --fix && yarn prettierbase --write",lint:"eslint . && yarn prettierbase --check",release:"release-it"},Y1={name:T1,version:A1,description:O1,keywords:_1,repository:R1,author:I1,contributors:L1,bugs:P1,homepage:z1,license:D1,main:j1,module:M1,unpkg:U1,jsdelivr:B1,types:W1,files:G1,devDependencies:H1,peerDependencies:q1,dependencies:V1,scripts:X1};const e0=Y1.version,at=Fp;let Bi=wE;const jl=typeof window<"u"?window:void 0;Bi===void 0&&jl?.vl?.compile&&(Bi=jl.vl);const K1={export:{svg:!0,png:!0},source:!0,compiled:!0,editor:!0},Q1={CLICK_TO_VIEW_ACTIONS:"Click to view actions",COMPILED_ACTION:"View Compiled Vega",EDITOR_ACTION:"Open in Vega Editor",PNG_ACTION:"Save as PNG",SOURCE_ACTION:"View Source",SVG_ACTION:"Save as SVG"},Ai={vega:"Vega","vega-lite":"Vega-Lite"},_r={vega:at.version,"vega-lite":Bi?Bi.version:"not available"},J1={vega:e=>e,"vega-lite":(e,t)=>Bi.compile(e,{config:t}).spec},Z1=`
<svg viewBox="0 0 16 16" fill="currentColor" stroke="none" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
  <circle r="2" cy="8" cx="2"></circle>
  <circle r="2" cy="8" cx="8"></circle>
  <circle r="2" cy="8" cx="14"></circle>
</svg>`,ew="chart-wrapper";function tw(e){return typeof e=="function"}function Ml(e,t,n,i){const r=`<html><head>${t}</head><body><pre><code class="json">`,s=`</code></pre>${n}</body></html>`,o=window.open("");o.document.write(r+e+s),o.document.title=`${Ai[i]} JSON Source`}function nw(e,t){if(e.$schema){const n=Wl(e.$schema);t&&t!==n.library&&console.warn(`The given visualization spec is written in ${Ai[n.library]}, but mode argument sets ${Ai[t]??t}.`);const i=n.library;return mp(_r[i],`^${n.version.slice(1)}`)||console.warn(`The input spec uses ${Ai[i]} ${n.version}, but the current version of ${Ai[i]} is v${_r[i]}.`),i}return"mark"in e||"encoding"in e||"layer"in e||"hconcat"in e||"vconcat"in e||"facet"in e||"repeat"in e?"vega-lite":"marks"in e||"signals"in e||"scales"in e||"axes"in e?"vega":t??"vega"}function bp(e){return!!(e&&"load"in e)}function Ul(e){return bp(e)?e:at.loader(e)}function iw(e){const t=e.usermeta?.embedOptions??{};return I(t.defaultStyle)&&(t.defaultStyle=!1),t}async function t0(e,t,n={}){let i,r;I(t)?(r=Ul(n.loader),i=JSON.parse(await r.load(t))):i=t;const s=iw(i),o=s.loader;(!r||o)&&(r=Ul(n.loader??o));const a=await Bl(s,r),c=await Bl(n,r),l={...yp(c,a),config:Rr(c.config??{},a.config??{})};return await sw(e,i,l,r)}async function Bl(e,t){const n=I(e.config)?JSON.parse(await t.load(e.config)):e.config??{},i=I(e.patch)?JSON.parse(await t.load(e.patch)):e.patch;return{...e,...i?{patch:i}:{},...n?{config:n}:{}}}function rw(e){const t=e.getRootNode?e.getRootNode():document;return t instanceof ShadowRoot?{root:t,rootContainer:t}:{root:document,rootContainer:document.head??document.body}}async function sw(e,t,n={},i){const r=n.theme?Rr(C$[n.theme],n.config??{}):n.config,s=di(n.actions)?n.actions:yp({},K1,n.actions??{}),o={...Q1,...n.i18n},a=n.renderer??"canvas",c=n.logLevel??at.Warn,l=n.downloadFileName??"visualization",u=typeof e=="string"?document.querySelector(e):e;if(!u)throw new Error(`${e} does not exist`);if(n.defaultStyle!==!1){const F="vega-embed-style",{root:_,rootContainer:P}=rw(u);if(!_.getElementById(F)){const U=document.createElement("style");U.id=F,U.innerHTML=n.defaultStyle===void 0||n.defaultStyle===!0?F1.toString():n.defaultStyle,P.appendChild(U)}}const f=nw(t,n.mode);let d=J1[f](t,r);if(f==="vega-lite"&&d.$schema){const F=Wl(d.$schema);mp(_r.vega,`^${F.version.slice(1)}`)||console.warn(`The compiled spec uses Vega ${F.version}, but current version is v${_r.vega}.`)}u.classList.add("vega-embed"),s&&u.classList.add("has-actions"),u.innerHTML="";let g=u;if(s){const F=document.createElement("div");F.classList.add(ew),u.appendChild(F),g=F}const p=n.patch;if(p&&(d=p instanceof Function?p(d):kp(d,p,!0,!1).newDocument),n.formatLocale&&at.formatLocale(n.formatLocale),n.timeFormatLocale&&at.timeFormatLocale(n.timeFormatLocale),n.expressionFunctions)for(const F in n.expressionFunctions){const _=n.expressionFunctions[F];"fn"in _?at.expressionFunction(F,_.fn,_.visitor):_ instanceof Function&&at.expressionFunction(F,_)}const{ast:h}=n,m=at.parse(d,f==="vega-lite"?{}:r,{ast:h}),y=new(n.viewClass||at.View)(m,{loader:i,logLevel:c,renderer:a,...h?{expr:at.expressionInterpreter??n.expr??Tp}:{}});if(y.addSignalListener("autosize",(F,_)=>{const{type:P}=_;P=="fit-x"?(g.classList.add("fit-x"),g.classList.remove("fit-y")):P=="fit-y"?(g.classList.remove("fit-x"),g.classList.add("fit-y")):P=="fit"?g.classList.add("fit-x","fit-y"):g.classList.remove("fit-x","fit-y")}),n.tooltip!==!1){const{loader:F,tooltip:_}=n,P=F&&!bp(F)?F?.baseURL:void 0,U=tw(_)?_:new Ap({baseURL:P,..._===!0?{}:_}).call;y.tooltip(U)}let{hover:b}=n;if(b===void 0&&(b=f==="vega"),b){const{hoverSet:F,updateSet:_}=typeof b=="boolean"?{}:b;y.hover(F,_)}n&&(n.width!=null&&y.width(n.width),n.height!=null&&y.height(n.height),n.padding!=null&&y.padding(n.padding)),await y.initialize(g,n.bind).runAsync();let C;if(s!==!1){let F=u;if(n.defaultStyle!==!1||n.forceActionsMenu){const P=document.createElement("details");P.title=o.CLICK_TO_VIEW_ACTIONS,u.append(P),F=P;const U=document.createElement("summary");U.innerHTML=Z1,P.append(U),C=oe=>{P.contains(oe.target)||P.removeAttribute("open")},document.addEventListener("click",C)}const _=document.createElement("div");if(F.append(_),_.classList.add("vega-actions"),s===!0||s.export!==!1){for(const P of["svg","png"])if(s===!0||s.export===!0||s.export[P]){const U=o[`${P.toUpperCase()}_ACTION`],oe=document.createElement("a"),de=X(n.scaleFactor)?n.scaleFactor[P]:n.scaleFactor;oe.text=U,oe.href="#",oe.target="_blank",oe.download=`${l}.${P}`,oe.addEventListener("mousedown",async function(Re){Re.preventDefault();const sn=await y.toImageURL(P,de);this.href=sn}),_.append(oe)}}if(s===!0||s.source!==!1){const P=document.createElement("a");P.text=o.SOURCE_ACTION,P.href="#",P.addEventListener("click",function(U){Ml(Ns(t),n.sourceHeader??"",n.sourceFooter??"",f),U.preventDefault()}),_.append(P)}if(f==="vega-lite"&&(s===!0||s.compiled!==!1)){const P=document.createElement("a");P.text=o.COMPILED_ACTION,P.href="#",P.addEventListener("click",function(U){Ml(Ns(d),n.sourceHeader??"",n.sourceFooter??"","vega"),U.preventDefault()}),_.append(P)}if(s===!0||s.editor!==!1){const P=n.editorUrl??"https://vega.github.io/editor/",U=document.createElement("a");U.text=o.EDITOR_ACTION,U.href="#",U.addEventListener("click",function(oe){N1(window,P,{config:r,mode:f,renderer:a,spec:Ns(t)}),oe.preventDefault()}),_.append(U)}}function O(){C&&document.removeEventListener("click",C),y.finalize()}return{view:y,spec:t,vgSpec:d,finalize:O,embedOptions:n}}export{K1 as DEFAULT_ACTIONS,t0 as default,nw as guessMode,at as vega,Bi as vegaLite,e0 as version};
//# sourceMappingURL=vega-embed.module-CtFeCQSL.js.map
