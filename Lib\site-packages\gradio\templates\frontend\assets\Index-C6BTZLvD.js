const __vite__fileDeps=["./browserAll-_TqEdkRH.js","./init--aHCNtmt.js","./colorToUniform-KTpA7KSL.js","./index-CKzrTzGp.js","./index-Bb8y38vF.css","./ImagePreview-BRCp84xM.js","./utils-BsGrhMNe.js","./MarkdownCode.svelte_svelte_type_style_lang-CF3ptt4z.js","./prism-python-DuIBek8H.js","./MarkdownCode-C3WxAKR4.css","./BlockLabel-3KxTaaiM.js","./IconButtonWrapper-BqcF4N5S.css","./IconButton-C_HS7fTi.js","./Empty-ZqppqzTN.js","./ShareButton-B50MnNFi.js","./Community-Dw1micSV.js","./Download-DVtk-Jv3.js","./Image-Bsh8Umrh.js","./IconButtonWrapper--EIOWuEM.js","./FullscreenButton-DolUE1RR.js","./utils-Gtzs_Zla.js","./Image-CnqB5dbD.js","./file-url-DoxvUUVV.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./Image-B8dFOee4.css","./DownloadLink-QIttOhoR.js","./ImagePreview-C_qhEOxI.css","./ImageUploader-BI_enGHK.js","./Clear-By3xiIwg.js","./SelectSource-ClK2AuTz.js","./Upload-BktIH97M.js","./Upload-L7mprsyN.css","./DropdownArrow-DYWFcSFn.js","./Square-oAGqOwsh.js","./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js","./StreamingBar-Cgs5stVH.css","./index-DtO3d7FT.js","./StreamingBar-BpqwJkLy.js","./ImageUploader-DMdYP1a9.css","./Block-CJdXVpa7.js","./index-DhCLfoKh.js","./tinycolor-DhRrpXkc.js","./Check-CEkiXcyC.js","./Trash-RbZEwH-j.js","./Undo-DCjBnnSO.js","./Example-DikqVAPo.css","./webworkerAll-B0_TnFkr.js","./WebGPURenderer-SBzZz9TH.js","./SharedSystems-BcnGIVru.js","./WebGLRenderer-BjKGPx6T.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{w as as,_ as Nn,f as ha,x as Ws,n as fr,F as gr}from"./index-CKzrTzGp.js";import h_ from"./ImagePreview-BRCp84xM.js";import{C as Ec,W as c_}from"./ImageUploader-BI_enGHK.js";import{B as Rc}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CF3ptt4z.js";/* empty css                                                        */import{s as mr,S as Fc}from"./index-DhCLfoKh.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./Image-CnqB5dbD.js";/* empty css                                              */import{I as Bc}from"./Image-Bsh8Umrh.js";import{U as u_,I as d_,c as __}from"./Upload-BktIH97M.js";import{W as f_}from"./SelectSource-ClK2AuTz.js";import{t as tt,E as g_}from"./tinycolor-DhRrpXkc.js";import{C as Lc}from"./Check-CEkiXcyC.js";/* empty css                                                   *//* empty css                                             */import{C as m_}from"./Clear-By3xiIwg.js";import{I as Ve}from"./IconButton-C_HS7fTi.js";import{D as p_}from"./Download-DVtk-Jv3.js";import{T as b_}from"./Trash-RbZEwH-j.js";import{U as y_}from"./Undo-DCjBnnSO.js";import{I as w_}from"./IconButtonWrapper--EIOWuEM.js";import{B as x_}from"./BlockLabel-3KxTaaiM.js";const{SvelteComponent:v_,append:Fa,attr:Ne,detach:k_,init:A_,insert:S_,noop:pr,safe_not_equal:C_,svg_element:br}=window.__gradio__svelte__internal;function M_(s){let e,t,i;return{c(){e=br("svg"),t=br("line"),i=br("polyline"),Ne(t,"x1","12"),Ne(t,"y1","19"),Ne(t,"x2","12"),Ne(t,"y2","5"),Ne(i,"points","5 12 12 5 19 12"),Ne(e,"width","100%"),Ne(e,"height","100%"),Ne(e,"xmlns","http://www.w3.org/2000/svg"),Ne(e,"viewBox","0 0 24 24"),Ne(e,"fill","none"),Ne(e,"stroke","currentColor"),Ne(e,"stroke-width","2"),Ne(e,"stroke-linecap","round"),Ne(e,"stroke-linejoin","round")},m(n,r){S_(n,e,r),Fa(e,t),Fa(e,i)},p:pr,i:pr,o:pr,d(n){n&&k_(e)}}}class T_ extends v_{constructor(e){super(),A_(this,e,null,M_,C_,{})}}const{SvelteComponent:P_,append:Ba,attr:Ue,detach:z_,init:I_,insert:E_,noop:yr,safe_not_equal:R_,svg_element:wr}=window.__gradio__svelte__internal;function F_(s){let e,t,i;return{c(){e=wr("svg"),t=wr("line"),i=wr("polyline"),Ue(t,"x1","12"),Ue(t,"y1","5"),Ue(t,"x2","12"),Ue(t,"y2","19"),Ue(i,"points","19 12 12 19 5 12"),Ue(e,"width","100%"),Ue(e,"height","100%"),Ue(e,"xmlns","http://www.w3.org/2000/svg"),Ue(e,"viewBox","0 0 24 24"),Ue(e,"fill","none"),Ue(e,"stroke","currentColor"),Ue(e,"stroke-width","2"),Ue(e,"stroke-linecap","round"),Ue(e,"stroke-linejoin","round")},m(n,r){E_(n,e,r),Ba(e,t),Ba(e,i)},p:yr,i:yr,o:yr,d(n){n&&z_(e)}}}class B_ extends P_{constructor(e){super(),I_(this,e,null,F_,R_,{})}}const{SvelteComponent:L_,append:La,attr:vi,detach:$_,init:O_,insert:G_,noop:xr,safe_not_equal:D_,svg_element:vr}=window.__gradio__svelte__internal;function N_(s){let e,t,i;return{c(){e=vr("svg"),t=vr("path"),i=vr("path"),vi(t,"d","M28.828 3.172a4.094 4.094 0 0 0-5.656 0L4.05 22.292A6.954 6.954 0 0 0 2 27.242V30h2.756a6.952 6.952 0 0 0 4.95-2.05L28.828 8.829a3.999 3.999 0 0 0 0-5.657zM10.91 18.26l2.829 2.829l-2.122 2.121l-2.828-2.828zm-2.619 8.276A4.966 4.966 0 0 1 4.756 28H4v-.759a4.967 4.967 0 0 1 1.464-3.535l1.91-1.91l2.829 2.828zM27.415 7.414l-12.261 12.26l-2.829-2.828l12.262-12.26a2.047 2.047 0 0 1 2.828 0a2 2 0 0 1 0 2.828z"),vi(t,"fill","currentColor"),vi(i,"d","M6.5 15a3.5 3.5 0 0 1-2.475-5.974l3.5-3.5a1.502 1.502 0 0 0 0-2.121a1.537 1.537 0 0 0-2.121 0L3.415 5.394L2 3.98l1.99-1.988a3.585 3.585 0 0 1 4.95 0a3.504 3.504 0 0 1 0 4.949L5.439 10.44a1.502 1.502 0 0 0 0 2.121a1.537 1.537 0 0 0 2.122 0l4.024-4.024L13 9.95l-4.025 4.024A3.475 3.475 0 0 1 6.5 15z"),vi(i,"fill","currentColor"),vi(e,"width","100%"),vi(e,"height","100%"),vi(e,"viewBox","0 0 32 32")},m(n,r){G_(n,e,r),La(e,t),La(e,i)},p:xr,i:xr,o:xr,d(n){n&&$_(e)}}}class U_ extends L_{constructor(e){super(),O_(this,e,null,N_,D_,{})}}const{SvelteComponent:W_,append:H_,attr:Wi,detach:q_,init:V_,insert:X_,noop:kr,safe_not_equal:Y_,svg_element:$a}=window.__gradio__svelte__internal;function j_(s){let e,t;return{c(){e=$a("svg"),t=$a("path"),Wi(t,"fill","currentColor"),Wi(t,"d","M2.753 2.933a.75.75 0 0 1 .814-.68l3.043.272c2.157.205 4.224.452 5.922.732c1.66.273 3.073.594 3.844.983c.197.1.412.233.578.415c.176.192.352.506.28.9c-.067.356-.304.59-.487.729a3.001 3.001 0 0 1-.695.369c-1.02.404-2.952.79-5.984 1.169c-1.442.18-2.489.357-3.214.522c.205.045.43.089.674.132c.992.174 2.241.323 3.568.437a31.21 31.21 0 0 1 3.016.398c.46.087.893.186 1.261.296c.352.105.707.236.971.412c.13.086.304.225.42.437a.988.988 0 0 1 .063.141A1.75 1.75 0 0 0 14.5 12.25v.158c-.758.154-1.743.302-2.986.444c-2.124.243-3.409.55-4.117.859c-.296.128-.442.236-.508.3c.026.037.073.094.156.17c.15.138.369.29.65.45c.56.316 1.282.61 1.979.838l2.637.814a.75.75 0 1 1-.443 1.433l-2.655-.819c-.754-.247-1.58-.578-2.257-.96a5.082 5.082 0 0 1-.924-.65c-.255-.233-.513-.544-.62-.935c-.12-.441-.016-.88.274-1.244c.261-.328.656-.574 1.113-.773c.92-.4 2.387-.727 4.545-.974c1.366-.156 2.354-.313 3.041-.462a16.007 16.007 0 0 0-.552-.114a29.716 29.716 0 0 0-2.865-.378c-1.352-.116-2.649-.27-3.7-.454c-.524-.092-1-.194-1.395-.307c-.376-.106-.75-.241-1.021-.426a1.186 1.186 0 0 1-.43-.49a.934.934 0 0 1 .059-.873c.13-.213.32-.352.472-.442a3.23 3.23 0 0 1 .559-.251c.807-.287 2.222-.562 4.37-.83c2.695-.338 4.377-.666 5.295-.962c-.638-.21-1.623-.427-2.89-.635c-1.65-.273-3.679-.515-5.816-.718l-3.038-.272a.75.75 0 0 1-.68-.814M17 12.25a.75.75 0 0 0-1.5 0v4.19l-.72-.72a.75.75 0 1 0-1.06 1.06l2 2a.75.75 0 0 0 1.06 0l2-2a.75.75 0 1 0-1.06-1.06l-.72.72z"),Wi(e,"xmlns","http://www.w3.org/2000/svg"),Wi(e,"width","100%"),Wi(e,"height","100%"),Wi(e,"viewBox","0 0 24 24")},m(i,n){X_(i,e,n),H_(e,t)},p:kr,i:kr,o:kr,d(i){i&&q_(e)}}}class K_ extends W_{constructor(e){super(),V_(this,e,null,j_,Y_,{})}}const{SvelteComponent:Z_,append:Q_,attr:Hi,detach:J_,init:ef,insert:tf,noop:Ar,safe_not_equal:sf,svg_element:Oa}=window.__gradio__svelte__internal;function nf(s){let e,t;return{c(){e=Oa("svg"),t=Oa("path"),Hi(t,"fill","currentColor"),Hi(t,"d","M12 22q-2.05 0-3.875-.788t-3.187-2.15t-2.15-3.187T2 12q0-2.075.813-3.9t2.2-3.175T8.25 2.788T12.2 2q2 0 3.775.688t3.113 1.9t2.125 2.875T22 11.05q0 2.875-1.75 4.413T16 17h-1.85q-.225 0-.312.125t-.088.275q0 .3.375.863t.375 1.287q0 1.25-.687 1.85T12 22m-5.5-9q.65 0 1.075-.425T8 11.5t-.425-1.075T6.5 10t-1.075.425T5 11.5t.425 1.075T6.5 13m3-4q.65 0 1.075-.425T11 7.5t-.425-1.075T9.5 6t-1.075.425T8 7.5t.425 1.075T9.5 9m5 0q.65 0 1.075-.425T16 7.5t-.425-1.075T14.5 6t-1.075.425T13 7.5t.425 1.075T14.5 9m3 4q.65 0 1.075-.425T19 11.5t-.425-1.075T17.5 10t-1.075.425T16 11.5t.425 1.075T17.5 13"),Hi(e,"xmlns","http://www.w3.org/2000/svg"),Hi(e,"width","100%"),Hi(e,"height","100%"),Hi(e,"viewBox","0 0 24 24")},m(i,n){tf(i,e,n),Q_(e,t)},p:Ar,i:Ar,o:Ar,d(i){i&&J_(e)}}}class rf extends Z_{constructor(e){super(),ef(this,e,null,nf,sf,{})}}const{SvelteComponent:of,append:af,attr:qi,detach:lf,init:hf,insert:cf,noop:Sr,safe_not_equal:uf,svg_element:Ga}=window.__gradio__svelte__internal;function df(s){let e,t;return{c(){e=Ga("svg"),t=Ga("path"),qi(t,"fill","currentColor"),qi(t,"d","M240 192a8 8 0 0 1-8 8h-32v32a8 8 0 0 1-16 0v-32H64a8 8 0 0 1-8-8V72H24a8 8 0 0 1 0-16h32V24a8 8 0 0 1 16 0v160h160a8 8 0 0 1 8 8M96 72h88v88a8 8 0 0 0 16 0V64a8 8 0 0 0-8-8H96a8 8 0 0 0 0 16"),qi(e,"xmlns","http://www.w3.org/2000/svg"),qi(e,"width","100%"),qi(e,"height","100%"),qi(e,"viewBox","0 0 256 256")},m(i,n){cf(i,e,n),af(e,t)},p:Sr,i:Sr,o:Sr,d(i){i&&lf(e)}}}class _f extends of{constructor(e){super(),hf(this,e,null,df,uf,{})}}const{SvelteComponent:ff,append:Cr,attr:bt,detach:gf,init:mf,insert:pf,noop:Mr,safe_not_equal:bf,svg_element:fn}=window.__gradio__svelte__internal;function yf(s){let e,t,i,n;return{c(){e=fn("svg"),t=fn("g"),i=fn("path"),n=fn("path"),bt(i,"fill","currentColor"),bt(i,"d","m5.505 11.41l.53.53l-.53-.53ZM3 14.952h-.75H3ZM9.048 21v.75V21ZM11.41 5.505l-.53-.53l.53.53Zm1.831 12.34a.75.75 0 0 0 1.06-1.061l-1.06 1.06ZM7.216 9.697a.75.75 0 1 0-1.06 1.061l1.06-1.06Zm10.749 2.362l-5.905 5.905l1.06 1.06l5.905-5.904l-1.06-1.06Zm-11.93-.12l5.905-5.905l-1.06-1.06l-5.905 5.904l1.06 1.06Zm0 6.025c-.85-.85-1.433-1.436-1.812-1.933c-.367-.481-.473-.79-.473-1.08h-1.5c0 .749.312 1.375.78 1.99c.455.596 1.125 1.263 1.945 2.083l1.06-1.06Zm-1.06-7.086c-.82.82-1.49 1.488-1.945 2.084c-.468.614-.78 1.24-.78 1.99h1.5c0-.29.106-.6.473-1.08c.38-.498.962-1.083 1.812-1.933l-1.06-1.06Zm7.085 7.086c-.85.85-1.435 1.433-1.933 1.813c-.48.366-.79.472-1.08.472v1.5c.75 0 1.376-.312 1.99-.78c.596-.455 1.264-1.125 2.084-1.945l-1.06-1.06Zm-7.085 1.06c.82.82 1.487 1.49 2.084 1.945c.614.468 1.24.78 1.989.78v-1.5c-.29 0-.599-.106-1.08-.473c-.497-.38-1.083-.962-1.933-1.812l-1.06 1.06Zm12.99-12.99c.85.85 1.433 1.436 1.813 1.933c.366.481.472.79.472 1.08h1.5c0-.749-.312-1.375-.78-1.99c-.455-.596-1.125-1.263-1.945-2.083l-1.06 1.06Zm1.06 7.086c.82-.82 1.49-1.488 1.945-2.084c.468-.614.78-1.24.78-1.99h-1.5c0 .29-.106.6-.473 1.08c-.38.498-.962 1.083-1.812 1.933l1.06 1.06Zm0-8.146c-.82-.82-1.487-1.49-2.084-1.945c-.614-.468-1.24-.78-1.989-.78v1.5c.29 0 .599.106 1.08.473c.497.38 1.083.962 1.933 1.812l1.06-1.06Zm-7.085 1.06c.85-.85 1.435-1.433 1.933-1.812c.48-.367.79-.473 1.08-.473v-1.5c-.75 0-1.376.312-1.99.78c-.596.455-1.264 1.125-2.084 1.945l1.06 1.06Zm2.362 10.749L7.216 9.698l-1.06 1.061l7.085 7.085l1.06-1.06Z"),bt(n,"stroke","currentColor"),bt(n,"stroke-linecap","round"),bt(n,"stroke-width","1.5"),bt(n,"d","M9 21h12"),bt(t,"fill","none"),bt(e,"xmlns","http://www.w3.org/2000/svg"),bt(e,"width","100%"),bt(e,"height","100%"),bt(e,"viewBox","0 0 24 24")},m(r,o){pf(r,e,o),Cr(e,t),Cr(t,i),Cr(t,n)},p:Mr,i:Mr,o:Mr,d(r){r&&gf(e)}}}class wf extends ff{constructor(e){super(),mf(this,e,null,yf,bf,{})}}const{SvelteComponent:xf,append:Da,attr:ht,detach:vf,init:kf,insert:Af,noop:Tr,safe_not_equal:Sf,svg_element:Pr}=window.__gradio__svelte__internal;function Cf(s){let e,t,i;return{c(){e=Pr("svg"),t=Pr("path"),i=Pr("path"),ht(t,"d","M1.35327 10.9495L6.77663 15.158C7.12221 15.4229 7.50051 15.5553 7.91154 15.5553C8.32258 15.5553 8.70126 15.4229 9.0476 15.158L14.471 10.9495"),ht(t,"stroke","currentColor"),ht(t,"stroke-width","1.5"),ht(t,"stroke-linecap","round"),ht(i,"d","M7.23461 11.4324C7.23406 11.432 7.2335 11.4316 7.23295 11.4312L1.81496 7.2268C1.81471 7.22661 1.81446 7.22641 1.8142 7.22621C1.52269 6.99826 1.39429 6.73321 1.39429 6.37014C1.39429 6.00782 1.52236 5.74301 1.81325 5.51507C1.8136 5.5148 1.81394 5.51453 1.81428 5.51426L7.2331 1.30812C7.45645 1.13785 7.67632 1.06653 7.91159 1.06653C8.14692 1.06653 8.36622 1.13787 8.58861 1.30787C8.58915 1.30828 8.58969 1.30869 8.59023 1.30911L14.0082 5.51462C14.0085 5.51485 14.0088 5.51507 14.0091 5.51529C14.3008 5.74345 14.4289 6.00823 14.4289 6.37014C14.4289 6.73356 14.3006 6.99862 14.01 7.22634C14.0096 7.22662 14.0093 7.22689 14.0089 7.22717L8.59007 11.4322C8.36672 11.6024 8.14686 11.6738 7.91159 11.6738C7.67628 11.6738 7.45699 11.6024 7.23461 11.4324Z"),ht(i,"stroke","currentColor"),ht(i,"stroke-width","1.5"),ht(e,"width","100%"),ht(e,"height","100%"),ht(e,"viewBox","0 0 17 17"),ht(e,"fill","none"),ht(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){Af(n,e,r),Da(e,t),Da(e,i)},p:Tr,i:Tr,o:Tr,d(n){n&&vf(e)}}}class Mf extends xf{constructor(e){super(),kf(this,e,null,Cf,Sf,{})}}const{SvelteComponent:Tf,append:Pf,attr:Vi,detach:zf,init:If,insert:Ef,noop:zr,safe_not_equal:Rf,svg_element:Na}=window.__gradio__svelte__internal;function Ff(s){let e,t;return{c(){e=Na("svg"),t=Na("path"),Vi(t,"fill","currentColor"),Vi(t,"d","M10.05 23q-.75 0-1.4-.337T7.575 21.7l-5.9-8.65q-.2-.3-.175-.65t.3-.6q.475-.475 1.125-.55t1.175.3L7 13.575V4q0-.425.288-.712T8 3t.713.288T9 4v11.5q0 .6-.537.888t-1.038-.063l-2.125-1.5l3.925 5.725q.125.2.35.325t.475.125H17q.825 0 1.413-.587T19 19V5q0-.425.288-.712T20 4t.713.288T21 5v14q0 1.65-1.175 2.825T17 23zM12 1q.425 0 .713.288T13 2v9q0 .425-.288.713T12 12t-.712-.288T11 11V2q0-.425.288-.712T12 1m4 1q.425 0 .713.288T17 3v8q0 .425-.288.713T16 12t-.712-.288T15 11V3q0-.425.288-.712T16 2m-3.85 14.5"),Vi(e,"xmlns","http://www.w3.org/2000/svg"),Vi(e,"width","100%"),Vi(e,"height","100%"),Vi(e,"viewBox","0 0 24 24")},m(i,n){Ef(i,e,n),Pf(e,t)},p:zr,i:zr,o:zr,d(i){i&&zf(e)}}}class Bf extends Tf{constructor(e){super(),If(this,e,null,Ff,Rf,{})}}const{SvelteComponent:Lf,append:$f,attr:Et,detach:Of,init:Gf,insert:Df,noop:Ir,safe_not_equal:Nf,svg_element:Ua}=window.__gradio__svelte__internal;function Uf(s){let e,t;return{c(){e=Ua("svg"),t=Ua("path"),Et(t,"d","M6 12H12M18 12H12M12 12V6M12 12V18"),Et(t,"stroke","currentColor"),Et(t,"stroke-width","1.5"),Et(t,"stroke-linecap","round"),Et(t,"stroke-linejoin","round"),Et(e,"width","100%"),Et(e,"height","100%"),Et(e,"viewBox","0 0 24 24"),Et(e,"fill","none"),Et(e,"xmlns","http://www.w3.org/2000/svg")},m(i,n){Df(i,e,n),$f(e,t)},p:Ir,i:Ir,o:Ir,d(i){i&&Of(e)}}}class $c extends Lf{constructor(e){super(),Gf(this,e,null,Uf,Nf,{})}}const{SvelteComponent:Wf,append:Wa,attr:ct,detach:Hf,init:qf,insert:Vf,noop:Er,safe_not_equal:Xf,set_style:Yf,svg_element:Rr}=window.__gradio__svelte__internal;function jf(s){let e,t,i;return{c(){e=Rr("svg"),t=Rr("polyline"),i=Rr("path"),ct(t,"points","1 4 1 10 7 10"),ct(i,"d","M3.51 15a9 9 0 1 0 2.13-9.36L1 10"),ct(e,"xmlns","http://www.w3.org/2000/svg"),ct(e,"width","100%"),ct(e,"height","100%"),ct(e,"viewBox","0 0 24 24"),ct(e,"fill","none"),ct(e,"stroke","currentColor"),ct(e,"stroke-width","2"),ct(e,"stroke-linecap","round"),ct(e,"stroke-linejoin","round"),ct(e,"class","feather feather-rotate-ccw"),Yf(e,"transform","rotateY(180deg)")},m(n,r){Vf(n,e,r),Wa(e,t),Wa(e,i)},p:Er,i:Er,o:Er,d(n){n&&Hf(e)}}}class Kf extends Wf{constructor(e){super(),qf(this,e,null,jf,Xf,{})}}const{SvelteComponent:Zf,append:Qf,attr:Xi,detach:Jf,init:eg,insert:tg,noop:Fr,safe_not_equal:ig,svg_element:Ha}=window.__gradio__svelte__internal;function sg(s){let e,t;return{c(){e=Ha("svg"),t=Ha("path"),Xi(t,"fill","currentColor"),Xi(t,"d","M12 16q1.875 0 3.188-1.312T16.5 11.5t-1.312-3.187T12 7T8.813 8.313T7.5 11.5t1.313 3.188T12 16m0-1.8q-1.125 0-1.912-.788T9.3 11.5t.788-1.912T12 8.8t1.913.788t.787 1.912t-.787 1.913T12 14.2m0 4.8q-3.65 0-6.65-2.037T1 11.5q1.35-3.425 4.35-5.462T12 4t6.65 2.038T23 11.5q-1.35 3.425-4.35 5.463T12 19"),Xi(e,"xmlns","http://www.w3.org/2000/svg"),Xi(e,"width","100%"),Xi(e,"height","100%"),Xi(e,"viewBox","0 0 24 24")},m(i,n){tg(i,e,n),Qf(e,t)},p:Fr,i:Fr,o:Fr,d(i){i&&Jf(e)}}}class ng extends Zf{constructor(e){super(),eg(this,e,null,sg,ig,{})}}const{SvelteComponent:rg,append:og,attr:Yi,detach:ag,init:lg,insert:hg,noop:Br,safe_not_equal:cg,svg_element:qa}=window.__gradio__svelte__internal;function ug(s){let e,t;return{c(){e=qa("svg"),t=qa("path"),Yi(t,"fill","currentColor"),Yi(t,"d","m19.8 22.6l-4.2-4.15q-.875.275-1.762.413T12 19q-3.775 0-6.725-2.087T1 11.5q.525-1.325 1.325-2.463T4.15 7L1.4 4.2l1.4-1.4l18.4 18.4zM12 16q.275 0 .513-.025t.512-.1l-5.4-5.4q-.075.275-.1.513T7.5 11.5q0 1.875 1.313 3.188T12 16m7.3.45l-3.175-3.15q.175-.425.275-.862t.1-.938q0-1.875-1.312-3.187T12 7q-.5 0-.937.1t-.863.3L7.65 4.85q1.025-.425 2.1-.637T12 4q3.775 0 6.725 2.088T23 11.5q-.575 1.475-1.513 2.738T19.3 16.45m-4.625-4.6l-3-3q.7-.125 1.288.113t1.012.687t.613 1.038t.087 1.162"),Yi(e,"xmlns","http://www.w3.org/2000/svg"),Yi(e,"width","100%"),Yi(e,"height","100%"),Yi(e,"viewBox","0 0 24 24")},m(i,n){hg(i,e,n),og(e,t)},p:Br,i:Br,o:Br,d(i){i&&ag(e)}}}class dg extends rg{constructor(e){super(),lg(this,e,null,ug,cg,{})}}const{SvelteComponent:_g,append:fg,attr:ji,detach:gg,init:mg,insert:pg,noop:Lr,safe_not_equal:bg,svg_element:Va}=window.__gradio__svelte__internal;function yg(s){let e,t;return{c(){e=Va("svg"),t=Va("path"),ji(t,"fill","currentColor"),ji(t,"d","M144 120v88a8 8 0 0 1-8 8H48a8 8 0 0 1-8-8v-88a8 8 0 0 1 8-8h88a8 8 0 0 1 8 8m64 56a8 8 0 0 0-8 8v16h-24a8 8 0 0 0 0 16h24a16 16 0 0 0 16-16v-16a8 8 0 0 0-8-8m0-72a8 8 0 0 0-8 8v32a8 8 0 0 0 16 0v-32a8 8 0 0 0-8-8m-8-64h-16a8 8 0 0 0 0 16h16v16a8 8 0 0 0 16 0V56a16 16 0 0 0-16-16m-56 0h-32a8 8 0 0 0 0 16h32a8 8 0 0 0 0-16M48 88a8 8 0 0 0 8-8V56h16a8 8 0 0 0 0-16H56a16 16 0 0 0-16 16v24a8 8 0 0 0 8 8"),ji(e,"xmlns","http://www.w3.org/2000/svg"),ji(e,"width","100%"),ji(e,"height","100%"),ji(e,"viewBox","0 0 256 256")},m(i,n){pg(i,e,n),fg(e,t)},p:Lr,i:Lr,o:Lr,d(i){i&&gg(e)}}}class wg extends _g{constructor(e){super(),mg(this,e,null,yg,bg,{})}}const{SvelteComponent:xg,append:vg,attr:Rt,detach:kg,init:Ag,insert:Sg,noop:$r,safe_not_equal:Cg,svg_element:Xa}=window.__gradio__svelte__internal;function Mg(s){let e,t;return{c(){e=Xa("svg"),t=Xa("path"),Rt(t,"fill","none"),Rt(t,"stroke","currentColor"),Rt(t,"stroke-linecap","round"),Rt(t,"stroke-linejoin","round"),Rt(t,"stroke-width","2"),Rt(t,"d","m21 21l-4.343-4.343m0 0A8 8 0 1 0 5.343 5.343a8 8 0 0 0 11.314 11.314M11 8v6m-3-3h6"),Rt(e,"xmlns","http://www.w3.org/2000/svg"),Rt(e,"width","100%"),Rt(e,"height","100%"),Rt(e,"viewBox","0 0 24 24")},m(i,n){Sg(i,e,n),vg(e,t)},p:$r,i:$r,o:$r,d(i){i&&kg(e)}}}class Tg extends xg{constructor(e){super(),Ag(this,e,null,Mg,Cg,{})}}const{SvelteComponent:Pg,append:zg,attr:Ft,detach:Ig,init:Eg,insert:Rg,noop:Or,safe_not_equal:Fg,svg_element:Ya}=window.__gradio__svelte__internal;function Bg(s){let e,t;return{c(){e=Ya("svg"),t=Ya("path"),Ft(t,"fill","none"),Ft(t,"stroke","currentColor"),Ft(t,"stroke-linecap","round"),Ft(t,"stroke-linejoin","round"),Ft(t,"stroke-width","2"),Ft(t,"d","m21 21l-4.343-4.343m0 0A8 8 0 1 0 5.343 5.343a8 8 0 0 0 11.314 11.314M8 11h6"),Ft(e,"xmlns","http://www.w3.org/2000/svg"),Ft(e,"width","100%"),Ft(e,"height","100%"),Ft(e,"viewBox","0 0 24 24")},m(i,n){Rg(i,e,n),zg(e,t)},p:Or,i:Or,o:Or,d(i){i&&Ig(e)}}}class Lg extends Pg{constructor(e){super(),Eg(this,e,null,Bg,Fg,{})}}class Un{command;next;previous;constructor(e){this.command=e||null,this.next=null,this.previous=null}push(e){const t=new Un(e);t.previous=this,this.next=t}}class ja{history=new Un;current_history=as(this.history);undo(){this.history.previous&&(this.history.command?.undo(),this.history=this.history.previous,this.current_history.set(this.history))}redo(){this.history.next&&(this.history=this.history.next,this.history.command?.execute()),this.current_history.set(this.history)}execute(e){e.execute(),this.history.push(e),this.history=this.history.next,this.current_history.set(this.history)}replay(e){setTimeout(()=>{for(;e.next;)this.execute(e.next.command),e=e.next},1e3)}contains(e){let t=this.history;for(;t;){if(t.command?.name===e)return!0;t=t.next}return!1}reset(){this.history=new Un,this.current_history.set(this.history)}}const{SvelteComponent:$g,append:Hs,attr:wt,bubble:Og,create_component:Gg,destroy_component:Dg,detach:ca,element:Wn,flush:Be,init:Ng,insert:ua,listen:Ug,mount_component:Wg,safe_not_equal:Hg,set_data:Oc,set_style:ai,space:Ka,text:Gc,toggle_class:We,transition_in:qg,transition_out:Vg}=window.__gradio__svelte__internal;function Za(s){let e,t;return{c(){e=Wn("span"),t=Gc(s[1]),ai(e,"margin-left","4px"),wt(e,"class","svelte-rrog8b")},m(i,n){ua(i,e,n),Hs(e,t)},p(i,n){n&2&&Oc(t,i[1])},d(i){i&&ca(e)}}}function Qa(s){let e,t;return{c(){e=Wn("span"),t=Gc(s[1]),ai(e,"margin-right","4px"),wt(e,"class","svelte-rrog8b")},m(i,n){ua(i,e,n),Hs(e,t)},p(i,n){n&2&&Oc(t,i[1])},d(i){i&&ca(e)}}}function Xg(s){let e,t,i,n,r,o,a,l,h,c=s[2]&&s[12]==="left"&&Za(s);n=new s[0]({});let u=s[2]&&s[12]==="right"&&Qa(s);return{c(){e=Wn("button"),c&&c.c(),t=Ka(),i=Wn("div"),Gg(n.$$.fragment),r=Ka(),u&&u.c(),wt(i,"class","svelte-rrog8b"),We(i,"small",s[4]==="small"),We(i,"large",s[4]==="large"),We(i,"medium",s[4]==="medium"),e.disabled=s[7],wt(e,"aria-label",s[1]),wt(e,"aria-haspopup",s[8]),wt(e,"title",s[1]),wt(e,"class",o=s[13]+"-round svelte-rrog8b"),We(e,"pending",s[3]),We(e,"padded",s[5]),We(e,"highlight",s[6]),We(e,"transparent",s[9]),ai(e,"color",!s[7]&&s[14]?s[14]:"var(--block-label-text-color)"),ai(e,"--bg-color",s[7]?"auto":s[10]),ai(e,"margin-left",s[11]+"px")},m(d,_){ua(d,e,_),c&&c.m(e,null),Hs(e,t),Hs(e,i),Wg(n,i,null),Hs(e,r),u&&u.m(e,null),a=!0,l||(h=Ug(e,"click",s[16]),l=!0)},p(d,[_]){d[2]&&d[12]==="left"?c?c.p(d,_):(c=Za(d),c.c(),c.m(e,t)):c&&(c.d(1),c=null),(!a||_&16)&&We(i,"small",d[4]==="small"),(!a||_&16)&&We(i,"large",d[4]==="large"),(!a||_&16)&&We(i,"medium",d[4]==="medium"),d[2]&&d[12]==="right"?u?u.p(d,_):(u=Qa(d),u.c(),u.m(e,null)):u&&(u.d(1),u=null),(!a||_&128)&&(e.disabled=d[7]),(!a||_&2)&&wt(e,"aria-label",d[1]),(!a||_&256)&&wt(e,"aria-haspopup",d[8]),(!a||_&2)&&wt(e,"title",d[1]),(!a||_&8192&&o!==(o=d[13]+"-round svelte-rrog8b"))&&wt(e,"class",o),(!a||_&8200)&&We(e,"pending",d[3]),(!a||_&8224)&&We(e,"padded",d[5]),(!a||_&8256)&&We(e,"highlight",d[6]),(!a||_&8704)&&We(e,"transparent",d[9]),_&16512&&ai(e,"color",!d[7]&&d[14]?d[14]:"var(--block-label-text-color)"),_&1152&&ai(e,"--bg-color",d[7]?"auto":d[10]),_&2048&&ai(e,"margin-left",d[11]+"px")},i(d){a||(qg(n.$$.fragment,d),a=!0)},o(d){Vg(n.$$.fragment,d),a=!1},d(d){d&&ca(e),c&&c.d(),Dg(n),u&&u.d(),l=!1,h()}}}function Yg(s,e,t){let i,{Icon:n}=e,{label:r=""}=e,{show_label:o=!1}=e,{pending:a=!1}=e,{size:l="small"}=e,{padded:h=!0}=e,{highlight:c=!1}=e,{disabled:u=!1}=e,{hasPopup:d=!1}=e,{color:_="var(--block-label-text-color)"}=e,{transparent:f=!1}=e,{background:m="var(--background-fill-primary)"}=e,{offset:g=0}=e,{label_position:p="left"}=e,{roundedness:y="quite"}=e;function b(w){Og.call(this,s,w)}return s.$$set=w=>{"Icon"in w&&t(0,n=w.Icon),"label"in w&&t(1,r=w.label),"show_label"in w&&t(2,o=w.show_label),"pending"in w&&t(3,a=w.pending),"size"in w&&t(4,l=w.size),"padded"in w&&t(5,h=w.padded),"highlight"in w&&t(6,c=w.highlight),"disabled"in w&&t(7,u=w.disabled),"hasPopup"in w&&t(8,d=w.hasPopup),"color"in w&&t(15,_=w.color),"transparent"in w&&t(9,f=w.transparent),"background"in w&&t(10,m=w.background),"offset"in w&&t(11,g=w.offset),"label_position"in w&&t(12,p=w.label_position),"roundedness"in w&&t(13,y=w.roundedness)},s.$$.update=()=>{s.$$.dirty&32832&&t(14,i=c?"var(--color-accent)":_.toString())},[n,r,o,a,l,h,c,u,d,f,m,g,p,y,i,_,b]}class st extends $g{constructor(e){super(),Ng(this,e,Yg,Xg,Hg,{Icon:0,label:1,show_label:2,pending:3,size:4,padded:5,highlight:6,disabled:7,hasPopup:8,color:15,transparent:9,background:10,offset:11,label_position:12,roundedness:13})}get Icon(){return this.$$.ctx[0]}set Icon(e){this.$$set({Icon:e}),Be()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),Be()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),Be()}get pending(){return this.$$.ctx[3]}set pending(e){this.$$set({pending:e}),Be()}get size(){return this.$$.ctx[4]}set size(e){this.$$set({size:e}),Be()}get padded(){return this.$$.ctx[5]}set padded(e){this.$$set({padded:e}),Be()}get highlight(){return this.$$.ctx[6]}set highlight(e){this.$$set({highlight:e}),Be()}get disabled(){return this.$$.ctx[7]}set disabled(e){this.$$set({disabled:e}),Be()}get hasPopup(){return this.$$.ctx[8]}set hasPopup(e){this.$$set({hasPopup:e}),Be()}get color(){return this.$$.ctx[15]}set color(e){this.$$set({color:e}),Be()}get transparent(){return this.$$.ctx[9]}set transparent(e){this.$$set({transparent:e}),Be()}get background(){return this.$$.ctx[10]}set background(e){this.$$set({background:e}),Be()}get offset(){return this.$$.ctx[11]}set offset(e){this.$$set({offset:e}),Be()}get label_position(){return this.$$.ctx[12]}set label_position(e){this.$$set({label_position:e}),Be()}get roundedness(){return this.$$.ctx[13]}set roundedness(e){this.$$set({roundedness:e}),Be()}}function da(s,e){const t=i=>{s&&!s.contains(i.target)&&!i.defaultPrevented&&e(i)};return document.addEventListener("mousedown",t,!0),{destroy(){document.removeEventListener("mousedown",t,!0)}}}var D=(s=>(s.Application="application",s.WebGLPipes="webgl-pipes",s.WebGLPipesAdaptor="webgl-pipes-adaptor",s.WebGLSystem="webgl-system",s.WebGPUPipes="webgpu-pipes",s.WebGPUPipesAdaptor="webgpu-pipes-adaptor",s.WebGPUSystem="webgpu-system",s.CanvasSystem="canvas-system",s.CanvasPipesAdaptor="canvas-pipes-adaptor",s.CanvasPipes="canvas-pipes",s.Asset="asset",s.LoadParser="load-parser",s.ResolveParser="resolve-parser",s.CacheParser="cache-parser",s.DetectionParser="detection-parser",s.MaskEffect="mask-effect",s.BlendMode="blend-mode",s.TextureSource="texture-source",s.Environment="environment",s.ShapeBuilder="shape-builder",s.Batcher="batcher",s))(D||{});const Po=s=>{if(typeof s=="function"||typeof s=="object"&&s.extension){if(!s.extension)throw new Error("Extension class must have an extension object");s={...typeof s.extension!="object"?{type:s.extension}:s.extension,ref:s}}if(typeof s=="object")s={...s};else throw new Error("Invalid extension type");return typeof s.type=="string"&&(s.type=[s.type]),s},gn=(s,e)=>Po(s).priority??e,De={_addHandlers:{},_removeHandlers:{},_queue:{},remove(...s){return s.map(Po).forEach(e=>{e.type.forEach(t=>this._removeHandlers[t]?.(e))}),this},add(...s){return s.map(Po).forEach(e=>{e.type.forEach(t=>{const i=this._addHandlers,n=this._queue;i[t]?i[t]?.(e):(n[t]=n[t]||[],n[t]?.push(e))})}),this},handle(s,e,t){const i=this._addHandlers,n=this._removeHandlers;if(i[s]||n[s])throw new Error(`Extension type ${s} already has a handler`);i[s]=e,n[s]=t;const r=this._queue;return r[s]&&(r[s]?.forEach(o=>e(o)),delete r[s]),this},handleByMap(s,e){return this.handle(s,t=>{t.name&&(e[t.name]=t.ref)},t=>{t.name&&delete e[t.name]})},handleByNamedList(s,e,t=-1){return this.handle(s,i=>{e.findIndex(r=>r.name===i.name)>=0||(e.push({name:i.name,value:i.ref}),e.sort((r,o)=>gn(o.value,t)-gn(r.value,t)))},i=>{const n=e.findIndex(r=>r.name===i.name);n!==-1&&e.splice(n,1)})},handleByList(s,e,t=-1){return this.handle(s,i=>{e.includes(i.ref)||(e.push(i.ref),e.sort((n,r)=>gn(r,t)-gn(n,t)))},i=>{const n=e.indexOf(i.ref);n!==-1&&e.splice(n,1)})}},jg={extension:{type:D.Environment,name:"browser",priority:-1},test:()=>!0,load:async()=>{await Nn(()=>import("./browserAll-_TqEdkRH.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46]),import.meta.url)}},Kg={extension:{type:D.Environment,name:"webworker",priority:0},test:()=>typeof self<"u"&&self.WorkerGlobalScope!==void 0,load:async()=>{await Nn(()=>import("./webworkerAll-B0_TnFkr.js"),__vite__mapDeps([47,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46]),import.meta.url)}};class et{constructor(e,t,i){this._x=t||0,this._y=i||0,this._observer=e}clone(e){return new et(e??this._observer,this._x,this._y)}set(e=0,t=e){return(this._x!==e||this._y!==t)&&(this._x=e,this._y=t,this._observer._onUpdate(this)),this}copyFrom(e){return(this._x!==e.x||this._y!==e.y)&&(this._x=e.x,this._y=e.y,this._observer._onUpdate(this)),this}copyTo(e){return e.set(this._x,this._y),e}equals(e){return e.x===this._x&&e.y===this._y}toString(){return`[pixi.js/math:ObservablePoint x=0 y=0 scope=${this._observer}]`}get x(){return this._x}set x(e){this._x!==e&&(this._x=e,this._observer._onUpdate(this))}get y(){return this._y}set y(e){this._y!==e&&(this._y=e,this._observer._onUpdate(this))}}var Dc={exports:{}};(function(s){var e=Object.prototype.hasOwnProperty,t="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(t=!1));function n(l,h,c){this.fn=l,this.context=h,this.once=c||!1}function r(l,h,c,u,d){if(typeof c!="function")throw new TypeError("The listener must be a function");var _=new n(c,u||l,d),f=t?t+h:h;return l._events[f]?l._events[f].fn?l._events[f]=[l._events[f],_]:l._events[f].push(_):(l._events[f]=_,l._eventsCount++),l}function o(l,h){--l._eventsCount===0?l._events=new i:delete l._events[h]}function a(){this._events=new i,this._eventsCount=0}a.prototype.eventNames=function(){var h=[],c,u;if(this._eventsCount===0)return h;for(u in c=this._events)e.call(c,u)&&h.push(t?u.slice(1):u);return Object.getOwnPropertySymbols?h.concat(Object.getOwnPropertySymbols(c)):h},a.prototype.listeners=function(h){var c=t?t+h:h,u=this._events[c];if(!u)return[];if(u.fn)return[u.fn];for(var d=0,_=u.length,f=new Array(_);d<_;d++)f[d]=u[d].fn;return f},a.prototype.listenerCount=function(h){var c=t?t+h:h,u=this._events[c];return u?u.fn?1:u.length:0},a.prototype.emit=function(h,c,u,d,_,f){var m=t?t+h:h;if(!this._events[m])return!1;var g=this._events[m],p=arguments.length,y,b;if(g.fn){switch(g.once&&this.removeListener(h,g.fn,void 0,!0),p){case 1:return g.fn.call(g.context),!0;case 2:return g.fn.call(g.context,c),!0;case 3:return g.fn.call(g.context,c,u),!0;case 4:return g.fn.call(g.context,c,u,d),!0;case 5:return g.fn.call(g.context,c,u,d,_),!0;case 6:return g.fn.call(g.context,c,u,d,_,f),!0}for(b=1,y=new Array(p-1);b<p;b++)y[b-1]=arguments[b];g.fn.apply(g.context,y)}else{var w=g.length,A;for(b=0;b<w;b++)switch(g[b].once&&this.removeListener(h,g[b].fn,void 0,!0),p){case 1:g[b].fn.call(g[b].context);break;case 2:g[b].fn.call(g[b].context,c);break;case 3:g[b].fn.call(g[b].context,c,u);break;case 4:g[b].fn.call(g[b].context,c,u,d);break;default:if(!y)for(A=1,y=new Array(p-1);A<p;A++)y[A-1]=arguments[A];g[b].fn.apply(g[b].context,y)}}return!0},a.prototype.on=function(h,c,u){return r(this,h,c,u,!1)},a.prototype.once=function(h,c,u){return r(this,h,c,u,!0)},a.prototype.removeListener=function(h,c,u,d){var _=t?t+h:h;if(!this._events[_])return this;if(!c)return o(this,_),this;var f=this._events[_];if(f.fn)f.fn===c&&(!d||f.once)&&(!u||f.context===u)&&o(this,_);else{for(var m=0,g=[],p=f.length;m<p;m++)(f[m].fn!==c||d&&!f[m].once||u&&f[m].context!==u)&&g.push(f[m]);g.length?this._events[_]=g.length===1?g[0]:g:o(this,_)}return this},a.prototype.removeAllListeners=function(h){var c;return h?(c=t?t+h:h,this._events[c]&&o(this,c)):(this._events=new i,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=t,a.EventEmitter=a,s.exports=a})(Dc);var Zg=Dc.exports;const Tt=ha(Zg),Qg=Math.PI*2,Jg=180/Math.PI,em=Math.PI/180;class X{constructor(e=0,t=0){this.x=0,this.y=0,this.x=e,this.y=t}clone(){return new X(this.x,this.y)}copyFrom(e){return this.set(e.x,e.y),this}copyTo(e){return e.set(this.x,this.y),e}equals(e){return e.x===this.x&&e.y===this.y}set(e=0,t=e){return this.x=e,this.y=t,this}toString(){return`[pixi.js/math:Point x=${this.x} y=${this.y}]`}static get shared(){return Gr.x=0,Gr.y=0,Gr}}const Gr=new X;class Z{constructor(e=1,t=0,i=0,n=1,r=0,o=0){this.array=null,this.a=e,this.b=t,this.c=i,this.d=n,this.tx=r,this.ty=o}fromArray(e){this.a=e[0],this.b=e[1],this.c=e[3],this.d=e[4],this.tx=e[2],this.ty=e[5]}set(e,t,i,n,r,o){return this.a=e,this.b=t,this.c=i,this.d=n,this.tx=r,this.ty=o,this}toArray(e,t){this.array||(this.array=new Float32Array(9));const i=t||this.array;return e?(i[0]=this.a,i[1]=this.b,i[2]=0,i[3]=this.c,i[4]=this.d,i[5]=0,i[6]=this.tx,i[7]=this.ty,i[8]=1):(i[0]=this.a,i[1]=this.c,i[2]=this.tx,i[3]=this.b,i[4]=this.d,i[5]=this.ty,i[6]=0,i[7]=0,i[8]=1),i}apply(e,t){t=t||new X;const i=e.x,n=e.y;return t.x=this.a*i+this.c*n+this.tx,t.y=this.b*i+this.d*n+this.ty,t}applyInverse(e,t){t=t||new X;const i=this.a,n=this.b,r=this.c,o=this.d,a=this.tx,l=this.ty,h=1/(i*o+r*-n),c=e.x,u=e.y;return t.x=o*h*c+-r*h*u+(l*r-a*o)*h,t.y=i*h*u+-n*h*c+(-l*i+a*n)*h,t}translate(e,t){return this.tx+=e,this.ty+=t,this}scale(e,t){return this.a*=e,this.d*=t,this.c*=e,this.b*=t,this.tx*=e,this.ty*=t,this}rotate(e){const t=Math.cos(e),i=Math.sin(e),n=this.a,r=this.c,o=this.tx;return this.a=n*t-this.b*i,this.b=n*i+this.b*t,this.c=r*t-this.d*i,this.d=r*i+this.d*t,this.tx=o*t-this.ty*i,this.ty=o*i+this.ty*t,this}append(e){const t=this.a,i=this.b,n=this.c,r=this.d;return this.a=e.a*t+e.b*n,this.b=e.a*i+e.b*r,this.c=e.c*t+e.d*n,this.d=e.c*i+e.d*r,this.tx=e.tx*t+e.ty*n+this.tx,this.ty=e.tx*i+e.ty*r+this.ty,this}appendFrom(e,t){const i=e.a,n=e.b,r=e.c,o=e.d,a=e.tx,l=e.ty,h=t.a,c=t.b,u=t.c,d=t.d;return this.a=i*h+n*u,this.b=i*c+n*d,this.c=r*h+o*u,this.d=r*c+o*d,this.tx=a*h+l*u+t.tx,this.ty=a*c+l*d+t.ty,this}setTransform(e,t,i,n,r,o,a,l,h){return this.a=Math.cos(a+h)*r,this.b=Math.sin(a+h)*r,this.c=-Math.sin(a-l)*o,this.d=Math.cos(a-l)*o,this.tx=e-(i*this.a+n*this.c),this.ty=t-(i*this.b+n*this.d),this}prepend(e){const t=this.tx;if(e.a!==1||e.b!==0||e.c!==0||e.d!==1){const i=this.a,n=this.c;this.a=i*e.a+this.b*e.c,this.b=i*e.b+this.b*e.d,this.c=n*e.a+this.d*e.c,this.d=n*e.b+this.d*e.d}return this.tx=t*e.a+this.ty*e.c+e.tx,this.ty=t*e.b+this.ty*e.d+e.ty,this}decompose(e){const t=this.a,i=this.b,n=this.c,r=this.d,o=e.pivot,a=-Math.atan2(-n,r),l=Math.atan2(i,t),h=Math.abs(a+l);return h<1e-5||Math.abs(Qg-h)<1e-5?(e.rotation=l,e.skew.x=e.skew.y=0):(e.rotation=0,e.skew.x=a,e.skew.y=l),e.scale.x=Math.sqrt(t*t+i*i),e.scale.y=Math.sqrt(n*n+r*r),e.position.x=this.tx+(o.x*t+o.y*n),e.position.y=this.ty+(o.x*i+o.y*r),e}invert(){const e=this.a,t=this.b,i=this.c,n=this.d,r=this.tx,o=e*n-t*i;return this.a=n/o,this.b=-t/o,this.c=-i/o,this.d=e/o,this.tx=(i*this.ty-n*r)/o,this.ty=-(e*this.ty-t*r)/o,this}isIdentity(){return this.a===1&&this.b===0&&this.c===0&&this.d===1&&this.tx===0&&this.ty===0}identity(){return this.a=1,this.b=0,this.c=0,this.d=1,this.tx=0,this.ty=0,this}clone(){const e=new Z;return e.a=this.a,e.b=this.b,e.c=this.c,e.d=this.d,e.tx=this.tx,e.ty=this.ty,e}copyTo(e){return e.a=this.a,e.b=this.b,e.c=this.c,e.d=this.d,e.tx=this.tx,e.ty=this.ty,e}copyFrom(e){return this.a=e.a,this.b=e.b,this.c=e.c,this.d=e.d,this.tx=e.tx,this.ty=e.ty,this}equals(e){return e.a===this.a&&e.b===this.b&&e.c===this.c&&e.d===this.d&&e.tx===this.tx&&e.ty===this.ty}toString(){return`[pixi.js:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`}static get IDENTITY(){return im.identity()}static get shared(){return tm.identity()}}const tm=new Z,im=new Z,Ai=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1],Si=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1],Ci=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1],Mi=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1],zo=[],Nc=[],mn=Math.sign;function sm(){for(let s=0;s<16;s++){const e=[];zo.push(e);for(let t=0;t<16;t++){const i=mn(Ai[s]*Ai[t]+Ci[s]*Si[t]),n=mn(Si[s]*Ai[t]+Mi[s]*Si[t]),r=mn(Ai[s]*Ci[t]+Ci[s]*Mi[t]),o=mn(Si[s]*Ci[t]+Mi[s]*Mi[t]);for(let a=0;a<16;a++)if(Ai[a]===i&&Si[a]===n&&Ci[a]===r&&Mi[a]===o){e.push(a);break}}}for(let s=0;s<16;s++){const e=new Z;e.set(Ai[s],Si[s],Ci[s],Mi[s],0,0),Nc.push(e)}}sm();const he={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MAIN_DIAGONAL:10,MIRROR_HORIZONTAL:12,REVERSE_DIAGONAL:14,uX:s=>Ai[s],uY:s=>Si[s],vX:s=>Ci[s],vY:s=>Mi[s],inv:s=>s&8?s&15:-s&7,add:(s,e)=>zo[s][e],sub:(s,e)=>zo[s][he.inv(e)],rotate180:s=>s^4,isVertical:s=>(s&3)===2,byDirection:(s,e)=>Math.abs(s)*2<=Math.abs(e)?e>=0?he.S:he.N:Math.abs(e)*2<=Math.abs(s)?s>0?he.E:he.W:e>0?s>0?he.SE:he.SW:s>0?he.NE:he.NW,matrixAppendRotationInv:(s,e,t=0,i=0)=>{const n=Nc[he.inv(e)];n.tx=t,n.ty=i,s.append(n)}},pn=[new X,new X,new X,new X];class se{constructor(e=0,t=0,i=0,n=0){this.type="rectangle",this.x=Number(e),this.y=Number(t),this.width=Number(i),this.height=Number(n)}get left(){return this.x}get right(){return this.x+this.width}get top(){return this.y}get bottom(){return this.y+this.height}isEmpty(){return this.left===this.right||this.top===this.bottom}static get EMPTY(){return new se(0,0,0,0)}clone(){return new se(this.x,this.y,this.width,this.height)}copyFromBounds(e){return this.x=e.minX,this.y=e.minY,this.width=e.maxX-e.minX,this.height=e.maxY-e.minY,this}copyFrom(e){return this.x=e.x,this.y=e.y,this.width=e.width,this.height=e.height,this}copyTo(e){return e.copyFrom(this),e}contains(e,t){return this.width<=0||this.height<=0?!1:e>=this.x&&e<this.x+this.width&&t>=this.y&&t<this.y+this.height}strokeContains(e,t,i,n=.5){const{width:r,height:o}=this;if(r<=0||o<=0)return!1;const a=this.x,l=this.y,h=i*(1-n),c=i-h,u=a-h,d=a+r+h,_=l-h,f=l+o+h,m=a+c,g=a+r-c,p=l+c,y=l+o-c;return e>=u&&e<=d&&t>=_&&t<=f&&!(e>m&&e<g&&t>p&&t<y)}intersects(e,t){if(!t){const R=this.x<e.x?e.x:this.x;if((this.right>e.right?e.right:this.right)<=R)return!1;const M=this.y<e.y?e.y:this.y;return(this.bottom>e.bottom?e.bottom:this.bottom)>M}const i=this.left,n=this.right,r=this.top,o=this.bottom;if(n<=i||o<=r)return!1;const a=pn[0].set(e.left,e.top),l=pn[1].set(e.left,e.bottom),h=pn[2].set(e.right,e.top),c=pn[3].set(e.right,e.bottom);if(h.x<=a.x||l.y<=a.y)return!1;const u=Math.sign(t.a*t.d-t.b*t.c);if(u===0||(t.apply(a,a),t.apply(l,l),t.apply(h,h),t.apply(c,c),Math.max(a.x,l.x,h.x,c.x)<=i||Math.min(a.x,l.x,h.x,c.x)>=n||Math.max(a.y,l.y,h.y,c.y)<=r||Math.min(a.y,l.y,h.y,c.y)>=o))return!1;const d=u*(l.y-a.y),_=u*(a.x-l.x),f=d*i+_*r,m=d*n+_*r,g=d*i+_*o,p=d*n+_*o;if(Math.max(f,m,g,p)<=d*a.x+_*a.y||Math.min(f,m,g,p)>=d*c.x+_*c.y)return!1;const y=u*(a.y-h.y),b=u*(h.x-a.x),w=y*i+b*r,A=y*n+b*r,v=y*i+b*o,S=y*n+b*o;return!(Math.max(w,A,v,S)<=y*a.x+b*a.y||Math.min(w,A,v,S)>=y*c.x+b*c.y)}pad(e=0,t=e){return this.x-=e,this.y-=t,this.width+=e*2,this.height+=t*2,this}fit(e){const t=Math.max(this.x,e.x),i=Math.min(this.x+this.width,e.x+e.width),n=Math.max(this.y,e.y),r=Math.min(this.y+this.height,e.y+e.height);return this.x=t,this.width=Math.max(i-t,0),this.y=n,this.height=Math.max(r-n,0),this}ceil(e=1,t=.001){const i=Math.ceil((this.x+this.width-t)*e)/e,n=Math.ceil((this.y+this.height-t)*e)/e;return this.x=Math.floor((this.x+t)*e)/e,this.y=Math.floor((this.y+t)*e)/e,this.width=i-this.x,this.height=n-this.y,this}enlarge(e){const t=Math.min(this.x,e.x),i=Math.max(this.x+this.width,e.x+e.width),n=Math.min(this.y,e.y),r=Math.max(this.y+this.height,e.y+e.height);return this.x=t,this.width=i-t,this.y=n,this.height=r-n,this}getBounds(e){return e||(e=new se),e.copyFrom(this),e}toString(){return`[pixi.js/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`}}const Dr={default:-1};function pe(s="default"){return Dr[s]===void 0&&(Dr[s]=-1),++Dr[s]}const Ja={},J="8.0.0",nm="8.3.4";function K(s,e,t=3){if(Ja[e])return;let i=new Error().stack;typeof i>"u"?console.warn("PixiJS Deprecation Warning: ",`${e}
Deprecated since v${s}`):(i=i.split(`
`).splice(t).join(`
`),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",`${e}
Deprecated since v${s}`),console.warn(i),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",`${e}
Deprecated since v${s}`),console.warn(i))),Ja[e]=!0}const Uc=()=>{};function Hn(s){return s+=s===0?1:0,--s,s|=s>>>1,s|=s>>>2,s|=s>>>4,s|=s>>>8,s|=s>>>16,s+1}function el(s){return!(s&s-1)&&!!s}function rm(s){const e={};for(const t in s)s[t]!==void 0&&(e[t]=s[t]);return e}const tl=Object.create(null);function om(s){const e=tl[s];return e===void 0&&(tl[s]=pe("resource")),e}const Wc=class Hc extends Tt{constructor(e={}){super(),this._resourceType="textureSampler",this._touched=0,this._maxAnisotropy=1,this.destroyed=!1,e={...Hc.defaultOptions,...e},this.addressMode=e.addressMode,this.addressModeU=e.addressModeU??this.addressModeU,this.addressModeV=e.addressModeV??this.addressModeV,this.addressModeW=e.addressModeW??this.addressModeW,this.scaleMode=e.scaleMode,this.magFilter=e.magFilter??this.magFilter,this.minFilter=e.minFilter??this.minFilter,this.mipmapFilter=e.mipmapFilter??this.mipmapFilter,this.lodMinClamp=e.lodMinClamp,this.lodMaxClamp=e.lodMaxClamp,this.compare=e.compare,this.maxAnisotropy=e.maxAnisotropy??1}set addressMode(e){this.addressModeU=e,this.addressModeV=e,this.addressModeW=e}get addressMode(){return this.addressModeU}set wrapMode(e){K(J,"TextureStyle.wrapMode is now TextureStyle.addressMode"),this.addressMode=e}get wrapMode(){return this.addressMode}set scaleMode(e){this.magFilter=e,this.minFilter=e,this.mipmapFilter=e}get scaleMode(){return this.magFilter}set maxAnisotropy(e){this._maxAnisotropy=Math.min(e,16),this._maxAnisotropy>1&&(this.scaleMode="linear")}get maxAnisotropy(){return this._maxAnisotropy}get _resourceId(){return this._sharedResourceId||this._generateResourceId()}update(){this.emit("change",this),this._sharedResourceId=null}_generateResourceId(){const e=`${this.addressModeU}-${this.addressModeV}-${this.addressModeW}-${this.magFilter}-${this.minFilter}-${this.mipmapFilter}-${this.lodMinClamp}-${this.lodMaxClamp}-${this.compare}-${this._maxAnisotropy}`;return this._sharedResourceId=om(e),this._resourceId}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this.removeAllListeners()}};Wc.defaultOptions={addressMode:"clamp-to-edge",scaleMode:"linear"};let am=Wc;const qc=class Vc extends Tt{constructor(e={}){super(),this.options=e,this.uid=pe("textureSource"),this._resourceType="textureSource",this._resourceId=pe("resource"),this.uploadMethodId="unknown",this._resolution=1,this.pixelWidth=1,this.pixelHeight=1,this.width=1,this.height=1,this.sampleCount=1,this.mipLevelCount=1,this.autoGenerateMipmaps=!1,this.format="rgba8unorm",this.dimension="2d",this.antialias=!1,this._touched=0,this._batchTick=-1,this._textureBindLocation=-1,e={...Vc.defaultOptions,...e},this.label=e.label??"",this.resource=e.resource,this.autoGarbageCollect=e.autoGarbageCollect,this._resolution=e.resolution,e.width?this.pixelWidth=e.width*this._resolution:this.pixelWidth=this.resource?this.resourceWidth??1:1,e.height?this.pixelHeight=e.height*this._resolution:this.pixelHeight=this.resource?this.resourceHeight??1:1,this.width=this.pixelWidth/this._resolution,this.height=this.pixelHeight/this._resolution,this.format=e.format,this.dimension=e.dimensions,this.mipLevelCount=e.mipLevelCount,this.autoGenerateMipmaps=e.autoGenerateMipmaps,this.sampleCount=e.sampleCount,this.antialias=e.antialias,this.alphaMode=e.alphaMode,this.style=new am(rm(e)),this.destroyed=!1,this._refreshPOT()}get source(){return this}get style(){return this._style}set style(e){this.style!==e&&(this._style?.off("change",this._onStyleChange,this),this._style=e,this._style?.on("change",this._onStyleChange,this),this._onStyleChange())}get addressMode(){return this._style.addressMode}set addressMode(e){this._style.addressMode=e}get repeatMode(){return this._style.addressMode}set repeatMode(e){this._style.addressMode=e}get magFilter(){return this._style.magFilter}set magFilter(e){this._style.magFilter=e}get minFilter(){return this._style.minFilter}set minFilter(e){this._style.minFilter=e}get mipmapFilter(){return this._style.mipmapFilter}set mipmapFilter(e){this._style.mipmapFilter=e}get lodMinClamp(){return this._style.lodMinClamp}set lodMinClamp(e){this._style.lodMinClamp=e}get lodMaxClamp(){return this._style.lodMaxClamp}set lodMaxClamp(e){this._style.lodMaxClamp=e}_onStyleChange(){this.emit("styleChange",this)}update(){if(this.resource){const e=this._resolution;if(this.resize(this.resourceWidth/e,this.resourceHeight/e))return}this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._style&&(this._style.destroy(),this._style=null),this.uploadMethodId=null,this.resource=null,this.removeAllListeners()}unload(){this._resourceId=pe("resource"),this.emit("change",this),this.emit("unload",this)}get resourceWidth(){const{resource:e}=this;return e.naturalWidth||e.videoWidth||e.displayWidth||e.width}get resourceHeight(){const{resource:e}=this;return e.naturalHeight||e.videoHeight||e.displayHeight||e.height}get resolution(){return this._resolution}set resolution(e){this._resolution!==e&&(this._resolution=e,this.width=this.pixelWidth/e,this.height=this.pixelHeight/e)}resize(e,t,i){i||(i=this._resolution),e||(e=this.width),t||(t=this.height);const n=Math.round(e*i),r=Math.round(t*i);return this.width=n/i,this.height=r/i,this._resolution=i,this.pixelWidth===n&&this.pixelHeight===r?!1:(this._refreshPOT(),this.pixelWidth=n,this.pixelHeight=r,this.emit("resize",this),this._resourceId=pe("resource"),this.emit("change",this),!0)}updateMipmaps(){this.autoGenerateMipmaps&&this.mipLevelCount>1&&this.emit("updateMipmaps",this)}set wrapMode(e){this._style.wrapMode=e}get wrapMode(){return this._style.wrapMode}set scaleMode(e){this._style.scaleMode=e}get scaleMode(){return this._style.scaleMode}_refreshPOT(){this.isPowerOfTwo=el(this.pixelWidth)&&el(this.pixelHeight)}static test(e){throw new Error("Unimplemented")}};qc.defaultOptions={resolution:1,format:"bgra8unorm",alphaMode:"premultiply-alpha-on-upload",dimensions:"2d",mipLevelCount:1,autoGenerateMipmaps:!1,sampleCount:1,antialias:!1,autoGarbageCollect:!1};let Pt=qc;class _a extends Pt{constructor(e){const t=e.resource||new Float32Array(e.width*e.height*4);let i=e.format;i||(t instanceof Float32Array?i="rgba32float":t instanceof Int32Array||t instanceof Uint32Array?i="rgba32uint":t instanceof Int16Array||t instanceof Uint16Array?i="rgba16uint":(t instanceof Int8Array,i="bgra8unorm")),super({...e,resource:t,format:i}),this.uploadMethodId="buffer"}static test(e){return e instanceof Int8Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array}}_a.extension=D.TextureSource;const il=new Z;class lm{constructor(e,t){this.mapCoord=new Z,this.uClampFrame=new Float32Array(4),this.uClampOffset=new Float32Array(2),this._textureID=-1,this._updateID=0,this.clampOffset=0,typeof t>"u"?this.clampMargin=e.width<10?0:.5:this.clampMargin=t,this.isSimple=!1,this.texture=e}get texture(){return this._texture}set texture(e){this.texture!==e&&(this._texture?.removeListener("update",this.update,this),this._texture=e,this._texture.addListener("update",this.update,this),this.update())}multiplyUvs(e,t){t===void 0&&(t=e);const i=this.mapCoord;for(let n=0;n<e.length;n+=2){const r=e[n],o=e[n+1];t[n]=r*i.a+o*i.c+i.tx,t[n+1]=r*i.b+o*i.d+i.ty}return t}update(){const e=this._texture;this._updateID++;const t=e.uvs;this.mapCoord.set(t.x1-t.x0,t.y1-t.y0,t.x3-t.x0,t.y3-t.y0,t.x0,t.y0);const i=e.orig,n=e.trim;n&&(il.set(i.width/n.width,0,0,i.height/n.height,-n.x/n.width,-n.y/n.height),this.mapCoord.append(il));const r=e.source,o=this.uClampFrame,a=this.clampMargin/r._resolution,l=this.clampOffset/r._resolution;return o[0]=(e.frame.x+a+l)/r.width,o[1]=(e.frame.y+a+l)/r.height,o[2]=(e.frame.x+e.frame.width-a+l)/r.width,o[3]=(e.frame.y+e.frame.height-a+l)/r.height,this.uClampOffset[0]=this.clampOffset/r.pixelWidth,this.uClampOffset[1]=this.clampOffset/r.pixelHeight,this.isSimple=e.frame.width===r.width&&e.frame.height===r.height&&e.rotate===0,!0}}class Y extends Tt{constructor({source:e,label:t,frame:i,orig:n,trim:r,defaultAnchor:o,defaultBorders:a,rotate:l,dynamic:h}={}){if(super(),this.uid=pe("texture"),this.uvs={x0:0,y0:0,x1:0,y1:0,x2:0,y2:0,x3:0,y3:0},this.frame=new se,this.noFrame=!1,this.dynamic=!1,this.isTexture=!0,this.label=t,this.source=e?.source??new Pt,this.noFrame=!i,i)this.frame.copyFrom(i);else{const{width:c,height:u}=this._source;this.frame.width=c,this.frame.height=u}this.orig=n||this.frame,this.trim=r,this.rotate=l??0,this.defaultAnchor=o,this.defaultBorders=a,this.destroyed=!1,this.dynamic=h||!1,this.updateUvs()}set source(e){this._source&&this._source.off("resize",this.update,this),this._source=e,e.on("resize",this.update,this),this.emit("update",this)}get source(){return this._source}get textureMatrix(){return this._textureMatrix||(this._textureMatrix=new lm(this)),this._textureMatrix}get width(){return this.orig.width}get height(){return this.orig.height}updateUvs(){const{uvs:e,frame:t}=this,{width:i,height:n}=this._source,r=t.x/i,o=t.y/n,a=t.width/i,l=t.height/n;let h=this.rotate;if(h){const c=a/2,u=l/2,d=r+c,_=o+u;h=he.add(h,he.NW),e.x0=d+c*he.uX(h),e.y0=_+u*he.uY(h),h=he.add(h,2),e.x1=d+c*he.uX(h),e.y1=_+u*he.uY(h),h=he.add(h,2),e.x2=d+c*he.uX(h),e.y2=_+u*he.uY(h),h=he.add(h,2),e.x3=d+c*he.uX(h),e.y3=_+u*he.uY(h)}else e.x0=r,e.y0=o,e.x1=r+a,e.y1=o,e.x2=r+a,e.y2=o+l,e.x3=r,e.y3=o+l}destroy(e=!1){this._source&&e&&(this._source.destroy(),this._source=null),this._textureMatrix=null,this.destroyed=!0,this.emit("destroy",this),this.removeAllListeners()}update(){this.noFrame&&(this.frame.width=this._source.width,this.frame.height=this._source.height),this.updateUvs(),this.emit("update",this)}get baseTexture(){return K(J,"Texture.baseTexture is now Texture.source"),this._source}}Y.EMPTY=new Y({label:"EMPTY",source:new Pt({label:"EMPTY"})});Y.EMPTY.destroy=Uc;Y.WHITE=new Y({source:new _a({resource:new Uint8Array([255,255,255,255]),width:1,height:1,alphaMode:"premultiply-alpha-on-upload",label:"WHITE"}),label:"WHITE"});Y.WHITE.destroy=Uc;function hm(s,e,t){const{width:i,height:n}=t.orig,r=t.trim;if(r){const o=r.width,a=r.height;s.minX=r.x-e._x*i,s.maxX=s.minX+o,s.minY=r.y-e._y*n,s.maxY=s.minY+a}else s.minX=-e._x*i,s.maxX=s.minX+i,s.minY=-e._y*n,s.maxY=s.minY+n}const sl=new Z;class St{constructor(e=1/0,t=1/0,i=-1/0,n=-1/0){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=sl,this.minX=e,this.minY=t,this.maxX=i,this.maxY=n}isEmpty(){return this.minX>this.maxX||this.minY>this.maxY}get rectangle(){this._rectangle||(this._rectangle=new se);const e=this._rectangle;return this.minX>this.maxX||this.minY>this.maxY?(e.x=0,e.y=0,e.width=0,e.height=0):e.copyFromBounds(this),e}clear(){return this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=sl,this}set(e,t,i,n){this.minX=e,this.minY=t,this.maxX=i,this.maxY=n}addFrame(e,t,i,n,r){r||(r=this.matrix);const o=r.a,a=r.b,l=r.c,h=r.d,c=r.tx,u=r.ty;let d=this.minX,_=this.minY,f=this.maxX,m=this.maxY,g=o*e+l*t+c,p=a*e+h*t+u;g<d&&(d=g),p<_&&(_=p),g>f&&(f=g),p>m&&(m=p),g=o*i+l*t+c,p=a*i+h*t+u,g<d&&(d=g),p<_&&(_=p),g>f&&(f=g),p>m&&(m=p),g=o*e+l*n+c,p=a*e+h*n+u,g<d&&(d=g),p<_&&(_=p),g>f&&(f=g),p>m&&(m=p),g=o*i+l*n+c,p=a*i+h*n+u,g<d&&(d=g),p<_&&(_=p),g>f&&(f=g),p>m&&(m=p),this.minX=d,this.minY=_,this.maxX=f,this.maxY=m}addRect(e,t){this.addFrame(e.x,e.y,e.x+e.width,e.y+e.height,t)}addBounds(e,t){this.addFrame(e.minX,e.minY,e.maxX,e.maxY,t)}addBoundsMask(e){this.minX=this.minX>e.minX?this.minX:e.minX,this.minY=this.minY>e.minY?this.minY:e.minY,this.maxX=this.maxX<e.maxX?this.maxX:e.maxX,this.maxY=this.maxY<e.maxY?this.maxY:e.maxY}applyMatrix(e){const t=this.minX,i=this.minY,n=this.maxX,r=this.maxY,{a:o,b:a,c:l,d:h,tx:c,ty:u}=e;let d=o*t+l*i+c,_=a*t+h*i+u;this.minX=d,this.minY=_,this.maxX=d,this.maxY=_,d=o*n+l*i+c,_=a*n+h*i+u,this.minX=d<this.minX?d:this.minX,this.minY=_<this.minY?_:this.minY,this.maxX=d>this.maxX?d:this.maxX,this.maxY=_>this.maxY?_:this.maxY,d=o*t+l*r+c,_=a*t+h*r+u,this.minX=d<this.minX?d:this.minX,this.minY=_<this.minY?_:this.minY,this.maxX=d>this.maxX?d:this.maxX,this.maxY=_>this.maxY?_:this.maxY,d=o*n+l*r+c,_=a*n+h*r+u,this.minX=d<this.minX?d:this.minX,this.minY=_<this.minY?_:this.minY,this.maxX=d>this.maxX?d:this.maxX,this.maxY=_>this.maxY?_:this.maxY}fit(e){return this.minX<e.left&&(this.minX=e.left),this.maxX>e.right&&(this.maxX=e.right),this.minY<e.top&&(this.minY=e.top),this.maxY>e.bottom&&(this.maxY=e.bottom),this}fitBounds(e,t,i,n){return this.minX<e&&(this.minX=e),this.maxX>t&&(this.maxX=t),this.minY<i&&(this.minY=i),this.maxY>n&&(this.maxY=n),this}pad(e,t=e){return this.minX-=e,this.maxX+=e,this.minY-=t,this.maxY+=t,this}ceil(){return this.minX=Math.floor(this.minX),this.minY=Math.floor(this.minY),this.maxX=Math.ceil(this.maxX),this.maxY=Math.ceil(this.maxY),this}clone(){return new St(this.minX,this.minY,this.maxX,this.maxY)}scale(e,t=e){return this.minX*=e,this.minY*=t,this.maxX*=e,this.maxY*=t,this}get x(){return this.minX}set x(e){const t=this.maxX-this.minX;this.minX=e,this.maxX=e+t}get y(){return this.minY}set y(e){const t=this.maxY-this.minY;this.minY=e,this.maxY=e+t}get width(){return this.maxX-this.minX}set width(e){this.maxX=this.minX+e}get height(){return this.maxY-this.minY}set height(e){this.maxY=this.minY+e}get left(){return this.minX}get right(){return this.maxX}get top(){return this.minY}get bottom(){return this.maxY}get isPositive(){return this.maxX-this.minX>0&&this.maxY-this.minY>0}get isValid(){return this.minX+this.minY!==1/0}addVertexData(e,t,i,n){let r=this.minX,o=this.minY,a=this.maxX,l=this.maxY;n||(n=this.matrix);const h=n.a,c=n.b,u=n.c,d=n.d,_=n.tx,f=n.ty;for(let m=t;m<i;m+=2){const g=e[m],p=e[m+1],y=h*g+u*p+_,b=c*g+d*p+f;r=y<r?y:r,o=b<o?b:o,a=y>a?y:a,l=b>l?b:l}this.minX=r,this.minY=o,this.maxX=a,this.maxY=l}containsPoint(e,t){return this.minX<=e&&this.minY<=t&&this.maxX>=e&&this.maxY>=t}toString(){return`[pixi.js:Bounds minX=${this.minX} minY=${this.minY} maxX=${this.maxX} maxY=${this.maxY} width=${this.width} height=${this.height}]`}copyFrom(e){return this.minX=e.minX,this.minY=e.minY,this.maxX=e.maxX,this.maxY=e.maxY,this}}var cm={grad:.9,turn:360,rad:360/(2*Math.PI)},Ut=function(s){return typeof s=="string"?s.length>0:typeof s=="number"},Me=function(s,e,t){return e===void 0&&(e=0),t===void 0&&(t=Math.pow(10,e)),Math.round(t*s)/t+0},mt=function(s,e,t){return e===void 0&&(e=0),t===void 0&&(t=1),s>t?t:s>e?s:e},Xc=function(s){return(s=isFinite(s)?s%360:0)>0?s:s+360},nl=function(s){return{r:mt(s.r,0,255),g:mt(s.g,0,255),b:mt(s.b,0,255),a:mt(s.a)}},Nr=function(s){return{r:Me(s.r),g:Me(s.g),b:Me(s.b),a:Me(s.a,3)}},um=/^#([0-9a-f]{3,8})$/i,bn=function(s){var e=s.toString(16);return e.length<2?"0"+e:e},Yc=function(s){var e=s.r,t=s.g,i=s.b,n=s.a,r=Math.max(e,t,i),o=r-Math.min(e,t,i),a=o?r===e?(t-i)/o:r===t?2+(i-e)/o:4+(e-t)/o:0;return{h:60*(a<0?a+6:a),s:r?o/r*100:0,v:r/255*100,a:n}},jc=function(s){var e=s.h,t=s.s,i=s.v,n=s.a;e=e/360*6,t/=100,i/=100;var r=Math.floor(e),o=i*(1-t),a=i*(1-(e-r)*t),l=i*(1-(1-e+r)*t),h=r%6;return{r:255*[i,a,o,o,l,i][h],g:255*[l,i,i,a,o,o][h],b:255*[o,o,l,i,i,a][h],a:n}},rl=function(s){return{h:Xc(s.h),s:mt(s.s,0,100),l:mt(s.l,0,100),a:mt(s.a)}},ol=function(s){return{h:Me(s.h),s:Me(s.s),l:Me(s.l),a:Me(s.a,3)}},al=function(s){return jc((t=(e=s).s,{h:e.h,s:(t*=((i=e.l)<50?i:100-i)/100)>0?2*t/(i+t)*100:0,v:i+t,a:e.a}));var e,t,i},qs=function(s){return{h:(e=Yc(s)).h,s:(n=(200-(t=e.s))*(i=e.v)/100)>0&&n<200?t*i/100/(n<=100?n:200-n)*100:0,l:n/2,a:e.a};var e,t,i,n},dm=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,_m=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,fm=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,gm=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Io={string:[[function(s){var e=um.exec(s);return e?(s=e[1]).length<=4?{r:parseInt(s[0]+s[0],16),g:parseInt(s[1]+s[1],16),b:parseInt(s[2]+s[2],16),a:s.length===4?Me(parseInt(s[3]+s[3],16)/255,2):1}:s.length===6||s.length===8?{r:parseInt(s.substr(0,2),16),g:parseInt(s.substr(2,2),16),b:parseInt(s.substr(4,2),16),a:s.length===8?Me(parseInt(s.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(s){var e=fm.exec(s)||gm.exec(s);return e?e[2]!==e[4]||e[4]!==e[6]?null:nl({r:Number(e[1])/(e[2]?100/255:1),g:Number(e[3])/(e[4]?100/255:1),b:Number(e[5])/(e[6]?100/255:1),a:e[7]===void 0?1:Number(e[7])/(e[8]?100:1)}):null},"rgb"],[function(s){var e=dm.exec(s)||_m.exec(s);if(!e)return null;var t,i,n=rl({h:(t=e[1],i=e[2],i===void 0&&(i="deg"),Number(t)*(cm[i]||1)),s:Number(e[3]),l:Number(e[4]),a:e[5]===void 0?1:Number(e[5])/(e[6]?100:1)});return al(n)},"hsl"]],object:[[function(s){var e=s.r,t=s.g,i=s.b,n=s.a,r=n===void 0?1:n;return Ut(e)&&Ut(t)&&Ut(i)?nl({r:Number(e),g:Number(t),b:Number(i),a:Number(r)}):null},"rgb"],[function(s){var e=s.h,t=s.s,i=s.l,n=s.a,r=n===void 0?1:n;if(!Ut(e)||!Ut(t)||!Ut(i))return null;var o=rl({h:Number(e),s:Number(t),l:Number(i),a:Number(r)});return al(o)},"hsl"],[function(s){var e=s.h,t=s.s,i=s.v,n=s.a,r=n===void 0?1:n;if(!Ut(e)||!Ut(t)||!Ut(i))return null;var o=function(a){return{h:Xc(a.h),s:mt(a.s,0,100),v:mt(a.v,0,100),a:mt(a.a)}}({h:Number(e),s:Number(t),v:Number(i),a:Number(r)});return jc(o)},"hsv"]]},ll=function(s,e){for(var t=0;t<e.length;t++){var i=e[t][0](s);if(i)return[i,e[t][1]]}return[null,void 0]},mm=function(s){return typeof s=="string"?ll(s.trim(),Io.string):typeof s=="object"&&s!==null?ll(s,Io.object):[null,void 0]},Ur=function(s,e){var t=qs(s);return{h:t.h,s:mt(t.s+100*e,0,100),l:t.l,a:t.a}},Wr=function(s){return(299*s.r+587*s.g+114*s.b)/1e3/255},hl=function(s,e){var t=qs(s);return{h:t.h,s:t.s,l:mt(t.l+100*e,0,100),a:t.a}},Eo=function(){function s(e){this.parsed=mm(e)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return s.prototype.isValid=function(){return this.parsed!==null},s.prototype.brightness=function(){return Me(Wr(this.rgba),2)},s.prototype.isDark=function(){return Wr(this.rgba)<.5},s.prototype.isLight=function(){return Wr(this.rgba)>=.5},s.prototype.toHex=function(){return e=Nr(this.rgba),t=e.r,i=e.g,n=e.b,o=(r=e.a)<1?bn(Me(255*r)):"","#"+bn(t)+bn(i)+bn(n)+o;var e,t,i,n,r,o},s.prototype.toRgb=function(){return Nr(this.rgba)},s.prototype.toRgbString=function(){return e=Nr(this.rgba),t=e.r,i=e.g,n=e.b,(r=e.a)<1?"rgba("+t+", "+i+", "+n+", "+r+")":"rgb("+t+", "+i+", "+n+")";var e,t,i,n,r},s.prototype.toHsl=function(){return ol(qs(this.rgba))},s.prototype.toHslString=function(){return e=ol(qs(this.rgba)),t=e.h,i=e.s,n=e.l,(r=e.a)<1?"hsla("+t+", "+i+"%, "+n+"%, "+r+")":"hsl("+t+", "+i+"%, "+n+"%)";var e,t,i,n,r},s.prototype.toHsv=function(){return e=Yc(this.rgba),{h:Me(e.h),s:Me(e.s),v:Me(e.v),a:Me(e.a,3)};var e},s.prototype.invert=function(){return Lt({r:255-(e=this.rgba).r,g:255-e.g,b:255-e.b,a:e.a});var e},s.prototype.saturate=function(e){return e===void 0&&(e=.1),Lt(Ur(this.rgba,e))},s.prototype.desaturate=function(e){return e===void 0&&(e=.1),Lt(Ur(this.rgba,-e))},s.prototype.grayscale=function(){return Lt(Ur(this.rgba,-1))},s.prototype.lighten=function(e){return e===void 0&&(e=.1),Lt(hl(this.rgba,e))},s.prototype.darken=function(e){return e===void 0&&(e=.1),Lt(hl(this.rgba,-e))},s.prototype.rotate=function(e){return e===void 0&&(e=15),this.hue(this.hue()+e)},s.prototype.alpha=function(e){return typeof e=="number"?Lt({r:(t=this.rgba).r,g:t.g,b:t.b,a:e}):Me(this.rgba.a,3);var t},s.prototype.hue=function(e){var t=qs(this.rgba);return typeof e=="number"?Lt({h:e,s:t.s,l:t.l,a:t.a}):Me(t.h)},s.prototype.isEqual=function(e){return this.toHex()===Lt(e).toHex()},s}(),Lt=function(s){return s instanceof Eo?s:new Eo(s)},cl=[],pm=function(s){s.forEach(function(e){cl.indexOf(e)<0&&(e(Eo,Io),cl.push(e))})};function bm(s,e){var t={white:"#ffffff",bisque:"#ffe4c4",blue:"#0000ff",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",antiquewhite:"#faebd7",aqua:"#00ffff",azure:"#f0ffff",whitesmoke:"#f5f5f5",papayawhip:"#ffefd5",plum:"#dda0dd",blanchedalmond:"#ffebcd",black:"#000000",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",cornsilk:"#fff8dc",cornflowerblue:"#6495ed",burlywood:"#deb887",aquamarine:"#7fffd4",beige:"#f5f5dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkkhaki:"#bdb76b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",peachpuff:"#ffdab9",darkmagenta:"#8b008b",darkred:"#8b0000",darkorchid:"#9932cc",darkorange:"#ff8c00",darkslateblue:"#483d8b",gray:"#808080",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",deeppink:"#ff1493",deepskyblue:"#00bfff",wheat:"#f5deb3",firebrick:"#b22222",floralwhite:"#fffaf0",ghostwhite:"#f8f8ff",darkviolet:"#9400d3",magenta:"#ff00ff",green:"#008000",dodgerblue:"#1e90ff",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",blueviolet:"#8a2be2",forestgreen:"#228b22",lawngreen:"#7cfc00",indianred:"#cd5c5c",indigo:"#4b0082",fuchsia:"#ff00ff",brown:"#a52a2a",maroon:"#800000",mediumblue:"#0000cd",lightcoral:"#f08080",darkturquoise:"#00ced1",lightcyan:"#e0ffff",ivory:"#fffff0",lightyellow:"#ffffe0",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",linen:"#faf0e6",mediumaquamarine:"#66cdaa",lemonchiffon:"#fffacd",lime:"#00ff00",khaki:"#f0e68c",mediumseagreen:"#3cb371",limegreen:"#32cd32",mediumspringgreen:"#00fa9a",lightskyblue:"#87cefa",lightblue:"#add8e6",midnightblue:"#191970",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",mintcream:"#f5fffa",lightslategray:"#778899",lightslategrey:"#778899",navajowhite:"#ffdead",navy:"#000080",mediumvioletred:"#c71585",powderblue:"#b0e0e6",palegoldenrod:"#eee8aa",oldlace:"#fdf5e6",paleturquoise:"#afeeee",mediumturquoise:"#48d1cc",mediumorchid:"#ba55d3",rebeccapurple:"#663399",lightsteelblue:"#b0c4de",mediumslateblue:"#7b68ee",thistle:"#d8bfd8",tan:"#d2b48c",orchid:"#da70d6",mediumpurple:"#9370db",purple:"#800080",pink:"#ffc0cb",skyblue:"#87ceeb",springgreen:"#00ff7f",palegreen:"#98fb98",red:"#ff0000",yellow:"#ffff00",slateblue:"#6a5acd",lavenderblush:"#fff0f5",peru:"#cd853f",palevioletred:"#db7093",violet:"#ee82ee",teal:"#008080",slategray:"#708090",slategrey:"#708090",aliceblue:"#f0f8ff",darkseagreen:"#8fbc8f",darkolivegreen:"#556b2f",greenyellow:"#adff2f",seagreen:"#2e8b57",seashell:"#fff5ee",tomato:"#ff6347",silver:"#c0c0c0",sienna:"#a0522d",lavender:"#e6e6fa",lightgreen:"#90ee90",orange:"#ffa500",orangered:"#ff4500",steelblue:"#4682b4",royalblue:"#4169e1",turquoise:"#40e0d0",yellowgreen:"#9acd32",salmon:"#fa8072",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",darksalmon:"#e9967a",lightgoldenrodyellow:"#fafad2",snow:"#fffafa",lightgrey:"#d3d3d3",lightgray:"#d3d3d3",dimgray:"#696969",dimgrey:"#696969",olivedrab:"#6b8e23",olive:"#808000"},i={};for(var n in t)i[t[n]]=n;var r={};s.prototype.toName=function(o){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return"transparent";var a,l,h=i[this.toHex()];if(h)return h;if(o?.closest){var c=this.toRgb(),u=1/0,d="black";if(!r.length)for(var _ in t)r[_]=new s(t[_]).toRgb();for(var f in t){var m=(a=c,l=r[f],Math.pow(a.r-l.r,2)+Math.pow(a.g-l.g,2)+Math.pow(a.b-l.b,2));m<u&&(u=m,d=f)}return d}},e.string.push([function(o){var a=o.toLowerCase(),l=a==="transparent"?"#0000":t[a];return l?new s(l).toRgb():null},"name"])}pm([bm]);const ds=class Ds{constructor(e=16777215){this._value=null,this._components=new Float32Array(4),this._components.fill(1),this._int=16777215,this.value=e}get red(){return this._components[0]}get green(){return this._components[1]}get blue(){return this._components[2]}get alpha(){return this._components[3]}setValue(e){return this.value=e,this}set value(e){if(e instanceof Ds)this._value=this._cloneSource(e._value),this._int=e._int,this._components.set(e._components);else{if(e===null)throw new Error("Cannot set Color#value to null");(this._value===null||!this._isSourceEqual(this._value,e))&&(this._value=this._cloneSource(e),this._normalize(this._value))}}get value(){return this._value}_cloneSource(e){return typeof e=="string"||typeof e=="number"||e instanceof Number||e===null?e:Array.isArray(e)||ArrayBuffer.isView(e)?e.slice(0):typeof e=="object"&&e!==null?{...e}:e}_isSourceEqual(e,t){const i=typeof e;if(i!==typeof t)return!1;if(i==="number"||i==="string"||e instanceof Number)return e===t;if(Array.isArray(e)&&Array.isArray(t)||ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return e.length!==t.length?!1:e.every((r,o)=>r===t[o]);if(e!==null&&t!==null){const r=Object.keys(e),o=Object.keys(t);return r.length!==o.length?!1:r.every(a=>e[a]===t[a])}return e===t}toRgba(){const[e,t,i,n]=this._components;return{r:e,g:t,b:i,a:n}}toRgb(){const[e,t,i]=this._components;return{r:e,g:t,b:i}}toRgbaString(){const[e,t,i]=this.toUint8RgbArray();return`rgba(${e},${t},${i},${this.alpha})`}toUint8RgbArray(e){const[t,i,n]=this._components;return this._arrayRgb||(this._arrayRgb=[]),e||(e=this._arrayRgb),e[0]=Math.round(t*255),e[1]=Math.round(i*255),e[2]=Math.round(n*255),e}toArray(e){this._arrayRgba||(this._arrayRgba=[]),e||(e=this._arrayRgba);const[t,i,n,r]=this._components;return e[0]=t,e[1]=i,e[2]=n,e[3]=r,e}toRgbArray(e){this._arrayRgb||(this._arrayRgb=[]),e||(e=this._arrayRgb);const[t,i,n]=this._components;return e[0]=t,e[1]=i,e[2]=n,e}toNumber(){return this._int}toBgrNumber(){const[e,t,i]=this.toUint8RgbArray();return(i<<16)+(t<<8)+e}toLittleEndianNumber(){const e=this._int;return(e>>16)+(e&65280)+((e&255)<<16)}multiply(e){const[t,i,n,r]=Ds._temp.setValue(e)._components;return this._components[0]*=t,this._components[1]*=i,this._components[2]*=n,this._components[3]*=r,this._refreshInt(),this._value=null,this}premultiply(e,t=!0){return t&&(this._components[0]*=e,this._components[1]*=e,this._components[2]*=e),this._components[3]=e,this._refreshInt(),this._value=null,this}toPremultiplied(e,t=!0){if(e===1)return(255<<24)+this._int;if(e===0)return t?0:this._int;let i=this._int>>16&255,n=this._int>>8&255,r=this._int&255;return t&&(i=i*e+.5|0,n=n*e+.5|0,r=r*e+.5|0),(e*255<<24)+(i<<16)+(n<<8)+r}toHex(){const e=this._int.toString(16);return`#${"000000".substring(0,6-e.length)+e}`}toHexa(){const t=Math.round(this._components[3]*255).toString(16);return this.toHex()+"00".substring(0,2-t.length)+t}setAlpha(e){return this._components[3]=this._clamp(e),this}_normalize(e){let t,i,n,r;if((typeof e=="number"||e instanceof Number)&&e>=0&&e<=16777215){const o=e;t=(o>>16&255)/255,i=(o>>8&255)/255,n=(o&255)/255,r=1}else if((Array.isArray(e)||e instanceof Float32Array)&&e.length>=3&&e.length<=4)e=this._clamp(e),[t,i,n,r=1]=e;else if((e instanceof Uint8Array||e instanceof Uint8ClampedArray)&&e.length>=3&&e.length<=4)e=this._clamp(e,0,255),[t,i,n,r=255]=e,t/=255,i/=255,n/=255,r/=255;else if(typeof e=="string"||typeof e=="object"){if(typeof e=="string"){const a=Ds.HEX_PATTERN.exec(e);a&&(e=`#${a[2]}`)}const o=Lt(e);o.isValid()&&({r:t,g:i,b:n,a:r}=o.rgba,t/=255,i/=255,n/=255)}if(t!==void 0)this._components[0]=t,this._components[1]=i,this._components[2]=n,this._components[3]=r,this._refreshInt();else throw new Error(`Unable to convert color ${e}`)}_refreshInt(){this._clamp(this._components);const[e,t,i]=this._components;this._int=(e*255<<16)+(t*255<<8)+(i*255|0)}_clamp(e,t=0,i=1){return typeof e=="number"?Math.min(Math.max(e,t),i):(e.forEach((n,r)=>{e[r]=Math.min(Math.max(n,t),i)}),e)}static isColorLike(e){return typeof e=="number"||typeof e=="string"||e instanceof Number||e instanceof Ds||Array.isArray(e)||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Float32Array||e.r!==void 0&&e.g!==void 0&&e.b!==void 0||e.r!==void 0&&e.g!==void 0&&e.b!==void 0&&e.a!==void 0||e.h!==void 0&&e.s!==void 0&&e.l!==void 0||e.h!==void 0&&e.s!==void 0&&e.l!==void 0&&e.a!==void 0||e.h!==void 0&&e.s!==void 0&&e.v!==void 0||e.h!==void 0&&e.s!==void 0&&e.v!==void 0&&e.a!==void 0}};ds.shared=new ds;ds._temp=new ds;ds.HEX_PATTERN=/^(#|0x)?(([a-f0-9]{3}){1,2}([a-f0-9]{2})?)$/i;let fe=ds;const ym={cullArea:null,cullable:!1,cullableChildren:!0};class fa{constructor(e,t){this._pool=[],this._count=0,this._index=0,this._classType=e,t&&this.prepopulate(t)}prepopulate(e){for(let t=0;t<e;t++)this._pool[this._index++]=new this._classType;this._count+=e}get(e){let t;return this._index>0?t=this._pool[--this._index]:t=new this._classType,t.init?.(e),t}return(e){e.reset?.(),this._pool[this._index++]=e}get totalSize(){return this._count}get totalFree(){return this._index}get totalUsed(){return this._count-this._index}clear(){this._pool.length=0,this._index=0}}class wm{constructor(){this._poolsByClass=new Map}prepopulate(e,t){this.getPool(e).prepopulate(t)}get(e,t){return this.getPool(e).get(t)}return(e){this.getPool(e.constructor).return(e)}getPool(e){return this._poolsByClass.has(e)||this._poolsByClass.set(e,new fa(e)),this._poolsByClass.get(e)}stats(){const e={};return this._poolsByClass.forEach(t=>{const i=e[t._classType.name]?t._classType.name+t._classType.ID:t._classType.name;e[i]={free:t.totalFree,used:t.totalUsed,size:t.totalSize}}),e}}const Kt=new wm,xm={get isCachedAsTexture(){return!!this.renderGroup?.isCachedAsTexture},cacheAsTexture(s){typeof s=="boolean"&&s===!1?this.disableRenderGroup():(this.enableRenderGroup(),this.renderGroup.enableCacheAsTexture(s===!0?{}:s))},updateCacheTexture(){this.renderGroup?.updateCacheTexture()},get cacheAsBitmap(){return this.isCachedAsTexture},set cacheAsBitmap(s){K("v8.6.0","cacheAsBitmap is deprecated, use cacheAsTexture instead."),this.cacheAsTexture(s)}};function vm(s,e,t){const i=s.length;let n;if(e>=i||t===0)return;t=e+t>i?i-e:t;const r=i-t;for(n=e;n<r;++n)s[n]=s[n+t];s.length=r}const km={allowChildren:!0,removeChildren(s=0,e){const t=e??this.children.length,i=t-s,n=[];if(i>0&&i<=t){for(let o=t-1;o>=s;o--){const a=this.children[o];a&&(n.push(a),a.parent=null)}vm(this.children,s,t);const r=this.renderGroup||this.parentRenderGroup;r&&r.removeChildren(n);for(let o=0;o<n.length;++o)this.emit("childRemoved",n[o],this,o),n[o].emit("removed",this);return n}else if(i===0&&this.children.length===0)return n;throw new RangeError("removeChildren: numeric values are outside the acceptable range.")},removeChildAt(s){const e=this.getChildAt(s);return this.removeChild(e)},getChildAt(s){if(s<0||s>=this.children.length)throw new Error(`getChildAt: Index (${s}) does not exist.`);return this.children[s]},setChildIndex(s,e){if(e<0||e>=this.children.length)throw new Error(`The index ${e} supplied is out of bounds ${this.children.length}`);this.getChildIndex(s),this.addChildAt(s,e)},getChildIndex(s){const e=this.children.indexOf(s);if(e===-1)throw new Error("The supplied Container must be a child of the caller");return e},addChildAt(s,e){this.allowChildren||K(J,"addChildAt: Only Containers will be allowed to add children in v8.0.0");const{children:t}=this;if(e<0||e>t.length)throw new Error(`${s}addChildAt: The index ${e} supplied is out of bounds ${t.length}`);if(s.parent){const n=s.parent.children.indexOf(s);if(s.parent===this&&n===e)return s;n!==-1&&s.parent.children.splice(n,1)}e===t.length?t.push(s):t.splice(e,0,s),s.parent=this,s.didChange=!0,s._updateFlags=15;const i=this.renderGroup||this.parentRenderGroup;return i&&i.addChild(s),this.sortableChildren&&(this.sortDirty=!0),this.emit("childAdded",s,this,e),s.emit("added",this),s},swapChildren(s,e){if(s===e)return;const t=this.getChildIndex(s),i=this.getChildIndex(e);this.children[t]=e,this.children[i]=s;const n=this.renderGroup||this.parentRenderGroup;n&&(n.structureDidChange=!0),this._didContainerChangeTick++},removeFromParent(){this.parent?.removeChild(this)},reparentChild(...s){return s.length===1?this.reparentChildAt(s[0],this.children.length):(s.forEach(e=>this.reparentChildAt(e,this.children.length)),s[0])},reparentChildAt(s,e){if(s.parent===this)return this.setChildIndex(s,e),s;const t=s.worldTransform.clone();s.removeFromParent(),this.addChildAt(s,e);const i=this.worldTransform.clone();return i.invert(),t.prepend(i),s.setFromMatrix(t),s}},Am={collectRenderables(s,e,t){this.parentRenderLayer&&this.parentRenderLayer!==t||this.globalDisplayStatus<7||!this.includeInBuild||(this.sortableChildren&&this.sortChildren(),this.isSimple?this.collectRenderablesSimple(s,e,t):this.renderGroup?e.renderPipes.renderGroup.addRenderGroup(this.renderGroup,s):this.collectRenderablesWithEffects(s,e,t))},collectRenderablesSimple(s,e,t){const i=this.children,n=i.length;for(let r=0;r<n;r++)i[r].collectRenderables(s,e,t)},collectRenderablesWithEffects(s,e,t){const{renderPipes:i}=e;for(let n=0;n<this.effects.length;n++){const r=this.effects[n];i[r.pipe].push(r,this,s)}this.collectRenderablesSimple(s,e,t);for(let n=this.effects.length-1;n>=0;n--){const r=this.effects[n];i[r.pipe].pop(r,this,s)}}};class ul{constructor(){this.pipe="filter",this.priority=1}destroy(){for(let e=0;e<this.filters.length;e++)this.filters[e].destroy();this.filters=null,this.filterArea=null}}class Sm{constructor(){this._effectClasses=[],this._tests=[],this._initialized=!1}init(){this._initialized||(this._initialized=!0,this._effectClasses.forEach(e=>{this.add({test:e.test,maskClass:e})}))}add(e){this._tests.push(e)}getMaskEffect(e){this._initialized||this.init();for(let t=0;t<this._tests.length;t++){const i=this._tests[t];if(i.test(e))return Kt.get(i.maskClass,e)}return e}returnMaskEffect(e){Kt.return(e)}}const Ro=new Sm;De.handleByList(D.MaskEffect,Ro._effectClasses);const Cm={_maskEffect:null,_maskOptions:{inverse:!1},_filterEffect:null,effects:[],_markStructureAsChanged(){const s=this.renderGroup||this.parentRenderGroup;s&&(s.structureDidChange=!0)},addEffect(s){this.effects.indexOf(s)===-1&&(this.effects.push(s),this.effects.sort((t,i)=>t.priority-i.priority),this._markStructureAsChanged(),this._updateIsSimple())},removeEffect(s){const e=this.effects.indexOf(s);e!==-1&&(this.effects.splice(e,1),this._markStructureAsChanged(),this._updateIsSimple())},set mask(s){const e=this._maskEffect;e?.mask!==s&&(e&&(this.removeEffect(e),Ro.returnMaskEffect(e),this._maskEffect=null),s!=null&&(this._maskEffect=Ro.getMaskEffect(s),this.addEffect(this._maskEffect)))},setMask(s){this._maskOptions={...this._maskOptions,...s},s.mask&&(this.mask=s.mask),this._markStructureAsChanged()},get mask(){return this._maskEffect?.mask},set filters(s){!Array.isArray(s)&&s&&(s=[s]);const e=this._filterEffect||(this._filterEffect=new ul);s=s;const t=s?.length>0,i=e.filters?.length>0,n=t!==i;s=Array.isArray(s)?s.slice(0):s,e.filters=Object.freeze(s),n&&(t?this.addEffect(e):(this.removeEffect(e),e.filters=s??null))},get filters(){return this._filterEffect?.filters},set filterArea(s){this._filterEffect||(this._filterEffect=new ul),this._filterEffect.filterArea=s},get filterArea(){return this._filterEffect?.filterArea}},Mm={label:null,get name(){return K(J,"Container.name property has been removed, use Container.label instead"),this.label},set name(s){K(J,"Container.name property has been removed, use Container.label instead"),this.label=s},getChildByName(s,e=!1){return this.getChildByLabel(s,e)},getChildByLabel(s,e=!1){const t=this.children;for(let i=0;i<t.length;i++){const n=t[i];if(n.label===s||s instanceof RegExp&&s.test(n.label))return n}if(e)for(let i=0;i<t.length;i++){const r=t[i].getChildByLabel(s,!0);if(r)return r}return null},getChildrenByLabel(s,e=!1,t=[]){const i=this.children;for(let n=0;n<i.length;n++){const r=i[n];(r.label===s||s instanceof RegExp&&s.test(r.label))&&t.push(r)}if(e)for(let n=0;n<i.length;n++)i[n].getChildrenByLabel(s,!0,t);return t}},Ge=new fa(Z),Zt=new fa(St),Tm=new Z,Pm={getFastGlobalBounds(s,e){e||(e=new St),e.clear(),this._getGlobalBoundsRecursive(!!s,e,this.parentRenderLayer),e.isValid||e.set(0,0,0,0);const t=this.renderGroup||this.parentRenderGroup;return e.applyMatrix(t.worldTransform),e},_getGlobalBoundsRecursive(s,e,t){let i=e;if(s&&this.parentRenderLayer!==t||this.localDisplayStatus!==7||!this.measurable)return;const n=!!this.effects.length;if((this.renderGroup||n)&&(i=Zt.get().clear()),this.boundsArea)e.addRect(this.boundsArea,this.worldTransform);else{if(this.renderPipeId){const o=this.bounds;i.addFrame(o.minX,o.minY,o.maxX,o.maxY,this.groupTransform)}const r=this.children;for(let o=0;o<r.length;o++)r[o]._getGlobalBoundsRecursive(s,i,t)}if(n){let r=!1;const o=this.renderGroup||this.parentRenderGroup;for(let a=0;a<this.effects.length;a++)this.effects[a].addBounds&&(r||(r=!0,i.applyMatrix(o.worldTransform)),this.effects[a].addBounds(i,!0));r&&(i.applyMatrix(o.worldTransform.copyTo(Tm).invert()),e.addBounds(i,this.relativeGroupTransform)),e.addBounds(i),Zt.return(i)}else this.renderGroup&&(e.addBounds(i,this.relativeGroupTransform),Zt.return(i))}};function Kc(s,e,t){t.clear();let i,n;return s.parent?e?i=s.parent.worldTransform:(n=Ge.get().identity(),i=ga(s,n)):i=Z.IDENTITY,Zc(s,t,i,e),n&&Ge.return(n),t.isValid||t.set(0,0,0,0),t}function Zc(s,e,t,i){if(!s.visible||!s.measurable)return;let n;i?n=s.worldTransform:(s.updateLocalTransform(),n=Ge.get(),n.appendFrom(s.localTransform,t));const r=e,o=!!s.effects.length;if(o&&(e=Zt.get().clear()),s.boundsArea)e.addRect(s.boundsArea,n);else{s.bounds&&(e.matrix=n,e.addBounds(s.bounds));for(let a=0;a<s.children.length;a++)Zc(s.children[a],e,n,i)}if(o){for(let a=0;a<s.effects.length;a++)s.effects[a].addBounds?.(e);r.addBounds(e,Z.IDENTITY),Zt.return(e)}i||Ge.return(n)}function ga(s,e){const t=s.parent;return t&&(ga(t,e),t.updateLocalTransform(),e.append(t.localTransform)),e}function Qc(s,e){if(s===16777215||!e)return e;if(e===16777215||!s)return s;const t=s>>16&255,i=s>>8&255,n=s&255,r=e>>16&255,o=e>>8&255,a=e&255,l=t*r/255|0,h=i*o/255|0,c=n*a/255|0;return(l<<16)+(h<<8)+c}const dl=16777215;function _l(s,e){return s===dl?e:e===dl?s:Qc(s,e)}function En(s){return((s&255)<<16)+(s&65280)+(s>>16&255)}const zm={getGlobalAlpha(s){if(s)return this.renderGroup?this.renderGroup.worldAlpha:this.parentRenderGroup?this.parentRenderGroup.worldAlpha*this.alpha:this.alpha;let e=this.alpha,t=this.parent;for(;t;)e*=t.alpha,t=t.parent;return e},getGlobalTransform(s,e){if(e)return s.copyFrom(this.worldTransform);this.updateLocalTransform();const t=ga(this,Ge.get().identity());return s.appendFrom(this.localTransform,t),Ge.return(t),s},getGlobalTint(s){if(s)return this.renderGroup?En(this.renderGroup.worldColor):this.parentRenderGroup?En(_l(this.localColor,this.parentRenderGroup.worldColor)):this.tint;let e=this.localColor,t=this.parent;for(;t;)e=_l(e,t.localColor),t=t.parent;return En(e)}};let Hr=0;const fl=500;function be(...s){Hr!==fl&&(Hr++,Hr===fl?console.warn("PixiJS Warning: too many warnings, no more warnings will be reported to the console by PixiJS."):console.warn("PixiJS Warning: ",...s))}function Jc(s,e,t){return e.clear(),t||(t=Z.IDENTITY),eu(s,e,t,s,!0),e.isValid||e.set(0,0,0,0),e}function eu(s,e,t,i,n){let r;if(n)r=Ge.get(),r=t.copyTo(r);else{if(!s.visible||!s.measurable)return;s.updateLocalTransform();const l=s.localTransform;r=Ge.get(),r.appendFrom(l,t)}const o=e,a=!!s.effects.length;if(a&&(e=Zt.get().clear()),s.boundsArea)e.addRect(s.boundsArea,r);else{s.renderPipeId&&(e.matrix=r,e.addBounds(s.bounds));const l=s.children;for(let h=0;h<l.length;h++)eu(l[h],e,r,i,!1)}if(a){for(let l=0;l<s.effects.length;l++)s.effects[l].addLocalBounds?.(e,i);o.addBounds(e,Z.IDENTITY),Zt.return(e)}Ge.return(r)}function tu(s,e){const t=s.children;for(let i=0;i<t.length;i++){const n=t[i],r=n.uid,o=(n._didViewChangeTick&65535)<<16|n._didContainerChangeTick&65535,a=e.index;(e.data[a]!==r||e.data[a+1]!==o)&&(e.data[e.index]=r,e.data[e.index+1]=o,e.didChange=!0),e.index=a+2,n.children.length&&tu(n,e)}return e.didChange}const Im=new Z,Em={_localBoundsCacheId:-1,_localBoundsCacheData:null,_setWidth(s,e){const t=Math.sign(this.scale.x)||1;e!==0?this.scale.x=s/e*t:this.scale.x=t},_setHeight(s,e){const t=Math.sign(this.scale.y)||1;e!==0?this.scale.y=s/e*t:this.scale.y=t},getLocalBounds(){this._localBoundsCacheData||(this._localBoundsCacheData={data:[],index:1,didChange:!1,localBounds:new St});const s=this._localBoundsCacheData;return s.index=1,s.didChange=!1,s.data[0]!==this._didViewChangeTick&&(s.didChange=!0,s.data[0]=this._didViewChangeTick),tu(this,s),s.didChange&&Jc(this,s.localBounds,Im),s.localBounds},getBounds(s,e){return Kc(this,s,e||new St)}},Rm={_onRender:null,set onRender(s){const e=this.renderGroup||this.parentRenderGroup;if(!s){this._onRender&&e?.removeOnRender(this),this._onRender=null;return}this._onRender||e?.addOnRender(this),this._onRender=s},get onRender(){return this._onRender}},Fm={_zIndex:0,sortDirty:!1,sortableChildren:!1,get zIndex(){return this._zIndex},set zIndex(s){this._zIndex!==s&&(this._zIndex=s,this.depthOfChildModified())},depthOfChildModified(){this.parent&&(this.parent.sortableChildren=!0,this.parent.sortDirty=!0),this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0)},sortChildren(){this.sortDirty&&(this.sortDirty=!1,this.children.sort(Bm))}};function Bm(s,e){return s._zIndex-e._zIndex}const Lm={getGlobalPosition(s=new X,e=!1){return this.parent?this.parent.toGlobal(this._position,s,e):(s.x=this._position.x,s.y=this._position.y),s},toGlobal(s,e,t=!1){const i=this.getGlobalTransform(Ge.get(),t);return e=i.apply(s,e),Ge.return(i),e},toLocal(s,e,t,i){e&&(s=e.toGlobal(s,t,i));const n=this.getGlobalTransform(Ge.get(),i);return t=n.applyInverse(s,t),Ge.return(n),t}};class iu{constructor(){this.uid=pe("instructionSet"),this.instructions=[],this.instructionSize=0,this.renderables=[],this.gcTick=0}reset(){this.instructionSize=0}add(e){this.instructions[this.instructionSize++]=e}log(){this.instructions.length=this.instructionSize,console.table(this.instructions,["type","action"])}}let $m=0;class Om{constructor(e){this._poolKeyHash=Object.create(null),this._texturePool={},this.textureOptions=e||{},this.enableFullScreen=!1}createTexture(e,t,i){const n=new Pt({...this.textureOptions,width:e,height:t,resolution:1,antialias:i,autoGarbageCollect:!1});return new Y({source:n,label:`texturePool_${$m++}`})}getOptimalTexture(e,t,i=1,n){let r=Math.ceil(e*i-1e-6),o=Math.ceil(t*i-1e-6);r=Hn(r),o=Hn(o);const a=(r<<17)+(o<<1)+(n?1:0);this._texturePool[a]||(this._texturePool[a]=[]);let l=this._texturePool[a].pop();return l||(l=this.createTexture(r,o,n)),l.source._resolution=i,l.source.width=r/i,l.source.height=o/i,l.source.pixelWidth=r,l.source.pixelHeight=o,l.frame.x=0,l.frame.y=0,l.frame.width=e,l.frame.height=t,l.updateUvs(),this._poolKeyHash[l.uid]=a,l}getSameSizeTexture(e,t=!1){const i=e.source;return this.getOptimalTexture(e.width,e.height,i._resolution,t)}returnTexture(e){const t=this._poolKeyHash[e.uid];this._texturePool[t].push(e)}clear(e){if(e=e!==!1,e)for(const t in this._texturePool){const i=this._texturePool[t];if(i)for(let n=0;n<i.length;n++)i[n].destroy(!0)}this._texturePool={}}}const Zs=new Om;class Gm{constructor(){this.renderPipeId="renderGroup",this.root=null,this.canBundle=!1,this.renderGroupParent=null,this.renderGroupChildren=[],this.worldTransform=new Z,this.worldColorAlpha=4294967295,this.worldColor=16777215,this.worldAlpha=1,this.childrenToUpdate=Object.create(null),this.updateTick=0,this.gcTick=0,this.childrenRenderablesToUpdate={list:[],index:0},this.structureDidChange=!0,this.instructionSet=new iu,this._onRenderContainers=[],this.textureNeedsUpdate=!0,this.isCachedAsTexture=!1,this._matrixDirty=7}init(e){this.root=e,e._onRender&&this.addOnRender(e),e.didChange=!0;const t=e.children;for(let i=0;i<t.length;i++){const n=t[i];n._updateFlags=15,this.addChild(n)}}enableCacheAsTexture(e={}){this.textureOptions=e,this.isCachedAsTexture=!0,this.textureNeedsUpdate=!0}disableCacheAsTexture(){this.isCachedAsTexture=!1,this.texture&&(Zs.returnTexture(this.texture),this.texture=null)}updateCacheTexture(){this.textureNeedsUpdate=!0}reset(){this.renderGroupChildren.length=0;for(const e in this.childrenToUpdate){const t=this.childrenToUpdate[e];t.list.fill(null),t.index=0}this.childrenRenderablesToUpdate.index=0,this.childrenRenderablesToUpdate.list.fill(null),this.root=null,this.updateTick=0,this.structureDidChange=!0,this._onRenderContainers.length=0,this.renderGroupParent=null,this.disableCacheAsTexture()}get localTransform(){return this.root.localTransform}addRenderGroupChild(e){e.renderGroupParent&&e.renderGroupParent._removeRenderGroupChild(e),e.renderGroupParent=this,this.renderGroupChildren.push(e)}_removeRenderGroupChild(e){const t=this.renderGroupChildren.indexOf(e);t>-1&&this.renderGroupChildren.splice(t,1),e.renderGroupParent=null}addChild(e){if(this.structureDidChange=!0,e.parentRenderGroup=this,e.updateTick=-1,e.parent===this.root?e.relativeRenderGroupDepth=1:e.relativeRenderGroupDepth=e.parent.relativeRenderGroupDepth+1,e.didChange=!0,this.onChildUpdate(e),e.renderGroup){this.addRenderGroupChild(e.renderGroup);return}e._onRender&&this.addOnRender(e);const t=e.children;for(let i=0;i<t.length;i++)this.addChild(t[i])}removeChild(e){if(this.structureDidChange=!0,e._onRender&&(e.renderGroup||this.removeOnRender(e)),e.parentRenderGroup=null,e.renderGroup){this._removeRenderGroupChild(e.renderGroup);return}const t=e.children;for(let i=0;i<t.length;i++)this.removeChild(t[i])}removeChildren(e){for(let t=0;t<e.length;t++)this.removeChild(e[t])}onChildUpdate(e){let t=this.childrenToUpdate[e.relativeRenderGroupDepth];t||(t=this.childrenToUpdate[e.relativeRenderGroupDepth]={index:0,list:[]}),t.list[t.index++]=e}updateRenderable(e){e.globalDisplayStatus<7||(this.instructionSet.renderPipes[e.renderPipeId].updateRenderable(e),e.didViewUpdate=!1)}onChildViewUpdate(e){this.childrenRenderablesToUpdate.list[this.childrenRenderablesToUpdate.index++]=e}get isRenderable(){return this.root.localDisplayStatus===7&&this.worldAlpha>0}addOnRender(e){this._onRenderContainers.push(e)}removeOnRender(e){this._onRenderContainers.splice(this._onRenderContainers.indexOf(e),1)}runOnRender(e){for(let t=0;t<this._onRenderContainers.length;t++)this._onRenderContainers[t]._onRender(e)}destroy(){this.disableCacheAsTexture(),this.renderGroupParent=null,this.root=null,this.childrenRenderablesToUpdate=null,this.childrenToUpdate=null,this.renderGroupChildren=null,this._onRenderContainers=null,this.instructionSet=null}getChildren(e=[]){const t=this.root.children;for(let i=0;i<t.length;i++)this._getChildren(t[i],e);return e}_getChildren(e,t=[]){if(t.push(e),e.renderGroup)return t;const i=e.children;for(let n=0;n<i.length;n++)this._getChildren(i[n],t);return t}invalidateMatrices(){this._matrixDirty=7}get inverseWorldTransform(){return this._matrixDirty&1?(this._matrixDirty&=-2,this._inverseWorldTransform||(this._inverseWorldTransform=new Z),this._inverseWorldTransform.copyFrom(this.worldTransform).invert()):this._inverseWorldTransform}get textureOffsetInverseTransform(){return this._matrixDirty&2?(this._matrixDirty&=-3,this._textureOffsetInverseTransform||(this._textureOffsetInverseTransform=new Z),this._textureOffsetInverseTransform.copyFrom(this.inverseWorldTransform).translate(-this._textureBounds.x,-this._textureBounds.y)):this._textureOffsetInverseTransform}get inverseParentTextureTransform(){if(!(this._matrixDirty&4))return this._inverseParentTextureTransform;this._matrixDirty&=-5;const e=this._parentCacheAsTextureRenderGroup;return e?(this._inverseParentTextureTransform||(this._inverseParentTextureTransform=new Z),this._inverseParentTextureTransform.copyFrom(this.worldTransform).prepend(e.inverseWorldTransform).translate(-e._textureBounds.x,-e._textureBounds.y)):this.worldTransform}get cacheToLocalTransform(){return this._parentCacheAsTextureRenderGroup?this._parentCacheAsTextureRenderGroup.textureOffsetInverseTransform:null}}function Dm(s,e,t={}){for(const i in e)!t[i]&&e[i]!==void 0&&(s[i]=e[i])}const qr=new et(null),Vr=new et(null),Xr=new et(null,1,1),gl=1,Nm=2,Yr=4;class U extends Tt{constructor(e={}){super(),this.uid=pe("renderable"),this._updateFlags=15,this.renderGroup=null,this.parentRenderGroup=null,this.parentRenderGroupIndex=0,this.didChange=!1,this.didViewUpdate=!1,this.relativeRenderGroupDepth=0,this.children=[],this.parent=null,this.includeInBuild=!0,this.measurable=!0,this.isSimple=!0,this.updateTick=-1,this.localTransform=new Z,this.relativeGroupTransform=new Z,this.groupTransform=this.relativeGroupTransform,this.destroyed=!1,this._position=new et(this,0,0),this._scale=Xr,this._pivot=Vr,this._skew=qr,this._cx=1,this._sx=0,this._cy=0,this._sy=1,this._rotation=0,this.localColor=16777215,this.localAlpha=1,this.groupAlpha=1,this.groupColor=16777215,this.groupColorAlpha=4294967295,this.localBlendMode="inherit",this.groupBlendMode="normal",this.localDisplayStatus=7,this.globalDisplayStatus=7,this._didContainerChangeTick=0,this._didViewChangeTick=0,this._didLocalTransformChangeId=-1,this.effects=[],Dm(this,e,{children:!0,parent:!0,effects:!0}),e.children?.forEach(t=>this.addChild(t)),e.parent?.addChild(this)}static mixin(e){Object.defineProperties(U.prototype,Object.getOwnPropertyDescriptors(e))}set _didChangeId(e){this._didViewChangeTick=e>>12&4095,this._didContainerChangeTick=e&4095}get _didChangeId(){return this._didContainerChangeTick&4095|(this._didViewChangeTick&4095)<<12}addChild(...e){if(this.allowChildren||K(J,"addChild: Only Containers will be allowed to add children in v8.0.0"),e.length>1){for(let n=0;n<e.length;n++)this.addChild(e[n]);return e[0]}const t=e[0],i=this.renderGroup||this.parentRenderGroup;return t.parent===this?(this.children.splice(this.children.indexOf(t),1),this.children.push(t),i&&(i.structureDidChange=!0),t):(t.parent&&t.parent.removeChild(t),this.children.push(t),this.sortableChildren&&(this.sortDirty=!0),t.parent=this,t.didChange=!0,t._updateFlags=15,i&&i.addChild(t),this.emit("childAdded",t,this,this.children.length-1),t.emit("added",this),this._didViewChangeTick++,t._zIndex!==0&&t.depthOfChildModified(),t)}removeChild(...e){if(e.length>1){for(let n=0;n<e.length;n++)this.removeChild(e[n]);return e[0]}const t=e[0],i=this.children.indexOf(t);return i>-1&&(this._didViewChangeTick++,this.children.splice(i,1),this.renderGroup?this.renderGroup.removeChild(t):this.parentRenderGroup&&this.parentRenderGroup.removeChild(t),t.parentRenderLayer&&t.parentRenderLayer.detach(t),t.parent=null,this.emit("childRemoved",t,this,i),t.emit("removed",this)),t}_onUpdate(e){e&&e===this._skew&&this._updateSkew(),this._didContainerChangeTick++,!this.didChange&&(this.didChange=!0,this.parentRenderGroup&&this.parentRenderGroup.onChildUpdate(this))}set isRenderGroup(e){!!this.renderGroup!==e&&(e?this.enableRenderGroup():this.disableRenderGroup())}get isRenderGroup(){return!!this.renderGroup}enableRenderGroup(){if(this.renderGroup)return;const e=this.parentRenderGroup;e?.removeChild(this),this.renderGroup=Kt.get(Gm,this),this.groupTransform=Z.IDENTITY,e?.addChild(this),this._updateIsSimple()}disableRenderGroup(){if(!this.renderGroup)return;const e=this.parentRenderGroup;e?.removeChild(this),Kt.return(this.renderGroup),this.renderGroup=null,this.groupTransform=this.relativeGroupTransform,e?.addChild(this),this._updateIsSimple()}_updateIsSimple(){this.isSimple=!this.renderGroup&&this.effects.length===0}get worldTransform(){return this._worldTransform||(this._worldTransform=new Z),this.renderGroup?this._worldTransform.copyFrom(this.renderGroup.worldTransform):this.parentRenderGroup&&this._worldTransform.appendFrom(this.relativeGroupTransform,this.parentRenderGroup.worldTransform),this._worldTransform}get x(){return this._position.x}set x(e){this._position.x=e}get y(){return this._position.y}set y(e){this._position.y=e}get position(){return this._position}set position(e){this._position.copyFrom(e)}get rotation(){return this._rotation}set rotation(e){this._rotation!==e&&(this._rotation=e,this._onUpdate(this._skew))}get angle(){return this.rotation*Jg}set angle(e){this.rotation=e*em}get pivot(){return this._pivot===Vr&&(this._pivot=new et(this,0,0)),this._pivot}set pivot(e){this._pivot===Vr&&(this._pivot=new et(this,0,0)),typeof e=="number"?this._pivot.set(e):this._pivot.copyFrom(e)}get skew(){return this._skew===qr&&(this._skew=new et(this,0,0)),this._skew}set skew(e){this._skew===qr&&(this._skew=new et(this,0,0)),this._skew.copyFrom(e)}get scale(){return this._scale===Xr&&(this._scale=new et(this,1,1)),this._scale}set scale(e){this._scale===Xr&&(this._scale=new et(this,0,0)),typeof e=="number"?this._scale.set(e):this._scale.copyFrom(e)}get width(){return Math.abs(this.scale.x*this.getLocalBounds().width)}set width(e){const t=this.getLocalBounds().width;this._setWidth(e,t)}get height(){return Math.abs(this.scale.y*this.getLocalBounds().height)}set height(e){const t=this.getLocalBounds().height;this._setHeight(e,t)}getSize(e){e||(e={});const t=this.getLocalBounds();return e.width=Math.abs(this.scale.x*t.width),e.height=Math.abs(this.scale.y*t.height),e}setSize(e,t){const i=this.getLocalBounds();typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,i.width),t!==void 0&&this._setHeight(t,i.height)}_updateSkew(){const e=this._rotation,t=this._skew;this._cx=Math.cos(e+t._y),this._sx=Math.sin(e+t._y),this._cy=-Math.sin(e-t._x),this._sy=Math.cos(e-t._x)}updateTransform(e){return this.position.set(typeof e.x=="number"?e.x:this.position.x,typeof e.y=="number"?e.y:this.position.y),this.scale.set(typeof e.scaleX=="number"?e.scaleX||1:this.scale.x,typeof e.scaleY=="number"?e.scaleY||1:this.scale.y),this.rotation=typeof e.rotation=="number"?e.rotation:this.rotation,this.skew.set(typeof e.skewX=="number"?e.skewX:this.skew.x,typeof e.skewY=="number"?e.skewY:this.skew.y),this.pivot.set(typeof e.pivotX=="number"?e.pivotX:this.pivot.x,typeof e.pivotY=="number"?e.pivotY:this.pivot.y),this}setFromMatrix(e){e.decompose(this)}updateLocalTransform(){const e=this._didContainerChangeTick;if(this._didLocalTransformChangeId===e)return;this._didLocalTransformChangeId=e;const t=this.localTransform,i=this._scale,n=this._pivot,r=this._position,o=i._x,a=i._y,l=n._x,h=n._y;t.a=this._cx*o,t.b=this._sx*o,t.c=this._cy*a,t.d=this._sy*a,t.tx=r._x-(l*t.a+h*t.c),t.ty=r._y-(l*t.b+h*t.d)}set alpha(e){e!==this.localAlpha&&(this.localAlpha=e,this._updateFlags|=gl,this._onUpdate())}get alpha(){return this.localAlpha}set tint(e){const i=fe.shared.setValue(e??16777215).toBgrNumber();i!==this.localColor&&(this.localColor=i,this._updateFlags|=gl,this._onUpdate())}get tint(){return En(this.localColor)}set blendMode(e){this.localBlendMode!==e&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Nm,this.localBlendMode=e,this._onUpdate())}get blendMode(){return this.localBlendMode}get visible(){return!!(this.localDisplayStatus&2)}set visible(e){const t=e?2:0;(this.localDisplayStatus&2)!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Yr,this.localDisplayStatus^=2,this._onUpdate())}get culled(){return!(this.localDisplayStatus&4)}set culled(e){const t=e?0:4;(this.localDisplayStatus&4)!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Yr,this.localDisplayStatus^=4,this._onUpdate())}get renderable(){return!!(this.localDisplayStatus&1)}set renderable(e){const t=e?1:0;(this.localDisplayStatus&1)!==t&&(this._updateFlags|=Yr,this.localDisplayStatus^=1,this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._onUpdate())}get isRenderable(){return this.localDisplayStatus===7&&this.groupAlpha>0}destroy(e=!1){if(this.destroyed)return;this.destroyed=!0;let t;if(this.children.length&&(t=this.removeChildren(0,this.children.length)),this.removeFromParent(),this.parent=null,this._maskEffect=null,this._filterEffect=null,this.effects=null,this._position=null,this._scale=null,this._pivot=null,this._skew=null,this.emit("destroyed",this),this.removeAllListeners(),(typeof e=="boolean"?e:e?.children)&&t)for(let n=0;n<t.length;++n)t[n].destroy(e);this.renderGroup?.destroy(),this.renderGroup=null}}U.mixin(km);U.mixin(Pm);U.mixin(Lm);U.mixin(Rm);U.mixin(Em);U.mixin(Cm);U.mixin(Mm);U.mixin(Fm);U.mixin(ym);U.mixin(xm);U.mixin(zm);U.mixin(Am);class su extends U{constructor(){super(...arguments),this.canBundle=!0,this.allowChildren=!1,this._roundPixels=0,this._lastUsed=-1,this._bounds=new St(0,1,0,0),this._boundsDirty=!0}get bounds(){return this._boundsDirty?(this.updateBounds(),this._boundsDirty=!1,this._bounds):this._bounds}get roundPixels(){return!!this._roundPixels}set roundPixels(e){this._roundPixels=e?1:0}containsPoint(e){const t=this.bounds,{x:i,y:n}=e;return i>=t.minX&&i<=t.maxX&&n>=t.minY&&n<=t.maxY}onViewUpdate(){if(this._didViewChangeTick++,this._boundsDirty=!0,this.didViewUpdate)return;this.didViewUpdate=!0;const e=this.renderGroup||this.parentRenderGroup;e&&e.onChildViewUpdate(this)}destroy(e){super.destroy(e),this._bounds=null}collectRenderablesSimple(e,t,i){const{renderPipes:n,renderableGC:r}=t;n.blendMode.setBlendMode(this,this.groupBlendMode,e),n[this.renderPipeId].addRenderable(this,e),r.addRenderable(this),this.didViewUpdate=!1;const a=this.children,l=a.length;for(let h=0;h<l;h++)a[h].collectRenderables(e,t,i)}}class ee extends su{constructor(e=Y.EMPTY){e instanceof Y&&(e={texture:e});const{texture:t=Y.EMPTY,anchor:i,roundPixels:n,width:r,height:o,...a}=e;super({label:"Sprite",...a}),this.renderPipeId="sprite",this.batched=!0,this._visualBounds={minX:0,maxX:1,minY:0,maxY:0},this._anchor=new et({_onUpdate:()=>{this.onViewUpdate()}}),i?this.anchor=i:t.defaultAnchor&&(this.anchor=t.defaultAnchor),this.texture=t,this.allowChildren=!1,this.roundPixels=n??!1,r!==void 0&&(this.width=r),o!==void 0&&(this.height=o)}static from(e,t=!1){return e instanceof Y?new ee(e):new ee(Y.from(e,t))}set texture(e){e||(e=Y.EMPTY);const t=this._texture;t!==e&&(t&&t.dynamic&&t.off("update",this.onViewUpdate,this),e.dynamic&&e.on("update",this.onViewUpdate,this),this._texture=e,this._width&&this._setWidth(this._width,this._texture.orig.width),this._height&&this._setHeight(this._height,this._texture.orig.height),this.onViewUpdate())}get texture(){return this._texture}get visualBounds(){return hm(this._visualBounds,this._anchor,this._texture),this._visualBounds}get sourceBounds(){return K("8.6.1","Sprite.sourceBounds is deprecated, use visualBounds instead."),this.visualBounds}updateBounds(){const e=this._anchor,t=this._texture,i=this._bounds,{width:n,height:r}=t.orig;i.minX=-e._x*n,i.maxX=i.minX+n,i.minY=-e._y*r,i.maxY=i.minY+r}destroy(e=!1){if(super.destroy(e),typeof e=="boolean"?e:e?.texture){const i=typeof e=="boolean"?e:e?.textureSource;this._texture.destroy(i)}this._texture=null,this._visualBounds=null,this._bounds=null,this._anchor=null}get anchor(){return this._anchor}set anchor(e){typeof e=="number"?this._anchor.set(e):this._anchor.copyFrom(e)}get width(){return Math.abs(this.scale.x)*this._texture.orig.width}set width(e){this._setWidth(e,this._texture.orig.width),this._width=e}get height(){return Math.abs(this.scale.y)*this._texture.orig.height}set height(e){this._setHeight(e,this._texture.orig.height),this._height=e}getSize(e){return e||(e={}),e.width=Math.abs(this.scale.x)*this._texture.orig.width,e.height=Math.abs(this.scale.y)*this._texture.orig.height,e}setSize(e,t){typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,this._texture.orig.width),t!==void 0&&this._setHeight(t,this._texture.orig.height)}}const Um=new St;function nu(s,e,t){const i=Um;s.measurable=!0,Kc(s,t,i),e.addBoundsMask(i),s.measurable=!1}function ru(s,e,t){const i=Zt.get();s.measurable=!0;const n=Ge.get().identity(),r=ou(s,t,n);Jc(s,i,r),s.measurable=!1,e.addBoundsMask(i),Ge.return(n),Zt.return(i)}function ou(s,e,t){return s?(s!==e&&(ou(s.parent,e,t),s.updateLocalTransform(),t.append(s.localTransform)),t):(be("Mask bounds, renderable is not inside the root container"),t)}class au{constructor(e){this.priority=0,this.inverse=!1,this.pipe="alphaMask",e?.mask&&this.init(e.mask)}init(e){this.mask=e,this.renderMaskToTexture=!(e instanceof ee),this.mask.renderable=this.renderMaskToTexture,this.mask.includeInBuild=!this.renderMaskToTexture,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask=null}addBounds(e,t){this.inverse||nu(this.mask,e,t)}addLocalBounds(e,t){ru(this.mask,e,t)}containsPoint(e,t){const i=this.mask;return t(i,e)}destroy(){this.reset()}static test(e){return e instanceof ee}}au.extension=D.MaskEffect;class lu{constructor(e){this.priority=0,this.pipe="colorMask",e?.mask&&this.init(e.mask)}init(e){this.mask=e}destroy(){}static test(e){return typeof e=="number"}}lu.extension=D.MaskEffect;class hu{constructor(e){this.priority=0,this.pipe="stencilMask",e?.mask&&this.init(e.mask)}init(e){this.mask=e,this.mask.includeInBuild=!1,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask.includeInBuild=!0,this.mask=null}addBounds(e,t){nu(this.mask,e,t)}addLocalBounds(e,t){ru(this.mask,e,t)}containsPoint(e,t){const i=this.mask;return t(i,e)}destroy(){this.reset()}static test(e){return e instanceof U}}hu.extension=D.MaskEffect;const Wm={createCanvas:(s,e)=>{const t=document.createElement("canvas");return t.width=s,t.height=e,t},getCanvasRenderingContext2D:()=>CanvasRenderingContext2D,getWebGLRenderingContext:()=>WebGLRenderingContext,getNavigator:()=>navigator,getBaseUrl:()=>document.baseURI??window.location.href,getFontFaceSet:()=>document.fonts,fetch:(s,e)=>fetch(s,e),parseXML:s=>new DOMParser().parseFromString(s,"text/xml")};let ml=Wm;const _e={get(){return ml},set(s){ml=s}};class cu extends Pt{constructor(e){e.resource||(e.resource=_e.get().createCanvas()),e.width||(e.width=e.resource.width,e.autoDensity||(e.width/=e.resolution)),e.height||(e.height=e.resource.height,e.autoDensity||(e.height/=e.resolution)),super(e),this.uploadMethodId="image",this.autoDensity=e.autoDensity,this.resizeCanvas(),this.transparent=!!e.transparent}resizeCanvas(){this.autoDensity&&(this.resource.style.width=`${this.width}px`,this.resource.style.height=`${this.height}px`),(this.resource.width!==this.pixelWidth||this.resource.height!==this.pixelHeight)&&(this.resource.width=this.pixelWidth,this.resource.height=this.pixelHeight)}resize(e=this.width,t=this.height,i=this._resolution){const n=super.resize(e,t,i);return n&&this.resizeCanvas(),n}static test(e){return globalThis.HTMLCanvasElement&&e instanceof HTMLCanvasElement||globalThis.OffscreenCanvas&&e instanceof OffscreenCanvas}get context2D(){return this._context2D||(this._context2D=this.resource.getContext("2d"))}}cu.extension=D.TextureSource;class ws extends Pt{constructor(e){if(e.resource&&globalThis.HTMLImageElement&&e.resource instanceof HTMLImageElement){const t=_e.get().createCanvas(e.resource.width,e.resource.height);t.getContext("2d").drawImage(e.resource,0,0,e.resource.width,e.resource.height),e.resource=t,be("ImageSource: Image element passed, converting to canvas. Use CanvasSource instead.")}super(e),this.uploadMethodId="image",this.autoGarbageCollect=!0}static test(e){return globalThis.HTMLImageElement&&e instanceof HTMLImageElement||typeof ImageBitmap<"u"&&e instanceof ImageBitmap||globalThis.VideoFrame&&e instanceof VideoFrame}}ws.extension=D.TextureSource;var Fo=(s=>(s[s.INTERACTION=50]="INTERACTION",s[s.HIGH=25]="HIGH",s[s.NORMAL=0]="NORMAL",s[s.LOW=-25]="LOW",s[s.UTILITY=-50]="UTILITY",s))(Fo||{});class jr{constructor(e,t=null,i=0,n=!1){this.next=null,this.previous=null,this._destroyed=!1,this._fn=e,this._context=t,this.priority=i,this._once=n}match(e,t=null){return this._fn===e&&this._context===t}emit(e){this._fn&&(this._context?this._fn.call(this._context,e):this._fn(e));const t=this.next;return this._once&&this.destroy(!0),this._destroyed&&(this.next=null),t}connect(e){this.previous=e,e.next&&(e.next.previous=this),this.next=e.next,e.next=this}destroy(e=!1){this._destroyed=!0,this._fn=null,this._context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);const t=this.next;return this.next=e?null:t,this.previous=null,t}}const uu=class Ke{constructor(){this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new jr(null,null,1/0),this.deltaMS=1/Ke.targetFPMS,this.elapsedMS=1/Ke.targetFPMS,this._tick=e=>{this._requestId=null,this.started&&(this.update(e),this.started&&this._requestId===null&&this._head.next&&(this._requestId=requestAnimationFrame(this._tick)))}}_requestIfNeeded(){this._requestId===null&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick))}_cancelIfNeeded(){this._requestId!==null&&(cancelAnimationFrame(this._requestId),this._requestId=null)}_startIfPossible(){this.started?this._requestIfNeeded():this.autoStart&&this.start()}add(e,t,i=Fo.NORMAL){return this._addListener(new jr(e,t,i))}addOnce(e,t,i=Fo.NORMAL){return this._addListener(new jr(e,t,i,!0))}_addListener(e){let t=this._head.next,i=this._head;if(!t)e.connect(i);else{for(;t;){if(e.priority>t.priority){e.connect(i);break}i=t,t=t.next}e.previous||e.connect(i)}return this._startIfPossible(),this}remove(e,t){let i=this._head.next;for(;i;)i.match(e,t)?i=i.destroy():i=i.next;return this._head.next||this._cancelIfNeeded(),this}get count(){if(!this._head)return 0;let e=0,t=this._head;for(;t=t.next;)e++;return e}start(){this.started||(this.started=!0,this._requestIfNeeded())}stop(){this.started&&(this.started=!1,this._cancelIfNeeded())}destroy(){if(!this._protected){this.stop();let e=this._head.next;for(;e;)e=e.destroy(!0);this._head.destroy(),this._head=null}}update(e=performance.now()){let t;if(e>this.lastTime){if(t=this.elapsedMS=e-this.lastTime,t>this._maxElapsedMS&&(t=this._maxElapsedMS),t*=this.speed,this._minElapsedMS){const r=e-this._lastFrame|0;if(r<this._minElapsedMS)return;this._lastFrame=e-r%this._minElapsedMS}this.deltaMS=t,this.deltaTime=this.deltaMS*Ke.targetFPMS;const i=this._head;let n=i.next;for(;n;)n=n.emit(this);i.next||this._cancelIfNeeded()}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=e}get FPS(){return 1e3/this.elapsedMS}get minFPS(){return 1e3/this._maxElapsedMS}set minFPS(e){const t=Math.min(this.maxFPS,e),i=Math.min(Math.max(0,t)/1e3,Ke.targetFPMS);this._maxElapsedMS=1/i}get maxFPS(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0}set maxFPS(e){if(e===0)this._minElapsedMS=0;else{const t=Math.max(this.minFPS,e);this._minElapsedMS=1/(t/1e3)}}static get shared(){if(!Ke._shared){const e=Ke._shared=new Ke;e.autoStart=!0,e._protected=!0}return Ke._shared}static get system(){if(!Ke._system){const e=Ke._system=new Ke;e.autoStart=!0,e._protected=!0}return Ke._system}};uu.targetFPMS=.06;let yn=uu,Kr;async function du(){return Kr??(Kr=(async()=>{const e=document.createElement("canvas").getContext("webgl");if(!e)return"premultiply-alpha-on-upload";const t=await new Promise(o=>{const a=document.createElement("video");a.onloadeddata=()=>o(a),a.onerror=()=>o(null),a.autoplay=!1,a.crossOrigin="anonymous",a.preload="auto",a.src="data:video/webm;base64,GkXfo59ChoEBQveBAULygQRC84EIQoKEd2VibUKHgQJChYECGFOAZwEAAAAAAAHTEU2bdLpNu4tTq4QVSalmU6yBoU27i1OrhBZUrmtTrIHGTbuMU6uEElTDZ1OsggEXTbuMU6uEHFO7a1OsggG97AEAAAAAAABZAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVSalmoCrXsYMPQkBNgIRMYXZmV0GETGF2ZkSJiEBEAAAAAAAAFlSua8yuAQAAAAAAAEPXgQFzxYgAAAAAAAAAAZyBACK1nIN1bmSIgQCGhVZfVlA5g4EBI+ODhAJiWgDglLCBArqBApqBAlPAgQFVsIRVuYEBElTDZ9Vzc9JjwItjxYgAAAAAAAAAAWfInEWjh0VOQ09ERVJEh49MYXZjIGxpYnZweC12cDlnyKJFo4hEVVJBVElPTkSHlDAwOjAwOjAwLjA0MDAwMDAwMAAAH0O2dcfngQCgwqGggQAAAIJJg0IAABAAFgA4JBwYSgAAICAAEb///4r+AAB1oZ2mm+6BAaWWgkmDQgAAEAAWADgkHBhKAAAgIABIQBxTu2uRu4+zgQC3iveBAfGCAXHwgQM=",a.load()});if(!t)return"premultiply-alpha-on-upload";const i=e.createTexture();e.bindTexture(e.TEXTURE_2D,i);const n=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,n),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,i,0),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),e.pixelStorei(e.UNPACK_COLORSPACE_CONVERSION_WEBGL,e.NONE),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t);const r=new Uint8Array(4);return e.readPixels(0,0,1,1,e.RGBA,e.UNSIGNED_BYTE,r),e.deleteFramebuffer(n),e.deleteTexture(i),e.getExtension("WEBGL_lose_context")?.loseContext(),r[0]<=r[3]?"premultiplied-alpha":"premultiply-alpha-on-upload"})()),Kr}const ir=class _u extends Pt{constructor(e){super(e),this.isReady=!1,this.uploadMethodId="video",e={..._u.defaultOptions,...e},this._autoUpdate=!0,this._isConnectedToTicker=!1,this._updateFPS=e.updateFPS||0,this._msToNextUpdate=0,this.autoPlay=e.autoPlay!==!1,this.alphaMode=e.alphaMode??"premultiply-alpha-on-upload",this._videoFrameRequestCallback=this._videoFrameRequestCallback.bind(this),this._videoFrameRequestCallbackHandle=null,this._load=null,this._resolve=null,this._reject=null,this._onCanPlay=this._onCanPlay.bind(this),this._onCanPlayThrough=this._onCanPlayThrough.bind(this),this._onError=this._onError.bind(this),this._onPlayStart=this._onPlayStart.bind(this),this._onPlayStop=this._onPlayStop.bind(this),this._onSeeked=this._onSeeked.bind(this),e.autoLoad!==!1&&this.load()}updateFrame(){if(!this.destroyed){if(this._updateFPS){const e=yn.shared.elapsedMS*this.resource.playbackRate;this._msToNextUpdate=Math.floor(this._msToNextUpdate-e)}(!this._updateFPS||this._msToNextUpdate<=0)&&(this._msToNextUpdate=this._updateFPS?Math.floor(1e3/this._updateFPS):0),this.isValid&&this.update()}}_videoFrameRequestCallback(){this.updateFrame(),this.destroyed?this._videoFrameRequestCallbackHandle=null:this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback)}get isValid(){return!!this.resource.videoWidth&&!!this.resource.videoHeight}async load(){if(this._load)return this._load;const e=this.resource,t=this.options;return(e.readyState===e.HAVE_ENOUGH_DATA||e.readyState===e.HAVE_FUTURE_DATA)&&e.width&&e.height&&(e.complete=!0),e.addEventListener("play",this._onPlayStart),e.addEventListener("pause",this._onPlayStop),e.addEventListener("seeked",this._onSeeked),this._isSourceReady()?this._mediaReady():(t.preload||e.addEventListener("canplay",this._onCanPlay),e.addEventListener("canplaythrough",this._onCanPlayThrough),e.addEventListener("error",this._onError,!0)),this.alphaMode=await du(),this._load=new Promise((i,n)=>{this.isValid?i(this):(this._resolve=i,this._reject=n,t.preloadTimeoutMs!==void 0&&(this._preloadTimeout=setTimeout(()=>{this._onError(new ErrorEvent(`Preload exceeded timeout of ${t.preloadTimeoutMs}ms`))})),e.load())}),this._load}_onError(e){this.resource.removeEventListener("error",this._onError,!0),this.emit("error",e),this._reject&&(this._reject(e),this._reject=null,this._resolve=null)}_isSourcePlaying(){const e=this.resource;return!e.paused&&!e.ended}_isSourceReady(){return this.resource.readyState>2}_onPlayStart(){this.isValid||this._mediaReady(),this._configureAutoUpdate()}_onPlayStop(){this._configureAutoUpdate()}_onSeeked(){this._autoUpdate&&!this._isSourcePlaying()&&(this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0)}_onCanPlay(){this.resource.removeEventListener("canplay",this._onCanPlay),this._mediaReady()}_onCanPlayThrough(){this.resource.removeEventListener("canplaythrough",this._onCanPlay),this._preloadTimeout&&(clearTimeout(this._preloadTimeout),this._preloadTimeout=void 0),this._mediaReady()}_mediaReady(){const e=this.resource;this.isValid&&(this.isReady=!0,this.resize(e.videoWidth,e.videoHeight)),this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0,this._resolve&&(this._resolve(this),this._resolve=null,this._reject=null),this._isSourcePlaying()?this._onPlayStart():this.autoPlay&&this.resource.play()}destroy(){this._configureAutoUpdate();const e=this.resource;e&&(e.removeEventListener("play",this._onPlayStart),e.removeEventListener("pause",this._onPlayStop),e.removeEventListener("seeked",this._onSeeked),e.removeEventListener("canplay",this._onCanPlay),e.removeEventListener("canplaythrough",this._onCanPlayThrough),e.removeEventListener("error",this._onError,!0),e.pause(),e.src="",e.load()),super.destroy()}get autoUpdate(){return this._autoUpdate}set autoUpdate(e){e!==this._autoUpdate&&(this._autoUpdate=e,this._configureAutoUpdate())}get updateFPS(){return this._updateFPS}set updateFPS(e){e!==this._updateFPS&&(this._updateFPS=e,this._configureAutoUpdate())}_configureAutoUpdate(){this._autoUpdate&&this._isSourcePlaying()?!this._updateFPS&&this.resource.requestVideoFrameCallback?(this._isConnectedToTicker&&(yn.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0),this._videoFrameRequestCallbackHandle===null&&(this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback))):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker||(yn.shared.add(this.updateFrame,this),this._isConnectedToTicker=!0,this._msToNextUpdate=0)):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker&&(yn.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0))}static test(e){return globalThis.HTMLVideoElement&&e instanceof HTMLVideoElement}};ir.extension=D.TextureSource;ir.defaultOptions={...Pt.defaultOptions,autoLoad:!0,autoPlay:!0,updateFPS:0,crossorigin:!0,loop:!1,muted:!0,playsinline:!0,preload:!1};ir.MIME_TYPES={ogv:"video/ogg",mov:"video/quicktime",m4v:"video/mp4"};let Rn=ir;const vt=(s,e,t=!1)=>(Array.isArray(s)||(s=[s]),e?s.map(i=>typeof i=="string"||t?e(i):i):s);class Hm{constructor(){this._parsers=[],this._cache=new Map,this._cacheMap=new Map}reset(){this._cacheMap.clear(),this._cache.clear()}has(e){return this._cache.has(e)}get(e){const t=this._cache.get(e);return t||be(`[Assets] Asset id ${e} was not found in the Cache`),t}set(e,t){const i=vt(e);let n;for(let l=0;l<this.parsers.length;l++){const h=this.parsers[l];if(h.test(t)){n=h.getCacheableAssets(i,t);break}}const r=new Map(Object.entries(n||{}));n||i.forEach(l=>{r.set(l,t)});const o=[...r.keys()],a={cacheKeys:o,keys:i};i.forEach(l=>{this._cacheMap.set(l,a)}),o.forEach(l=>{const h=n?n[l]:t;this._cache.has(l)&&this._cache.get(l)!==h&&be("[Cache] already has key:",l),this._cache.set(l,r.get(l))})}remove(e){if(!this._cacheMap.has(e)){be(`[Assets] Asset id ${e} was not found in the Cache`);return}const t=this._cacheMap.get(e);t.cacheKeys.forEach(n=>{this._cache.delete(n)}),t.keys.forEach(n=>{this._cacheMap.delete(n)})}get parsers(){return this._parsers}}const de=new Hm,Bo=[];De.handleByList(D.TextureSource,Bo);function fu(s={}){const e=s&&s.resource,t=e?s.resource:s,i=e?s:{resource:s};for(let n=0;n<Bo.length;n++){const r=Bo[n];if(r.test(t))return new r(i)}throw new Error(`Could not find a source type for resource: ${i.resource}`)}function qm(s={},e=!1){const t=s&&s.resource,i=t?s.resource:s,n=t?s:{resource:s};if(!e&&de.has(i))return de.get(i);const r=new Y({source:fu(n)});return r.on("destroy",()=>{de.has(i)&&de.remove(i)}),e||de.set(i,r),r}function Vm(s,e=!1){return typeof s=="string"?de.get(s):s instanceof Pt?new Y({source:s}):qm(s,e)}Y.from=Vm;Pt.from=fu;De.add(au,lu,hu,Rn,ws,cu,_a);var yi=(s=>(s[s.Low=0]="Low",s[s.Normal=1]="Normal",s[s.High=2]="High",s))(yi||{});function yt(s){if(typeof s!="string")throw new TypeError(`Path must be a string. Received ${JSON.stringify(s)}`)}function Ts(s){return s.split("?")[0].split("#")[0]}function Xm(s){return s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Ym(s,e,t){return s.replace(new RegExp(Xm(e),"g"),t)}function jm(s,e){let t="",i=0,n=-1,r=0,o=-1;for(let a=0;a<=s.length;++a){if(a<s.length)o=s.charCodeAt(a);else{if(o===47)break;o=47}if(o===47){if(!(n===a-1||r===1))if(n!==a-1&&r===2){if(t.length<2||i!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){const l=t.lastIndexOf("/");if(l!==t.length-1){l===-1?(t="",i=0):(t=t.slice(0,l),i=t.length-1-t.lastIndexOf("/")),n=a,r=0;continue}}else if(t.length===2||t.length===1){t="",i=0,n=a,r=0;continue}}}else t.length>0?t+=`/${s.slice(n+1,a)}`:t=s.slice(n+1,a),i=a-n-1;n=a,r=0}else o===46&&r!==-1?++r:r=-1}return t}const it={toPosix(s){return Ym(s,"\\","/")},isUrl(s){return/^https?:/.test(this.toPosix(s))},isDataUrl(s){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(s)},isBlobUrl(s){return s.startsWith("blob:")},hasProtocol(s){return/^[^/:]+:/.test(this.toPosix(s))},getProtocol(s){yt(s),s=this.toPosix(s);const e=/^file:\/\/\//.exec(s);if(e)return e[0];const t=/^[^/:]+:\/{0,2}/.exec(s);return t?t[0]:""},toAbsolute(s,e,t){if(yt(s),this.isDataUrl(s)||this.isBlobUrl(s))return s;const i=Ts(this.toPosix(e??_e.get().getBaseUrl())),n=Ts(this.toPosix(t??this.rootname(i)));return s=this.toPosix(s),s.startsWith("/")?it.join(n,s.slice(1)):this.isAbsolute(s)?s:this.join(i,s)},normalize(s){if(yt(s),s.length===0)return".";if(this.isDataUrl(s)||this.isBlobUrl(s))return s;s=this.toPosix(s);let e="";const t=s.startsWith("/");this.hasProtocol(s)&&(e=this.rootname(s),s=s.slice(e.length));const i=s.endsWith("/");return s=jm(s),s.length>0&&i&&(s+="/"),t?`/${s}`:e+s},isAbsolute(s){return yt(s),s=this.toPosix(s),this.hasProtocol(s)?!0:s.startsWith("/")},join(...s){if(s.length===0)return".";let e;for(let t=0;t<s.length;++t){const i=s[t];if(yt(i),i.length>0)if(e===void 0)e=i;else{const n=s[t-1]??"";this.joinExtensions.includes(this.extname(n).toLowerCase())?e+=`/../${i}`:e+=`/${i}`}}return e===void 0?".":this.normalize(e)},dirname(s){if(yt(s),s.length===0)return".";s=this.toPosix(s);let e=s.charCodeAt(0);const t=e===47;let i=-1,n=!0;const r=this.getProtocol(s),o=s;s=s.slice(r.length);for(let a=s.length-1;a>=1;--a)if(e=s.charCodeAt(a),e===47){if(!n){i=a;break}}else n=!1;return i===-1?t?"/":this.isUrl(o)?r+s:r:t&&i===1?"//":r+s.slice(0,i)},rootname(s){yt(s),s=this.toPosix(s);let e="";if(s.startsWith("/")?e="/":e=this.getProtocol(s),this.isUrl(s)){const t=s.indexOf("/",e.length);t!==-1?e=s.slice(0,t):e=s,e.endsWith("/")||(e+="/")}return e},basename(s,e){yt(s),e&&yt(e),s=Ts(this.toPosix(s));let t=0,i=-1,n=!0,r;if(e!==void 0&&e.length>0&&e.length<=s.length){if(e.length===s.length&&e===s)return"";let o=e.length-1,a=-1;for(r=s.length-1;r>=0;--r){const l=s.charCodeAt(r);if(l===47){if(!n){t=r+1;break}}else a===-1&&(n=!1,a=r+1),o>=0&&(l===e.charCodeAt(o)?--o===-1&&(i=r):(o=-1,i=a))}return t===i?i=a:i===-1&&(i=s.length),s.slice(t,i)}for(r=s.length-1;r>=0;--r)if(s.charCodeAt(r)===47){if(!n){t=r+1;break}}else i===-1&&(n=!1,i=r+1);return i===-1?"":s.slice(t,i)},extname(s){yt(s),s=Ts(this.toPosix(s));let e=-1,t=0,i=-1,n=!0,r=0;for(let o=s.length-1;o>=0;--o){const a=s.charCodeAt(o);if(a===47){if(!n){t=o+1;break}continue}i===-1&&(n=!1,i=o+1),a===46?e===-1?e=o:r!==1&&(r=1):e!==-1&&(r=-1)}return e===-1||i===-1||r===0||r===1&&e===i-1&&e===t+1?"":s.slice(e,i)},parse(s){yt(s);const e={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return e;s=Ts(this.toPosix(s));let t=s.charCodeAt(0);const i=this.isAbsolute(s);let n;e.root=this.rootname(s),i||this.hasProtocol(s)?n=1:n=0;let r=-1,o=0,a=-1,l=!0,h=s.length-1,c=0;for(;h>=n;--h){if(t=s.charCodeAt(h),t===47){if(!l){o=h+1;break}continue}a===-1&&(l=!1,a=h+1),t===46?r===-1?r=h:c!==1&&(c=1):r!==-1&&(c=-1)}return r===-1||a===-1||c===0||c===1&&r===a-1&&r===o+1?a!==-1&&(o===0&&i?e.base=e.name=s.slice(1,a):e.base=e.name=s.slice(o,a)):(o===0&&i?(e.name=s.slice(1,r),e.base=s.slice(1,a)):(e.name=s.slice(o,r),e.base=s.slice(o,a)),e.ext=s.slice(r,a)),e.dir=this.dirname(s),e},sep:"/",delimiter:":",joinExtensions:[".html"]};function gu(s,e,t,i,n){const r=e[t];for(let o=0;o<r.length;o++){const a=r[o];t<e.length-1?gu(s.replace(i[t],a),e,t+1,i,n):n.push(s.replace(i[t],a))}}function Km(s){const e=/\{(.*?)\}/g,t=s.match(e),i=[];if(t){const n=[];t.forEach(r=>{const o=r.substring(1,r.length-1).split(",");n.push(o)}),gu(s,n,0,t,i)}else i.push(s);return i}const qn=s=>!Array.isArray(s);class xs{constructor(){this._defaultBundleIdentifierOptions={connector:"-",createBundleAssetId:(e,t)=>`${e}${this._bundleIdConnector}${t}`,extractAssetIdFromBundle:(e,t)=>t.replace(`${e}${this._bundleIdConnector}`,"")},this._bundleIdConnector=this._defaultBundleIdentifierOptions.connector,this._createBundleAssetId=this._defaultBundleIdentifierOptions.createBundleAssetId,this._extractAssetIdFromBundle=this._defaultBundleIdentifierOptions.extractAssetIdFromBundle,this._assetMap={},this._preferredOrder=[],this._parsers=[],this._resolverHash={},this._bundles={}}setBundleIdentifier(e){if(this._bundleIdConnector=e.connector??this._bundleIdConnector,this._createBundleAssetId=e.createBundleAssetId??this._createBundleAssetId,this._extractAssetIdFromBundle=e.extractAssetIdFromBundle??this._extractAssetIdFromBundle,this._extractAssetIdFromBundle("foo",this._createBundleAssetId("foo","bar"))!=="bar")throw new Error("[Resolver] GenerateBundleAssetId are not working correctly")}prefer(...e){e.forEach(t=>{this._preferredOrder.push(t),t.priority||(t.priority=Object.keys(t.params))}),this._resolverHash={}}set basePath(e){this._basePath=e}get basePath(){return this._basePath}set rootPath(e){this._rootPath=e}get rootPath(){return this._rootPath}get parsers(){return this._parsers}reset(){this.setBundleIdentifier(this._defaultBundleIdentifierOptions),this._assetMap={},this._preferredOrder=[],this._resolverHash={},this._rootPath=null,this._basePath=null,this._manifest=null,this._bundles={},this._defaultSearchParams=null}setDefaultSearchParams(e){if(typeof e=="string")this._defaultSearchParams=e;else{const t=e;this._defaultSearchParams=Object.keys(t).map(i=>`${encodeURIComponent(i)}=${encodeURIComponent(t[i])}`).join("&")}}getAlias(e){const{alias:t,src:i}=e;return vt(t||i,r=>typeof r=="string"?r:Array.isArray(r)?r.map(o=>o?.src??o):r?.src?r.src:r,!0)}addManifest(e){this._manifest&&be("[Resolver] Manifest already exists, this will be overwritten"),this._manifest=e,e.bundles.forEach(t=>{this.addBundle(t.name,t.assets)})}addBundle(e,t){const i=[];let n=t;Array.isArray(t)||(n=Object.entries(t).map(([r,o])=>typeof o=="string"||Array.isArray(o)?{alias:r,src:o}:{alias:r,...o})),n.forEach(r=>{const o=r.src,a=r.alias;let l;if(typeof a=="string"){const h=this._createBundleAssetId(e,a);i.push(h),l=[a,h]}else{const h=a.map(c=>this._createBundleAssetId(e,c));i.push(...h),l=[...a,...h]}this.add({...r,alias:l,src:o})}),this._bundles[e]=i}add(e){const t=[];Array.isArray(e)?t.push(...e):t.push(e);let i;i=r=>{this.hasKey(r)&&be(`[Resolver] already has key: ${r} overwriting`)},vt(t).forEach(r=>{const{src:o}=r;let{data:a,format:l,loadParser:h}=r;const c=vt(o).map(_=>typeof _=="string"?Km(_):Array.isArray(_)?_:[_]),u=this.getAlias(r);Array.isArray(u)?u.forEach(i):i(u);const d=[];c.forEach(_=>{_.forEach(f=>{let m={};if(typeof f!="object"){m.src=f;for(let g=0;g<this._parsers.length;g++){const p=this._parsers[g];if(p.test(f)){m=p.parse(f);break}}}else a=f.data??a,l=f.format??l,h=f.loadParser??h,m={...m,...f};if(!u)throw new Error(`[Resolver] alias is undefined for this asset: ${m.src}`);m=this._buildResolvedAsset(m,{aliases:u,data:a,format:l,loadParser:h}),d.push(m)})}),u.forEach(_=>{this._assetMap[_]=d})})}resolveBundle(e){const t=qn(e);e=vt(e);const i={};return e.forEach(n=>{const r=this._bundles[n];if(r){const o=this.resolve(r),a={};for(const l in o){const h=o[l];a[this._extractAssetIdFromBundle(n,l)]=h}i[n]=a}}),t?i[e[0]]:i}resolveUrl(e){const t=this.resolve(e);if(typeof e!="string"){const i={};for(const n in t)i[n]=t[n].src;return i}return t.src}resolve(e){const t=qn(e);e=vt(e);const i={};return e.forEach(n=>{if(!this._resolverHash[n])if(this._assetMap[n]){let r=this._assetMap[n];const o=this._getPreferredOrder(r);o?.priority.forEach(a=>{o.params[a].forEach(l=>{const h=r.filter(c=>c[a]?c[a]===l:!1);h.length&&(r=h)})}),this._resolverHash[n]=r[0]}else this._resolverHash[n]=this._buildResolvedAsset({alias:[n],src:n},{});i[n]=this._resolverHash[n]}),t?i[e[0]]:i}hasKey(e){return!!this._assetMap[e]}hasBundle(e){return!!this._bundles[e]}_getPreferredOrder(e){for(let t=0;t<e.length;t++){const i=e[t],n=this._preferredOrder.find(r=>r.params.format.includes(i.format));if(n)return n}return this._preferredOrder[0]}_appendDefaultSearchParams(e){if(!this._defaultSearchParams)return e;const t=/\?/.test(e)?"&":"?";return`${e}${t}${this._defaultSearchParams}`}_buildResolvedAsset(e,t){const{aliases:i,data:n,loadParser:r,format:o}=t;return(this._basePath||this._rootPath)&&(e.src=it.toAbsolute(e.src,this._basePath,this._rootPath)),e.alias=i??e.alias??[e.src],e.src=this._appendDefaultSearchParams(e.src),e.data={...n||{},...e.data},e.loadParser=r??e.loadParser,e.format=o??e.format??Zm(e.src),e}}xs.RETINA_PREFIX=/@([0-9\.]+)x/;function Zm(s){return s.split(".").pop().split("?").shift().split("#").shift()}const Lo=(s,e)=>{const t=e.split("?")[1];return t&&(s+=`?${t}`),s},mu=class Ns{constructor(e,t){this.linkedSheets=[],this._texture=e instanceof Y?e:null,this.textureSource=e.source,this.textures={},this.animations={},this.data=t;const i=parseFloat(t.meta.scale);i?(this.resolution=i,e.source.resolution=this.resolution):this.resolution=e.source._resolution,this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}parse(){return new Promise(e=>{this._callback=e,this._batchIndex=0,this._frameKeys.length<=Ns.BATCH_SIZE?(this._processFrames(0),this._processAnimations(),this._parseComplete()):this._nextBatch()})}_processFrames(e){let t=e;const i=Ns.BATCH_SIZE;for(;t-e<i&&t<this._frameKeys.length;){const n=this._frameKeys[t],r=this._frames[n],o=r.frame;if(o){let a=null,l=null;const h=r.trimmed!==!1&&r.sourceSize?r.sourceSize:r.frame,c=new se(0,0,Math.floor(h.w)/this.resolution,Math.floor(h.h)/this.resolution);r.rotated?a=new se(Math.floor(o.x)/this.resolution,Math.floor(o.y)/this.resolution,Math.floor(o.h)/this.resolution,Math.floor(o.w)/this.resolution):a=new se(Math.floor(o.x)/this.resolution,Math.floor(o.y)/this.resolution,Math.floor(o.w)/this.resolution,Math.floor(o.h)/this.resolution),r.trimmed!==!1&&r.spriteSourceSize&&(l=new se(Math.floor(r.spriteSourceSize.x)/this.resolution,Math.floor(r.spriteSourceSize.y)/this.resolution,Math.floor(o.w)/this.resolution,Math.floor(o.h)/this.resolution)),this.textures[n]=new Y({source:this.textureSource,frame:a,orig:c,trim:l,rotate:r.rotated?2:0,defaultAnchor:r.anchor,defaultBorders:r.borders,label:n.toString()})}t++}}_processAnimations(){const e=this.data.animations||{};for(const t in e){this.animations[t]=[];for(let i=0;i<e[t].length;i++){const n=e[t][i];this.animations[t].push(this.textures[n])}}}_parseComplete(){const e=this._callback;this._callback=null,this._batchIndex=0,e.call(this,this.textures)}_nextBatch(){this._processFrames(this._batchIndex*Ns.BATCH_SIZE),this._batchIndex++,setTimeout(()=>{this._batchIndex*Ns.BATCH_SIZE<this._frameKeys.length?this._nextBatch():(this._processAnimations(),this._parseComplete())},0)}destroy(e=!1){for(const t in this.textures)this.textures[t].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,e&&(this._texture?.destroy(),this.textureSource.destroy()),this._texture=null,this.textureSource=null,this.linkedSheets=[]}};mu.BATCH_SIZE=1e3;let pl=mu;const Qm=["jpg","png","jpeg","avif","webp","basis","etc2","bc7","bc6h","bc5","bc4","bc3","bc2","bc1","eac","astc"];function pu(s,e,t){const i={};if(s.forEach(n=>{i[n]=e}),Object.keys(e.textures).forEach(n=>{i[n]=e.textures[n]}),!t){const n=it.dirname(s[0]);e.linkedSheets.forEach((r,o)=>{const a=pu([`${n}/${e.data.meta.related_multi_packs[o]}`],r,!0);Object.assign(i,a)})}return i}const Jm={extension:D.Asset,cache:{test:s=>s instanceof pl,getCacheableAssets:(s,e)=>pu(s,e,!1)},resolver:{extension:{type:D.ResolveParser,name:"resolveSpritesheet"},test:s=>{const t=s.split("?")[0].split("."),i=t.pop(),n=t.pop();return i==="json"&&Qm.includes(n)},parse:s=>{const e=s.split(".");return{resolution:parseFloat(xs.RETINA_PREFIX.exec(s)?.[1]??"1"),format:e[e.length-2],src:s}}},loader:{name:"spritesheetLoader",extension:{type:D.LoadParser,priority:yi.Normal,name:"spritesheetLoader"},async testParse(s,e){return it.extname(e.src).toLowerCase()===".json"&&!!s.frames},async parse(s,e,t){const{texture:i,imageFilename:n,textureOptions:r}=e?.data??{};let o=it.dirname(e.src);o&&o.lastIndexOf("/")!==o.length-1&&(o+="/");let a;if(i instanceof Y)a=i;else{const c=Lo(o+(n??s.meta.image),e.src);a=(await t.load([{src:c,data:r}]))[c]}const l=new pl(a.source,s);await l.parse();const h=s?.meta?.related_multi_packs;if(Array.isArray(h)){const c=[];for(const d of h){if(typeof d!="string")continue;let _=o+d;e.data?.ignoreMultiPack||(_=Lo(_,e.src),c.push(t.load({src:_,data:{ignoreMultiPack:!0}})))}const u=await Promise.all(c);l.linkedSheets=u,u.forEach(d=>{d.linkedSheets=[l].concat(l.linkedSheets.filter(_=>_!==d))})}return l},async unload(s,e,t){await t.unload(s.textureSource._sourceOrigin),s.destroy(!1)}}};De.add(Jm);const Zr=Object.create(null),bl=Object.create(null);function ma(s,e){let t=bl[s];return t===void 0&&(Zr[e]===void 0&&(Zr[e]=1),bl[s]=t=Zr[e]++),t}let wn;function bu(){return(!wn||wn?.isContextLost())&&(wn=_e.get().createCanvas().getContext("webgl",{})),wn}let xn;function ep(){if(!xn){xn="mediump";const s=bu();s&&s.getShaderPrecisionFormat&&(xn=s.getShaderPrecisionFormat(s.FRAGMENT_SHADER,s.HIGH_FLOAT).precision?"highp":"mediump")}return xn}function tp(s,e,t){return e?s:t?(s=s.replace("out vec4 finalColor;",""),`
        
        #ifdef GL_ES // This checks if it is WebGL1
        #define in varying
        #define finalColor gl_FragColor
        #define texture texture2D
        #endif
        ${s}
        `):`
        
        #ifdef GL_ES // This checks if it is WebGL1
        #define in attribute
        #define out varying
        #endif
        ${s}
        `}function ip(s,e,t){const i=t?e.maxSupportedFragmentPrecision:e.maxSupportedVertexPrecision;if(s.substring(0,9)!=="precision"){let n=t?e.requestedFragmentPrecision:e.requestedVertexPrecision;return n==="highp"&&i!=="highp"&&(n="mediump"),`precision ${n} float;
${s}`}else if(i!=="highp"&&s.substring(0,15)==="precision highp")return s.replace("precision highp","precision mediump");return s}function sp(s,e){return e?`#version 300 es
${s}`:s}const np={},rp={};function op(s,{name:e="pixi-program"},t=!0){e=e.replace(/\s+/g,"-"),e+=t?"-fragment":"-vertex";const i=t?np:rp;return i[e]?(i[e]++,e+=`-${i[e]}`):i[e]=1,s.indexOf("#define SHADER_NAME")!==-1?s:`${`#define SHADER_NAME ${e}`}
${s}`}function ap(s,e){return e?s.replace("#version 300 es",""):s}const Qr={stripVersion:ap,ensurePrecision:ip,addProgramDefines:tp,setProgramName:op,insertVersion:sp},Jr=Object.create(null),yu=class $o{constructor(e){e={...$o.defaultOptions,...e};const t=e.fragment.indexOf("#version 300 es")!==-1,i={stripVersion:t,ensurePrecision:{requestedFragmentPrecision:e.preferredFragmentPrecision,requestedVertexPrecision:e.preferredVertexPrecision,maxSupportedVertexPrecision:"highp",maxSupportedFragmentPrecision:ep()},setProgramName:{name:e.name},addProgramDefines:t,insertVersion:t};let n=e.fragment,r=e.vertex;Object.keys(Qr).forEach(o=>{const a=i[o];n=Qr[o](n,a,!0),r=Qr[o](r,a,!1)}),this.fragment=n,this.vertex=r,this.transformFeedbackVaryings=e.transformFeedbackVaryings,this._key=ma(`${this.vertex}:${this.fragment}`,"gl-program")}destroy(){this.fragment=null,this.vertex=null,this._attributeData=null,this._uniformData=null,this._uniformBlockData=null,this.transformFeedbackVaryings=null}static from(e){const t=`${e.vertex}:${e.fragment}`;return Jr[t]||(Jr[t]=new $o(e)),Jr[t]}};yu.defaultOptions={preferredVertexPrecision:"highp",preferredFragmentPrecision:"mediump"};let _s=yu;const yl={uint8x2:{size:2,stride:2,normalised:!1},uint8x4:{size:4,stride:4,normalised:!1},sint8x2:{size:2,stride:2,normalised:!1},sint8x4:{size:4,stride:4,normalised:!1},unorm8x2:{size:2,stride:2,normalised:!0},unorm8x4:{size:4,stride:4,normalised:!0},snorm8x2:{size:2,stride:2,normalised:!0},snorm8x4:{size:4,stride:4,normalised:!0},uint16x2:{size:2,stride:4,normalised:!1},uint16x4:{size:4,stride:8,normalised:!1},sint16x2:{size:2,stride:4,normalised:!1},sint16x4:{size:4,stride:8,normalised:!1},unorm16x2:{size:2,stride:4,normalised:!0},unorm16x4:{size:4,stride:8,normalised:!0},snorm16x2:{size:2,stride:4,normalised:!0},snorm16x4:{size:4,stride:8,normalised:!0},float16x2:{size:2,stride:4,normalised:!1},float16x4:{size:4,stride:8,normalised:!1},float32:{size:1,stride:4,normalised:!1},float32x2:{size:2,stride:8,normalised:!1},float32x3:{size:3,stride:12,normalised:!1},float32x4:{size:4,stride:16,normalised:!1},uint32:{size:1,stride:4,normalised:!1},uint32x2:{size:2,stride:8,normalised:!1},uint32x3:{size:3,stride:12,normalised:!1},uint32x4:{size:4,stride:16,normalised:!1},sint32:{size:1,stride:4,normalised:!1},sint32x2:{size:2,stride:8,normalised:!1},sint32x3:{size:3,stride:12,normalised:!1},sint32x4:{size:4,stride:16,normalised:!1}};function lp(s){return yl[s]??yl.float32}const hp={f32:"float32","vec2<f32>":"float32x2","vec3<f32>":"float32x3","vec4<f32>":"float32x4",vec2f:"float32x2",vec3f:"float32x3",vec4f:"float32x4",i32:"sint32","vec2<i32>":"sint32x2","vec3<i32>":"sint32x3","vec4<i32>":"sint32x4",u32:"uint32","vec2<u32>":"uint32x2","vec3<u32>":"uint32x3","vec4<u32>":"uint32x4",bool:"uint32","vec2<bool>":"uint32x2","vec3<bool>":"uint32x3","vec4<bool>":"uint32x4"};function cp({source:s,entryPoint:e}){const t={},i=s.indexOf(`fn ${e}`);if(i!==-1){const n=s.indexOf("->",i);if(n!==-1){const r=s.substring(i,n),o=/@location\((\d+)\)\s+([a-zA-Z0-9_]+)\s*:\s*([a-zA-Z0-9_<>]+)(?:,|\s|$)/g;let a;for(;(a=o.exec(r))!==null;){const l=hp[a[3]]??"float32";t[a[2]]={location:parseInt(a[1],10),format:l,stride:lp(l).stride,offset:0,instance:!1,start:0}}}}return t}function eo(s){const e=/(^|[^/])@(group|binding)\(\d+\)[^;]+;/g,t=/@group\((\d+)\)/,i=/@binding\((\d+)\)/,n=/var(<[^>]+>)? (\w+)/,r=/:\s*(\w+)/,o=/struct\s+(\w+)\s*{([^}]+)}/g,a=/(\w+)\s*:\s*([\w\<\>]+)/g,l=/struct\s+(\w+)/,h=s.match(e)?.map(u=>({group:parseInt(u.match(t)[1],10),binding:parseInt(u.match(i)[1],10),name:u.match(n)[2],isUniform:u.match(n)[1]==="<uniform>",type:u.match(r)[1]}));if(!h)return{groups:[],structs:[]};const c=s.match(o)?.map(u=>{const d=u.match(l)[1],_=u.match(a).reduce((f,m)=>{const[g,p]=m.split(":");return f[g.trim()]=p.trim(),f},{});return _?{name:d,members:_}:null}).filter(({name:u})=>h.some(d=>d.type===u))??[];return{groups:h,structs:c}}var Us=(s=>(s[s.VERTEX=1]="VERTEX",s[s.FRAGMENT=2]="FRAGMENT",s[s.COMPUTE=4]="COMPUTE",s))(Us||{});function up({groups:s}){const e=[];for(let t=0;t<s.length;t++){const i=s[t];e[i.group]||(e[i.group]=[]),i.isUniform?e[i.group].push({binding:i.binding,visibility:Us.VERTEX|Us.FRAGMENT,buffer:{type:"uniform"}}):i.type==="sampler"?e[i.group].push({binding:i.binding,visibility:Us.FRAGMENT,sampler:{type:"filtering"}}):i.type==="texture_2d"&&e[i.group].push({binding:i.binding,visibility:Us.FRAGMENT,texture:{sampleType:"float",viewDimension:"2d",multisampled:!1}})}return e}function dp({groups:s}){const e=[];for(let t=0;t<s.length;t++){const i=s[t];e[i.group]||(e[i.group]={}),e[i.group][i.name]=i.binding}return e}function _p(s,e){const t=new Set,i=new Set,n=[...s.structs,...e.structs].filter(o=>t.has(o.name)?!1:(t.add(o.name),!0)),r=[...s.groups,...e.groups].filter(o=>{const a=`${o.name}-${o.binding}`;return i.has(a)?!1:(i.add(a),!0)});return{structs:n,groups:r}}const to=Object.create(null);class mi{constructor(e){this._layoutKey=0,this._attributeLocationsKey=0;const{fragment:t,vertex:i,layout:n,gpuLayout:r,name:o}=e;if(this.name=o,this.fragment=t,this.vertex=i,t.source===i.source){const a=eo(t.source);this.structsAndGroups=a}else{const a=eo(i.source),l=eo(t.source);this.structsAndGroups=_p(a,l)}this.layout=n??dp(this.structsAndGroups),this.gpuLayout=r??up(this.structsAndGroups),this.autoAssignGlobalUniforms=this.layout[0]?.globalUniforms!==void 0,this.autoAssignLocalUniforms=this.layout[1]?.localUniforms!==void 0,this._generateProgramKey()}_generateProgramKey(){const{vertex:e,fragment:t}=this,i=e.source+t.source+e.entryPoint+t.entryPoint;this._layoutKey=ma(i,"program")}get attributeData(){return this._attributeData??(this._attributeData=cp(this.vertex)),this._attributeData}destroy(){this.gpuLayout=null,this.layout=null,this.structsAndGroups=null,this.fragment=null,this.vertex=null}static from(e){const t=`${e.vertex.source}:${e.fragment.source}:${e.fragment.entryPoint}:${e.vertex.entryPoint}`;return to[t]||(to[t]=new mi(e)),to[t]}}const wu=["f32","i32","vec2<f32>","vec3<f32>","vec4<f32>","mat2x2<f32>","mat3x3<f32>","mat4x4<f32>","mat3x2<f32>","mat4x2<f32>","mat2x3<f32>","mat4x3<f32>","mat2x4<f32>","mat3x4<f32>","vec2<i32>","vec3<i32>","vec4<i32>"],fp=wu.reduce((s,e)=>(s[e]=!0,s),{});function gp(s,e){switch(s){case"f32":return 0;case"vec2<f32>":return new Float32Array(2*e);case"vec3<f32>":return new Float32Array(3*e);case"vec4<f32>":return new Float32Array(4*e);case"mat2x2<f32>":return new Float32Array([1,0,0,1]);case"mat3x3<f32>":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4x4<f32>":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}return null}const xu=class vu{constructor(e,t){this._touched=0,this.uid=pe("uniform"),this._resourceType="uniformGroup",this._resourceId=pe("resource"),this.isUniformGroup=!0,this._dirtyId=0,this.destroyed=!1,t={...vu.defaultOptions,...t},this.uniformStructures=e;const i={};for(const n in e){const r=e[n];if(r.name=n,r.size=r.size??1,!fp[r.type])throw new Error(`Uniform type ${r.type} is not supported. Supported uniform types are: ${wu.join(", ")}`);r.value??(r.value=gp(r.type,r.size)),i[n]=r.value}this.uniforms=i,this._dirtyId=1,this.ubo=t.ubo,this.isStatic=t.isStatic,this._signature=ma(Object.keys(i).map(n=>`${n}-${e[n].type}`).join("-"),"uniform-group")}update(){this._dirtyId++}};xu.defaultOptions={ubo:!1,isStatic:!1};let ku=xu;class Fn{constructor(e){this.resources=Object.create(null),this._dirty=!0;let t=0;for(const i in e){const n=e[i];this.setResource(n,t++)}this._updateKey()}_updateKey(){if(!this._dirty)return;this._dirty=!1;const e=[];let t=0;for(const i in this.resources)e[t++]=this.resources[i]._resourceId;this._key=e.join("|")}setResource(e,t){const i=this.resources[t];e!==i&&(i&&e.off?.("change",this.onResourceChange,this),e.on?.("change",this.onResourceChange,this),this.resources[t]=e,this._dirty=!0)}getResource(e){return this.resources[e]}_touch(e){const t=this.resources;for(const i in t)t[i]._touched=e}destroy(){const e=this.resources;for(const t in e)e[t].off?.("change",this.onResourceChange,this);this.resources=null}onResourceChange(e){if(this._dirty=!0,e.destroyed){const t=this.resources;for(const i in t)t[i]===e&&(t[i]=null)}else this._updateKey()}}var Oo=(s=>(s[s.WEBGL=1]="WEBGL",s[s.WEBGPU=2]="WEBGPU",s[s.BOTH=3]="BOTH",s))(Oo||{});class sr extends Tt{constructor(e){super(),this.uid=pe("shader"),this._uniformBindMap=Object.create(null),this._ownedBindGroups=[];let{gpuProgram:t,glProgram:i,groups:n,resources:r,compatibleRenderers:o,groupMap:a}=e;this.gpuProgram=t,this.glProgram=i,o===void 0&&(o=0,t&&(o|=Oo.WEBGPU),i&&(o|=Oo.WEBGL)),this.compatibleRenderers=o;const l={};if(!r&&!n&&(r={}),r&&n)throw new Error("[Shader] Cannot have both resources and groups");if(!t&&n&&!a)throw new Error("[Shader] No group map or WebGPU shader provided - consider using resources instead.");if(!t&&n&&a)for(const h in a)for(const c in a[h]){const u=a[h][c];l[u]={group:h,binding:c,name:u}}else if(t&&n&&!a){const h=t.structsAndGroups.groups;a={},h.forEach(c=>{a[c.group]=a[c.group]||{},a[c.group][c.binding]=c.name,l[c.name]=c})}else if(r){n={},a={},t&&t.structsAndGroups.groups.forEach(u=>{a[u.group]=a[u.group]||{},a[u.group][u.binding]=u.name,l[u.name]=u});let h=0;for(const c in r)l[c]||(n[99]||(n[99]=new Fn,this._ownedBindGroups.push(n[99])),l[c]={group:99,binding:h,name:c},a[99]=a[99]||{},a[99][h]=c,h++);for(const c in r){const u=c;let d=r[c];!d.source&&!d._resourceType&&(d=new ku(d));const _=l[u];_&&(n[_.group]||(n[_.group]=new Fn,this._ownedBindGroups.push(n[_.group])),n[_.group].setResource(d,_.binding))}}this.groups=n,this._uniformBindMap=a,this.resources=this._buildResourceAccessor(n,l)}addResource(e,t,i){var n,r;(n=this._uniformBindMap)[t]||(n[t]={}),(r=this._uniformBindMap[t])[i]||(r[i]=e),this.groups[t]||(this.groups[t]=new Fn,this._ownedBindGroups.push(this.groups[t]))}_buildResourceAccessor(e,t){const i={};for(const n in t){const r=t[n];Object.defineProperty(i,r.name,{get(){return e[r.group].getResource(r.binding)},set(o){e[r.group].setResource(o,r.binding)}})}return i}destroy(e=!1){this.emit("destroy",this),e&&(this.gpuProgram?.destroy(),this.glProgram?.destroy()),this.gpuProgram=null,this.glProgram=null,this.removeAllListeners(),this._uniformBindMap=null,this._ownedBindGroups.forEach(t=>{t.destroy()}),this._ownedBindGroups=null,this.resources=null,this.groups=null}static from(e){const{gpu:t,gl:i,...n}=e;let r,o;return t&&(r=mi.from(t)),i&&(o=_s.from(i)),new sr({gpuProgram:r,glProgram:o,...n})}}const mp={normal:0,add:1,multiply:2,screen:3,overlay:4,erase:5,"normal-npm":6,"add-npm":7,"screen-npm":8,min:9,max:10},io=0,so=1,no=2,ro=3,oo=4,ao=5,Go=class Au{constructor(){this.data=0,this.blendMode="normal",this.polygonOffset=0,this.blend=!0,this.depthMask=!0}get blend(){return!!(this.data&1<<io)}set blend(e){!!(this.data&1<<io)!==e&&(this.data^=1<<io)}get offsets(){return!!(this.data&1<<so)}set offsets(e){!!(this.data&1<<so)!==e&&(this.data^=1<<so)}set cullMode(e){if(e==="none"){this.culling=!1;return}this.culling=!0,this.clockwiseFrontFace=e==="front"}get cullMode(){return this.culling?this.clockwiseFrontFace?"front":"back":"none"}get culling(){return!!(this.data&1<<no)}set culling(e){!!(this.data&1<<no)!==e&&(this.data^=1<<no)}get depthTest(){return!!(this.data&1<<ro)}set depthTest(e){!!(this.data&1<<ro)!==e&&(this.data^=1<<ro)}get depthMask(){return!!(this.data&1<<ao)}set depthMask(e){!!(this.data&1<<ao)!==e&&(this.data^=1<<ao)}get clockwiseFrontFace(){return!!(this.data&1<<oo)}set clockwiseFrontFace(e){!!(this.data&1<<oo)!==e&&(this.data^=1<<oo)}get blendMode(){return this._blendMode}set blendMode(e){this.blend=e!=="none",this._blendMode=e,this._blendModeId=mp[e]||0}get polygonOffset(){return this._polygonOffset}set polygonOffset(e){this.offsets=!!e,this._polygonOffset=e}toString(){return`[pixi.js/core:State blendMode=${this.blendMode} clockwiseFrontFace=${this.clockwiseFrontFace} culling=${this.culling} depthMask=${this.depthMask} polygonOffset=${this.polygonOffset}]`}static for2d(){const e=new Au;return e.depthTest=!1,e.blend=!0,e}};Go.default2d=Go.for2d();let pp=Go;const Su=class Do extends sr{constructor(e){e={...Do.defaultOptions,...e},super(e),this.enabled=!0,this._state=pp.for2d(),this.blendMode=e.blendMode,this.padding=e.padding,typeof e.antialias=="boolean"?this.antialias=e.antialias?"on":"off":this.antialias=e.antialias,this.resolution=e.resolution,this.blendRequired=e.blendRequired,this.clipToViewport=e.clipToViewport,this.addResource("uTexture",0,1)}apply(e,t,i,n){e.applyFilter(this,t,i,n)}get blendMode(){return this._state.blendMode}set blendMode(e){this._state.blendMode=e}static from(e){const{gpu:t,gl:i,...n}=e;let r,o;return t&&(r=mi.from(t)),i&&(o=_s.from(i)),new Do({gpuProgram:r,glProgram:o,...n})}};Su.defaultOptions={blendMode:"normal",resolution:1,padding:0,antialias:"off",blendRequired:!1,clipToViewport:!0};let No=Su;const Uo=[];De.handleByNamedList(D.Environment,Uo);async function bp(s){if(!s)for(let e=0;e<Uo.length;e++){const t=Uo[e];if(t.value.test()){await t.value.load();return}}}let Ps;function yp(){if(typeof Ps=="boolean")return Ps;try{Ps=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0}catch{Ps=!1}return Ps}var pa={exports:{}};pa.exports=nr;pa.exports.default=nr;function nr(s,e,t){t=t||2;var i=e&&e.length,n=i?e[0]*t:s.length,r=Cu(s,0,n,t,!0),o=[];if(!r||r.next===r.prev)return o;var a,l,h,c,u,d,_;if(i&&(r=Ap(s,e,r,t)),s.length>80*t){a=h=s[0],l=c=s[1];for(var f=t;f<n;f+=t)u=s[f],d=s[f+1],u<a&&(a=u),d<l&&(l=d),u>h&&(h=u),d>c&&(c=d);_=Math.max(h-a,c-l),_=_!==0?32767/_:0}return Qs(r,o,t,a,l,_,0),o}function Cu(s,e,t,i,n){var r,o;if(n===qo(s,e,t,i)>0)for(r=e;r<t;r+=i)o=wl(r,s[r],s[r+1],o);else for(r=t-i;r>=e;r-=i)o=wl(r,s[r],s[r+1],o);return o&&rr(o,o.next)&&(en(o),o=o.next),o}function $i(s,e){if(!s)return s;e||(e=s);var t=s,i;do if(i=!1,!t.steiner&&(rr(t,t.next)||ce(t.prev,t,t.next)===0)){if(en(t),t=e=t.prev,t===t.next)break;i=!0}else t=t.next;while(i||t!==e);return e}function Qs(s,e,t,i,n,r,o){if(s){!o&&r&&Pp(s,i,n,r);for(var a=s,l,h;s.prev!==s.next;){if(l=s.prev,h=s.next,r?xp(s,i,n,r):wp(s)){e.push(l.i/t|0),e.push(s.i/t|0),e.push(h.i/t|0),en(s),s=h.next,a=h.next;continue}if(s=h,s===a){o?o===1?(s=vp($i(s),e,t),Qs(s,e,t,i,n,r,2)):o===2&&kp(s,e,t,i,n,r):Qs($i(s),e,t,i,n,r,1);break}}}}function wp(s){var e=s.prev,t=s,i=s.next;if(ce(e,t,i)>=0)return!1;for(var n=e.x,r=t.x,o=i.x,a=e.y,l=t.y,h=i.y,c=n<r?n<o?n:o:r<o?r:o,u=a<l?a<h?a:h:l<h?l:h,d=n>r?n>o?n:o:r>o?r:o,_=a>l?a>h?a:h:l>h?l:h,f=i.next;f!==e;){if(f.x>=c&&f.x<=d&&f.y>=u&&f.y<=_&&ns(n,a,r,l,o,h,f.x,f.y)&&ce(f.prev,f,f.next)>=0)return!1;f=f.next}return!0}function xp(s,e,t,i){var n=s.prev,r=s,o=s.next;if(ce(n,r,o)>=0)return!1;for(var a=n.x,l=r.x,h=o.x,c=n.y,u=r.y,d=o.y,_=a<l?a<h?a:h:l<h?l:h,f=c<u?c<d?c:d:u<d?u:d,m=a>l?a>h?a:h:l>h?l:h,g=c>u?c>d?c:d:u>d?u:d,p=Wo(_,f,e,t,i),y=Wo(m,g,e,t,i),b=s.prevZ,w=s.nextZ;b&&b.z>=p&&w&&w.z<=y;){if(b.x>=_&&b.x<=m&&b.y>=f&&b.y<=g&&b!==n&&b!==o&&ns(a,c,l,u,h,d,b.x,b.y)&&ce(b.prev,b,b.next)>=0||(b=b.prevZ,w.x>=_&&w.x<=m&&w.y>=f&&w.y<=g&&w!==n&&w!==o&&ns(a,c,l,u,h,d,w.x,w.y)&&ce(w.prev,w,w.next)>=0))return!1;w=w.nextZ}for(;b&&b.z>=p;){if(b.x>=_&&b.x<=m&&b.y>=f&&b.y<=g&&b!==n&&b!==o&&ns(a,c,l,u,h,d,b.x,b.y)&&ce(b.prev,b,b.next)>=0)return!1;b=b.prevZ}for(;w&&w.z<=y;){if(w.x>=_&&w.x<=m&&w.y>=f&&w.y<=g&&w!==n&&w!==o&&ns(a,c,l,u,h,d,w.x,w.y)&&ce(w.prev,w,w.next)>=0)return!1;w=w.nextZ}return!0}function vp(s,e,t){var i=s;do{var n=i.prev,r=i.next.next;!rr(n,r)&&Mu(n,i,i.next,r)&&Js(n,r)&&Js(r,n)&&(e.push(n.i/t|0),e.push(i.i/t|0),e.push(r.i/t|0),en(i),en(i.next),i=s=r),i=i.next}while(i!==s);return $i(i)}function kp(s,e,t,i,n,r){var o=s;do{for(var a=o.next.next;a!==o.prev;){if(o.i!==a.i&&Ep(o,a)){var l=Tu(o,a);o=$i(o,o.next),l=$i(l,l.next),Qs(o,e,t,i,n,r,0),Qs(l,e,t,i,n,r,0);return}a=a.next}o=o.next}while(o!==s)}function Ap(s,e,t,i){var n=[],r,o,a,l,h;for(r=0,o=e.length;r<o;r++)a=e[r]*i,l=r<o-1?e[r+1]*i:s.length,h=Cu(s,a,l,i,!1),h===h.next&&(h.steiner=!0),n.push(Ip(h));for(n.sort(Sp),r=0;r<n.length;r++)t=Cp(n[r],t);return t}function Sp(s,e){return s.x-e.x}function Cp(s,e){var t=Mp(s,e);if(!t)return e;var i=Tu(t,s);return $i(i,i.next),$i(t,t.next)}function Mp(s,e){var t=e,i=s.x,n=s.y,r=-1/0,o;do{if(n<=t.y&&n>=t.next.y&&t.next.y!==t.y){var a=t.x+(n-t.y)*(t.next.x-t.x)/(t.next.y-t.y);if(a<=i&&a>r&&(r=a,o=t.x<t.next.x?t:t.next,a===i))return o}t=t.next}while(t!==e);if(!o)return null;var l=o,h=o.x,c=o.y,u=1/0,d;t=o;do i>=t.x&&t.x>=h&&i!==t.x&&ns(n<c?i:r,n,h,c,n<c?r:i,n,t.x,t.y)&&(d=Math.abs(n-t.y)/(i-t.x),Js(t,s)&&(d<u||d===u&&(t.x>o.x||t.x===o.x&&Tp(o,t)))&&(o=t,u=d)),t=t.next;while(t!==l);return o}function Tp(s,e){return ce(s.prev,s,e.prev)<0&&ce(e.next,s,s.next)<0}function Pp(s,e,t,i){var n=s;do n.z===0&&(n.z=Wo(n.x,n.y,e,t,i)),n.prevZ=n.prev,n.nextZ=n.next,n=n.next;while(n!==s);n.prevZ.nextZ=null,n.prevZ=null,zp(n)}function zp(s){var e,t,i,n,r,o,a,l,h=1;do{for(t=s,s=null,r=null,o=0;t;){for(o++,i=t,a=0,e=0;e<h&&(a++,i=i.nextZ,!!i);e++);for(l=h;a>0||l>0&&i;)a!==0&&(l===0||!i||t.z<=i.z)?(n=t,t=t.nextZ,a--):(n=i,i=i.nextZ,l--),r?r.nextZ=n:s=n,n.prevZ=r,r=n;t=i}r.nextZ=null,h*=2}while(o>1);return s}function Wo(s,e,t,i,n){return s=(s-t)*n|0,e=(e-i)*n|0,s=(s|s<<8)&16711935,s=(s|s<<4)&252645135,s=(s|s<<2)&858993459,s=(s|s<<1)&1431655765,e=(e|e<<8)&16711935,e=(e|e<<4)&252645135,e=(e|e<<2)&858993459,e=(e|e<<1)&1431655765,s|e<<1}function Ip(s){var e=s,t=s;do(e.x<t.x||e.x===t.x&&e.y<t.y)&&(t=e),e=e.next;while(e!==s);return t}function ns(s,e,t,i,n,r,o,a){return(n-o)*(e-a)>=(s-o)*(r-a)&&(s-o)*(i-a)>=(t-o)*(e-a)&&(t-o)*(r-a)>=(n-o)*(i-a)}function Ep(s,e){return s.next.i!==e.i&&s.prev.i!==e.i&&!Rp(s,e)&&(Js(s,e)&&Js(e,s)&&Fp(s,e)&&(ce(s.prev,s,e.prev)||ce(s,e.prev,e))||rr(s,e)&&ce(s.prev,s,s.next)>0&&ce(e.prev,e,e.next)>0)}function ce(s,e,t){return(e.y-s.y)*(t.x-e.x)-(e.x-s.x)*(t.y-e.y)}function rr(s,e){return s.x===e.x&&s.y===e.y}function Mu(s,e,t,i){var n=kn(ce(s,e,t)),r=kn(ce(s,e,i)),o=kn(ce(t,i,s)),a=kn(ce(t,i,e));return!!(n!==r&&o!==a||n===0&&vn(s,t,e)||r===0&&vn(s,i,e)||o===0&&vn(t,s,i)||a===0&&vn(t,e,i))}function vn(s,e,t){return e.x<=Math.max(s.x,t.x)&&e.x>=Math.min(s.x,t.x)&&e.y<=Math.max(s.y,t.y)&&e.y>=Math.min(s.y,t.y)}function kn(s){return s>0?1:s<0?-1:0}function Rp(s,e){var t=s;do{if(t.i!==s.i&&t.next.i!==s.i&&t.i!==e.i&&t.next.i!==e.i&&Mu(t,t.next,s,e))return!0;t=t.next}while(t!==s);return!1}function Js(s,e){return ce(s.prev,s,s.next)<0?ce(s,e,s.next)>=0&&ce(s,s.prev,e)>=0:ce(s,e,s.prev)<0||ce(s,s.next,e)<0}function Fp(s,e){var t=s,i=!1,n=(s.x+e.x)/2,r=(s.y+e.y)/2;do t.y>r!=t.next.y>r&&t.next.y!==t.y&&n<(t.next.x-t.x)*(r-t.y)/(t.next.y-t.y)+t.x&&(i=!i),t=t.next;while(t!==s);return i}function Tu(s,e){var t=new Ho(s.i,s.x,s.y),i=new Ho(e.i,e.x,e.y),n=s.next,r=e.prev;return s.next=e,e.prev=s,t.next=n,n.prev=t,i.next=t,t.prev=i,r.next=i,i.prev=r,i}function wl(s,e,t,i){var n=new Ho(s,e,t);return i?(n.next=i.next,n.prev=i,i.next.prev=n,i.next=n):(n.prev=n,n.next=n),n}function en(s){s.next.prev=s.prev,s.prev.next=s.next,s.prevZ&&(s.prevZ.nextZ=s.nextZ),s.nextZ&&(s.nextZ.prevZ=s.prevZ)}function Ho(s,e,t){this.i=s,this.x=e,this.y=t,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}nr.deviation=function(s,e,t,i){var n=e&&e.length,r=n?e[0]*t:s.length,o=Math.abs(qo(s,0,r,t));if(n)for(var a=0,l=e.length;a<l;a++){var h=e[a]*t,c=a<l-1?e[a+1]*t:s.length;o-=Math.abs(qo(s,h,c,t))}var u=0;for(a=0;a<i.length;a+=3){var d=i[a]*t,_=i[a+1]*t,f=i[a+2]*t;u+=Math.abs((s[d]-s[f])*(s[_+1]-s[d+1])-(s[d]-s[_])*(s[f+1]-s[d+1]))}return o===0&&u===0?0:Math.abs((u-o)/o)};function qo(s,e,t,i){for(var n=0,r=e,o=t-i;r<t;r+=i)n+=(s[o]-s[r])*(s[r+1]+s[o+1]),o=r;return n}nr.flatten=function(s){for(var e=s[0][0].length,t={vertices:[],holes:[],dimensions:e},i=0,n=0;n<s.length;n++){for(var r=0;r<s[n].length;r++)for(var o=0;o<e;o++)t.vertices.push(s[n][r][o]);n>0&&(i+=s[n-1].length,t.holes.push(i))}return t};var Bp=pa.exports;const Lp=ha(Bp);var Pu=(s=>(s[s.NONE=0]="NONE",s[s.COLOR=16384]="COLOR",s[s.STENCIL=1024]="STENCIL",s[s.DEPTH=256]="DEPTH",s[s.COLOR_DEPTH=16640]="COLOR_DEPTH",s[s.COLOR_STENCIL=17408]="COLOR_STENCIL",s[s.DEPTH_STENCIL=1280]="DEPTH_STENCIL",s[s.ALL=17664]="ALL",s))(Pu||{});class $p{constructor(e){this.items=[],this._name=e}emit(e,t,i,n,r,o,a,l){const{name:h,items:c}=this;for(let u=0,d=c.length;u<d;u++)c[u][h](e,t,i,n,r,o,a,l);return this}add(e){return e[this._name]&&(this.remove(e),this.items.push(e)),this}remove(e){const t=this.items.indexOf(e);return t!==-1&&this.items.splice(t,1),this}contains(e){return this.items.indexOf(e)!==-1}removeAll(){return this.items.length=0,this}destroy(){this.removeAll(),this.items=null,this._name=null}get empty(){return this.items.length===0}get name(){return this._name}}const Op=["init","destroy","contextChange","resolutionChange","resetState","renderEnd","renderStart","render","update","postrender","prerender"],zu=class Iu extends Tt{constructor(e){super(),this.runners=Object.create(null),this.renderPipes=Object.create(null),this._initOptions={},this._systemsHash=Object.create(null),this.type=e.type,this.name=e.name,this.config=e;const t=[...Op,...this.config.runners??[]];this._addRunners(...t),this._unsafeEvalCheck()}async init(e={}){const t=e.skipExtensionImports===!0?!0:e.manageImports===!1;await bp(t),this._addSystems(this.config.systems),this._addPipes(this.config.renderPipes,this.config.renderPipeAdaptors);for(const i in this._systemsHash)e={...this._systemsHash[i].constructor.defaultOptions,...e};e={...Iu.defaultOptions,...e},this._roundPixels=e.roundPixels?1:0;for(let i=0;i<this.runners.init.items.length;i++)await this.runners.init.items[i].init(e);this._initOptions=e}render(e,t){let i=e;if(i instanceof U&&(i={container:i},t&&(K(J,"passing a second argument is deprecated, please use render options instead"),i.target=t.renderTexture)),i.target||(i.target=this.view.renderTarget),i.target===this.view.renderTarget&&(this._lastObjectRendered=i.container,i.clearColor??(i.clearColor=this.background.colorRgba),i.clear??(i.clear=this.background.clearBeforeRender)),i.clearColor){const n=Array.isArray(i.clearColor)&&i.clearColor.length===4;i.clearColor=n?i.clearColor:fe.shared.setValue(i.clearColor).toArray()}i.transform||(i.container.updateLocalTransform(),i.transform=i.container.localTransform),i.container.enableRenderGroup(),this.runners.prerender.emit(i),this.runners.renderStart.emit(i),this.runners.render.emit(i),this.runners.renderEnd.emit(i),this.runners.postrender.emit(i)}resize(e,t,i){const n=this.view.resolution;this.view.resize(e,t,i),this.emit("resize",this.view.screen.width,this.view.screen.height,this.view.resolution),i!==void 0&&i!==n&&this.runners.resolutionChange.emit(i)}clear(e={}){const t=this;e.target||(e.target=t.renderTarget.renderTarget),e.clearColor||(e.clearColor=this.background.colorRgba),e.clear??(e.clear=Pu.ALL);const{clear:i,clearColor:n,target:r}=e;fe.shared.setValue(n??this.background.colorRgba),t.renderTarget.clear(r,i,fe.shared.toArray())}get resolution(){return this.view.resolution}set resolution(e){this.view.resolution=e,this.runners.resolutionChange.emit(e)}get width(){return this.view.texture.frame.width}get height(){return this.view.texture.frame.height}get canvas(){return this.view.canvas}get lastObjectRendered(){return this._lastObjectRendered}get renderingToScreen(){return this.renderTarget.renderingToScreen}get screen(){return this.view.screen}_addRunners(...e){e.forEach(t=>{this.runners[t]=new $p(t)})}_addSystems(e){let t;for(t in e){const i=e[t];this._addSystem(i.value,i.name)}}_addSystem(e,t){const i=new e(this);if(this[t])throw new Error(`Whoops! The name "${t}" is already in use`);this[t]=i,this._systemsHash[t]=i;for(const n in this.runners)this.runners[n].add(i);return this}_addPipes(e,t){const i=t.reduce((n,r)=>(n[r.name]=r.value,n),{});e.forEach(n=>{const r=n.value,o=n.name,a=i[o];this.renderPipes[o]=new r(this,a?new a:null)})}destroy(e=!1){this.runners.destroy.items.reverse(),this.runners.destroy.emit(e),Object.values(this.runners).forEach(t=>{t.destroy()}),this._systemsHash=null,this.renderPipes=null}generateTexture(e){return this.textureGenerator.generateTexture(e)}get roundPixels(){return!!this._roundPixels}_unsafeEvalCheck(){if(!yp())throw new Error("Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.")}resetState(){this.runners.resetState.emit()}};zu.defaultOptions={resolution:1,failIfMajorPerformanceCaveat:!1,roundPixels:!1};let Eu=zu,An;function Gp(s){return An!==void 0||(An=(()=>{const e={stencil:!0,failIfMajorPerformanceCaveat:s??Eu.defaultOptions.failIfMajorPerformanceCaveat};try{if(!_e.get().getWebGLRenderingContext())return!1;let i=_e.get().createCanvas().getContext("webgl",e);const n=!!i?.getContextAttributes()?.stencil;if(i){const r=i.getExtension("WEBGL_lose_context");r&&r.loseContext()}return i=null,n}catch{return!1}})()),An}let Sn;async function Dp(s={}){return Sn!==void 0||(Sn=await(async()=>{const e=_e.get().getNavigator().gpu;if(!e)return!1;try{return await(await e.requestAdapter(s)).requestDevice(),!0}catch{return!1}})()),Sn}const xl=["webgl","webgpu","canvas"];async function Np(s){let e=[];s.preference?(e.push(s.preference),xl.forEach(r=>{r!==s.preference&&e.push(r)})):e=xl.slice();let t,i={};for(let r=0;r<e.length;r++){const o=e[r];if(o==="webgpu"&&await Dp()){const{WebGPURenderer:a}=await Nn(()=>import("./WebGPURenderer-SBzZz9TH.js"),__vite__mapDeps([48,2,49,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46]),import.meta.url);t=a,i={...s,...s.webgpu};break}else if(o==="webgl"&&Gp(s.failIfMajorPerformanceCaveat??Eu.defaultOptions.failIfMajorPerformanceCaveat)){const{WebGLRenderer:a}=await Nn(()=>import("./WebGLRenderer-BjKGPx6T.js"),__vite__mapDeps([50,2,49,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46]),import.meta.url);t=a,i={...s,...s.webgl};break}else if(o==="canvas")throw i={...s},new Error("CanvasRenderer is not yet implemented")}if(delete i.webgpu,delete i.webgl,!t)throw new Error("No available renderer for the current environment");const n=new t;return await n.init(i),n}const Ru="8.7.3";class Fu{static init(){globalThis.__PIXI_APP_INIT__?.(this,Ru)}static destroy(){}}Fu.extension=D.Application;class Up{constructor(e){this._renderer=e}init(){globalThis.__PIXI_RENDERER_INIT__?.(this._renderer,Ru)}destroy(){this._renderer=null}}Up.extension={type:[D.WebGLSystem,D.WebGPUSystem],name:"initHook",priority:-10};const Bu=class Vo{constructor(...e){this.stage=new U,e[0]!==void 0&&K(J,"Application constructor options are deprecated, please use Application.init() instead.")}async init(e){e={...e},this.renderer=await Np(e),Vo._plugins.forEach(t=>{t.init.call(this,e)})}render(){this.renderer.render({container:this.stage})}get canvas(){return this.renderer.canvas}get view(){return K(J,"Application.view is deprecated, please use Application.canvas instead."),this.renderer.canvas}get screen(){return this.renderer.screen}destroy(e=!1,t=!1){const i=Vo._plugins.slice(0);i.reverse(),i.forEach(n=>{n.destroy.call(this)}),this.stage.destroy(t),this.stage=null,this.renderer.destroy(e),this.renderer=null}};Bu._plugins=[];let Lu=Bu;De.handleByList(D.Application,Lu._plugins);De.add(Fu);class $u extends Tt{constructor(){super(...arguments),this.chars=Object.create(null),this.lineHeight=0,this.fontFamily="",this.fontMetrics={fontSize:0,ascent:0,descent:0},this.baseLineOffset=0,this.distanceField={type:"none",range:0},this.pages=[],this.applyFillAsTint=!0,this.baseMeasurementFontSize=100,this.baseRenderedFontSize=100}get font(){return K(J,"BitmapFont.font is deprecated, please use BitmapFont.fontFamily instead."),this.fontFamily}get pageTextures(){return K(J,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}get size(){return K(J,"BitmapFont.size is deprecated, please use BitmapFont.fontMetrics.fontSize instead."),this.fontMetrics.fontSize}get distanceFieldRange(){return K(J,"BitmapFont.distanceFieldRange is deprecated, please use BitmapFont.distanceField.range instead."),this.distanceField.range}get distanceFieldType(){return K(J,"BitmapFont.distanceFieldType is deprecated, please use BitmapFont.distanceField.type instead."),this.distanceField.type}destroy(e=!1){this.emit("destroy",this),this.removeAllListeners();for(const t in this.chars)this.chars[t].texture?.destroy();this.chars=null,e&&(this.pages.forEach(t=>t.texture.destroy(!0)),this.pages=null)}}const Ou=class Xo{constructor(e,t,i,n){this.uid=pe("fillGradient"),this.type="linear",this.gradientStops=[],this._styleKey=null,this.x0=e,this.y0=t,this.x1=i,this.y1=n}addColorStop(e,t){return this.gradientStops.push({offset:e,color:fe.shared.setValue(t).toHexa()}),this._styleKey=null,this}buildLinearGradient(){if(this.texture)return;const e=Xo.defaultTextureSize,{gradientStops:t}=this,i=_e.get().createCanvas();i.width=e,i.height=e;const n=i.getContext("2d"),r=n.createLinearGradient(0,0,Xo.defaultTextureSize,1);for(let m=0;m<t.length;m++){const g=t[m];r.addColorStop(g.offset,g.color)}n.fillStyle=r,n.fillRect(0,0,e,e),this.texture=new Y({source:new ws({resource:i,addressModeU:"clamp-to-edge",addressModeV:"repeat"})});const{x0:o,y0:a,x1:l,y1:h}=this,c=new Z,u=l-o,d=h-a,_=Math.sqrt(u*u+d*d),f=Math.atan2(d,u);c.translate(-o,-a),c.scale(1/e,1/e),c.rotate(-f),c.scale(256/_,1),this.transform=c,this._styleKey=null}get styleKey(){if(this._styleKey)return this._styleKey;const e=this.gradientStops.map(n=>`${n.offset}-${n.color}`).join("-"),t=this.texture.uid,i=this.transform.toArray().join("-");return`fill-gradient-${this.uid}-${e}-${t}-${i}-${this.x0}-${this.y0}-${this.x1}-${this.y1}`}};Ou.defaultTextureSize=256;let tn=Ou;const vl={repeat:{addressModeU:"repeat",addressModeV:"repeat"},"repeat-x":{addressModeU:"repeat",addressModeV:"clamp-to-edge"},"repeat-y":{addressModeU:"clamp-to-edge",addressModeV:"repeat"},"no-repeat":{addressModeU:"clamp-to-edge",addressModeV:"clamp-to-edge"}};class or{constructor(e,t){this.uid=pe("fillPattern"),this.transform=new Z,this._styleKey=null,this.texture=e,this.transform.scale(1/e.frame.width,1/e.frame.height),t&&(e.source.style.addressModeU=vl[t].addressModeU,e.source.style.addressModeV=vl[t].addressModeV)}setTransform(e){const t=this.texture;this.transform.copyFrom(e),this.transform.invert(),this.transform.scale(1/t.frame.width,1/t.frame.height),this._styleKey=null}get styleKey(){return this._styleKey?this._styleKey:(this._styleKey=`fill-pattern-${this.uid}-${this.texture.uid}-${this.transform.toArray().join("-")}`,this._styleKey)}}var Wp=qp,lo={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},Hp=/([astvzqmhlc])([^astvzqmhlc]*)/ig;function qp(s){var e=[];return s.replace(Hp,function(t,i,n){var r=i.toLowerCase();for(n=Xp(n),r=="m"&&n.length>2&&(e.push([i].concat(n.splice(0,2))),r="l",i=i=="m"?"l":"L");;){if(n.length==lo[r])return n.unshift(i),e.push(n);if(n.length<lo[r])throw new Error("malformed path data");e.push([i].concat(n.splice(0,lo[r])))}}),e}var Vp=/-?[0-9]*\.?[0-9]+(?:e[-+]?\d+)?/ig;function Xp(s){var e=s.match(Vp);return e?e.map(Number):[]}const Yp=ha(Wp);function jp(s,e){const t=Yp(s),i=[];let n=null,r=0,o=0;for(let a=0;a<t.length;a++){const l=t[a],h=l[0],c=l;switch(h){case"M":r=c[1],o=c[2],e.moveTo(r,o);break;case"m":r+=c[1],o+=c[2],e.moveTo(r,o);break;case"H":r=c[1],e.lineTo(r,o);break;case"h":r+=c[1],e.lineTo(r,o);break;case"V":o=c[1],e.lineTo(r,o);break;case"v":o+=c[1],e.lineTo(r,o);break;case"L":r=c[1],o=c[2],e.lineTo(r,o);break;case"l":r+=c[1],o+=c[2],e.lineTo(r,o);break;case"C":r=c[5],o=c[6],e.bezierCurveTo(c[1],c[2],c[3],c[4],r,o);break;case"c":e.bezierCurveTo(r+c[1],o+c[2],r+c[3],o+c[4],r+c[5],o+c[6]),r+=c[5],o+=c[6];break;case"S":r=c[3],o=c[4],e.bezierCurveToShort(c[1],c[2],r,o);break;case"s":e.bezierCurveToShort(r+c[1],o+c[2],r+c[3],o+c[4]),r+=c[3],o+=c[4];break;case"Q":r=c[3],o=c[4],e.quadraticCurveTo(c[1],c[2],r,o);break;case"q":e.quadraticCurveTo(r+c[1],o+c[2],r+c[3],o+c[4]),r+=c[3],o+=c[4];break;case"T":r=c[1],o=c[2],e.quadraticCurveToShort(r,o);break;case"t":r+=c[1],o+=c[2],e.quadraticCurveToShort(r,o);break;case"A":r=c[6],o=c[7],e.arcToSvg(c[1],c[2],c[3],c[4],c[5],r,o);break;case"a":r+=c[6],o+=c[7],e.arcToSvg(c[1],c[2],c[3],c[4],c[5],r,o);break;case"Z":case"z":e.closePath(),i.length>0&&(n=i.pop(),n?(r=n.startX,o=n.startY):(r=0,o=0)),n=null;break;default:be(`Unknown SVG path command: ${h}`)}h!=="Z"&&h!=="z"&&n===null&&(n={startX:r,startY:o},i.push(n))}return e}class ba{constructor(e=0,t=0,i=0){this.type="circle",this.x=e,this.y=t,this.radius=i}clone(){return new ba(this.x,this.y,this.radius)}contains(e,t){if(this.radius<=0)return!1;const i=this.radius*this.radius;let n=this.x-e,r=this.y-t;return n*=n,r*=r,n+r<=i}strokeContains(e,t,i,n=.5){if(this.radius===0)return!1;const r=this.x-e,o=this.y-t,a=this.radius,l=(1-n)*i,h=Math.sqrt(r*r+o*o);return h<=a+l&&h>a-(i-l)}getBounds(e){return e||(e=new se),e.x=this.x-this.radius,e.y=this.y-this.radius,e.width=this.radius*2,e.height=this.radius*2,e}copyFrom(e){return this.x=e.x,this.y=e.y,this.radius=e.radius,this}copyTo(e){return e.copyFrom(this),e}toString(){return`[pixi.js/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`}}class ya{constructor(e=0,t=0,i=0,n=0){this.type="ellipse",this.x=e,this.y=t,this.halfWidth=i,this.halfHeight=n}clone(){return new ya(this.x,this.y,this.halfWidth,this.halfHeight)}contains(e,t){if(this.halfWidth<=0||this.halfHeight<=0)return!1;let i=(e-this.x)/this.halfWidth,n=(t-this.y)/this.halfHeight;return i*=i,n*=n,i+n<=1}strokeContains(e,t,i,n=.5){const{halfWidth:r,halfHeight:o}=this;if(r<=0||o<=0)return!1;const a=i*(1-n),l=i-a,h=r-l,c=o-l,u=r+a,d=o+a,_=e-this.x,f=t-this.y,m=_*_/(h*h)+f*f/(c*c),g=_*_/(u*u)+f*f/(d*d);return m>1&&g<=1}getBounds(e){return e||(e=new se),e.x=this.x-this.halfWidth,e.y=this.y-this.halfHeight,e.width=this.halfWidth*2,e.height=this.halfHeight*2,e}copyFrom(e){return this.x=e.x,this.y=e.y,this.halfWidth=e.halfWidth,this.halfHeight=e.halfHeight,this}copyTo(e){return e.copyFrom(this),e}toString(){return`[pixi.js/math:Ellipse x=${this.x} y=${this.y} halfWidth=${this.halfWidth} halfHeight=${this.halfHeight}]`}}function Kp(s,e,t,i,n,r){const o=s-t,a=e-i,l=n-t,h=r-i,c=o*l+a*h,u=l*l+h*h;let d=-1;u!==0&&(d=c/u);let _,f;d<0?(_=t,f=i):d>1?(_=n,f=r):(_=t+d*l,f=i+d*h);const m=s-_,g=e-f;return m*m+g*g}class Vs{constructor(...e){this.type="polygon";let t=Array.isArray(e[0])?e[0]:e;if(typeof t[0]!="number"){const i=[];for(let n=0,r=t.length;n<r;n++)i.push(t[n].x,t[n].y);t=i}this.points=t,this.closePath=!0}clone(){const e=this.points.slice(),t=new Vs(e);return t.closePath=this.closePath,t}contains(e,t){let i=!1;const n=this.points.length/2;for(let r=0,o=n-1;r<n;o=r++){const a=this.points[r*2],l=this.points[r*2+1],h=this.points[o*2],c=this.points[o*2+1];l>t!=c>t&&e<(h-a)*((t-l)/(c-l))+a&&(i=!i)}return i}strokeContains(e,t,i,n=.5){const r=i*i,o=r*(1-n),a=r-o,{points:l}=this,h=l.length-(this.closePath?0:2);for(let c=0;c<h;c+=2){const u=l[c],d=l[c+1],_=l[(c+2)%l.length],f=l[(c+3)%l.length],m=Kp(e,t,u,d,_,f),g=Math.sign((_-u)*(t-d)-(f-d)*(e-u));if(m<=(g<0?a:o))return!0}return!1}getBounds(e){e||(e=new se);const t=this.points;let i=1/0,n=-1/0,r=1/0,o=-1/0;for(let a=0,l=t.length;a<l;a+=2){const h=t[a],c=t[a+1];i=h<i?h:i,n=h>n?h:n,r=c<r?c:r,o=c>o?c:o}return e.x=i,e.width=n-i,e.y=r,e.height=o-r,e}copyFrom(e){return this.points=e.points.slice(),this.closePath=e.closePath,this}copyTo(e){return e.copyFrom(this),e}toString(){return`[pixi.js/math:PolygoncloseStroke=${this.closePath}points=${this.points.reduce((e,t)=>`${e}, ${t}`,"")}]`}get lastX(){return this.points[this.points.length-2]}get lastY(){return this.points[this.points.length-1]}get x(){return this.points[this.points.length-2]}get y(){return this.points[this.points.length-1]}}const Cn=(s,e,t,i,n,r,o)=>{const a=s-t,l=e-i,h=Math.sqrt(a*a+l*l);return h>=n-r&&h<=n+o};class wa{constructor(e=0,t=0,i=0,n=0,r=20){this.type="roundedRectangle",this.x=e,this.y=t,this.width=i,this.height=n,this.radius=r}getBounds(e){return e||(e=new se),e.x=this.x,e.y=this.y,e.width=this.width,e.height=this.height,e}clone(){return new wa(this.x,this.y,this.width,this.height,this.radius)}copyFrom(e){return this.x=e.x,this.y=e.y,this.width=e.width,this.height=e.height,this}copyTo(e){return e.copyFrom(this),e}contains(e,t){if(this.width<=0||this.height<=0)return!1;if(e>=this.x&&e<=this.x+this.width&&t>=this.y&&t<=this.y+this.height){const i=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(t>=this.y+i&&t<=this.y+this.height-i||e>=this.x+i&&e<=this.x+this.width-i)return!0;let n=e-(this.x+i),r=t-(this.y+i);const o=i*i;if(n*n+r*r<=o||(n=e-(this.x+this.width-i),n*n+r*r<=o)||(r=t-(this.y+this.height-i),n*n+r*r<=o)||(n=e-(this.x+i),n*n+r*r<=o))return!0}return!1}strokeContains(e,t,i,n=.5){const{x:r,y:o,width:a,height:l,radius:h}=this,c=i*(1-n),u=i-c,d=r+h,_=o+h,f=a-h*2,m=l-h*2,g=r+a,p=o+l;return(e>=r-c&&e<=r+u||e>=g-u&&e<=g+c)&&t>=_&&t<=_+m||(t>=o-c&&t<=o+u||t>=p-u&&t<=p+c)&&e>=d&&e<=d+f?!0:e<d&&t<_&&Cn(e,t,d,_,h,u,c)||e>g-h&&t<_&&Cn(e,t,g-h,_,h,u,c)||e>g-h&&t>p-h&&Cn(e,t,g-h,p-h,h,u,c)||e<d&&t>p-h&&Cn(e,t,d,p-h,h,u,c)}toString(){return`[pixi.js/math:RoundedRectangle x=${this.x} y=${this.y}width=${this.width} height=${this.height} radius=${this.radius}]`}}const Zp=["precision mediump float;","void main(void){","float test = 0.1;","%forloop%","gl_FragColor = vec4(0.0);","}"].join(`
`);function Qp(s){let e="";for(let t=0;t<s;++t)t>0&&(e+=`
else `),t<s-1&&(e+=`if(test == ${t}.0){}`);return e}function Jp(s,e){if(s===0)throw new Error("Invalid value of `0` passed to `checkMaxIfStatementsInShader`");const t=e.createShader(e.FRAGMENT_SHADER);try{for(;;){const i=Zp.replace(/%forloop%/gi,Qp(s));if(e.shaderSource(t,i),e.compileShader(t),!e.getShaderParameter(t,e.COMPILE_STATUS))s=s/2|0;else break}}finally{e.deleteShader(t)}return s}let Ki=null;function Gu(){if(Ki)return Ki;const s=bu();return Ki=s.getParameter(s.MAX_TEXTURE_IMAGE_UNITS),Ki=Jp(Ki,s),s.getExtension("WEBGL_lose_context")?.loseContext(),Ki}const Du={};function eb(s,e){let t=2166136261;for(let i=0;i<e;i++)t^=s[i].uid,t=Math.imul(t,16777619),t>>>=0;return Du[t]||tb(s,e,t)}let ho=0;function tb(s,e,t){const i={};let n=0;ho||(ho=Gu());for(let o=0;o<ho;o++){const a=o<e?s[o]:Y.EMPTY.source;i[n++]=a.source,i[n++]=a.style}const r=new Fn(i);return Du[t]=r,r}class kl{constructor(e){typeof e=="number"?this.rawBinaryData=new ArrayBuffer(e):e instanceof Uint8Array?this.rawBinaryData=e.buffer:this.rawBinaryData=e,this.uint32View=new Uint32Array(this.rawBinaryData),this.float32View=new Float32Array(this.rawBinaryData),this.size=this.rawBinaryData.byteLength}get int8View(){return this._int8View||(this._int8View=new Int8Array(this.rawBinaryData)),this._int8View}get uint8View(){return this._uint8View||(this._uint8View=new Uint8Array(this.rawBinaryData)),this._uint8View}get int16View(){return this._int16View||(this._int16View=new Int16Array(this.rawBinaryData)),this._int16View}get int32View(){return this._int32View||(this._int32View=new Int32Array(this.rawBinaryData)),this._int32View}get float64View(){return this._float64Array||(this._float64Array=new Float64Array(this.rawBinaryData)),this._float64Array}get bigUint64View(){return this._bigUint64Array||(this._bigUint64Array=new BigUint64Array(this.rawBinaryData)),this._bigUint64Array}view(e){return this[`${e}View`]}destroy(){this.rawBinaryData=null,this._int8View=null,this._uint8View=null,this._int16View=null,this.uint16View=null,this._int32View=null,this.uint32View=null,this.float32View=null}static sizeOf(e){switch(e){case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;default:throw new Error(`${e} isn't a valid view type`)}}}function Al(s,e){const t=s.byteLength/8|0,i=new Float64Array(s,0,t);new Float64Array(e,0,t).set(i);const r=s.byteLength-t*8;if(r>0){const o=new Uint8Array(s,t*8,r);new Uint8Array(e,t*8,r).set(o)}}const ib={normal:"normal-npm",add:"add-npm",screen:"screen-npm"};var sb=(s=>(s[s.DISABLED=0]="DISABLED",s[s.RENDERING_MASK_ADD=1]="RENDERING_MASK_ADD",s[s.MASK_ACTIVE=2]="MASK_ACTIVE",s[s.INVERSE_MASK_ACTIVE=3]="INVERSE_MASK_ACTIVE",s[s.RENDERING_MASK_REMOVE=4]="RENDERING_MASK_REMOVE",s[s.NONE=5]="NONE",s))(sb||{});function Sl(s,e){return e.alphaMode==="no-premultiply-alpha"&&ib[s]||s}class nb{constructor(){this.ids=Object.create(null),this.textures=[],this.count=0}clear(){for(let e=0;e<this.count;e++){const t=this.textures[e];this.textures[e]=null,this.ids[t.uid]=null}this.count=0}}class rb{constructor(){this.renderPipeId="batch",this.action="startBatch",this.start=0,this.size=0,this.textures=new nb,this.blendMode="normal",this.topology="triangle-strip",this.canBundle=!0}destroy(){this.textures=null,this.gpuBindGroup=null,this.bindGroup=null,this.batcher=null}}const Nu=[];let Yo=0;function Cl(){return Yo>0?Nu[--Yo]:new rb}function Ml(s){Nu[Yo++]=s}let zs=0;const Uu=class Bn{constructor(e={}){this.uid=pe("batcher"),this.dirty=!0,this.batchIndex=0,this.batches=[],this._elements=[],Bn.defaultOptions.maxTextures=Bn.defaultOptions.maxTextures??Gu(),e={...Bn.defaultOptions,...e};const{maxTextures:t,attributesInitialSize:i,indicesInitialSize:n}=e;this.attributeBuffer=new kl(i*4),this.indexBuffer=new Uint16Array(n),this.maxTextures=t}begin(){this.elementSize=0,this.elementStart=0,this.indexSize=0,this.attributeSize=0;for(let e=0;e<this.batchIndex;e++)Ml(this.batches[e]);this.batchIndex=0,this._batchIndexStart=0,this._batchIndexSize=0,this.dirty=!0}add(e){this._elements[this.elementSize++]=e,e._indexStart=this.indexSize,e._attributeStart=this.attributeSize,e._batcher=this,this.indexSize+=e.indexSize,this.attributeSize+=e.attributeSize*this.vertexSize}checkAndUpdateTexture(e,t){const i=e._batch.textures.ids[t._source.uid];return!i&&i!==0?!1:(e._textureId=i,e.texture=t,!0)}updateElement(e){this.dirty=!0;const t=this.attributeBuffer;e.packAsQuad?this.packQuadAttributes(e,t.float32View,t.uint32View,e._attributeStart,e._textureId):this.packAttributes(e,t.float32View,t.uint32View,e._attributeStart,e._textureId)}break(e){const t=this._elements;if(!t[this.elementStart])return;let i=Cl(),n=i.textures;n.clear();const r=t[this.elementStart];let o=Sl(r.blendMode,r.texture._source),a=r.topology;this.attributeSize*4>this.attributeBuffer.size&&this._resizeAttributeBuffer(this.attributeSize*4),this.indexSize>this.indexBuffer.length&&this._resizeIndexBuffer(this.indexSize);const l=this.attributeBuffer.float32View,h=this.attributeBuffer.uint32View,c=this.indexBuffer;let u=this._batchIndexSize,d=this._batchIndexStart,_="startBatch";const f=this.maxTextures;for(let m=this.elementStart;m<this.elementSize;++m){const g=t[m];t[m]=null;const y=g.texture._source,b=Sl(g.blendMode,y),w=o!==b||a!==g.topology;if(y._batchTick===zs&&!w){g._textureId=y._textureBindLocation,u+=g.indexSize,g.packAsQuad?(this.packQuadAttributes(g,l,h,g._attributeStart,g._textureId),this.packQuadIndex(c,g._indexStart,g._attributeStart/this.vertexSize)):(this.packAttributes(g,l,h,g._attributeStart,g._textureId),this.packIndex(g,c,g._indexStart,g._attributeStart/this.vertexSize)),g._batch=i;continue}y._batchTick=zs,(n.count>=f||w)&&(this._finishBatch(i,d,u-d,n,o,a,e,_),_="renderBatch",d=u,o=b,a=g.topology,i=Cl(),n=i.textures,n.clear(),++zs),g._textureId=y._textureBindLocation=n.count,n.ids[y.uid]=n.count,n.textures[n.count++]=y,g._batch=i,u+=g.indexSize,g.packAsQuad?(this.packQuadAttributes(g,l,h,g._attributeStart,g._textureId),this.packQuadIndex(c,g._indexStart,g._attributeStart/this.vertexSize)):(this.packAttributes(g,l,h,g._attributeStart,g._textureId),this.packIndex(g,c,g._indexStart,g._attributeStart/this.vertexSize))}n.count>0&&(this._finishBatch(i,d,u-d,n,o,a,e,_),d=u,++zs),this.elementStart=this.elementSize,this._batchIndexStart=d,this._batchIndexSize=u}_finishBatch(e,t,i,n,r,o,a,l){e.gpuBindGroup=null,e.bindGroup=null,e.action=l,e.batcher=this,e.textures=n,e.blendMode=r,e.topology=o,e.start=t,e.size=i,++zs,this.batches[this.batchIndex++]=e,a.add(e)}finish(e){this.break(e)}ensureAttributeBuffer(e){e*4<=this.attributeBuffer.size||this._resizeAttributeBuffer(e*4)}ensureIndexBuffer(e){e<=this.indexBuffer.length||this._resizeIndexBuffer(e)}_resizeAttributeBuffer(e){const t=Math.max(e,this.attributeBuffer.size*2),i=new kl(t);Al(this.attributeBuffer.rawBinaryData,i.rawBinaryData),this.attributeBuffer=i}_resizeIndexBuffer(e){const t=this.indexBuffer;let i=Math.max(e,t.length*1.5);i+=i%2;const n=i>65535?new Uint32Array(i):new Uint16Array(i);if(n.BYTES_PER_ELEMENT!==t.BYTES_PER_ELEMENT)for(let r=0;r<t.length;r++)n[r]=t[r];else Al(t.buffer,n.buffer);this.indexBuffer=n}packQuadIndex(e,t,i){e[t]=i+0,e[t+1]=i+1,e[t+2]=i+2,e[t+3]=i+0,e[t+4]=i+2,e[t+5]=i+3}packIndex(e,t,i,n){const r=e.indices,o=e.indexSize,a=e.indexOffset,l=e.attributeOffset;for(let h=0;h<o;h++)t[i++]=n+r[h+a]-l}destroy(){for(let e=0;e<this.batches.length;e++)Ml(this.batches[e]);this.batches=null;for(let e=0;e<this._elements.length;e++)this._elements[e]._batch=null;this._elements=null,this.indexBuffer=null,this.attributeBuffer.destroy(),this.attributeBuffer=null}};Uu.defaultOptions={maxTextures:null,attributesInitialSize:4,indicesInitialSize:6};let ob=Uu;var Xe=(s=>(s[s.MAP_READ=1]="MAP_READ",s[s.MAP_WRITE=2]="MAP_WRITE",s[s.COPY_SRC=4]="COPY_SRC",s[s.COPY_DST=8]="COPY_DST",s[s.INDEX=16]="INDEX",s[s.VERTEX=32]="VERTEX",s[s.UNIFORM=64]="UNIFORM",s[s.STORAGE=128]="STORAGE",s[s.INDIRECT=256]="INDIRECT",s[s.QUERY_RESOLVE=512]="QUERY_RESOLVE",s[s.STATIC=1024]="STATIC",s))(Xe||{});class sn extends Tt{constructor(e){let{data:t,size:i}=e;const{usage:n,label:r,shrinkToFit:o}=e;super(),this.uid=pe("buffer"),this._resourceType="buffer",this._resourceId=pe("resource"),this._touched=0,this._updateID=1,this._dataInt32=null,this.shrinkToFit=!0,this.destroyed=!1,t instanceof Array&&(t=new Float32Array(t)),this._data=t,i??(i=t?.byteLength);const a=!!t;this.descriptor={size:i,usage:n,mappedAtCreation:a,label:r},this.shrinkToFit=o??!0}get data(){return this._data}set data(e){this.setDataWithSize(e,e.length,!0)}get dataInt32(){return this._dataInt32||(this._dataInt32=new Int32Array(this.data.buffer)),this._dataInt32}get static(){return!!(this.descriptor.usage&Xe.STATIC)}set static(e){e?this.descriptor.usage|=Xe.STATIC:this.descriptor.usage&=~Xe.STATIC}setDataWithSize(e,t,i){if(this._updateID++,this._updateSize=t*e.BYTES_PER_ELEMENT,this._data===e){i&&this.emit("update",this);return}const n=this._data;if(this._data=e,this._dataInt32=null,!n||n.length!==e.length){!this.shrinkToFit&&n&&e.byteLength<n.byteLength?i&&this.emit("update",this):(this.descriptor.size=e.byteLength,this._resourceId=pe("resource"),this.emit("change",this));return}i&&this.emit("update",this)}update(e){this._updateSize=e??this._updateSize,this._updateID++,this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._data=null,this.descriptor=null,this.removeAllListeners()}}function Wu(s,e){if(!(s instanceof sn)){let t=e?Xe.INDEX:Xe.VERTEX;s instanceof Array&&(e?(s=new Uint32Array(s),t=Xe.INDEX|Xe.COPY_DST):(s=new Float32Array(s),t=Xe.VERTEX|Xe.COPY_DST)),s=new sn({data:s,label:e?"index-mesh-buffer":"vertex-mesh-buffer",usage:t})}return s}function ab(s,e,t){const i=s.getAttribute(e);if(!i)return t.minX=0,t.minY=0,t.maxX=0,t.maxY=0,t;const n=i.buffer.data;let r=1/0,o=1/0,a=-1/0,l=-1/0;const h=n.BYTES_PER_ELEMENT,c=(i.offset||0)/h,u=(i.stride||2*4)/h;for(let d=c;d<n.length;d+=u){const _=n[d],f=n[d+1];_>a&&(a=_),f>l&&(l=f),_<r&&(r=_),f<o&&(o=f)}return t.minX=r,t.minY=o,t.maxX=a,t.maxY=l,t}function lb(s){return(s instanceof sn||Array.isArray(s)||s.BYTES_PER_ELEMENT)&&(s={buffer:s}),s.buffer=Wu(s.buffer,!1),s}class hb extends Tt{constructor(e={}){super(),this.uid=pe("geometry"),this._layoutKey=0,this.instanceCount=1,this._bounds=new St,this._boundsDirty=!0;const{attributes:t,indexBuffer:i,topology:n}=e;if(this.buffers=[],this.attributes={},t)for(const r in t)this.addAttribute(r,t[r]);this.instanceCount=e.instanceCount??1,i&&this.addIndex(i),this.topology=n||"triangle-list"}onBufferUpdate(){this._boundsDirty=!0,this.emit("update",this)}getAttribute(e){return this.attributes[e]}getIndex(){return this.indexBuffer}getBuffer(e){return this.getAttribute(e).buffer}getSize(){for(const e in this.attributes){const t=this.attributes[e];return t.buffer.data.length/(t.stride/4||t.size)}return 0}addAttribute(e,t){const i=lb(t);this.buffers.indexOf(i.buffer)===-1&&(this.buffers.push(i.buffer),i.buffer.on("update",this.onBufferUpdate,this),i.buffer.on("change",this.onBufferUpdate,this)),this.attributes[e]=i}addIndex(e){this.indexBuffer=Wu(e,!0),this.buffers.push(this.indexBuffer)}get bounds(){return this._boundsDirty?(this._boundsDirty=!1,ab(this,"aPosition",this._bounds)):this._bounds}destroy(e=!1){this.emit("destroy",this),this.removeAllListeners(),e&&this.buffers.forEach(t=>t.destroy()),this.attributes=null,this.buffers=null,this.indexBuffer=null,this._bounds=null}}const cb=new Float32Array(1),ub=new Uint32Array(1);class db extends hb{constructor(){const t=new sn({data:cb,label:"attribute-batch-buffer",usage:Xe.VERTEX|Xe.COPY_DST,shrinkToFit:!1}),i=new sn({data:ub,label:"index-batch-buffer",usage:Xe.INDEX|Xe.COPY_DST,shrinkToFit:!1}),n=6*4;super({attributes:{aPosition:{buffer:t,format:"float32x2",stride:n,offset:0},aUV:{buffer:t,format:"float32x2",stride:n,offset:2*4},aColor:{buffer:t,format:"unorm8x4",stride:n,offset:4*4},aTextureIdAndRound:{buffer:t,format:"uint16x2",stride:n,offset:5*4}},indexBuffer:i})}}function Tl(s,e,t){if(s)for(const i in s){const n=i.toLocaleLowerCase(),r=e[n];if(r){let o=s[i];i==="header"&&(o=o.replace(/@in\s+[^;]+;\s*/g,"").replace(/@out\s+[^;]+;\s*/g,"")),t&&r.push(`//----${t}----//`),r.push(o)}else be(`${i} placement hook does not exist in shader`)}}const _b=/\{\{(.*?)\}\}/g;function Pl(s){const e={};return(s.match(_b)?.map(i=>i.replace(/[{()}]/g,""))??[]).forEach(i=>{e[i]=[]}),e}function zl(s,e){let t;const i=/@in\s+([^;]+);/g;for(;(t=i.exec(s))!==null;)e.push(t[1])}function Il(s,e,t=!1){const i=[];zl(e,i),s.forEach(a=>{a.header&&zl(a.header,i)});const n=i;t&&n.sort();const r=n.map((a,l)=>`       @location(${l}) ${a},`).join(`
`);let o=e.replace(/@in\s+[^;]+;\s*/g,"");return o=o.replace("{{in}}",`
${r}
`),o}function El(s,e){let t;const i=/@out\s+([^;]+);/g;for(;(t=i.exec(s))!==null;)e.push(t[1])}function fb(s){const t=/\b(\w+)\s*:/g.exec(s);return t?t[1]:""}function gb(s){const e=/@.*?\s+/g;return s.replace(e,"")}function mb(s,e){const t=[];El(e,t),s.forEach(l=>{l.header&&El(l.header,t)});let i=0;const n=t.sort().map(l=>l.indexOf("builtin")>-1?l:`@location(${i++}) ${l}`).join(`,
`),r=t.sort().map(l=>`       var ${gb(l)};`).join(`
`),o=`return VSOutput(
            ${t.sort().map(l=>` ${fb(l)}`).join(`,
`)});`;let a=e.replace(/@out\s+[^;]+;\s*/g,"");return a=a.replace("{{struct}}",`
${n}
`),a=a.replace("{{start}}",`
${r}
`),a=a.replace("{{return}}",`
${o}
`),a}function Rl(s,e){let t=s;for(const i in e){const n=e[i];n.join(`
`).length?t=t.replace(`{{${i}}}`,`//-----${i} START-----//
${n.join(`
`)}
//----${i} FINISH----//`):t=t.replace(`{{${i}}}`,"")}return t}const li=Object.create(null),co=new Map;let pb=0;function bb({template:s,bits:e}){const t=Hu(s,e);if(li[t])return li[t];const{vertex:i,fragment:n}=wb(s,e);return li[t]=qu(i,n,e),li[t]}function yb({template:s,bits:e}){const t=Hu(s,e);return li[t]||(li[t]=qu(s.vertex,s.fragment,e)),li[t]}function wb(s,e){const t=e.map(o=>o.vertex).filter(o=>!!o),i=e.map(o=>o.fragment).filter(o=>!!o);let n=Il(t,s.vertex,!0);n=mb(t,n);const r=Il(i,s.fragment,!0);return{vertex:n,fragment:r}}function Hu(s,e){return e.map(t=>(co.has(t)||co.set(t,pb++),co.get(t))).sort((t,i)=>t-i).join("-")+s.vertex+s.fragment}function qu(s,e,t){const i=Pl(s),n=Pl(e);return t.forEach(r=>{Tl(r.vertex,i,r.name),Tl(r.fragment,n,r.name)}),{vertex:Rl(s,i),fragment:Rl(e,n)}}const xb=`
    @in aPosition: vec2<f32>;
    @in aUV: vec2<f32>;

    @out @builtin(position) vPosition: vec4<f32>;
    @out vUV : vec2<f32>;
    @out vColor : vec4<f32>;

    {{header}}

    struct VSOutput {
        {{struct}}
    };

    @vertex
    fn main( {{in}} ) -> VSOutput {

        var worldTransformMatrix = globalUniforms.uWorldTransformMatrix;
        var modelMatrix = mat3x3<f32>(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        var position = aPosition;
        var uv = aUV;

        {{start}}
        
        vColor = vec4<f32>(1., 1., 1., 1.);

        {{main}}

        vUV = uv;

        var modelViewProjectionMatrix = globalUniforms.uProjectionMatrix * worldTransformMatrix * modelMatrix;

        vPosition =  vec4<f32>((modelViewProjectionMatrix *  vec3<f32>(position, 1.0)).xy, 0.0, 1.0);
       
        vColor *= globalUniforms.uWorldColorAlpha;

        {{end}}

        {{return}}
    };
`,vb=`
    @in vUV : vec2<f32>;
    @in vColor : vec4<f32>;
   
    {{header}}

    @fragment
    fn main(
        {{in}}
      ) -> @location(0) vec4<f32> {
        
        {{start}}

        var outColor:vec4<f32>;
      
        {{main}}
        
        var finalColor:vec4<f32> = outColor * vColor;

        {{end}}

        return finalColor;
      };
`,kb=`
    in vec2 aPosition;
    in vec2 aUV;

    out vec4 vColor;
    out vec2 vUV;

    {{header}}

    void main(void){

        mat3 worldTransformMatrix = uWorldTransformMatrix;
        mat3 modelMatrix = mat3(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        vec2 position = aPosition;
        vec2 uv = aUV;
        
        {{start}}
        
        vColor = vec4(1.);
        
        {{main}}
        
        vUV = uv;
        
        mat3 modelViewProjectionMatrix = uProjectionMatrix * worldTransformMatrix * modelMatrix;

        gl_Position = vec4((modelViewProjectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);

        vColor *= uWorldColorAlpha;

        {{end}}
    }
`,Ab=`
   
    in vec4 vColor;
    in vec2 vUV;

    out vec4 finalColor;

    {{header}}

    void main(void) {
        
        {{start}}

        vec4 outColor;
      
        {{main}}
        
        finalColor = outColor * vColor;
        
        {{end}}
    }
`,Sb={name:"global-uniforms-bit",vertex:{header:`
        struct GlobalUniforms {
            uProjectionMatrix:mat3x3<f32>,
            uWorldTransformMatrix:mat3x3<f32>,
            uWorldColorAlpha: vec4<f32>,
            uResolution: vec2<f32>,
        }

        @group(0) @binding(0) var<uniform> globalUniforms : GlobalUniforms;
        `}},Cb={name:"global-uniforms-bit",vertex:{header:`
          uniform mat3 uProjectionMatrix;
          uniform mat3 uWorldTransformMatrix;
          uniform vec4 uWorldColorAlpha;
          uniform vec2 uResolution;
        `}};function Mb({bits:s,name:e}){const t=bb({template:{fragment:vb,vertex:xb},bits:[Sb,...s]});return mi.from({name:e,vertex:{source:t.vertex,entryPoint:"main"},fragment:{source:t.fragment,entryPoint:"main"}})}function Tb({bits:s,name:e}){return new _s({name:e,...yb({template:{vertex:kb,fragment:Ab},bits:[Cb,...s]})})}const Pb={name:"color-bit",vertex:{header:`
            @in aColor: vec4<f32>;
        `,main:`
            vColor *= vec4<f32>(aColor.rgb * aColor.a, aColor.a);
        `}},zb={name:"color-bit",vertex:{header:`
            in vec4 aColor;
        `,main:`
            vColor *= vec4(aColor.rgb * aColor.a, aColor.a);
        `}},uo={};function Ib(s){const e=[];if(s===1)e.push("@group(1) @binding(0) var textureSource1: texture_2d<f32>;"),e.push("@group(1) @binding(1) var textureSampler1: sampler;");else{let t=0;for(let i=0;i<s;i++)e.push(`@group(1) @binding(${t++}) var textureSource${i+1}: texture_2d<f32>;`),e.push(`@group(1) @binding(${t++}) var textureSampler${i+1}: sampler;`)}return e.join(`
`)}function Eb(s){const e=[];if(s===1)e.push("outColor = textureSampleGrad(textureSource1, textureSampler1, vUV, uvDx, uvDy);");else{e.push("switch vTextureId {");for(let t=0;t<s;t++)t===s-1?e.push("  default:{"):e.push(`  case ${t}:{`),e.push(`      outColor = textureSampleGrad(textureSource${t+1}, textureSampler${t+1}, vUV, uvDx, uvDy);`),e.push("      break;}");e.push("}")}return e.join(`
`)}function Rb(s){return uo[s]||(uo[s]={name:"texture-batch-bit",vertex:{header:`
                @in aTextureIdAndRound: vec2<u32>;
                @out @interpolate(flat) vTextureId : u32;
            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1)
                {
                    vPosition = vec4<f32>(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
                }
            `},fragment:{header:`
                @in @interpolate(flat) vTextureId: u32;

                ${Ib(s)}
            `,main:`
                var uvDx = dpdx(vUV);
                var uvDy = dpdy(vUV);

                ${Eb(s)}
            `}}),uo[s]}const _o={};function Fb(s){const e=[];for(let t=0;t<s;t++)t>0&&e.push("else"),t<s-1&&e.push(`if(vTextureId < ${t}.5)`),e.push("{"),e.push(`	outColor = texture(uTextures[${t}], vUV);`),e.push("}");return e.join(`
`)}function Bb(s){return _o[s]||(_o[s]={name:"texture-batch-bit",vertex:{header:`
                in vec2 aTextureIdAndRound;
                out float vTextureId;

            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1.)
                {
                    gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
                }
            `},fragment:{header:`
                in float vTextureId;

                uniform sampler2D uTextures[${s}];

            `,main:`

                ${Fb(s)}
            `}}),_o[s]}const Lb={name:"round-pixels-bit",vertex:{header:`
            fn roundPixels(position: vec2<f32>, targetSize: vec2<f32>) -> vec2<f32> 
            {
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},$b={name:"round-pixels-bit",vertex:{header:`   
            vec2 roundPixels(vec2 position, vec2 targetSize)
            {       
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},Fl={};function Ob(s){let e=Fl[s];if(e)return e;const t=new Int32Array(s);for(let i=0;i<s;i++)t[i]=i;return e=Fl[s]=new ku({uTextures:{value:t,type:"i32",size:s}},{isStatic:!0}),e}class Gb extends sr{constructor(e){const t=Tb({name:"batch",bits:[zb,Bb(e),$b]}),i=Mb({name:"batch",bits:[Pb,Rb(e),Lb]});super({glProgram:t,gpuProgram:i,resources:{batchSamplers:Ob(e)}})}}let Bl=null;const Vu=class Xu extends ob{constructor(){super(...arguments),this.geometry=new db,this.shader=Bl||(Bl=new Gb(this.maxTextures)),this.name=Xu.extension.name,this.vertexSize=6}packAttributes(e,t,i,n,r){const o=r<<16|e.roundPixels&65535,a=e.transform,l=a.a,h=a.b,c=a.c,u=a.d,d=a.tx,_=a.ty,{positions:f,uvs:m}=e,g=e.color,p=e.attributeOffset,y=p+e.attributeSize;for(let b=p;b<y;b++){const w=b*2,A=f[w],v=f[w+1];t[n++]=l*A+c*v+d,t[n++]=u*v+h*A+_,t[n++]=m[w],t[n++]=m[w+1],i[n++]=g,i[n++]=o}}packQuadAttributes(e,t,i,n,r){const o=e.texture,a=e.transform,l=a.a,h=a.b,c=a.c,u=a.d,d=a.tx,_=a.ty,f=e.bounds,m=f.maxX,g=f.minX,p=f.maxY,y=f.minY,b=o.uvs,w=e.color,A=r<<16|e.roundPixels&65535;t[n+0]=l*g+c*y+d,t[n+1]=u*y+h*g+_,t[n+2]=b.x0,t[n+3]=b.y0,i[n+4]=w,i[n+5]=A,t[n+6]=l*m+c*y+d,t[n+7]=u*y+h*m+_,t[n+8]=b.x1,t[n+9]=b.y1,i[n+10]=w,i[n+11]=A,t[n+12]=l*m+c*p+d,t[n+13]=u*p+h*m+_,t[n+14]=b.x2,t[n+15]=b.y2,i[n+16]=w,i[n+17]=A,t[n+18]=l*g+c*p+d,t[n+19]=u*p+h*g+_,t[n+20]=b.x3,t[n+21]=b.y3,i[n+22]=w,i[n+23]=A}};Vu.extension={type:[D.Batcher],name:"default"};let Db=Vu;function Nb(s,e,t,i,n,r,o,a=null){let l=0;t*=e,n*=r;const h=a.a,c=a.b,u=a.c,d=a.d,_=a.tx,f=a.ty;for(;l<o;){const m=s[t],g=s[t+1];i[n]=h*m+u*g+_,i[n+1]=c*m+d*g+f,n+=r,t+=e,l++}}function Ub(s,e,t,i){let n=0;for(e*=t;n<i;)s[e]=0,s[e+1]=0,e+=t,n++}function Yu(s,e,t,i,n){const r=e.a,o=e.b,a=e.c,l=e.d,h=e.tx,c=e.ty;t||(t=0),i||(i=2),n||(n=s.length/i-t);let u=t*i;for(let d=0;d<n;d++){const _=s[u],f=s[u+1];s[u]=r*_+a*f+h,s[u+1]=o*_+l*f+c,u+=i}}const Wb=new Z;class ju{constructor(){this.packAsQuad=!1,this.batcherName="default",this.topology="triangle-list",this.applyTransform=!0,this.roundPixels=0,this._batcher=null,this._batch=null}get uvs(){return this.geometryData.uvs}get positions(){return this.geometryData.vertices}get indices(){return this.geometryData.indices}get blendMode(){return this.applyTransform?this.renderable.groupBlendMode:"normal"}get color(){const e=this.baseColor,t=e>>16|e&65280|(e&255)<<16,i=this.renderable;return i?Qc(t,i.groupColor)+(this.alpha*i.groupAlpha*255<<24):t+(this.alpha*255<<24)}get transform(){return this.renderable?.groupTransform||Wb}copyTo(e){e.indexOffset=this.indexOffset,e.indexSize=this.indexSize,e.attributeOffset=this.attributeOffset,e.attributeSize=this.attributeSize,e.baseColor=this.baseColor,e.alpha=this.alpha,e.texture=this.texture,e.geometryData=this.geometryData,e.topology=this.topology}reset(){this.applyTransform=!0,this.renderable=null,this.topology="triangle-list"}}const nn={extension:{type:D.ShapeBuilder,name:"circle"},build(s,e){let t,i,n,r,o,a;if(s.type==="circle"){const w=s;t=w.x,i=w.y,o=a=w.radius,n=r=0}else if(s.type==="ellipse"){const w=s;t=w.x,i=w.y,o=w.halfWidth,a=w.halfHeight,n=r=0}else{const w=s,A=w.width/2,v=w.height/2;t=w.x+A,i=w.y+v,o=a=Math.max(0,Math.min(w.radius,Math.min(A,v))),n=A-o,r=v-a}if(!(o>=0&&a>=0&&n>=0&&r>=0))return e;const l=Math.ceil(2.3*Math.sqrt(o+a)),h=l*8+(n?4:0)+(r?4:0);if(h===0)return e;if(l===0)return e[0]=e[6]=t+n,e[1]=e[3]=i+r,e[2]=e[4]=t-n,e[5]=e[7]=i-r,e;let c=0,u=l*4+(n?2:0)+2,d=u,_=h,f=n+o,m=r,g=t+f,p=t-f,y=i+m;if(e[c++]=g,e[c++]=y,e[--u]=y,e[--u]=p,r){const w=i-m;e[d++]=p,e[d++]=w,e[--_]=w,e[--_]=g}for(let w=1;w<l;w++){const A=Math.PI/2*(w/l),v=n+Math.cos(A)*o,S=r+Math.sin(A)*a,R=t+v,C=t-v,M=i+S,k=i-S;e[c++]=R,e[c++]=M,e[--u]=M,e[--u]=C,e[d++]=C,e[d++]=k,e[--_]=k,e[--_]=R}f=n,m=r+a,g=t+f,p=t-f,y=i+m;const b=i-m;return e[c++]=g,e[c++]=y,e[--_]=b,e[--_]=g,n&&(e[c++]=p,e[c++]=y,e[--_]=b,e[--_]=p),e},triangulate(s,e,t,i,n,r){if(s.length===0)return;let o=0,a=0;for(let c=0;c<s.length;c+=2)o+=s[c],a+=s[c+1];o/=s.length/2,a/=s.length/2;let l=i;e[l*t]=o,e[l*t+1]=a;const h=l++;for(let c=0;c<s.length;c+=2)e[l*t]=s[c],e[l*t+1]=s[c+1],c>0&&(n[r++]=l,n[r++]=h,n[r++]=l-1),l++;n[r++]=h+1,n[r++]=h,n[r++]=l-1}},Hb={...nn,extension:{...nn.extension,name:"ellipse"}},qb={...nn,extension:{...nn.extension,name:"roundedRectangle"}},Ku=1e-4,Ll=1e-4;function Vb(s){const e=s.length;if(e<6)return 1;let t=0;for(let i=0,n=s[e-2],r=s[e-1];i<e;i+=2){const o=s[i],a=s[i+1];t+=(o-n)*(a+r),n=o,r=a}return t<0?-1:1}function $l(s,e,t,i,n,r,o,a){const l=s-t*n,h=e-i*n,c=s+t*r,u=e+i*r;let d,_;o?(d=i,_=-t):(d=-i,_=t);const f=l+d,m=h+_,g=c+d,p=u+_;return a.push(f,m),a.push(g,p),2}function ki(s,e,t,i,n,r,o,a){const l=t-s,h=i-e;let c=Math.atan2(l,h),u=Math.atan2(n-s,r-e);a&&c<u?c+=Math.PI*2:!a&&c>u&&(u+=Math.PI*2);let d=c;const _=u-c,f=Math.abs(_),m=Math.sqrt(l*l+h*h),g=(15*f*Math.sqrt(m)/Math.PI>>0)+1,p=_/g;if(d+=p,a){o.push(s,e),o.push(t,i);for(let y=1,b=d;y<g;y++,b+=p)o.push(s,e),o.push(s+Math.sin(b)*m,e+Math.cos(b)*m);o.push(s,e),o.push(n,r)}else{o.push(t,i),o.push(s,e);for(let y=1,b=d;y<g;y++,b+=p)o.push(s+Math.sin(b)*m,e+Math.cos(b)*m),o.push(s,e);o.push(n,r),o.push(s,e)}return g*2}function Xb(s,e,t,i,n,r){const o=Ku;if(s.length===0)return;const a=e;let l=a.alignment;if(e.alignment!==.5){let q=Vb(s);l=(l-.5)*q+.5}const h=new X(s[0],s[1]),c=new X(s[s.length-2],s[s.length-1]),u=i,d=Math.abs(h.x-c.x)<o&&Math.abs(h.y-c.y)<o;if(u){s=s.slice(),d&&(s.pop(),s.pop(),c.set(s[s.length-2],s[s.length-1]));const q=(h.x+c.x)*.5,te=(c.y+h.y)*.5;s.unshift(q,te),s.push(q,te)}const _=n,f=s.length/2;let m=s.length;const g=_.length/2,p=a.width/2,y=p*p,b=a.miterLimit*a.miterLimit;let w=s[0],A=s[1],v=s[2],S=s[3],R=0,C=0,M=-(A-S),k=w-v,P=0,L=0,B=Math.sqrt(M*M+k*k);M/=B,k/=B,M*=p,k*=p;const H=l,F=(1-H)*2,O=H*2;u||(a.cap==="round"?m+=ki(w-M*(F-O)*.5,A-k*(F-O)*.5,w-M*F,A-k*F,w+M*O,A+k*O,_,!0)+2:a.cap==="square"&&(m+=$l(w,A,M,k,F,O,!0,_))),_.push(w-M*F,A-k*F),_.push(w+M*O,A+k*O);for(let q=1;q<f-1;++q){w=s[(q-1)*2],A=s[(q-1)*2+1],v=s[q*2],S=s[q*2+1],R=s[(q+1)*2],C=s[(q+1)*2+1],M=-(A-S),k=w-v,B=Math.sqrt(M*M+k*k),M/=B,k/=B,M*=p,k*=p,P=-(S-C),L=v-R,B=Math.sqrt(P*P+L*L),P/=B,L/=B,P*=p,L*=p;const te=v-w,E=A-S,$=v-R,ye=C-S,Pe=te*$+E*ye,ge=E*$-ye*te,me=ge<0;if(Math.abs(ge)<.001*Math.abs(Pe)){_.push(v-M*F,S-k*F),_.push(v+M*O,S+k*O),Pe>=0&&(a.join==="round"?m+=ki(v,S,v-M*F,S-k*F,v-P*F,S-L*F,_,!1)+4:m+=2,_.push(v-P*O,S-L*O),_.push(v+P*F,S+L*F));continue}const xe=(-M+w)*(-k+S)-(-M+v)*(-k+A),ve=(-P+R)*(-L+S)-(-P+v)*(-L+C),ke=(te*ve-$*xe)/ge,ze=(ye*xe-E*ve)/ge,at=(ke-v)*(ke-v)+(ze-S)*(ze-S),z=v+(ke-v)*F,ue=S+(ze-S)*F,ne=v-(ke-v)*O,Ee=S-(ze-S)*O,I=Math.min(te*te+E*E,$*$+ye*ye),Ae=me?F:O,lt=I+Ae*Ae*y;at<=lt?a.join==="bevel"||at/y>b?(me?(_.push(z,ue),_.push(v+M*O,S+k*O),_.push(z,ue),_.push(v+P*O,S+L*O)):(_.push(v-M*F,S-k*F),_.push(ne,Ee),_.push(v-P*F,S-L*F),_.push(ne,Ee)),m+=2):a.join==="round"?me?(_.push(z,ue),_.push(v+M*O,S+k*O),m+=ki(v,S,v+M*O,S+k*O,v+P*O,S+L*O,_,!0)+4,_.push(z,ue),_.push(v+P*O,S+L*O)):(_.push(v-M*F,S-k*F),_.push(ne,Ee),m+=ki(v,S,v-M*F,S-k*F,v-P*F,S-L*F,_,!1)+4,_.push(v-P*F,S-L*F),_.push(ne,Ee)):(_.push(z,ue),_.push(ne,Ee)):(_.push(v-M*F,S-k*F),_.push(v+M*O,S+k*O),a.join==="round"?me?m+=ki(v,S,v+M*O,S+k*O,v+P*O,S+L*O,_,!0)+2:m+=ki(v,S,v-M*F,S-k*F,v-P*F,S-L*F,_,!1)+2:a.join==="miter"&&at/y<=b&&(me?(_.push(ne,Ee),_.push(ne,Ee)):(_.push(z,ue),_.push(z,ue)),m+=2),_.push(v-P*F,S-L*F),_.push(v+P*O,S+L*O),m+=2)}w=s[(f-2)*2],A=s[(f-2)*2+1],v=s[(f-1)*2],S=s[(f-1)*2+1],M=-(A-S),k=w-v,B=Math.sqrt(M*M+k*k),M/=B,k/=B,M*=p,k*=p,_.push(v-M*F,S-k*F),_.push(v+M*O,S+k*O),u||(a.cap==="round"?m+=ki(v-M*(F-O)*.5,S-k*(F-O)*.5,v-M*F,S-k*F,v+M*O,S+k*O,_,!1)+2:a.cap==="square"&&(m+=$l(v,S,M,k,F,O,!1,_)));const Ie=Ll*Ll;for(let q=g;q<m+g-2;++q)w=_[q*2],A=_[q*2+1],v=_[(q+1)*2],S=_[(q+1)*2+1],R=_[(q+2)*2],C=_[(q+2)*2+1],!(Math.abs(w*(S-C)+v*(C-A)+R*(A-S))<Ie)&&r.push(q,q+1,q+2)}function Yb(s,e,t,i){const n=Ku;if(s.length===0)return;const r=s[0],o=s[1],a=s[s.length-2],l=s[s.length-1],h=e||Math.abs(r-a)<n&&Math.abs(o-l)<n,c=t,u=s.length/2,d=c.length/2;for(let _=0;_<u;_++)c.push(s[_*2]),c.push(s[_*2+1]);for(let _=0;_<u-1;_++)i.push(d+_,d+_+1);h&&i.push(d+u-1,d)}function Zu(s,e,t,i,n,r,o){const a=Lp(s,e,2);if(!a)return;for(let h=0;h<a.length;h+=3)r[o++]=a[h]+n,r[o++]=a[h+1]+n,r[o++]=a[h+2]+n;let l=n*i;for(let h=0;h<s.length;h+=2)t[l]=s[h],t[l+1]=s[h+1],l+=i}const jb=[],Kb={extension:{type:D.ShapeBuilder,name:"polygon"},build(s,e){for(let t=0;t<s.points.length;t++)e[t]=s.points[t];return e},triangulate(s,e,t,i,n,r){Zu(s,jb,e,t,i,n,r)}},Zb={extension:{type:D.ShapeBuilder,name:"rectangle"},build(s,e){const t=s,i=t.x,n=t.y,r=t.width,o=t.height;return r>=0&&o>=0&&(e[0]=i,e[1]=n,e[2]=i+r,e[3]=n,e[4]=i+r,e[5]=n+o,e[6]=i,e[7]=n+o),e},triangulate(s,e,t,i,n,r){let o=0;i*=t,e[i+o]=s[0],e[i+o+1]=s[1],o+=t,e[i+o]=s[2],e[i+o+1]=s[3],o+=t,e[i+o]=s[6],e[i+o+1]=s[7],o+=t,e[i+o]=s[4],e[i+o+1]=s[5],o+=t;const a=i/t;n[r++]=a,n[r++]=a+1,n[r++]=a+2,n[r++]=a+1,n[r++]=a+3,n[r++]=a+2}},Qb={extension:{type:D.ShapeBuilder,name:"triangle"},build(s,e){return e[0]=s.x,e[1]=s.y,e[2]=s.x2,e[3]=s.y2,e[4]=s.x3,e[5]=s.y3,e},triangulate(s,e,t,i,n,r){let o=0;i*=t,e[i+o]=s[0],e[i+o+1]=s[1],o+=t,e[i+o]=s[2],e[i+o+1]=s[3],o+=t,e[i+o]=s[4],e[i+o+1]=s[5];const a=i/t;n[r++]=a,n[r++]=a+1,n[r++]=a+2}},ar={};De.handleByMap(D.ShapeBuilder,ar);De.add(Zb,Kb,Qb,nn,Hb,qb);const Jb=new se;function e0(s,e){const{geometryData:t,batches:i}=e;i.length=0,t.indices.length=0,t.vertices.length=0,t.uvs.length=0;for(let n=0;n<s.instructions.length;n++){const r=s.instructions[n];if(r.action==="texture")t0(r.data,i,t);else if(r.action==="fill"||r.action==="stroke"){const o=r.action==="stroke",a=r.data.path.shapePath,l=r.data.style,h=r.data.hole;o&&h&&Ol(h.shapePath,l,null,!0,i,t),Ol(a,l,h,o,i,t)}}}function t0(s,e,t){const{vertices:i,uvs:n,indices:r}=t,o=r.length,a=i.length/2,l=[],h=ar.rectangle,c=Jb,u=s.image;c.x=s.dx,c.y=s.dy,c.width=s.dw,c.height=s.dh;const d=s.transform;h.build(c,l),d&&Yu(l,d),h.triangulate(l,i,2,a,r,o);const _=u.uvs;n.push(_.x0,_.y0,_.x1,_.y1,_.x3,_.y3,_.x2,_.y2);const f=Kt.get(ju);f.indexOffset=o,f.indexSize=r.length-o,f.attributeOffset=a,f.attributeSize=i.length/2-a,f.baseColor=s.style,f.alpha=s.alpha,f.texture=u,f.geometryData=t,e.push(f)}function Ol(s,e,t,i,n,r){const{vertices:o,uvs:a,indices:l}=r,h=s.shapePrimitives.length-1;s.shapePrimitives.forEach(({shape:c,transform:u},d)=>{const _=l.length,f=o.length/2,m=[],g=ar[c.type];let p="triangle-list";if(g.build(c,m),u&&Yu(m,u),i){const A=c.closePath??!0,v=e;v.pixelLine?(Yb(m,A,o,l),p="line-list"):Xb(m,v,!1,A,o,l)}else if(t&&h===d){h!==0&&console.warn("[Pixi Graphics] only the last shape have be cut out");const A=[],v=m.slice();i0(t.shapePath).forEach(R=>{A.push(v.length/2),v.push(...R)}),Zu(v,A,o,2,f,l,_)}else g.triangulate(m,o,2,f,l,_);const y=a.length/2,b=e.texture;if(b!==Y.WHITE){const A=e.matrix;A&&(u&&A.append(u.clone().invert()),Nb(o,2,f,a,y,2,o.length/2-f,A))}else Ub(a,y,2,o.length/2-f);const w=Kt.get(ju);w.indexOffset=_,w.indexSize=l.length-_,w.attributeOffset=f,w.attributeSize=o.length/2-f,w.baseColor=e.color,w.alpha=e.alpha,w.texture=b,w.geometryData=r,w.topology=p,n.push(w)})}function i0(s){if(!s)return[];const e=s.shapePrimitives,t=[];for(let i=0;i<e.length;i++){const n=e[i].shape,r=[];ar[n.type].build(n,r),t.push(r)}return t}class s0{constructor(){this.batches=[],this.geometryData={vertices:[],uvs:[],indices:[]}}}class n0{constructor(){this.batcher=new Db,this.instructions=new iu}init(){this.instructions.reset()}get geometry(){return K(nm,"GraphicsContextRenderData#geometry is deprecated, please use batcher.geometry instead."),this.batcher.geometry}}const xa=class jo{constructor(e){this._gpuContextHash={},this._graphicsDataContextHash=Object.create(null),e.renderableGC.addManagedHash(this,"_gpuContextHash"),e.renderableGC.addManagedHash(this,"_graphicsDataContextHash")}init(e){jo.defaultOptions.bezierSmoothness=e?.bezierSmoothness??jo.defaultOptions.bezierSmoothness}getContextRenderData(e){return this._graphicsDataContextHash[e.uid]||this._initContextRenderData(e)}updateGpuContext(e){let t=this._gpuContextHash[e.uid]||this._initContext(e);if(e.dirty){t?this._cleanGraphicsContextData(e):t=this._initContext(e),e0(e,t);const i=e.batchMode;e.customShader||i==="no-batch"?t.isBatchable=!1:i==="auto"&&(t.isBatchable=t.geometryData.vertices.length<400),e.dirty=!1}return t}getGpuContext(e){return this._gpuContextHash[e.uid]||this._initContext(e)}_initContextRenderData(e){const t=Kt.get(n0),{batches:i,geometryData:n}=this._gpuContextHash[e.uid],r=n.vertices.length,o=n.indices.length;for(let c=0;c<i.length;c++)i[c].applyTransform=!1;const a=t.batcher;a.ensureAttributeBuffer(r),a.ensureIndexBuffer(o),a.begin();for(let c=0;c<i.length;c++){const u=i[c];a.add(u)}a.finish(t.instructions);const l=a.geometry;l.indexBuffer.setDataWithSize(a.indexBuffer,a.indexSize,!0),l.buffers[0].setDataWithSize(a.attributeBuffer.float32View,a.attributeSize,!0);const h=a.batches;for(let c=0;c<h.length;c++){const u=h[c];u.bindGroup=eb(u.textures.textures,u.textures.count)}return this._graphicsDataContextHash[e.uid]=t,t}_initContext(e){const t=new s0;return t.context=e,this._gpuContextHash[e.uid]=t,e.on("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[e.uid]}onGraphicsContextDestroy(e){this._cleanGraphicsContextData(e),e.off("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[e.uid]=null}_cleanGraphicsContextData(e){const t=this._gpuContextHash[e.uid];t.isBatchable||this._graphicsDataContextHash[e.uid]&&(Kt.return(this.getContextRenderData(e)),this._graphicsDataContextHash[e.uid]=null),t.batches&&t.batches.forEach(i=>{Kt.return(i)})}destroy(){for(const e in this._gpuContextHash)this._gpuContextHash[e]&&this.onGraphicsContextDestroy(this._gpuContextHash[e].context)}};xa.extension={type:[D.WebGLSystem,D.WebGPUSystem,D.CanvasSystem],name:"graphicsContext"};xa.defaultOptions={bezierSmoothness:.5};let Qu=xa;const r0=8,Mn=11920929e-14,o0=1;function Ju(s,e,t,i,n,r,o,a,l,h){const u=Math.min(.99,Math.max(0,h??Qu.defaultOptions.bezierSmoothness));let d=(o0-u)/1;return d*=d,a0(e,t,i,n,r,o,a,l,s,d),s}function a0(s,e,t,i,n,r,o,a,l,h){Ko(s,e,t,i,n,r,o,a,l,h,0),l.push(o,a)}function Ko(s,e,t,i,n,r,o,a,l,h,c){if(c>r0)return;const u=(s+t)/2,d=(e+i)/2,_=(t+n)/2,f=(i+r)/2,m=(n+o)/2,g=(r+a)/2,p=(u+_)/2,y=(d+f)/2,b=(_+m)/2,w=(f+g)/2,A=(p+b)/2,v=(y+w)/2;if(c>0){let S=o-s,R=a-e;const C=Math.abs((t-o)*R-(i-a)*S),M=Math.abs((n-o)*R-(r-a)*S);if(C>Mn&&M>Mn){if((C+M)*(C+M)<=h*(S*S+R*R)){l.push(A,v);return}}else if(C>Mn){if(C*C<=h*(S*S+R*R)){l.push(A,v);return}}else if(M>Mn){if(M*M<=h*(S*S+R*R)){l.push(A,v);return}}else if(S=A-(s+o)/2,R=v-(e+a)/2,S*S+R*R<=h){l.push(A,v);return}}Ko(s,e,u,d,p,y,A,v,l,h,c+1),Ko(A,v,b,w,m,g,o,a,l,h,c+1)}const l0=8,h0=11920929e-14,c0=1;function u0(s,e,t,i,n,r,o,a){const h=Math.min(.99,Math.max(0,a??Qu.defaultOptions.bezierSmoothness));let c=(c0-h)/1;return c*=c,d0(e,t,i,n,r,o,s,c),s}function d0(s,e,t,i,n,r,o,a){Zo(o,s,e,t,i,n,r,a,0),o.push(n,r)}function Zo(s,e,t,i,n,r,o,a,l){if(l>l0)return;const h=(e+i)/2,c=(t+n)/2,u=(i+r)/2,d=(n+o)/2,_=(h+u)/2,f=(c+d)/2;let m=r-e,g=o-t;const p=Math.abs((i-r)*g-(n-o)*m);if(p>h0){if(p*p<=a*(m*m+g*g)){s.push(_,f);return}}else if(m=_-(e+r)/2,g=f-(t+o)/2,m*m+g*g<=a){s.push(_,f);return}Zo(s,e,t,h,c,_,f,a,l+1),Zo(s,_,f,u,d,r,o,a,l+1)}function ed(s,e,t,i,n,r,o,a){let l=Math.abs(n-r);(!o&&n>r||o&&r>n)&&(l=2*Math.PI-l),a||(a=Math.max(6,Math.floor(6*Math.pow(i,1/3)*(l/Math.PI)))),a=Math.max(a,3);let h=l/a,c=n;h*=o?-1:1;for(let u=0;u<a+1;u++){const d=Math.cos(c),_=Math.sin(c),f=e+d*i,m=t+_*i;s.push(f,m),c+=h}}function _0(s,e,t,i,n,r){const o=s[s.length-2],l=s[s.length-1]-t,h=o-e,c=n-t,u=i-e,d=Math.abs(l*u-h*c);if(d<1e-8||r===0){(s[s.length-2]!==e||s[s.length-1]!==t)&&s.push(e,t);return}const _=l*l+h*h,f=c*c+u*u,m=l*c+h*u,g=r*Math.sqrt(_)/d,p=r*Math.sqrt(f)/d,y=g*m/_,b=p*m/f,w=g*u+p*h,A=g*c+p*l,v=h*(p+y),S=l*(p+y),R=u*(g+b),C=c*(g+b),M=Math.atan2(S-A,v-w),k=Math.atan2(C-A,R-w);ed(s,w+e,A+t,r,M,k,h*c>u*l)}const Xs=Math.PI*2,fo={centerX:0,centerY:0,ang1:0,ang2:0},go=({x:s,y:e},t,i,n,r,o,a,l)=>{s*=t,e*=i;const h=n*s-r*e,c=r*s+n*e;return l.x=h+o,l.y=c+a,l};function f0(s,e){const t=e===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(e/4),i=e===1.5707963267948966?.551915024494:t,n=Math.cos(s),r=Math.sin(s),o=Math.cos(s+e),a=Math.sin(s+e);return[{x:n-r*i,y:r+n*i},{x:o+a*i,y:a-o*i},{x:o,y:a}]}const Gl=(s,e,t,i)=>{const n=s*i-e*t<0?-1:1;let r=s*t+e*i;return r>1&&(r=1),r<-1&&(r=-1),n*Math.acos(r)},g0=(s,e,t,i,n,r,o,a,l,h,c,u,d)=>{const _=Math.pow(n,2),f=Math.pow(r,2),m=Math.pow(c,2),g=Math.pow(u,2);let p=_*f-_*g-f*m;p<0&&(p=0),p/=_*g+f*m,p=Math.sqrt(p)*(o===a?-1:1);const y=p*n/r*u,b=p*-r/n*c,w=h*y-l*b+(s+t)/2,A=l*y+h*b+(e+i)/2,v=(c-y)/n,S=(u-b)/r,R=(-c-y)/n,C=(-u-b)/r,M=Gl(1,0,v,S);let k=Gl(v,S,R,C);a===0&&k>0&&(k-=Xs),a===1&&k<0&&(k+=Xs),d.centerX=w,d.centerY=A,d.ang1=M,d.ang2=k};function m0(s,e,t,i,n,r,o,a=0,l=0,h=0){if(r===0||o===0)return;const c=Math.sin(a*Xs/360),u=Math.cos(a*Xs/360),d=u*(e-i)/2+c*(t-n)/2,_=-c*(e-i)/2+u*(t-n)/2;if(d===0&&_===0)return;r=Math.abs(r),o=Math.abs(o);const f=Math.pow(d,2)/Math.pow(r,2)+Math.pow(_,2)/Math.pow(o,2);f>1&&(r*=Math.sqrt(f),o*=Math.sqrt(f)),g0(e,t,i,n,r,o,l,h,c,u,d,_,fo);let{ang1:m,ang2:g}=fo;const{centerX:p,centerY:y}=fo;let b=Math.abs(g)/(Xs/4);Math.abs(1-b)<1e-7&&(b=1);const w=Math.max(Math.ceil(b),1);g/=w;let A=s[s.length-2],v=s[s.length-1];const S={x:0,y:0};for(let R=0;R<w;R++){const C=f0(m,g),{x:M,y:k}=go(C[0],r,o,u,c,p,y,S),{x:P,y:L}=go(C[1],r,o,u,c,p,y,S),{x:B,y:H}=go(C[2],r,o,u,c,p,y,S);Ju(s,A,v,M,k,P,L,B,H),A=B,v=H,m+=g}}function p0(s,e,t){const i=(o,a)=>{const l=a.x-o.x,h=a.y-o.y,c=Math.sqrt(l*l+h*h),u=l/c,d=h/c;return{len:c,nx:u,ny:d}},n=(o,a)=>{o===0?s.moveTo(a.x,a.y):s.lineTo(a.x,a.y)};let r=e[e.length-1];for(let o=0;o<e.length;o++){const a=e[o%e.length],l=a.radius??t;if(l<=0){n(o,a),r=a;continue}const h=e[(o+1)%e.length],c=i(a,r),u=i(a,h);if(c.len<1e-4||u.len<1e-4){n(o,a),r=a;continue}let d=Math.asin(c.nx*u.ny-c.ny*u.nx),_=1,f=!1;c.nx*u.nx-c.ny*-u.ny<0?d<0?d=Math.PI+d:(d=Math.PI-d,_=-1,f=!0):d>0&&(_=-1,f=!0);const m=d/2;let g,p=Math.abs(Math.cos(m)*l/Math.sin(m));p>Math.min(c.len/2,u.len/2)?(p=Math.min(c.len/2,u.len/2),g=Math.abs(p*Math.sin(m)/Math.cos(m))):g=l;const y=a.x+u.nx*p+-u.ny*g*_,b=a.y+u.ny*p+u.nx*g*_,w=Math.atan2(c.ny,c.nx)+Math.PI/2*_,A=Math.atan2(u.ny,u.nx)-Math.PI/2*_;o===0&&s.moveTo(y+Math.cos(w)*g,b+Math.sin(w)*g),s.arc(y,b,g,w,A,f),r=a}}function b0(s,e,t,i){const n=(a,l)=>Math.sqrt((a.x-l.x)**2+(a.y-l.y)**2),r=(a,l,h)=>({x:a.x+(l.x-a.x)*h,y:a.y+(l.y-a.y)*h}),o=e.length;for(let a=0;a<o;a++){const l=e[(a+1)%o],h=l.radius??t;if(h<=0){a===0?s.moveTo(l.x,l.y):s.lineTo(l.x,l.y);continue}const c=e[a],u=e[(a+2)%o],d=n(c,l);let _;if(d<1e-4)_=l;else{const g=Math.min(d/2,h);_=r(l,c,g/d)}const f=n(u,l);let m;if(f<1e-4)m=l;else{const g=Math.min(f/2,h);m=r(l,u,g/f)}a===0?s.moveTo(_.x,_.y):s.lineTo(_.x,_.y),s.quadraticCurveTo(l.x,l.y,m.x,m.y,i)}}const y0=new se;class w0{constructor(e){this.shapePrimitives=[],this._currentPoly=null,this._bounds=new St,this._graphicsPath2D=e}moveTo(e,t){return this.startPoly(e,t),this}lineTo(e,t){this._ensurePoly();const i=this._currentPoly.points,n=i[i.length-2],r=i[i.length-1];return(n!==e||r!==t)&&i.push(e,t),this}arc(e,t,i,n,r,o){this._ensurePoly(!1);const a=this._currentPoly.points;return ed(a,e,t,i,n,r,o),this}arcTo(e,t,i,n,r){this._ensurePoly();const o=this._currentPoly.points;return _0(o,e,t,i,n,r),this}arcToSvg(e,t,i,n,r,o,a){const l=this._currentPoly.points;return m0(l,this._currentPoly.lastX,this._currentPoly.lastY,o,a,e,t,i,n,r),this}bezierCurveTo(e,t,i,n,r,o,a){this._ensurePoly();const l=this._currentPoly;return Ju(this._currentPoly.points,l.lastX,l.lastY,e,t,i,n,r,o,a),this}quadraticCurveTo(e,t,i,n,r){this._ensurePoly();const o=this._currentPoly;return u0(this._currentPoly.points,o.lastX,o.lastY,e,t,i,n,r),this}closePath(){return this.endPoly(!0),this}addPath(e,t){this.endPoly(),t&&!t.isIdentity()&&(e=e.clone(!0),e.transform(t));for(let i=0;i<e.instructions.length;i++){const n=e.instructions[i];this[n.action](...n.data)}return this}finish(e=!1){this.endPoly(e)}rect(e,t,i,n,r){return this.drawShape(new se(e,t,i,n),r),this}circle(e,t,i,n){return this.drawShape(new ba(e,t,i),n),this}poly(e,t,i){const n=new Vs(e);return n.closePath=t,this.drawShape(n,i),this}regularPoly(e,t,i,n,r=0,o){n=Math.max(n|0,3);const a=-1*Math.PI/2+r,l=Math.PI*2/n,h=[];for(let c=0;c<n;c++){const u=a-c*l;h.push(e+i*Math.cos(u),t+i*Math.sin(u))}return this.poly(h,!0,o),this}roundPoly(e,t,i,n,r,o=0,a){if(n=Math.max(n|0,3),r<=0)return this.regularPoly(e,t,i,n,o);const l=i*Math.sin(Math.PI/n)-.001;r=Math.min(r,l);const h=-1*Math.PI/2+o,c=Math.PI*2/n,u=(n-2)*Math.PI/n/2;for(let d=0;d<n;d++){const _=d*c+h,f=e+i*Math.cos(_),m=t+i*Math.sin(_),g=_+Math.PI+u,p=_-Math.PI-u,y=f+r*Math.cos(g),b=m+r*Math.sin(g),w=f+r*Math.cos(p),A=m+r*Math.sin(p);d===0?this.moveTo(y,b):this.lineTo(y,b),this.quadraticCurveTo(f,m,w,A,a)}return this.closePath()}roundShape(e,t,i=!1,n){return e.length<3?this:(i?b0(this,e,t,n):p0(this,e,t),this.closePath())}filletRect(e,t,i,n,r){if(r===0)return this.rect(e,t,i,n);const o=Math.min(i,n)/2,a=Math.min(o,Math.max(-o,r)),l=e+i,h=t+n,c=a<0?-a:0,u=Math.abs(a);return this.moveTo(e,t+u).arcTo(e+c,t+c,e+u,t,u).lineTo(l-u,t).arcTo(l-c,t+c,l,t+u,u).lineTo(l,h-u).arcTo(l-c,h-c,e+i-u,h,u).lineTo(e+u,h).arcTo(e+c,h-c,e,h-u,u).closePath()}chamferRect(e,t,i,n,r,o){if(r<=0)return this.rect(e,t,i,n);const a=Math.min(r,Math.min(i,n)/2),l=e+i,h=t+n,c=[e+a,t,l-a,t,l,t+a,l,h-a,l-a,h,e+a,h,e,h-a,e,t+a];for(let u=c.length-1;u>=2;u-=2)c[u]===c[u-2]&&c[u-1]===c[u-3]&&c.splice(u-1,2);return this.poly(c,!0,o)}ellipse(e,t,i,n,r){return this.drawShape(new ya(e,t,i,n),r),this}roundRect(e,t,i,n,r,o){return this.drawShape(new wa(e,t,i,n,r),o),this}drawShape(e,t){return this.endPoly(),this.shapePrimitives.push({shape:e,transform:t}),this}startPoly(e,t){let i=this._currentPoly;return i&&this.endPoly(),i=new Vs,i.points.push(e,t),this._currentPoly=i,this}endPoly(e=!1){const t=this._currentPoly;return t&&t.points.length>2&&(t.closePath=e,this.shapePrimitives.push({shape:t})),this._currentPoly=null,this}_ensurePoly(e=!0){if(!this._currentPoly&&(this._currentPoly=new Vs,e)){const t=this.shapePrimitives[this.shapePrimitives.length-1];if(t){let i=t.shape.x,n=t.shape.y;if(t.transform&&!t.transform.isIdentity()){const r=t.transform,o=i;i=r.a*i+r.c*n+r.tx,n=r.b*o+r.d*n+r.ty}this._currentPoly.points.push(i,n)}else this._currentPoly.points.push(0,0)}}buildPath(){const e=this._graphicsPath2D;this.shapePrimitives.length=0,this._currentPoly=null;for(let t=0;t<e.instructions.length;t++){const i=e.instructions[t];this[i.action](...i.data)}this.finish()}get bounds(){const e=this._bounds;e.clear();const t=this.shapePrimitives;for(let i=0;i<t.length;i++){const n=t[i],r=n.shape.getBounds(y0);n.transform?e.addRect(r,n.transform):e.addRect(r)}return e}}class fs{constructor(e){this.instructions=[],this.uid=pe("graphicsPath"),this._dirty=!0,typeof e=="string"?jp(e,this):this.instructions=e?.slice()??[]}get shapePath(){return this._shapePath||(this._shapePath=new w0(this)),this._dirty&&(this._dirty=!1,this._shapePath.buildPath()),this._shapePath}addPath(e,t){return e=e.clone(),this.instructions.push({action:"addPath",data:[e,t]}),this._dirty=!0,this}arc(...e){return this.instructions.push({action:"arc",data:e}),this._dirty=!0,this}arcTo(...e){return this.instructions.push({action:"arcTo",data:e}),this._dirty=!0,this}arcToSvg(...e){return this.instructions.push({action:"arcToSvg",data:e}),this._dirty=!0,this}bezierCurveTo(...e){return this.instructions.push({action:"bezierCurveTo",data:e}),this._dirty=!0,this}bezierCurveToShort(e,t,i,n,r){const o=this.instructions[this.instructions.length-1],a=this.getLastPoint(X.shared);let l=0,h=0;if(!o||o.action!=="bezierCurveTo")l=a.x,h=a.y;else{l=o.data[2],h=o.data[3];const c=a.x,u=a.y;l=c+(c-l),h=u+(u-h)}return this.instructions.push({action:"bezierCurveTo",data:[l,h,e,t,i,n,r]}),this._dirty=!0,this}closePath(){return this.instructions.push({action:"closePath",data:[]}),this._dirty=!0,this}ellipse(...e){return this.instructions.push({action:"ellipse",data:e}),this._dirty=!0,this}lineTo(...e){return this.instructions.push({action:"lineTo",data:e}),this._dirty=!0,this}moveTo(...e){return this.instructions.push({action:"moveTo",data:e}),this}quadraticCurveTo(...e){return this.instructions.push({action:"quadraticCurveTo",data:e}),this._dirty=!0,this}quadraticCurveToShort(e,t,i){const n=this.instructions[this.instructions.length-1],r=this.getLastPoint(X.shared);let o=0,a=0;if(!n||n.action!=="quadraticCurveTo")o=r.x,a=r.y;else{o=n.data[0],a=n.data[1];const l=r.x,h=r.y;o=l+(l-o),a=h+(h-a)}return this.instructions.push({action:"quadraticCurveTo",data:[o,a,e,t,i]}),this._dirty=!0,this}rect(e,t,i,n,r){return this.instructions.push({action:"rect",data:[e,t,i,n,r]}),this._dirty=!0,this}circle(e,t,i,n){return this.instructions.push({action:"circle",data:[e,t,i,n]}),this._dirty=!0,this}roundRect(...e){return this.instructions.push({action:"roundRect",data:e}),this._dirty=!0,this}poly(...e){return this.instructions.push({action:"poly",data:e}),this._dirty=!0,this}regularPoly(...e){return this.instructions.push({action:"regularPoly",data:e}),this._dirty=!0,this}roundPoly(...e){return this.instructions.push({action:"roundPoly",data:e}),this._dirty=!0,this}roundShape(...e){return this.instructions.push({action:"roundShape",data:e}),this._dirty=!0,this}filletRect(...e){return this.instructions.push({action:"filletRect",data:e}),this._dirty=!0,this}chamferRect(...e){return this.instructions.push({action:"chamferRect",data:e}),this._dirty=!0,this}star(e,t,i,n,r,o,a){r||(r=n/2);const l=-1*Math.PI/2+o,h=i*2,c=Math.PI*2/h,u=[];for(let d=0;d<h;d++){const _=d%2?r:n,f=d*c+l;u.push(e+_*Math.cos(f),t+_*Math.sin(f))}return this.poly(u,!0,a),this}clone(e=!1){const t=new fs;if(!e)t.instructions=this.instructions.slice();else for(let i=0;i<this.instructions.length;i++){const n=this.instructions[i];t.instructions.push({action:n.action,data:n.data.slice()})}return t}clear(){return this.instructions.length=0,this._dirty=!0,this}transform(e){if(e.isIdentity())return this;const t=e.a,i=e.b,n=e.c,r=e.d,o=e.tx,a=e.ty;let l=0,h=0,c=0,u=0,d=0,_=0,f=0,m=0;for(let g=0;g<this.instructions.length;g++){const p=this.instructions[g],y=p.data;switch(p.action){case"moveTo":case"lineTo":l=y[0],h=y[1],y[0]=t*l+n*h+o,y[1]=i*l+r*h+a;break;case"bezierCurveTo":c=y[0],u=y[1],d=y[2],_=y[3],l=y[4],h=y[5],y[0]=t*c+n*u+o,y[1]=i*c+r*u+a,y[2]=t*d+n*_+o,y[3]=i*d+r*_+a,y[4]=t*l+n*h+o,y[5]=i*l+r*h+a;break;case"quadraticCurveTo":c=y[0],u=y[1],l=y[2],h=y[3],y[0]=t*c+n*u+o,y[1]=i*c+r*u+a,y[2]=t*l+n*h+o,y[3]=i*l+r*h+a;break;case"arcToSvg":l=y[5],h=y[6],f=y[0],m=y[1],y[0]=t*f+n*m,y[1]=i*f+r*m,y[5]=t*l+n*h+o,y[6]=i*l+r*h+a;break;case"circle":y[4]=Is(y[3],e);break;case"rect":y[4]=Is(y[4],e);break;case"ellipse":y[8]=Is(y[8],e);break;case"roundRect":y[5]=Is(y[5],e);break;case"addPath":y[0].transform(e);break;case"poly":y[2]=Is(y[2],e);break;default:be("unknown transform action",p.action);break}}return this._dirty=!0,this}get bounds(){return this.shapePath.bounds}getLastPoint(e){let t=this.instructions.length-1,i=this.instructions[t];if(!i)return e.x=0,e.y=0,e;for(;i.action==="closePath";){if(t--,t<0)return e.x=0,e.y=0,e;i=this.instructions[t]}switch(i.action){case"moveTo":case"lineTo":e.x=i.data[0],e.y=i.data[1];break;case"quadraticCurveTo":e.x=i.data[2],e.y=i.data[3];break;case"bezierCurveTo":e.x=i.data[4],e.y=i.data[5];break;case"arc":case"arcToSvg":e.x=i.data[5],e.y=i.data[6];break;case"addPath":i.data[0].getLastPoint(e);break}return e}}function Is(s,e){return s?s.prepend(e):e.clone()}function x0(s,e){if(typeof s=="string"){const i=document.createElement("div");i.innerHTML=s.trim(),s=i.querySelector("svg")}const t={context:e,path:new fs};return td(s,t,null,null),e}function td(s,e,t,i){const n=s.children,{fillStyle:r,strokeStyle:o}=v0(s);r&&t?t={...t,...r}:r&&(t=r),o&&i?i={...i,...o}:o&&(i=o),e.context.fillStyle=t,e.context.strokeStyle=i;let a,l,h,c,u,d,_,f,m,g,p,y,b,w,A,v,S;switch(s.nodeName.toLowerCase()){case"path":w=s.getAttribute("d"),A=new fs(w),e.context.path(A),t&&e.context.fill(),i&&e.context.stroke();break;case"circle":_=Ce(s,"cx",0),f=Ce(s,"cy",0),m=Ce(s,"r",0),e.context.ellipse(_,f,m,m),t&&e.context.fill(),i&&e.context.stroke();break;case"rect":a=Ce(s,"x",0),l=Ce(s,"y",0),v=Ce(s,"width",0),S=Ce(s,"height",0),g=Ce(s,"rx",0),p=Ce(s,"ry",0),g||p?e.context.roundRect(a,l,v,S,g||p):e.context.rect(a,l,v,S),t&&e.context.fill(),i&&e.context.stroke();break;case"ellipse":_=Ce(s,"cx",0),f=Ce(s,"cy",0),g=Ce(s,"rx",0),p=Ce(s,"ry",0),e.context.beginPath(),e.context.ellipse(_,f,g,p),t&&e.context.fill(),i&&e.context.stroke();break;case"line":h=Ce(s,"x1",0),c=Ce(s,"y1",0),u=Ce(s,"x2",0),d=Ce(s,"y2",0),e.context.beginPath(),e.context.moveTo(h,c),e.context.lineTo(u,d),i&&e.context.stroke();break;case"polygon":b=s.getAttribute("points"),y=b.match(/\d+/g).map(R=>parseInt(R,10)),e.context.poly(y,!0),t&&e.context.fill(),i&&e.context.stroke();break;case"polyline":b=s.getAttribute("points"),y=b.match(/\d+/g).map(R=>parseInt(R,10)),e.context.poly(y,!1),i&&e.context.stroke();break;case"g":case"svg":break;default:{console.info(`[SVG parser] <${s.nodeName}> elements unsupported`);break}}for(let R=0;R<n.length;R++)td(n[R],e,t,i)}function Ce(s,e,t){const i=s.getAttribute(e);return i?Number(i):t}function v0(s){const e=s.getAttribute("style"),t={},i={};let n=!1,r=!1;if(e){const o=e.split(";");for(let a=0;a<o.length;a++){const l=o[a],[h,c]=l.split(":");switch(h){case"stroke":c!=="none"&&(t.color=fe.shared.setValue(c).toNumber(),r=!0);break;case"stroke-width":t.width=Number(c);break;case"fill":c!=="none"&&(n=!0,i.color=fe.shared.setValue(c).toNumber());break;case"fill-opacity":i.alpha=Number(c);break;case"stroke-opacity":t.alpha=Number(c);break;case"opacity":i.alpha=Number(c),t.alpha=Number(c);break}}}else{const o=s.getAttribute("stroke");o&&o!=="none"&&(r=!0,t.color=fe.shared.setValue(o).toNumber(),t.width=Ce(s,"stroke-width",1));const a=s.getAttribute("fill");a&&a!=="none"&&(n=!0,i.color=fe.shared.setValue(a).toNumber())}return{strokeStyle:r?t:null,fillStyle:n?i:null}}function k0(s){return fe.isColorLike(s)}function Dl(s){return s instanceof or}function Nl(s){return s instanceof tn}function A0(s,e,t){const i=fe.shared.setValue(e??0);return s.color=i.toNumber(),s.alpha=i.alpha===1?t.alpha:i.alpha,s.texture=Y.WHITE,{...t,...s}}function Ul(s,e,t){return s.fill=e,s.color=16777215,s.texture=e.texture,s.matrix=e.transform,{...t,...s}}function Wl(s,e,t){return e.buildLinearGradient(),s.fill=e,s.color=16777215,s.texture=e.texture,s.matrix=e.transform,{...t,...s}}function S0(s,e){const t={...e,...s};if(t.texture){if(t.texture!==Y.WHITE){const r=t.matrix?.clone().invert()||new Z;r.translate(t.texture.frame.x,t.texture.frame.y),r.scale(1/t.texture.source.width,1/t.texture.source.height),t.matrix=r}const n=t.texture.source.style;n.addressMode==="clamp-to-edge"&&(n.addressMode="repeat",n.update())}const i=fe.shared.setValue(t.color);return t.alpha*=i.alpha,t.color=i.toNumber(),t.matrix=t.matrix?t.matrix.clone():null,t}function Ii(s,e){if(s==null)return null;const t={},i=s;return k0(s)?A0(t,s,e):Dl(s)?Ul(t,s,e):Nl(s)?Wl(t,s,e):i.fill&&Dl(i.fill)?Ul(i,i.fill,e):i.fill&&Nl(i.fill)?Wl(i,i.fill,e):S0(i,e)}function Vn(s,e){const{width:t,alignment:i,miterLimit:n,cap:r,join:o,pixelLine:a,...l}=e,h=Ii(s,l);return h?{width:t,alignment:i,miterLimit:n,cap:r,join:o,pixelLine:a,...h}:null}const C0=new X,Hl=new Z,va=class $t extends Tt{constructor(){super(...arguments),this.uid=pe("graphicsContext"),this.dirty=!0,this.batchMode="auto",this.instructions=[],this._activePath=new fs,this._transform=new Z,this._fillStyle={...$t.defaultFillStyle},this._strokeStyle={...$t.defaultStrokeStyle},this._stateStack=[],this._tick=0,this._bounds=new St,this._boundsDirty=!0}clone(){const e=new $t;return e.batchMode=this.batchMode,e.instructions=this.instructions.slice(),e._activePath=this._activePath.clone(),e._transform=this._transform.clone(),e._fillStyle={...this._fillStyle},e._strokeStyle={...this._strokeStyle},e._stateStack=this._stateStack.slice(),e._bounds=this._bounds.clone(),e._boundsDirty=!0,e}get fillStyle(){return this._fillStyle}set fillStyle(e){this._fillStyle=Ii(e,$t.defaultFillStyle)}get strokeStyle(){return this._strokeStyle}set strokeStyle(e){this._strokeStyle=Vn(e,$t.defaultStrokeStyle)}setFillStyle(e){return this._fillStyle=Ii(e,$t.defaultFillStyle),this}setStrokeStyle(e){return this._strokeStyle=Ii(e,$t.defaultStrokeStyle),this}texture(e,t,i,n,r,o){return this.instructions.push({action:"texture",data:{image:e,dx:i||0,dy:n||0,dw:r||e.frame.width,dh:o||e.frame.height,transform:this._transform.clone(),alpha:this._fillStyle.alpha,style:t?fe.shared.setValue(t).toNumber():16777215}}),this.onUpdate(),this}beginPath(){return this._activePath=new fs,this}fill(e,t){let i;const n=this.instructions[this.instructions.length-1];return this._tick===0&&n&&n.action==="stroke"?i=n.data.path:i=this._activePath.clone(),i?(e!=null&&(t!==void 0&&typeof e=="number"&&(K(J,"GraphicsContext.fill(color, alpha) is deprecated, use GraphicsContext.fill({ color, alpha }) instead"),e={color:e,alpha:t}),this._fillStyle=Ii(e,$t.defaultFillStyle)),this.instructions.push({action:"fill",data:{style:this.fillStyle,path:i}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}_initNextPathLocation(){const{x:e,y:t}=this._activePath.getLastPoint(X.shared);this._activePath.clear(),this._activePath.moveTo(e,t)}stroke(e){let t;const i=this.instructions[this.instructions.length-1];return this._tick===0&&i&&i.action==="fill"?t=i.data.path:t=this._activePath.clone(),t?(e!=null&&(this._strokeStyle=Vn(e,$t.defaultStrokeStyle)),this.instructions.push({action:"stroke",data:{style:this.strokeStyle,path:t}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}cut(){for(let e=0;e<2;e++){const t=this.instructions[this.instructions.length-1-e],i=this._activePath.clone();if(t&&(t.action==="stroke"||t.action==="fill"))if(t.data.hole)t.data.hole.addPath(i);else{t.data.hole=i;break}}return this._initNextPathLocation(),this}arc(e,t,i,n,r,o){this._tick++;const a=this._transform;return this._activePath.arc(a.a*e+a.c*t+a.tx,a.b*e+a.d*t+a.ty,i,n,r,o),this}arcTo(e,t,i,n,r){this._tick++;const o=this._transform;return this._activePath.arcTo(o.a*e+o.c*t+o.tx,o.b*e+o.d*t+o.ty,o.a*i+o.c*n+o.tx,o.b*i+o.d*n+o.ty,r),this}arcToSvg(e,t,i,n,r,o,a){this._tick++;const l=this._transform;return this._activePath.arcToSvg(e,t,i,n,r,l.a*o+l.c*a+l.tx,l.b*o+l.d*a+l.ty),this}bezierCurveTo(e,t,i,n,r,o,a){this._tick++;const l=this._transform;return this._activePath.bezierCurveTo(l.a*e+l.c*t+l.tx,l.b*e+l.d*t+l.ty,l.a*i+l.c*n+l.tx,l.b*i+l.d*n+l.ty,l.a*r+l.c*o+l.tx,l.b*r+l.d*o+l.ty,a),this}closePath(){return this._tick++,this._activePath?.closePath(),this}ellipse(e,t,i,n){return this._tick++,this._activePath.ellipse(e,t,i,n,this._transform.clone()),this}circle(e,t,i){return this._tick++,this._activePath.circle(e,t,i,this._transform.clone()),this}path(e){return this._tick++,this._activePath.addPath(e,this._transform.clone()),this}lineTo(e,t){this._tick++;const i=this._transform;return this._activePath.lineTo(i.a*e+i.c*t+i.tx,i.b*e+i.d*t+i.ty),this}moveTo(e,t){this._tick++;const i=this._transform,n=this._activePath.instructions,r=i.a*e+i.c*t+i.tx,o=i.b*e+i.d*t+i.ty;return n.length===1&&n[0].action==="moveTo"?(n[0].data[0]=r,n[0].data[1]=o,this):(this._activePath.moveTo(r,o),this)}quadraticCurveTo(e,t,i,n,r){this._tick++;const o=this._transform;return this._activePath.quadraticCurveTo(o.a*e+o.c*t+o.tx,o.b*e+o.d*t+o.ty,o.a*i+o.c*n+o.tx,o.b*i+o.d*n+o.ty,r),this}rect(e,t,i,n){return this._tick++,this._activePath.rect(e,t,i,n,this._transform.clone()),this}roundRect(e,t,i,n,r){return this._tick++,this._activePath.roundRect(e,t,i,n,r,this._transform.clone()),this}poly(e,t){return this._tick++,this._activePath.poly(e,t,this._transform.clone()),this}regularPoly(e,t,i,n,r=0,o){return this._tick++,this._activePath.regularPoly(e,t,i,n,r,o),this}roundPoly(e,t,i,n,r,o){return this._tick++,this._activePath.roundPoly(e,t,i,n,r,o),this}roundShape(e,t,i,n){return this._tick++,this._activePath.roundShape(e,t,i,n),this}filletRect(e,t,i,n,r){return this._tick++,this._activePath.filletRect(e,t,i,n,r),this}chamferRect(e,t,i,n,r,o){return this._tick++,this._activePath.chamferRect(e,t,i,n,r,o),this}star(e,t,i,n,r=0,o=0){return this._tick++,this._activePath.star(e,t,i,n,r,o,this._transform.clone()),this}svg(e){return this._tick++,x0(e,this),this}restore(){const e=this._stateStack.pop();return e&&(this._transform=e.transform,this._fillStyle=e.fillStyle,this._strokeStyle=e.strokeStyle),this}save(){return this._stateStack.push({transform:this._transform.clone(),fillStyle:{...this._fillStyle},strokeStyle:{...this._strokeStyle}}),this}getTransform(){return this._transform}resetTransform(){return this._transform.identity(),this}rotate(e){return this._transform.rotate(e),this}scale(e,t=e){return this._transform.scale(e,t),this}setTransform(e,t,i,n,r,o){return e instanceof Z?(this._transform.set(e.a,e.b,e.c,e.d,e.tx,e.ty),this):(this._transform.set(e,t,i,n,r,o),this)}transform(e,t,i,n,r,o){return e instanceof Z?(this._transform.append(e),this):(Hl.set(e,t,i,n,r,o),this._transform.append(Hl),this)}translate(e,t=e){return this._transform.translate(e,t),this}clear(){return this._activePath.clear(),this.instructions.length=0,this.resetTransform(),this.onUpdate(),this}onUpdate(){this.dirty||(this.emit("update",this,16),this.dirty=!0,this._boundsDirty=!0)}get bounds(){if(!this._boundsDirty)return this._bounds;const e=this._bounds;e.clear();for(let t=0;t<this.instructions.length;t++){const i=this.instructions[t],n=i.action;if(n==="fill"){const r=i.data;e.addBounds(r.path.bounds)}else if(n==="texture"){const r=i.data;e.addFrame(r.dx,r.dy,r.dx+r.dw,r.dy+r.dh,r.transform)}if(n==="stroke"){const r=i.data,o=r.style.alignment,a=r.style.width*(1-o),l=r.path.bounds;e.addFrame(l.minX-a,l.minY-a,l.maxX+a,l.maxY+a)}}return e}containsPoint(e){if(!this.bounds.containsPoint(e.x,e.y))return!1;const t=this.instructions;let i=!1;for(let n=0;n<t.length;n++){const r=t[n],o=r.data,a=o.path;if(!r.action||!a)continue;const l=o.style,h=a.shapePath.shapePrimitives;for(let c=0;c<h.length;c++){const u=h[c].shape;if(!l||!u)continue;const d=h[c].transform,_=d?d.applyInverse(e,C0):e;if(r.action==="fill")i=u.contains(_.x,_.y);else{const m=l;i=u.strokeContains(_.x,_.y,m.width,m.alignment)}const f=o.hole;if(f){const m=f.shapePath?.shapePrimitives;if(m)for(let g=0;g<m.length;g++)m[g].shape.contains(_.x,_.y)&&(i=!1)}if(i)return!0}}return i}destroy(e=!1){if(this._stateStack.length=0,this._transform=null,this.emit("destroy",this),this.removeAllListeners(),typeof e=="boolean"?e:e?.texture){const i=typeof e=="boolean"?e:e?.textureSource;this._fillStyle.texture&&this._fillStyle.texture.destroy(i),this._strokeStyle.texture&&this._strokeStyle.texture.destroy(i)}this._fillStyle=null,this._strokeStyle=null,this.instructions=null,this._activePath=null,this._bounds=null,this._stateStack=null,this.customShader=null,this._transform=null}};va.defaultFillStyle={color:16777215,alpha:1,texture:Y.WHITE,matrix:null,fill:null};va.defaultStrokeStyle={width:1,color:16777215,alpha:1,alignment:.5,miterLimit:10,cap:"butt",join:"miter",texture:Y.WHITE,matrix:null,fill:null,pixelLine:!1};let dt=va;const ql=["align","breakWords","cssOverrides","fontVariant","fontWeight","leading","letterSpacing","lineHeight","padding","textBaseline","trim","whiteSpace","wordWrap","wordWrapWidth","fontFamily","fontStyle","fontSize"];function M0(s){const e=[];let t=0;for(let i=0;i<ql.length;i++){const n=`_${ql[i]}`;e[t++]=s[n]}return t=id(s._fill,e,t),t=T0(s._stroke,e,t),t=P0(s.dropShadow,e,t),e.join("-")}function id(s,e,t){return s&&(e[t++]=s.color,e[t++]=s.alpha,e[t++]=s.fill?.styleKey),t}function T0(s,e,t){return s&&(t=id(s,e,t),e[t++]=s.width,e[t++]=s.alignment,e[t++]=s.cap,e[t++]=s.join,e[t++]=s.miterLimit),t}function P0(s,e,t){return s&&(e[t++]=s.alpha,e[t++]=s.angle,e[t++]=s.blur,e[t++]=s.distance,e[t++]=fe.shared.setValue(s.color).toNumber()),t}const ka=class ts extends Tt{constructor(e={}){super(),z0(e);const t={...ts.defaultTextStyle,...e};for(const i in t){const n=i;this[n]=t[i]}this.update()}get align(){return this._align}set align(e){this._align=e,this.update()}get breakWords(){return this._breakWords}set breakWords(e){this._breakWords=e,this.update()}get dropShadow(){return this._dropShadow}set dropShadow(e){e!==null&&typeof e=="object"?this._dropShadow=this._createProxy({...ts.defaultDropShadow,...e}):this._dropShadow=e?this._createProxy({...ts.defaultDropShadow}):null,this.update()}get fontFamily(){return this._fontFamily}set fontFamily(e){this._fontFamily=e,this.update()}get fontSize(){return this._fontSize}set fontSize(e){typeof e=="string"?this._fontSize=parseInt(e,10):this._fontSize=e,this.update()}get fontStyle(){return this._fontStyle}set fontStyle(e){this._fontStyle=e.toLowerCase(),this.update()}get fontVariant(){return this._fontVariant}set fontVariant(e){this._fontVariant=e,this.update()}get fontWeight(){return this._fontWeight}set fontWeight(e){this._fontWeight=e,this.update()}get leading(){return this._leading}set leading(e){this._leading=e,this.update()}get letterSpacing(){return this._letterSpacing}set letterSpacing(e){this._letterSpacing=e,this.update()}get lineHeight(){return this._lineHeight}set lineHeight(e){this._lineHeight=e,this.update()}get padding(){return this._padding}set padding(e){this._padding=e,this.update()}get trim(){return this._trim}set trim(e){this._trim=e,this.update()}get textBaseline(){return this._textBaseline}set textBaseline(e){this._textBaseline=e,this.update()}get whiteSpace(){return this._whiteSpace}set whiteSpace(e){this._whiteSpace=e,this.update()}get wordWrap(){return this._wordWrap}set wordWrap(e){this._wordWrap=e,this.update()}get wordWrapWidth(){return this._wordWrapWidth}set wordWrapWidth(e){this._wordWrapWidth=e,this.update()}get fill(){return this._originalFill}set fill(e){e!==this._originalFill&&(this._originalFill=e,this._isFillStyle(e)&&(this._originalFill=this._createProxy({...dt.defaultFillStyle,...e},()=>{this._fill=Ii({...this._originalFill},dt.defaultFillStyle)})),this._fill=Ii(e===0?"black":e,dt.defaultFillStyle),this.update())}get stroke(){return this._originalStroke}set stroke(e){e!==this._originalStroke&&(this._originalStroke=e,this._isFillStyle(e)&&(this._originalStroke=this._createProxy({...dt.defaultStrokeStyle,...e},()=>{this._stroke=Vn({...this._originalStroke},dt.defaultStrokeStyle)})),this._stroke=Vn(e,dt.defaultStrokeStyle),this.update())}_generateKey(){return this._styleKey=M0(this),this._styleKey}update(){this._styleKey=null,this.emit("update",this)}reset(){const e=ts.defaultTextStyle;for(const t in e)this[t]=e[t]}get styleKey(){return this._styleKey||this._generateKey()}clone(){return new ts({align:this.align,breakWords:this.breakWords,dropShadow:this._dropShadow?{...this._dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,leading:this.leading,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,textBaseline:this.textBaseline,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth})}destroy(e=!1){if(this.removeAllListeners(),typeof e=="boolean"?e:e?.texture){const i=typeof e=="boolean"?e:e?.textureSource;this._fill?.texture&&this._fill.texture.destroy(i),this._originalFill?.texture&&this._originalFill.texture.destroy(i),this._stroke?.texture&&this._stroke.texture.destroy(i),this._originalStroke?.texture&&this._originalStroke.texture.destroy(i)}this._fill=null,this._stroke=null,this.dropShadow=null,this._originalStroke=null,this._originalFill=null}_createProxy(e,t){return new Proxy(e,{set:(i,n,r)=>(i[n]=r,t?.(n,r),this.update(),!0)})}_isFillStyle(e){return(e??null)!==null&&!(fe.isColorLike(e)||e instanceof tn||e instanceof or)}};ka.defaultDropShadow={alpha:1,angle:Math.PI/6,blur:0,color:"black",distance:5};ka.defaultTextStyle={align:"left",breakWords:!1,dropShadow:null,fill:"black",fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",leading:0,letterSpacing:0,lineHeight:0,padding:0,stroke:null,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100};let rn=ka;function z0(s){const e=s;if(typeof e.dropShadow=="boolean"&&e.dropShadow){const t=rn.defaultDropShadow;s.dropShadow={alpha:e.dropShadowAlpha??t.alpha,angle:e.dropShadowAngle??t.angle,blur:e.dropShadowBlur??t.blur,color:e.dropShadowColor??t.color,distance:e.dropShadowDistance??t.distance}}if(e.strokeThickness!==void 0){K(J,"strokeThickness is now a part of stroke");const t=e.stroke;let i={};if(fe.isColorLike(t))i.color=t;else if(t instanceof tn||t instanceof or)i.fill=t;else if(Object.hasOwnProperty.call(t,"color")||Object.hasOwnProperty.call(t,"fill"))i=t;else throw new Error("Invalid stroke value.");s.stroke={...i,width:e.strokeThickness}}if(Array.isArray(e.fillGradientStops)){K(J,"gradient fill is now a fill pattern: `new FillGradient(...)`");let t;s.fontSize==null?s.fontSize=rn.defaultTextStyle.fontSize:typeof s.fontSize=="string"?t=parseInt(s.fontSize,10):t=s.fontSize;const i=new tn(0,0,0,t*1.7),n=e.fillGradientStops.map(r=>fe.shared.setValue(r).toNumber());n.forEach((r,o)=>{const a=o/(n.length-1);i.addColorStop(a,r)}),s.fill={fill:i}}}class I0{constructor(e){this._canvasPool=Object.create(null),this.canvasOptions=e||{},this.enableFullScreen=!1}_createCanvasAndContext(e,t){const i=_e.get().createCanvas();i.width=e,i.height=t;const n=i.getContext("2d");return{canvas:i,context:n}}getOptimalCanvasAndContext(e,t,i=1){e=Math.ceil(e*i-1e-6),t=Math.ceil(t*i-1e-6),e=Hn(e),t=Hn(t);const n=(e<<17)+(t<<1);this._canvasPool[n]||(this._canvasPool[n]=[]);let r=this._canvasPool[n].pop();return r||(r=this._createCanvasAndContext(e,t)),r}returnCanvasAndContext(e){const t=e.canvas,{width:i,height:n}=t,r=(i<<17)+(n<<1);e.context.clearRect(0,0,i,n),this._canvasPool[r].push(e)}clear(){this._canvasPool={}}}const Vl=new I0,E0=["serif","sans-serif","monospace","cursive","fantasy","system-ui"];function Qo(s){const e=typeof s.fontSize=="number"?`${s.fontSize}px`:s.fontSize;let t=s.fontFamily;Array.isArray(s.fontFamily)||(t=s.fontFamily.split(","));for(let i=t.length-1;i>=0;i--){let n=t[i].trim();!/([\"\'])[^\'\"]+\1/.test(n)&&!E0.includes(n)&&(n=`"${n}"`),t[i]=n}return`${s.fontStyle} ${s.fontVariant} ${s.fontWeight} ${e} ${t.join(",")}`}const mo={willReadFrequently:!0},zt=class G{static get experimentalLetterSpacingSupported(){let e=G._experimentalLetterSpacingSupported;if(e!==void 0){const t=_e.get().getCanvasRenderingContext2D().prototype;e=G._experimentalLetterSpacingSupported="letterSpacing"in t||"textLetterSpacing"in t}return e}constructor(e,t,i,n,r,o,a,l,h){this.text=e,this.style=t,this.width=i,this.height=n,this.lines=r,this.lineWidths=o,this.lineHeight=a,this.maxLineWidth=l,this.fontProperties=h}static measureText(e=" ",t,i=G._canvas,n=t.wordWrap){const r=`${e}:${t.styleKey}`;if(G._measurementCache[r])return G._measurementCache[r];const o=Qo(t),a=G.measureFont(o);a.fontSize===0&&(a.fontSize=t.fontSize,a.ascent=t.fontSize);const l=G.__context;l.font=o;const c=(n?G._wordWrap(e,t,i):e).split(/(?:\r\n|\r|\n)/),u=new Array(c.length);let d=0;for(let y=0;y<c.length;y++){const b=G._measureText(c[y],t.letterSpacing,l);u[y]=b,d=Math.max(d,b)}const _=t._stroke?.width||0;let f=d+_;t.dropShadow&&(f+=t.dropShadow.distance);const m=t.lineHeight||a.fontSize;let g=Math.max(m,a.fontSize+_)+(c.length-1)*(m+t.leading);return t.dropShadow&&(g+=t.dropShadow.distance),new G(e,t,f,g,c,u,m+t.leading,d,a)}static _measureText(e,t,i){let n=!1;G.experimentalLetterSpacingSupported&&(G.experimentalLetterSpacing?(i.letterSpacing=`${t}px`,i.textLetterSpacing=`${t}px`,n=!0):(i.letterSpacing="0px",i.textLetterSpacing="0px"));const r=i.measureText(e);let o=r.width;const a=-r.actualBoundingBoxLeft;let h=r.actualBoundingBoxRight-a;if(o>0)if(n)o-=t,h-=t;else{const c=(G.graphemeSegmenter(e).length-1)*t;o+=c,h+=c}return Math.max(o,h)}static _wordWrap(e,t,i=G._canvas){const n=i.getContext("2d",mo);let r=0,o="",a="";const l=Object.create(null),{letterSpacing:h,whiteSpace:c}=t,u=G._collapseSpaces(c),d=G._collapseNewlines(c);let _=!u;const f=t.wordWrapWidth+h,m=G._tokenize(e);for(let g=0;g<m.length;g++){let p=m[g];if(G._isNewline(p)){if(!d){a+=G._addLine(o),_=!u,o="",r=0;continue}p=" "}if(u){const b=G.isBreakingSpace(p),w=G.isBreakingSpace(o[o.length-1]);if(b&&w)continue}const y=G._getFromCache(p,h,l,n);if(y>f)if(o!==""&&(a+=G._addLine(o),o="",r=0),G.canBreakWords(p,t.breakWords)){const b=G.wordWrapSplit(p);for(let w=0;w<b.length;w++){let A=b[w],v=A,S=1;for(;b[w+S];){const C=b[w+S];if(!G.canBreakChars(v,C,p,w,t.breakWords))A+=C;else break;v=C,S++}w+=S-1;const R=G._getFromCache(A,h,l,n);R+r>f&&(a+=G._addLine(o),_=!1,o="",r=0),o+=A,r+=R}}else{o.length>0&&(a+=G._addLine(o),o="",r=0);const b=g===m.length-1;a+=G._addLine(p,!b),_=!1,o="",r=0}else y+r>f&&(_=!1,a+=G._addLine(o),o="",r=0),(o.length>0||!G.isBreakingSpace(p)||_)&&(o+=p,r+=y)}return a+=G._addLine(o,!1),a}static _addLine(e,t=!0){return e=G._trimRight(e),e=t?`${e}
`:e,e}static _getFromCache(e,t,i,n){let r=i[e];return typeof r!="number"&&(r=G._measureText(e,t,n)+t,i[e]=r),r}static _collapseSpaces(e){return e==="normal"||e==="pre-line"}static _collapseNewlines(e){return e==="normal"}static _trimRight(e){if(typeof e!="string")return"";for(let t=e.length-1;t>=0;t--){const i=e[t];if(!G.isBreakingSpace(i))break;e=e.slice(0,-1)}return e}static _isNewline(e){return typeof e!="string"?!1:G._newlines.includes(e.charCodeAt(0))}static isBreakingSpace(e,t){return typeof e!="string"?!1:G._breakingSpaces.includes(e.charCodeAt(0))}static _tokenize(e){const t=[];let i="";if(typeof e!="string")return t;for(let n=0;n<e.length;n++){const r=e[n],o=e[n+1];if(G.isBreakingSpace(r,o)||G._isNewline(r)){i!==""&&(t.push(i),i=""),t.push(r);continue}i+=r}return i!==""&&t.push(i),t}static canBreakWords(e,t){return t}static canBreakChars(e,t,i,n,r){return!0}static wordWrapSplit(e){return G.graphemeSegmenter(e)}static measureFont(e){if(G._fonts[e])return G._fonts[e];const t=G._context;t.font=e;const i=t.measureText(G.METRICS_STRING+G.BASELINE_SYMBOL),n={ascent:i.actualBoundingBoxAscent,descent:i.actualBoundingBoxDescent,fontSize:i.actualBoundingBoxAscent+i.actualBoundingBoxDescent};return G._fonts[e]=n,n}static clearMetrics(e=""){e?delete G._fonts[e]:G._fonts={}}static get _canvas(){if(!G.__canvas){let e;try{const t=new OffscreenCanvas(0,0);if(t.getContext("2d",mo)?.measureText)return G.__canvas=t,t;e=_e.get().createCanvas()}catch{e=_e.get().createCanvas()}e.width=e.height=10,G.__canvas=e}return G.__canvas}static get _context(){return G.__context||(G.__context=G._canvas.getContext("2d",mo)),G.__context}};zt.METRICS_STRING="|ÉqÅ";zt.BASELINE_SYMBOL="M";zt.BASELINE_MULTIPLIER=1.4;zt.HEIGHT_MULTIPLIER=2;zt.graphemeSegmenter=(()=>{if(typeof Intl?.Segmenter=="function"){const s=new Intl.Segmenter;return e=>[...s.segment(e)].map(t=>t.segment)}return s=>[...s]})();zt.experimentalLetterSpacing=!1;zt._fonts={};zt._newlines=[10,13];zt._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];zt._measurementCache={};let Xl=zt;function Yl(s,e){if(s.texture===Y.WHITE&&!s.fill)return fe.shared.setValue(s.color).setAlpha(s.alpha??1).toHexa();if(s.fill){if(s.fill instanceof or){const t=s.fill,i=e.createPattern(t.texture.source.resource,"repeat"),n=t.transform.copyTo(Z.shared);return n.scale(t.texture.frame.width,t.texture.frame.height),i.setTransform(n),i}else if(s.fill instanceof tn){const t=s.fill;if(t.type==="linear"){const i=e.createLinearGradient(t.x0,t.y0,t.x1,t.y1);return t.gradientStops.forEach(n=>{i.addColorStop(n.offset,fe.shared.setValue(n.color).toHex())}),i}}}else{const t=e.createPattern(s.texture.source.resource,"repeat"),i=s.matrix.copyTo(Z.shared);return i.scale(s.texture.frame.width,s.texture.frame.height),t.setTransform(i),t}return be("FillStyle not recognised",s),"red"}function sd(s){if(s==="")return[];typeof s=="string"&&(s=[s]);const e=[];for(let t=0,i=s.length;t<i;t++){const n=s[t];if(Array.isArray(n)){if(n.length!==2)throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${n.length}.`);if(n[0].length===0||n[1].length===0)throw new Error("[BitmapFont]: Invalid character delimiter.");const r=n[0].charCodeAt(0),o=n[1].charCodeAt(0);if(o<r)throw new Error("[BitmapFont]: Invalid character range.");for(let a=r,l=o;a<=l;a++)e.push(String.fromCharCode(a))}else e.push(...Array.from(n))}if(e.length===0)throw new Error("[BitmapFont]: Empty set when resolving characters.");return e}const nd=class rd extends $u{constructor(e){super(),this.resolution=1,this.pages=[],this._padding=0,this._measureCache=Object.create(null),this._currentChars=[],this._currentX=0,this._currentY=0,this._currentPageIndex=-1,this._skipKerning=!1;const t={...rd.defaultOptions,...e};this._textureSize=t.textureSize,this._mipmap=t.mipmap;const i=t.style.clone();t.overrideFill&&(i._fill.color=16777215,i._fill.alpha=1,i._fill.texture=Y.WHITE,i._fill.fill=null),this.applyFillAsTint=t.overrideFill;const n=i.fontSize;i.fontSize=this.baseMeasurementFontSize;const r=Qo(i);t.overrideSize?i._stroke&&(i._stroke.width*=this.baseRenderedFontSize/n):i.fontSize=this.baseRenderedFontSize=n,this._style=i,this._skipKerning=t.skipKerning??!1,this.resolution=t.resolution??1,this._padding=t.padding??4,this.fontMetrics=Xl.measureFont(r),this.lineHeight=i.lineHeight||this.fontMetrics.fontSize||i.fontSize}ensureCharacters(e){const t=sd(e).filter(g=>!this._currentChars.includes(g)).filter((g,p,y)=>y.indexOf(g)===p);if(!t.length)return;this._currentChars=[...this._currentChars,...t];let i;this._currentPageIndex===-1?i=this._nextPage():i=this.pages[this._currentPageIndex];let{canvas:n,context:r}=i.canvasAndContext,o=i.texture.source;const a=this._style;let l=this._currentX,h=this._currentY;const c=this.baseRenderedFontSize/this.baseMeasurementFontSize,u=this._padding*c;let d=0,_=!1;const f=n.width/this.resolution,m=n.height/this.resolution;for(let g=0;g<t.length;g++){const p=t[g],y=Xl.measureText(p,a,n,!1);y.lineHeight=y.height;const b=y.width*c,w=Math.ceil((a.fontStyle==="italic"?2:1)*b),A=y.height*c,v=w+u*2,S=A+u*2;if(_=!1,p!==`
`&&p!=="\r"&&p!=="	"&&p!==" "&&(_=!0,d=Math.ceil(Math.max(S,d))),l+v>f&&(h+=d,d=S,l=0,h+d>m)){o.update();const C=this._nextPage();n=C.canvasAndContext.canvas,r=C.canvasAndContext.context,o=C.texture.source,h=0}const R=b/c-(a.dropShadow?.distance??0)-(a._stroke?.width??0);if(this.chars[p]={id:p.codePointAt(0),xOffset:-this._padding,yOffset:-this._padding,xAdvance:R,kerning:{}},_){this._drawGlyph(r,y,l+u,h+u,c,a);const C=o.width*c,M=o.height*c,k=new se(l/C*o.width,h/M*o.height,v/C*o.width,S/M*o.height);this.chars[p].texture=new Y({source:o,frame:k}),l+=Math.ceil(v)}}o.update(),this._currentX=l,this._currentY=h,this._skipKerning&&this._applyKerning(t,r)}get pageTextures(){return K(J,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}_applyKerning(e,t){const i=this._measureCache;for(let n=0;n<e.length;n++){const r=e[n];for(let o=0;o<this._currentChars.length;o++){const a=this._currentChars[o];let l=i[r];l||(l=i[r]=t.measureText(r).width);let h=i[a];h||(h=i[a]=t.measureText(a).width);let c=t.measureText(r+a).width,u=c-(l+h);u&&(this.chars[r].kerning[a]=u),c=t.measureText(r+a).width,u=c-(l+h),u&&(this.chars[a].kerning[r]=u)}}}_nextPage(){this._currentPageIndex++;const e=this.resolution,t=Vl.getOptimalCanvasAndContext(this._textureSize,this._textureSize,e);this._setupContext(t.context,this._style,e);const i=e*(this.baseRenderedFontSize/this.baseMeasurementFontSize),n=new Y({source:new ws({resource:t.canvas,resolution:i,alphaMode:"premultiply-alpha-on-upload",autoGenerateMipmaps:this._mipmap})}),r={canvasAndContext:t,texture:n};return this.pages[this._currentPageIndex]=r,r}_setupContext(e,t,i){t.fontSize=this.baseRenderedFontSize,e.scale(i,i),e.font=Qo(t),t.fontSize=this.baseMeasurementFontSize,e.textBaseline=t.textBaseline;const n=t._stroke,r=n?.width??0;if(n&&(e.lineWidth=r,e.lineJoin=n.join,e.miterLimit=n.miterLimit,e.strokeStyle=Yl(n,e)),t._fill&&(e.fillStyle=Yl(t._fill,e)),t.dropShadow){const o=t.dropShadow,a=fe.shared.setValue(o.color).toArray(),l=o.blur*i,h=o.distance*i;e.shadowColor=`rgba(${a[0]*255},${a[1]*255},${a[2]*255},${o.alpha})`,e.shadowBlur=l,e.shadowOffsetX=Math.cos(o.angle)*h,e.shadowOffsetY=Math.sin(o.angle)*h}else e.shadowColor="black",e.shadowBlur=0,e.shadowOffsetX=0,e.shadowOffsetY=0}_drawGlyph(e,t,i,n,r,o){const a=t.text,l=t.fontProperties,c=(o._stroke?.width??0)*r,u=i+c/2,d=n-c/2,_=l.descent*r,f=t.lineHeight*r;o.stroke&&c&&e.strokeText(a,u,d+f-_),o._fill&&e.fillText(a,u,d+f-_)}destroy(){super.destroy();for(let e=0;e<this.pages.length;e++){const{canvasAndContext:t,texture:i}=this.pages[e];Vl.returnCanvasAndContext(t),i.destroy(!0)}this.pages=null}};nd.defaultOptions={textureSize:512,style:new rn,mipmap:!0};let jl=nd;function R0(s,e,t,i){const n={width:0,height:0,offsetY:0,scale:e.fontSize/t.baseMeasurementFontSize,lines:[{width:0,charPositions:[],spaceWidth:0,spacesIndex:[],chars:[]}]};n.offsetY=t.baseLineOffset;let r=n.lines[0],o=null,a=!0;const l={spaceWord:!1,width:0,start:0,index:0,positions:[],chars:[]},h=f=>{const m=r.width;for(let g=0;g<l.index;g++){const p=f.positions[g];r.chars.push(f.chars[g]),r.charPositions.push(p+m)}r.width+=f.width,a=!1,l.width=0,l.index=0,l.chars.length=0},c=()=>{let f=r.chars.length-1;if(i){let m=r.chars[f];for(;m===" ";)r.width-=t.chars[m].xAdvance,m=r.chars[--f]}n.width=Math.max(n.width,r.width),r={width:0,charPositions:[],chars:[],spaceWidth:0,spacesIndex:[]},a=!0,n.lines.push(r),n.height+=t.lineHeight},u=t.baseMeasurementFontSize/e.fontSize,d=e.letterSpacing*u,_=e.wordWrapWidth*u;for(let f=0;f<s.length+1;f++){let m;const g=f===s.length;g||(m=s[f]);const p=t.chars[m]||t.chars[" "];if(/(?:\s)/.test(m)||m==="\r"||m===`
`||g){if(!a&&e.wordWrap&&r.width+l.width-d>_?(c(),h(l),g||r.charPositions.push(0)):(l.start=r.width,h(l),g||r.charPositions.push(0)),m==="\r"||m===`
`)r.width!==0&&c();else if(!g){const A=p.xAdvance+(p.kerning[o]||0)+d;r.width+=A,r.spaceWidth=A,r.spacesIndex.push(r.charPositions.length),r.chars.push(m)}}else{const w=p.kerning[o]||0,A=p.xAdvance+w+d;l.positions[l.index++]=l.width+w,l.chars.push(m),l.width+=A}o=m}return c(),e.align==="center"?F0(n):e.align==="right"?B0(n):e.align==="justify"&&L0(n),n}function F0(s){for(let e=0;e<s.lines.length;e++){const t=s.lines[e],i=s.width/2-t.width/2;for(let n=0;n<t.charPositions.length;n++)t.charPositions[n]+=i}}function B0(s){for(let e=0;e<s.lines.length;e++){const t=s.lines[e],i=s.width-t.width;for(let n=0;n<t.charPositions.length;n++)t.charPositions[n]+=i}}function L0(s){const e=s.width;for(let t=0;t<s.lines.length;t++){const i=s.lines[t];let n=0,r=i.spacesIndex[n++],o=0;const a=i.spacesIndex.length,h=(e-i.width)/a;for(let c=0;c<i.charPositions.length;c++)c===r&&(r=i.spacesIndex[n++],o+=h),i.charPositions[c]+=o}}let Tn=0;class $0{constructor(){this.ALPHA=[["a","z"],["A","Z"]," "],this.NUMERIC=[["0","9"]],this.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],this.ASCII=[[" ","~"]],this.defaultOptions={chars:this.ALPHANUMERIC,resolution:1,padding:4,skipKerning:!1}}getFont(e,t){let i=`${t.fontFamily}-bitmap`,n=!0;if(t._fill.fill&&!t._stroke)i+=t._fill.fill.styleKey,n=!1;else if(t._stroke||t.dropShadow){let o=t.styleKey;o=o.substring(0,o.lastIndexOf("-")),i=`${o}-bitmap`,n=!1}if(!de.has(i)){const o=new jl({style:t,overrideFill:n,overrideSize:!0,...this.defaultOptions});Tn++,Tn>50&&be("BitmapText",`You have dynamically created ${Tn} bitmap fonts, this can be inefficient. Try pre installing your font styles using \`BitmapFont.install({name:"style1", style})\``),o.once("destroy",()=>{Tn--,de.remove(i)}),de.set(i,o)}const r=de.get(i);return r.ensureCharacters?.(e),r}getLayout(e,t,i=!0){const n=this.getFont(e,t);return R0([...e],t,n,i)}measureText(e,t,i=!0){return this.getLayout(e,t,i)}install(...e){let t=e[0];typeof t=="string"&&(t={name:t,style:e[1],chars:e[2]?.chars,resolution:e[2]?.resolution,padding:e[2]?.padding,skipKerning:e[2]?.skipKerning},K(J,"BitmapFontManager.install(name, style, options) is deprecated, use BitmapFontManager.install({name, style, ...options})"));const i=t?.name;if(!i)throw new Error("[BitmapFontManager] Property `name` is required.");t={...this.defaultOptions,...t};const n=t.style,r=n instanceof rn?n:new rn(n),o=r._fill.fill!==null&&r._fill.fill!==void 0,a=new jl({style:r,overrideFill:o,skipKerning:t.skipKerning,padding:t.padding,resolution:t.resolution,overrideSize:!1}),l=sd(t.chars);return a.ensureCharacters(l.join("")),de.set(`${i}-bitmap`,a),a.once("destroy",()=>de.remove(`${i}-bitmap`)),a}uninstall(e){const t=`${e}-bitmap`,i=de.get(t);i&&i.destroy()}}const Kl=new $0;class od extends $u{constructor(e,t){super();const{textures:i,data:n}=e;Object.keys(n.pages).forEach(r=>{const o=n.pages[parseInt(r,10)],a=i[o.id];this.pages.push({texture:a})}),Object.keys(n.chars).forEach(r=>{const o=n.chars[r],{frame:a,source:l}=i[o.page],h=new se(o.x+a.x,o.y+a.y,o.width,o.height),c=new Y({source:l,frame:h});this.chars[r]={id:r.codePointAt(0),xOffset:o.xOffset,yOffset:o.yOffset,xAdvance:o.xAdvance,kerning:o.kerning??{},texture:c}}),this.baseRenderedFontSize=n.fontSize,this.baseMeasurementFontSize=n.fontSize,this.fontMetrics={ascent:0,descent:0,fontSize:n.fontSize},this.baseLineOffset=n.baseLineOffset,this.lineHeight=n.lineHeight,this.fontFamily=n.fontFamily,this.distanceField=n.distanceField??{type:"none",range:0},this.url=t}destroy(){super.destroy();for(let e=0;e<this.pages.length;e++){const{texture:t}=this.pages[e];t.destroy(!0)}this.pages=null}static install(e){Kl.install(e)}static uninstall(e){Kl.uninstall(e)}}const po={test(s){return typeof s=="string"&&s.startsWith("info face=")},parse(s){const e=s.match(/^[a-z]+\s+.+$/gm),t={info:[],common:[],page:[],char:[],chars:[],kerning:[],kernings:[],distanceField:[]};for(const u in e){const d=e[u].match(/^[a-z]+/gm)[0],_=e[u].match(/[a-zA-Z]+=([^\s"']+|"([^"]*)")/gm),f={};for(const m in _){const g=_[m].split("="),p=g[0],y=g[1].replace(/"/gm,""),b=parseFloat(y),w=isNaN(b)?y:b;f[p]=w}t[d].push(f)}const i={chars:{},pages:[],lineHeight:0,fontSize:0,fontFamily:"",distanceField:null,baseLineOffset:0},[n]=t.info,[r]=t.common,[o]=t.distanceField??[];o&&(i.distanceField={range:parseInt(o.distanceRange,10),type:o.fieldType}),i.fontSize=parseInt(n.size,10),i.fontFamily=n.face,i.lineHeight=parseInt(r.lineHeight,10);const a=t.page;for(let u=0;u<a.length;u++)i.pages.push({id:parseInt(a[u].id,10)||0,file:a[u].file});const l={};i.baseLineOffset=i.lineHeight-parseInt(r.base,10);const h=t.char;for(let u=0;u<h.length;u++){const d=h[u],_=parseInt(d.id,10);let f=d.letter??d.char??String.fromCharCode(_);f==="space"&&(f=" "),l[_]=f,i.chars[f]={id:_,page:parseInt(d.page,10)||0,x:parseInt(d.x,10),y:parseInt(d.y,10),width:parseInt(d.width,10),height:parseInt(d.height,10),xOffset:parseInt(d.xoffset,10),yOffset:parseInt(d.yoffset,10),xAdvance:parseInt(d.xadvance,10),kerning:{}}}const c=t.kerning||[];for(let u=0;u<c.length;u++){const d=parseInt(c[u].first,10),_=parseInt(c[u].second,10),f=parseInt(c[u].amount,10);i.chars[l[_]].kerning[l[d]]=f}return i}},Zl={test(s){const e=s;return typeof e!="string"&&"getElementsByTagName"in e&&e.getElementsByTagName("page").length&&e.getElementsByTagName("info")[0].getAttribute("face")!==null},parse(s){const e={chars:{},pages:[],lineHeight:0,fontSize:0,fontFamily:"",distanceField:null,baseLineOffset:0},t=s.getElementsByTagName("info")[0],i=s.getElementsByTagName("common")[0],n=s.getElementsByTagName("distanceField")[0];n&&(e.distanceField={type:n.getAttribute("fieldType"),range:parseInt(n.getAttribute("distanceRange"),10)});const r=s.getElementsByTagName("page"),o=s.getElementsByTagName("char"),a=s.getElementsByTagName("kerning");e.fontSize=parseInt(t.getAttribute("size"),10),e.fontFamily=t.getAttribute("face"),e.lineHeight=parseInt(i.getAttribute("lineHeight"),10);for(let h=0;h<r.length;h++)e.pages.push({id:parseInt(r[h].getAttribute("id"),10)||0,file:r[h].getAttribute("file")});const l={};e.baseLineOffset=e.lineHeight-parseInt(i.getAttribute("base"),10);for(let h=0;h<o.length;h++){const c=o[h],u=parseInt(c.getAttribute("id"),10);let d=c.getAttribute("letter")??c.getAttribute("char")??String.fromCharCode(u);d==="space"&&(d=" "),l[u]=d,e.chars[d]={id:u,page:parseInt(c.getAttribute("page"),10)||0,x:parseInt(c.getAttribute("x"),10),y:parseInt(c.getAttribute("y"),10),width:parseInt(c.getAttribute("width"),10),height:parseInt(c.getAttribute("height"),10),xOffset:parseInt(c.getAttribute("xoffset"),10),yOffset:parseInt(c.getAttribute("yoffset"),10),xAdvance:parseInt(c.getAttribute("xadvance"),10),kerning:{}}}for(let h=0;h<a.length;h++){const c=parseInt(a[h].getAttribute("first"),10),u=parseInt(a[h].getAttribute("second"),10),d=parseInt(a[h].getAttribute("amount"),10);e.chars[l[u]].kerning[l[c]]=d}return e}},Ql={test(s){return typeof s=="string"&&s.includes("<font>")?Zl.test(_e.get().parseXML(s)):!1},parse(s){return Zl.parse(_e.get().parseXML(s))}},O0=[".xml",".fnt"],G0={extension:{type:D.CacheParser,name:"cacheBitmapFont"},test:s=>s instanceof od,getCacheableAssets(s,e){const t={};return s.forEach(i=>{t[i]=e,t[`${i}-bitmap`]=e}),t[`${e.fontFamily}-bitmap`]=e,t}},D0={extension:{type:D.LoadParser,priority:yi.Normal},name:"loadBitmapFont",test(s){return O0.includes(it.extname(s).toLowerCase())},async testParse(s){return po.test(s)||Ql.test(s)},async parse(s,e,t){const i=po.test(s)?po.parse(s):Ql.parse(s),{src:n}=e,{pages:r}=i,o=[],a=i.distanceField?{scaleMode:"linear",alphaMode:"premultiply-alpha-on-upload",autoGenerateMipmaps:!1,resolution:1}:{};for(let u=0;u<r.length;++u){const d=r[u].file;let _=it.join(it.dirname(n),d);_=Lo(_,n),o.push({src:_,data:a})}const l=await t.load(o),h=o.map(u=>l[u.src]);return new od({data:i,textures:h},n)},async load(s,e){return await(await _e.get().fetch(s)).text()},async unload(s,e,t){await Promise.all(s.pages.map(i=>t.unload(i.texture.source._sourceOrigin))),s.destroy()}};class N0{constructor(e,t=!1){this._loader=e,this._assetList=[],this._isLoading=!1,this._maxConcurrent=1,this.verbose=t}add(e){e.forEach(t=>{this._assetList.push(t)}),this.verbose&&console.log("[BackgroundLoader] assets: ",this._assetList),this._isActive&&!this._isLoading&&this._next()}async _next(){if(this._assetList.length&&this._isActive){this._isLoading=!0;const e=[],t=Math.min(this._assetList.length,this._maxConcurrent);for(let i=0;i<t;i++)e.push(this._assetList.pop());await this._loader.load(e),this._isLoading=!1,this._next()}}get active(){return this._isActive}set active(e){this._isActive!==e&&(this._isActive=e,e&&!this._isLoading&&this._next())}}const U0={extension:{type:D.CacheParser,name:"cacheTextureArray"},test:s=>Array.isArray(s)&&s.every(e=>e instanceof Y),getCacheableAssets:(s,e)=>{const t={};return s.forEach(i=>{e.forEach((n,r)=>{t[i+(r===0?"":r+1)]=n})}),t}};async function ad(s){if("Image"in globalThis)return new Promise(e=>{const t=new Image;t.onload=()=>{e(!0)},t.onerror=()=>{e(!1)},t.src=s});if("createImageBitmap"in globalThis&&"fetch"in globalThis){try{const e=await(await fetch(s)).blob();await createImageBitmap(e)}catch{return!1}return!0}return!1}const W0={extension:{type:D.DetectionParser,priority:1},test:async()=>ad("data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A="),add:async s=>[...s,"avif"],remove:async s=>s.filter(e=>e!=="avif")},Jl=["png","jpg","jpeg"],H0={extension:{type:D.DetectionParser,priority:-1},test:()=>Promise.resolve(!0),add:async s=>[...s,...Jl],remove:async s=>s.filter(e=>!Jl.includes(e))},q0="WorkerGlobalScope"in globalThis&&globalThis instanceof globalThis.WorkerGlobalScope;function Aa(s){return q0?!1:document.createElement("video").canPlayType(s)!==""}const V0={extension:{type:D.DetectionParser,priority:0},test:async()=>Aa("video/mp4"),add:async s=>[...s,"mp4","m4v"],remove:async s=>s.filter(e=>e!=="mp4"&&e!=="m4v")},X0={extension:{type:D.DetectionParser,priority:0},test:async()=>Aa("video/ogg"),add:async s=>[...s,"ogv"],remove:async s=>s.filter(e=>e!=="ogv")},Y0={extension:{type:D.DetectionParser,priority:0},test:async()=>Aa("video/webm"),add:async s=>[...s,"webm"],remove:async s=>s.filter(e=>e!=="webm")},j0={extension:{type:D.DetectionParser,priority:0},test:async()=>ad("data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA="),add:async s=>[...s,"webp"],remove:async s=>s.filter(e=>e!=="webp")};class K0{constructor(){this._parsers=[],this._parsersValidated=!1,this.parsers=new Proxy(this._parsers,{set:(e,t,i)=>(this._parsersValidated=!1,e[t]=i,!0)}),this.promiseCache={}}reset(){this._parsersValidated=!1,this.promiseCache={}}_getLoadPromiseAndParser(e,t){const i={promise:null,parser:null};return i.promise=(async()=>{let n=null,r=null;if(t.loadParser&&(r=this._parserHash[t.loadParser],r||be(`[Assets] specified load parser "${t.loadParser}" not found while loading ${e}`)),!r){for(let o=0;o<this.parsers.length;o++){const a=this.parsers[o];if(a.load&&a.test?.(e,t,this)){r=a;break}}if(!r)return be(`[Assets] ${e} could not be loaded as we don't know how to parse it, ensure the correct parser has been added`),null}n=await r.load(e,t,this),i.parser=r;for(let o=0;o<this.parsers.length;o++){const a=this.parsers[o];a.parse&&a.parse&&await a.testParse?.(n,t,this)&&(n=await a.parse(n,t,this)||n,i.parser=a)}return n})(),i}async load(e,t){this._parsersValidated||this._validateParsers();let i=0;const n={},r=qn(e),o=vt(e,h=>({alias:[h],src:h,data:{}})),a=o.length,l=o.map(async h=>{const c=it.toAbsolute(h.src);if(!n[h.src])try{this.promiseCache[c]||(this.promiseCache[c]=this._getLoadPromiseAndParser(c,h)),n[h.src]=await this.promiseCache[c].promise,t&&t(++i/a)}catch(u){throw delete this.promiseCache[c],delete n[h.src],new Error(`[Loader.load] Failed to load ${c}.
${u}`)}});return await Promise.all(l),r?n[o[0].src]:n}async unload(e){const i=vt(e,n=>({alias:[n],src:n})).map(async n=>{const r=it.toAbsolute(n.src),o=this.promiseCache[r];if(o){const a=await o.promise;delete this.promiseCache[r],await o.parser?.unload?.(a,n,this)}});await Promise.all(i)}_validateParsers(){this._parsersValidated=!0,this._parserHash=this._parsers.filter(e=>e.name).reduce((e,t)=>(t.name?e[t.name]&&be(`[Assets] loadParser name conflict "${t.name}"`):be("[Assets] loadParser should have a name"),{...e,[t.name]:t}),{})}}function vs(s,e){if(Array.isArray(e)){for(const t of e)if(s.startsWith(`data:${t}`))return!0;return!1}return s.startsWith(`data:${e}`)}function ks(s,e){const t=s.split("?")[0],i=it.extname(t).toLowerCase();return Array.isArray(e)?e.includes(i):i===e}const Z0=".json",Q0="application/json",J0={extension:{type:D.LoadParser,priority:yi.Low},name:"loadJson",test(s){return vs(s,Q0)||ks(s,Z0)},async load(s){return await(await _e.get().fetch(s)).json()}},e1=".txt",t1="text/plain",i1={name:"loadTxt",extension:{type:D.LoadParser,priority:yi.Low,name:"loadTxt"},test(s){return vs(s,t1)||ks(s,e1)},async load(s){return await(await _e.get().fetch(s)).text()}},s1=["normal","bold","100","200","300","400","500","600","700","800","900"],n1=[".ttf",".otf",".woff",".woff2"],r1=["font/ttf","font/otf","font/woff","font/woff2"],o1=/^(--|-?[A-Z_])[0-9A-Z_-]*$/i;function a1(s){const e=it.extname(s),n=it.basename(s,e).replace(/(-|_)/g," ").toLowerCase().split(" ").map(a=>a.charAt(0).toUpperCase()+a.slice(1));let r=n.length>0;for(const a of n)if(!a.match(o1)){r=!1;break}let o=n.join(" ");return r||(o=`"${o.replace(/[\\"]/g,"\\$&")}"`),o}const l1=/^[0-9A-Za-z%:/?#\[\]@!\$&'()\*\+,;=\-._~]*$/;function h1(s){return l1.test(s)?s:encodeURI(s)}const c1={extension:{type:D.LoadParser,priority:yi.Low},name:"loadWebFont",test(s){return vs(s,r1)||ks(s,n1)},async load(s,e){const t=_e.get().getFontFaceSet();if(t){const i=[],n=e.data?.family??a1(s),r=e.data?.weights?.filter(a=>s1.includes(a))??["normal"],o=e.data??{};for(let a=0;a<r.length;a++){const l=r[a],h=new FontFace(n,`url(${h1(s)})`,{...o,weight:l});await h.load(),t.add(h),i.push(h)}return de.set(`${n}-and-url`,{url:s,fontFaces:i}),i.length===1?i[0]:i}return be("[loadWebFont] FontFace API is not supported. Skipping loading font"),null},unload(s){(Array.isArray(s)?s:[s]).forEach(e=>{de.remove(`${e.family}-and-url`),_e.get().getFontFaceSet().delete(e)})}};function Sa(s,e=1){const t=xs.RETINA_PREFIX?.exec(s);return t?parseFloat(t[1]):e}function Ca(s,e,t){s.label=t,s._sourceOrigin=t;const i=new Y({source:s,label:t}),n=()=>{delete e.promiseCache[t],de.has(t)&&de.remove(t)};return i.source.once("destroy",()=>{e.promiseCache[t]&&(be("[Assets] A TextureSource managed by Assets was destroyed instead of unloaded! Use Assets.unload() instead of destroying the TextureSource."),n())}),i.once("destroy",()=>{s.destroyed||(be("[Assets] A Texture managed by Assets was destroyed instead of unloaded! Use Assets.unload() instead of destroying the Texture."),n())}),i}const u1=".svg",d1="image/svg+xml",_1={extension:{type:D.LoadParser,priority:yi.Low,name:"loadSVG"},name:"loadSVG",config:{crossOrigin:"anonymous",parseAsGraphicsContext:!1},test(s){return vs(s,d1)||ks(s,u1)},async load(s,e,t){return e.data?.parseAsGraphicsContext??this.config.parseAsGraphicsContext?g1(s):f1(s,e,t,this.config.crossOrigin)},unload(s){s.destroy(!0)}};async function f1(s,e,t,i){const r=await(await _e.get().fetch(s)).blob(),o=URL.createObjectURL(r),a=new Image;a.src=o,a.crossOrigin=i,await a.decode(),URL.revokeObjectURL(o);const l=document.createElement("canvas"),h=l.getContext("2d"),c=e.data?.resolution||Sa(s),u=e.data?.width??a.width,d=e.data?.height??a.height;l.width=u*c,l.height=d*c,h.drawImage(a,0,0,u*c,d*c);const{parseAsGraphicsContext:_,...f}=e.data??{},m=new ws({resource:l,alphaMode:"premultiply-alpha-on-upload",resolution:c,...f});return Ca(m,t,s)}async function g1(s){const t=await(await _e.get().fetch(s)).text(),i=new dt;return i.svg(t),i}const m1=`(function () {
    'use strict';

    const WHITE_PNG = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII=";
    async function checkImageBitmap() {
      try {
        if (typeof createImageBitmap !== "function")
          return false;
        const response = await fetch(WHITE_PNG);
        const imageBlob = await response.blob();
        const imageBitmap = await createImageBitmap(imageBlob);
        return imageBitmap.width === 1 && imageBitmap.height === 1;
      } catch (_e) {
        return false;
      }
    }
    void checkImageBitmap().then((result) => {
      self.postMessage(result);
    });

})();
`;let ls=null,Jo=class{constructor(){ls||(ls=URL.createObjectURL(new Blob([m1],{type:"application/javascript"}))),this.worker=new Worker(ls)}};Jo.revokeObjectURL=function(){ls&&(URL.revokeObjectURL(ls),ls=null)};const p1=`(function () {
    'use strict';

    async function loadImageBitmap(url, alphaMode) {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(\`[WorkerManager.loadImageBitmap] Failed to fetch \${url}: \${response.status} \${response.statusText}\`);
      }
      const imageBlob = await response.blob();
      return alphaMode === "premultiplied-alpha" ? createImageBitmap(imageBlob, { premultiplyAlpha: "none" }) : createImageBitmap(imageBlob);
    }
    self.onmessage = async (event) => {
      try {
        const imageBitmap = await loadImageBitmap(event.data.data[0], event.data.data[1]);
        self.postMessage({
          data: imageBitmap,
          uuid: event.data.uuid,
          id: event.data.id
        }, [imageBitmap]);
      } catch (e) {
        self.postMessage({
          error: e,
          uuid: event.data.uuid,
          id: event.data.id
        });
      }
    };

})();
`;let hs=null;class ld{constructor(){hs||(hs=URL.createObjectURL(new Blob([p1],{type:"application/javascript"}))),this.worker=new Worker(hs)}}ld.revokeObjectURL=function(){hs&&(URL.revokeObjectURL(hs),hs=null)};let eh=0,bo;class b1{constructor(){this._initialized=!1,this._createdWorkers=0,this._workerPool=[],this._queue=[],this._resolveHash={}}isImageBitmapSupported(){return this._isImageBitmapSupported!==void 0?this._isImageBitmapSupported:(this._isImageBitmapSupported=new Promise(e=>{const{worker:t}=new Jo;t.addEventListener("message",i=>{t.terminate(),Jo.revokeObjectURL(),e(i.data)})}),this._isImageBitmapSupported)}loadImageBitmap(e,t){return this._run("loadImageBitmap",[e,t?.data?.alphaMode])}async _initWorkers(){this._initialized||(this._initialized=!0)}_getWorker(){bo===void 0&&(bo=navigator.hardwareConcurrency||4);let e=this._workerPool.pop();return!e&&this._createdWorkers<bo&&(this._createdWorkers++,e=new ld().worker,e.addEventListener("message",t=>{this._complete(t.data),this._returnWorker(t.target),this._next()})),e}_returnWorker(e){this._workerPool.push(e)}_complete(e){e.error!==void 0?this._resolveHash[e.uuid].reject(e.error):this._resolveHash[e.uuid].resolve(e.data),this._resolveHash[e.uuid]=null}async _run(e,t){await this._initWorkers();const i=new Promise((n,r)=>{this._queue.push({id:e,arguments:t,resolve:n,reject:r})});return this._next(),i}_next(){if(!this._queue.length)return;const e=this._getWorker();if(!e)return;const t=this._queue.pop(),i=t.id;this._resolveHash[eh]={resolve:t.resolve,reject:t.reject},e.postMessage({data:t.arguments,uuid:eh++,id:i})}}const th=new b1,y1=[".jpeg",".jpg",".png",".webp",".avif"],w1=["image/jpeg","image/png","image/webp","image/avif"];async function x1(s,e){const t=await _e.get().fetch(s);if(!t.ok)throw new Error(`[loadImageBitmap] Failed to fetch ${s}: ${t.status} ${t.statusText}`);const i=await t.blob();return e?.data?.alphaMode==="premultiplied-alpha"?createImageBitmap(i,{premultiplyAlpha:"none"}):createImageBitmap(i)}const hd={name:"loadTextures",extension:{type:D.LoadParser,priority:yi.High,name:"loadTextures"},config:{preferWorkers:!0,preferCreateImageBitmap:!0,crossOrigin:"anonymous"},test(s){return vs(s,w1)||ks(s,y1)},async load(s,e,t){let i=null;globalThis.createImageBitmap&&this.config.preferCreateImageBitmap?this.config.preferWorkers&&await th.isImageBitmapSupported()?i=await th.loadImageBitmap(s,e):i=await x1(s,e):i=await new Promise((r,o)=>{i=new Image,i.crossOrigin=this.config.crossOrigin,i.src=s,i.complete?r(i):(i.onload=()=>{r(i)},i.onerror=o)});const n=new ws({resource:i,alphaMode:"premultiply-alpha-on-upload",resolution:e.data?.resolution||Sa(s),...e.data});return Ca(n,t,s)},unload(s){s.destroy(!0)}},cd=[".mp4",".m4v",".webm",".ogg",".ogv",".h264",".avi",".mov"],v1=cd.map(s=>`video/${s.substring(1)}`);function k1(s,e,t){t===void 0&&!e.startsWith("data:")?s.crossOrigin=S1(e):t!==!1&&(s.crossOrigin=typeof t=="string"?t:"anonymous")}function A1(s){return new Promise((e,t)=>{s.addEventListener("canplaythrough",i),s.addEventListener("error",n),s.load();function i(){r(),e()}function n(o){r(),t(o)}function r(){s.removeEventListener("canplaythrough",i),s.removeEventListener("error",n)}})}function S1(s,e=globalThis.location){if(s.startsWith("data:"))return"";e||(e=globalThis.location);const t=new URL(s,document.baseURI);return t.hostname!==e.hostname||t.port!==e.port||t.protocol!==e.protocol?"anonymous":""}const C1={name:"loadVideo",extension:{type:D.LoadParser,name:"loadVideo"},test(s){const e=vs(s,v1),t=ks(s,cd);return e||t},async load(s,e,t){const i={...Rn.defaultOptions,resolution:e.data?.resolution||Sa(s),alphaMode:e.data?.alphaMode||await du(),...e.data},n=document.createElement("video"),r={preload:i.autoLoad!==!1?"auto":void 0,"webkit-playsinline":i.playsinline!==!1?"":void 0,playsinline:i.playsinline!==!1?"":void 0,muted:i.muted===!0?"":void 0,loop:i.loop===!0?"":void 0,autoplay:i.autoPlay!==!1?"":void 0};Object.keys(r).forEach(l=>{const h=r[l];h!==void 0&&n.setAttribute(l,h)}),i.muted===!0&&(n.muted=!0),k1(n,s,i.crossorigin);const o=document.createElement("source");let a;if(s.startsWith("data:"))a=s.slice(5,s.indexOf(";"));else if(!s.startsWith("blob:")){const l=s.split("?")[0].slice(s.lastIndexOf(".")+1).toLowerCase();a=Rn.MIME_TYPES[l]||`video/${l}`}return o.src=s,a&&(o.type=a),new Promise(l=>{const h=async()=>{const c=new Rn({...i,resource:n});n.removeEventListener("canplay",h),e.data.preload&&await A1(n),l(Ca(c,t,s))};n.addEventListener("canplay",h),n.appendChild(o)})},unload(s){s.destroy(!0)}},ud={extension:{type:D.ResolveParser,name:"resolveTexture"},test:hd.test,parse:s=>({resolution:parseFloat(xs.RETINA_PREFIX.exec(s)?.[1]??"1"),format:s.split(".").pop(),src:s})},M1={extension:{type:D.ResolveParser,priority:-2,name:"resolveJson"},test:s=>xs.RETINA_PREFIX.test(s)&&s.endsWith(".json"),parse:ud.parse};class T1{constructor(){this._detections=[],this._initialized=!1,this.resolver=new xs,this.loader=new K0,this.cache=de,this._backgroundLoader=new N0(this.loader),this._backgroundLoader.active=!0,this.reset()}async init(e={}){if(this._initialized){be("[Assets]AssetManager already initialized, did you load before calling this Assets.init()?");return}if(this._initialized=!0,e.defaultSearchParams&&this.resolver.setDefaultSearchParams(e.defaultSearchParams),e.basePath&&(this.resolver.basePath=e.basePath),e.bundleIdentifier&&this.resolver.setBundleIdentifier(e.bundleIdentifier),e.manifest){let r=e.manifest;typeof r=="string"&&(r=await this.load(r)),this.resolver.addManifest(r)}const t=e.texturePreference?.resolution??1,i=typeof t=="number"?[t]:t,n=await this._detectFormats({preferredFormats:e.texturePreference?.format,skipDetections:e.skipDetections,detections:this._detections});this.resolver.prefer({params:{format:n,resolution:i}}),e.preferences&&this.setPreferences(e.preferences)}add(e){this.resolver.add(e)}async load(e,t){this._initialized||await this.init();const i=qn(e),n=vt(e).map(a=>{if(typeof a!="string"){const l=this.resolver.getAlias(a);return l.some(h=>!this.resolver.hasKey(h))&&this.add(a),Array.isArray(l)?l[0]:l}return this.resolver.hasKey(a)||this.add({alias:a,src:a}),a}),r=this.resolver.resolve(n),o=await this._mapLoadToResolve(r,t);return i?o[n[0]]:o}addBundle(e,t){this.resolver.addBundle(e,t)}async loadBundle(e,t){this._initialized||await this.init();let i=!1;typeof e=="string"&&(i=!0,e=[e]);const n=this.resolver.resolveBundle(e),r={},o=Object.keys(n);let a=0,l=0;const h=()=>{t?.(++a/l)},c=o.map(u=>{const d=n[u];return l+=Object.keys(d).length,this._mapLoadToResolve(d,h).then(_=>{r[u]=_})});return await Promise.all(c),i?r[e[0]]:r}async backgroundLoad(e){this._initialized||await this.init(),typeof e=="string"&&(e=[e]);const t=this.resolver.resolve(e);this._backgroundLoader.add(Object.values(t))}async backgroundLoadBundle(e){this._initialized||await this.init(),typeof e=="string"&&(e=[e]);const t=this.resolver.resolveBundle(e);Object.values(t).forEach(i=>{this._backgroundLoader.add(Object.values(i))})}reset(){this.resolver.reset(),this.loader.reset(),this.cache.reset(),this._initialized=!1}get(e){if(typeof e=="string")return de.get(e);const t={};for(let i=0;i<e.length;i++)t[i]=de.get(e[i]);return t}async _mapLoadToResolve(e,t){const i=[...new Set(Object.values(e))];this._backgroundLoader.active=!1;const n=await this.loader.load(i,t);this._backgroundLoader.active=!0;const r={};return i.forEach(o=>{const a=n[o.src],l=[o.src];o.alias&&l.push(...o.alias),l.forEach(h=>{r[h]=a}),de.set(l,a)}),r}async unload(e){this._initialized||await this.init();const t=vt(e).map(n=>typeof n!="string"?n.src:n),i=this.resolver.resolve(t);await this._unloadFromResolved(i)}async unloadBundle(e){this._initialized||await this.init(),e=vt(e);const t=this.resolver.resolveBundle(e),i=Object.keys(t).map(n=>this._unloadFromResolved(t[n]));await Promise.all(i)}async _unloadFromResolved(e){const t=Object.values(e);t.forEach(i=>{de.remove(i.src)}),await this.loader.unload(t)}async _detectFormats(e){let t=[];e.preferredFormats&&(t=Array.isArray(e.preferredFormats)?e.preferredFormats:[e.preferredFormats]);for(const i of e.detections)e.skipDetections||await i.test()?t=await i.add(t):e.skipDetections||(t=await i.remove(t));return t=t.filter((i,n)=>t.indexOf(i)===n),t}get detections(){return this._detections}setPreferences(e){this.loader.parsers.forEach(t=>{t.config&&Object.keys(t.config).filter(i=>i in e).forEach(i=>{t.config[i]=e[i]})})}}const Ei=new T1;De.handleByList(D.LoadParser,Ei.loader.parsers).handleByList(D.ResolveParser,Ei.resolver.parsers).handleByList(D.CacheParser,Ei.cache.parsers).handleByList(D.DetectionParser,Ei.detections);De.add(U0,H0,W0,j0,V0,X0,Y0,J0,i1,c1,_1,hd,C1,D0,G0,ud,M1);const ih={loader:D.LoadParser,resolver:D.ResolveParser,cache:D.CacheParser,detection:D.DetectionParser};De.handle(D.Asset,s=>{const e=s.ref;Object.entries(ih).filter(([t])=>!!e[t]).forEach(([t,i])=>De.add(Object.assign(e[t],{extension:e[t].extension??i})))},s=>{const e=s.ref;Object.keys(ih).filter(t=>!!e[t]).forEach(t=>De.remove(e[t]))});class we extends su{constructor(e){e instanceof dt&&(e={context:e});const{context:t,roundPixels:i,...n}=e||{};super({label:"Graphics",...n}),this.renderPipeId="graphics",t?this._context=t:this._context=this._ownedContext=new dt,this._context.on("update",this.onViewUpdate,this),this.allowChildren=!1,this.roundPixels=i??!1}set context(e){e!==this._context&&(this._context.off("update",this.onViewUpdate,this),this._context=e,this._context.on("update",this.onViewUpdate,this),this.onViewUpdate())}get context(){return this._context}get bounds(){return this._context.bounds}updateBounds(){}containsPoint(e){return this._context.containsPoint(e)}destroy(e){this._ownedContext&&!e?this._ownedContext.destroy(e):(e===!0||e?.context===!0)&&this._context.destroy(e),this._ownedContext=null,this._context=null,super.destroy(e)}_callContextMethod(e,t){return this.context[e](...t),this}setFillStyle(...e){return this._callContextMethod("setFillStyle",e)}setStrokeStyle(...e){return this._callContextMethod("setStrokeStyle",e)}fill(...e){return this._callContextMethod("fill",e)}stroke(...e){return this._callContextMethod("stroke",e)}texture(...e){return this._callContextMethod("texture",e)}beginPath(){return this._callContextMethod("beginPath",[])}cut(){return this._callContextMethod("cut",[])}arc(...e){return this._callContextMethod("arc",e)}arcTo(...e){return this._callContextMethod("arcTo",e)}arcToSvg(...e){return this._callContextMethod("arcToSvg",e)}bezierCurveTo(...e){return this._callContextMethod("bezierCurveTo",e)}closePath(){return this._callContextMethod("closePath",[])}ellipse(...e){return this._callContextMethod("ellipse",e)}circle(...e){return this._callContextMethod("circle",e)}path(...e){return this._callContextMethod("path",e)}lineTo(...e){return this._callContextMethod("lineTo",e)}moveTo(...e){return this._callContextMethod("moveTo",e)}quadraticCurveTo(...e){return this._callContextMethod("quadraticCurveTo",e)}rect(...e){return this._callContextMethod("rect",e)}roundRect(...e){return this._callContextMethod("roundRect",e)}poly(...e){return this._callContextMethod("poly",e)}regularPoly(...e){return this._callContextMethod("regularPoly",e)}roundPoly(...e){return this._callContextMethod("roundPoly",e)}roundShape(...e){return this._callContextMethod("roundShape",e)}filletRect(...e){return this._callContextMethod("filletRect",e)}chamferRect(...e){return this._callContextMethod("chamferRect",e)}star(...e){return this._callContextMethod("star",e)}svg(...e){return this._callContextMethod("svg",e)}restore(...e){return this._callContextMethod("restore",e)}save(){return this._callContextMethod("save",[])}getTransform(){return this.context.getTransform()}resetTransform(){return this._callContextMethod("resetTransform",[])}rotateTransform(...e){return this._callContextMethod("rotate",e)}scaleTransform(...e){return this._callContextMethod("scale",e)}setTransform(...e){return this._callContextMethod("setTransform",e)}transform(...e){return this._callContextMethod("transform",e)}translateTransform(...e){return this._callContextMethod("translate",e)}clear(){return this._callContextMethod("clear",[])}get fillStyle(){return this._context.fillStyle}set fillStyle(e){this._context.fillStyle=e}get strokeStyle(){return this._context.strokeStyle}set strokeStyle(e){this._context.strokeStyle=e}clone(e=!1){return e?new we(this._context.clone()):(this._ownedContext=null,new we(this._context))}lineStyle(e,t,i){K(J,"Graphics#lineStyle is no longer needed. Use Graphics#setStrokeStyle to set the stroke style.");const n={};return e&&(n.width=e),t&&(n.color=t),i&&(n.alpha=i),this.context.strokeStyle=n,this}beginFill(e,t){K(J,"Graphics#beginFill is no longer needed. Use Graphics#fill to fill the shape with the desired style.");const i={};return e!==void 0&&(i.color=e),t!==void 0&&(i.alpha=t),this.context.fillStyle=i,this}endFill(){K(J,"Graphics#endFill is no longer needed. Use Graphics#fill to fill the shape with the desired style."),this.context.fill();const e=this.context.strokeStyle;return(e.width!==dt.defaultStrokeStyle.width||e.color!==dt.defaultStrokeStyle.color||e.alpha!==dt.defaultStrokeStyle.alpha)&&this.context.stroke(),this}drawCircle(...e){return K(J,"Graphics#drawCircle has been renamed to Graphics#circle"),this._callContextMethod("circle",e)}drawEllipse(...e){return K(J,"Graphics#drawEllipse has been renamed to Graphics#ellipse"),this._callContextMethod("ellipse",e)}drawPolygon(...e){return K(J,"Graphics#drawPolygon has been renamed to Graphics#poly"),this._callContextMethod("poly",e)}drawRect(...e){return K(J,"Graphics#drawRect has been renamed to Graphics#rect"),this._callContextMethod("rect",e)}drawRoundedRect(...e){return K(J,"Graphics#drawRoundedRect has been renamed to Graphics#roundRect"),this._callContextMethod("roundRect",e)}drawStar(...e){return K(J,"Graphics#drawStar has been renamed to Graphics#star"),this._callContextMethod("star",e)}}class ft extends Y{static create(e){return new ft({source:new Pt(e)})}resize(e,t,i){return this.source.resize(e,t,i),this}}var ea=(s=>(s.CLAMP="clamp-to-edge",s.REPEAT="repeat",s.MIRRORED_REPEAT="mirror-repeat",s))(ea||{});new Proxy(ea,{get(s,e){return K(J,`DRAW_MODES.${e} is deprecated, use '${ea[e]}' instead`),s[e]}});var ta=(s=>(s.NEAREST="nearest",s.LINEAR="linear",s))(ta||{});const sh=new Proxy(ta,{get(s,e){return K(J,`DRAW_MODES.${e} is deprecated, use '${ta[e]}' instead`),s[e]}});De.add(jg,Kg);function Pn(s,e,t){return s<e?e:s>t?t:s}function Ln(s,e,t){return new Promise(i=>{if(!e){i(null);return}const n=e.getLocalBounds(),r=t?new se(t.x,t.y,t.width,t.height):new se(0,0,n.width,n.height);s.extract.canvas({target:e,resolution:1,frame:r}).toBlob?.(a=>{a||i(null),i(a)})})}const{SvelteComponent:P1,append:yo,attr:Zi,binding_callbacks:wo,detach:Es,element:Qi,flush:nh,init:z1,insert:Rs,listen:Fs,noop:rh,run_all:I1,safe_not_equal:E1,set_style:Le,space:oh}=window.__gradio__svelte__internal,{onMount:R1,tick:F1}=window.__gradio__svelte__internal;function B1(s){let e,t,i=`translate(${s[2][0]}px,${s[2][1]}px)`,n,r,o,a=`translateX(${s[1]*(s[8]?.width??1)}px)`,l,h,c,u=`translateX(${s[4]}px)`,d,_;return{c(){e=Qi("div"),t=Qi("div"),n=oh(),r=Qi("div"),o=Qi("div"),l=oh(),h=Qi("div"),c=Qi("div"),Zi(t,"class","marker svelte-9hvvq8"),Le(t,"transform",i),Le(t,"background",s[0]),Zi(e,"class","color-gradient svelte-9hvvq8"),Le(e,"--hue",s[3]),Zi(o,"class","opacity-marker svelte-9hvvq8"),Le(o,"background","white"),Le(o,"transform",a),Zi(r,"class","opacity-slider svelte-9hvvq8"),Le(r,"--color",s[0]==="auto"?"transparent":s[0]),Zi(c,"class","marker svelte-9hvvq8"),Le(c,"background","hsl("+s[3]+", 100%, 50%)"),Le(c,"transform",u),Zi(h,"class","hue-slider svelte-9hvvq8")},m(f,m){Rs(f,e,m),yo(e,t),s[14](e),Rs(f,n,m),Rs(f,r,m),yo(r,o),s[15](r),Rs(f,l,m),Rs(f,h,m),yo(h,c),s[16](h),d||(_=[Fs(window,"mousemove",s[11]),Fs(window,"mouseup",s[12]),Fs(e,"mousedown",s[10]),Fs(r,"mousedown",s[13]),Fs(h,"mousedown",s[9])],d=!0)},p(f,[m]){m&4&&i!==(i=`translate(${f[2][0]}px,${f[2][1]}px)`)&&Le(t,"transform",i),m&1&&Le(t,"background",f[0]),m&8&&Le(e,"--hue",f[3]),m&258&&a!==(a=`translateX(${f[1]*(f[8]?.width??1)}px)`)&&Le(o,"transform",a),m&1&&Le(r,"--color",f[0]==="auto"?"transparent":f[0]),m&8&&Le(c,"background","hsl("+f[3]+", 100%, 50%)"),m&16&&u!==(u=`translateX(${f[4]}px)`)&&Le(c,"transform",u)},i:rh,o:rh,d(f){f&&(Es(e),Es(n),Es(r),Es(l),Es(h)),s[14](null),s[15](null),s[16](null),d=!1,I1(_)}}}function ah(s){const e=s.s,t=s.v;let i=e*t;const n=s.h/60;let r=i*(1-Math.abs(n%2-1));const o=t-i;i=i+o,r=r+o;const a=Math.floor(n)%6,l=[i,r,o,o,r,i][a],h=[r,i,i,r,o,o][a],c=[o,o,r,i,i,r][a];return`rgba(${l*255}, ${h*255}, ${c*255}, ${s.a})`}function L1(s,e,t){let{color:i="rgb(255, 255, 255)"}=e,n=[0,0],r=null,o=!1,a=[0,0],l=0,h=0,c=null,u=!1,d=!1;function _(B){c=B.currentTarget.getBoundingClientRect(),u=!0,f(B.clientX)}function f(B){if(!c)return;const H=Pn(B-c.left,0,c.width);t(4,h=H);const F=H/c.width*360;t(3,l=F),t(0,i=ah({h:F,s:a[0],v:a[1],a:1}))}function m(B,H){if(!r)return;const F=Pn(B-r.left,0,r.width),O=Pn(H-r.top,0,r.height);t(2,n=[F,O]);const Ie={h:l*1,s:F/r.width,v:1-O/r.height,a:1};a=[Ie.s,Ie.v],t(0,i=ah(Ie))}function g(B){o=!0,r=B.currentTarget.getBoundingClientRect(),m(B.clientX,B.clientY)}function p(B){o&&m(B.clientX,B.clientY),u&&f(B.clientX),d&&M(B.clientX)}function y(){o&&(o=!1),u&&(u=!1),d&&(d=!1)}async function b(B){if(o||u||(await F1(),!B))return;r||(r=w.getBoundingClientRect()),c||(c=A.getBoundingClientRect());const H=tt(B).toHsv(),F=H.s*r.width,O=(1-H.v)*r.height;t(2,n=[F,O]),a=[H.s,H.v],t(3,l=H.h),t(4,h=H.h/360*c.width)}let w,A,{opacity:v=1}=e,S;function R(B){t(8,C=B.currentTarget.getBoundingClientRect()),d=!0,M(B.clientX)}let C=null;function M(B){if(!C)return;const H=Pn(B-C.left,0,C.width);t(1,v=H/C.width)}R1(()=>{t(8,C=S.getBoundingClientRect())});function k(B){wo[B?"unshift":"push"](()=>{w=B,t(5,w)})}function P(B){wo[B?"unshift":"push"](()=>{S=B,t(7,S)})}function L(B){wo[B?"unshift":"push"](()=>{A=B,t(6,A)})}return s.$$set=B=>{"color"in B&&t(0,i=B.color),"opacity"in B&&t(1,v=B.opacity)},s.$$.update=()=>{s.$$.dirty&1&&b(i)},[i,v,n,l,h,w,A,S,C,_,g,p,y,R,k,P,L]}class $1 extends P1{constructor(e){super(),z1(this,e,L1,B1,E1,{color:0,opacity:1})}get color(){return this.$$.ctx[0]}set color(e){this.$$set({color:e}),nh()}get opacity(){return this.$$.ctx[1]}set opacity(e){this.$$set({opacity:e}),nh()}}const{SvelteComponent:O1,append:cs,attr:_i,check_outros:dd,create_component:G1,destroy_component:D1,destroy_each:_d,detach:gs,element:fi,ensure_array_like:Xn,flush:Ji,group_outros:fd,init:N1,insert:ms,listen:Ma,mount_component:U1,safe_not_equal:W1,set_style:hi,space:Yn,toggle_class:pt,transition_in:Li,transition_out:on}=window.__gradio__svelte__internal,{createEventDispatcher:H1}=window.__gradio__svelte__internal;function lh(s,e,t){const i=s.slice();i[17]=e[t],i[21]=t;const n=an(i[17]);i[18]=n;const r=i[6](i[17]);return i[19]=r,i}function hh(s,e,t){const i=s.slice();i[22]=e[t],i[21]=t;const n=an(i[22]);i[18]=n;const r=i[6](i[22]);return i[19]=r,i}function ch(s){let e;return{c(){e=fi("span"),_i(e,"class","svelte-1ngpqve"),pt(e,"lg",s[2])},m(t,i){ms(t,e,i)},p(t,i){i&4&&pt(e,"lg",t[2])},d(t){t&&gs(e)}}}function uh(s){let e,t,i,n,r,o,a,l=s[0]&&dh(s),h=Xn(s[2]),c=[];for(let u=0;u<h.length;u+=1)c[u]=_h(hh(s,h,u));return{c(){e=fi("div"),l&&l.c(),t=Yn();for(let u=0;u<c.length;u+=1)c[u].c();i=Yn(),n=fi("button"),_i(n,"class","color colorpicker svelte-1ngpqve"),pt(n,"hidden",!s[0]),_i(e,"class","swatch svelte-1ngpqve")},m(u,d){ms(u,e,d),l&&l.m(e,null),cs(e,t);for(let _=0;_<c.length;_+=1)c[_]&&c[_].m(e,null);cs(e,i),cs(e,n),r=!0,o||(a=Ma(n,"click",s[8]),o=!0)},p(u,d){if(u[0]?l?(l.p(u,d),d&1&&Li(l,1)):(l=dh(u),l.c(),Li(l,1),l.m(e,t)):l&&(fd(),on(l,1,1,()=>{l=null}),dd()),d&206){h=Xn(u[2]);let _;for(_=0;_<h.length;_+=1){const f=hh(u,h,_);c[_]?c[_].p(f,d):(c[_]=_h(f),c[_].c(),c[_].m(e,i))}for(;_<c.length;_+=1)c[_].d(1);c.length=h.length}(!r||d&1)&&pt(n,"hidden",!u[0])},i(u){r||(Li(l),r=!0)},o(u){on(l),r=!1},d(u){u&&gs(e),l&&l.d(),_d(c,u),o=!1,a()}}}function dh(s){let e,t;return e=new st({props:{Icon:$c,roundedness:"very",background:an(s[1]),color:"white"}}),e.$on("click",s[12]),{c(){G1(e.$$.fragment)},m(i,n){U1(e,i,n),t=!0},p(i,n){const r={};n&2&&(r.background=an(i[1])),e.$set(r)},i(i){t||(Li(e.$$.fragment,i),t=!0)},o(i){on(e.$$.fragment,i),t=!1},d(i){D1(e,i)}}}function _h(s){let e,t,i;function n(){return s[13](s[21],s[18],s[19])}return{c(){e=fi("button"),_i(e,"class","color svelte-1ngpqve"),hi(e,"background-color",s[18]),pt(e,"empty",s[22]===null),pt(e,"selected",`${s[18]}-${s[19]}`==`${s[1]}-${s[3]}`),hi(e,"opacity",s[19])},m(r,o){ms(r,e,o),t||(i=Ma(e,"click",n),t=!0)},p(r,o){s=r,o&4&&hi(e,"background-color",s[18]),o&4&&pt(e,"empty",s[22]===null),o&78&&pt(e,"selected",`${s[18]}-${s[19]}`==`${s[1]}-${s[3]}`);const a=o&4;(o&4||a)&&hi(e,"opacity",s[19])},d(r){r&&gs(e),t=!1,i()}}}function fh(s){let e,t,i;function n(){return s[14](s[21],s[18],s[19])}return{c(){e=fi("button"),_i(e,"class","color svelte-1ngpqve"),hi(e,"background-color",s[18]),pt(e,"empty",s[17]===null),pt(e,"selected",`${s[18]}-${s[19]}`==`${s[1]}-${s[3]}`),hi(e,"opacity",s[19])},m(r,o){ms(r,e,o),t||(i=Ma(e,"click",n),t=!0)},p(r,o){s=r,o&16&&hi(e,"background-color",s[18]),o&16&&pt(e,"empty",s[17]===null),o&90&&pt(e,"selected",`${s[18]}-${s[19]}`==`${s[1]}-${s[3]}`);const a=o&16;(o&16||a)&&hi(e,"opacity",s[19])},d(r){r&&gs(e),t=!1,i()}}}function q1(s){let e,t,i,n,r,o,a=!s[0]&&ch(s),l=s[2]&&uh(s),h=Xn(s[4]),c=[];for(let u=0;u<h.length;u+=1)c[u]=fh(lh(s,h,u));return{c(){a&&a.c(),e=Yn(),t=fi("div"),i=fi("div"),l&&l.c(),n=Yn(),r=fi("menu");for(let u=0;u<c.length;u+=1)c[u].c();_i(r,"class","swatch svelte-1ngpqve"),_i(i,"class","swatch-container svelte-1ngpqve"),_i(t,"class","swatch-wrap svelte-1ngpqve")},m(u,d){a&&a.m(u,d),ms(u,e,d),ms(u,t,d),cs(t,i),l&&l.m(i,null),cs(i,n),cs(i,r);for(let _=0;_<c.length;_+=1)c[_]&&c[_].m(r,null);o=!0},p(u,[d]){if(u[0]?a&&(a.d(1),a=null):a?a.p(u,d):(a=ch(u),a.c(),a.m(e.parentNode,e)),u[2]?l?(l.p(u,d),d&4&&Li(l,1)):(l=uh(u),l.c(),Li(l,1),l.m(i,n)):l&&(fd(),on(l,1,1,()=>{l=null}),dd()),d&218){h=Xn(u[4]);let _;for(_=0;_<h.length;_+=1){const f=lh(u,h,_);c[_]?c[_].p(f,d):(c[_]=fh(f),c[_].c(),c[_].m(r,null))}for(;_<c.length;_+=1)c[_].d(1);c.length=h.length}},i(u){o||(Li(l),o=!0)},o(u){on(l),o=!1},d(u){u&&(gs(e),gs(t)),a&&a.d(u),l&&l.d(),_d(c,u)}}}function an(s){return Array.isArray(s)?s[0]:s}function V1(s,e,t){let i,n,{selected_color:r}=e,{colors:o}=e,{user_colors:a=[]}=e,{show_empty:l=!1}=e,{current_mode:h="hex"}=e,{color_picker:c=!1}=e;const u=H1();function d(b){return Array.isArray(b)?b[1]:tt(b).getAlpha()}function _(b,w){const A=an(b);return w==="hex"?tt(A).toHexString():w==="rgb"?tt(A).toRgbString():tt(A).toHslString()}`${o.findIndex(b=>_(b,h)===_(r,h))}`;function f(b,w){`${b}${w.index}`,u(b,w)}function m(){u("select",{index:null,color:r}),t(0,c=!c)}const g=()=>u("add_color"),p=(b,w,A)=>f("edit",{index:b,color:w,opacity:A}),y=(b,w,A)=>f("select",{index:b,color:w,opacity:A});return s.$$set=b=>{"selected_color"in b&&t(1,r=b.selected_color),"colors"in b&&t(9,o=b.colors),"user_colors"in b&&t(2,a=b.user_colors),"show_empty"in b&&t(10,l=b.show_empty),"current_mode"in b&&t(11,h=b.current_mode),"color_picker"in b&&t(0,c=b.color_picker)},s.$$.update=()=>{s.$$.dirty&1536&&t(4,i=l?o:o.filter(b=>b)),s.$$.dirty&2&&t(3,n=d(r))},[c,r,a,n,i,u,d,f,m,o,l,h,g,p,y]}class X1 extends O1{constructor(e){super(),N1(this,e,V1,q1,W1,{selected_color:1,colors:9,user_colors:2,show_empty:10,current_mode:11,color_picker:0})}get selected_color(){return this.$$.ctx[1]}set selected_color(e){this.$$set({selected_color:e}),Ji()}get colors(){return this.$$.ctx[9]}set colors(e){this.$$set({colors:e}),Ji()}get user_colors(){return this.$$.ctx[2]}set user_colors(e){this.$$set({user_colors:e}),Ji()}get show_empty(){return this.$$.ctx[10]}set show_empty(e){this.$$set({show_empty:e}),Ji()}get current_mode(){return this.$$.ctx[11]}set current_mode(e){this.$$set({current_mode:e}),Ji()}get color_picker(){return this.$$.ctx[0]}set color_picker(e){this.$$set({color_picker:e}),Ji()}}const{SvelteComponent:Y1,append:Wt,attr:ri,create_component:j1,destroy_component:K1,destroy_each:Z1,detach:gd,element:oi,ensure_array_like:gh,flush:mh,init:Q1,insert:md,listen:$n,mount_component:J1,run_all:ey,safe_not_equal:ty,set_style:ph,space:xo,toggle_class:bh,transition_in:pd,transition_out:bd}=window.__gradio__svelte__internal,{createEventDispatcher:iy}=window.__gradio__svelte__internal;function yh(s,e,t){const i=s.slice();return i[11]=e[t][0],i[12]=e[t][1],i}function sy(s){let e,t;return e=new g_({}),{c(){j1(e.$$.fragment)},m(i,n){J1(e,i,n),t=!0},i(i){t||(pd(e.$$.fragment,i),t=!0)},o(i){bd(e.$$.fragment,i),t=!1},d(i){K1(e,i)}}}function wh(s){let e,t,i;function n(){return s[8](s[12])}return{c(){e=oi("button"),e.textContent=`${s[11]}`,ri(e,"class","button svelte-w84deg"),bh(e,"active",s[1]===s[12])},m(r,o){md(r,e,o),t||(i=$n(e,"click",n),t=!0)},p(r,o){s=r,o&10&&bh(e,"active",s[1]===s[12])},d(r){r&&gd(e),t=!1,i()}}}function ny(s){let e,t,i,n,r,o,a,l,h,c,u,d,_,f=s[5]&&sy(),m=gh(s[3]),g=[];for(let p=0;p<m.length;p+=1)g[p]=wh(yh(s,m,p));return{c(){e=oi("div"),t=oi("button"),i=xo(),n=oi("div"),r=oi("div"),o=oi("input"),a=xo(),l=oi("button"),f&&f.c(),h=xo(),c=oi("div");for(let p=0;p<g.length;p+=1)g[p].c();ri(t,"class","swatch svelte-w84deg"),ph(t,"background",s[0]),ri(o,"type","text"),o.value=s[2],ri(o,"class","svelte-w84deg"),ri(l,"class","eyedropper svelte-w84deg"),ri(r,"class","input-wrap svelte-w84deg"),ri(c,"class","buttons svelte-w84deg"),ri(e,"class","input svelte-w84deg")},m(p,y){md(p,e,y),Wt(e,t),Wt(e,i),Wt(e,n),Wt(n,r),Wt(r,o),Wt(r,a),Wt(r,l),f&&f.m(l,null),Wt(n,h),Wt(n,c);for(let b=0;b<g.length;b+=1)g[b]&&g[b].m(c,null);u=!0,d||(_=[$n(t,"click",s[6]),$n(o,"change",s[7]),$n(l,"click",s[4])],d=!0)},p(p,[y]){if(y&1&&ph(t,"background",p[0]),(!u||y&4&&o.value!==p[2])&&(o.value=p[2]),y&10){m=gh(p[3]);let b;for(b=0;b<m.length;b+=1){const w=yh(p,m,b);g[b]?g[b].p(w,y):(g[b]=wh(w),g[b].c(),g[b].m(c,null))}for(;b<g.length;b+=1)g[b].d(1);g.length=m.length}},i(p){u||(pd(f),u=!0)},o(p){bd(f),u=!1},d(p){p&&gd(e),f&&f.d(),Z1(g,p),d=!1,ey(_)}}}function ry(s,e,t){let i,{color:n}=e,{current_mode:r="hex"}=e;const o=iy(),a=[["Hex","hex"],["RGB","rgb"],["HSL","hsl"]];function l(f,m){return m==="hex"?tt(f).toHexString():m==="rgb"?tt(f).toRgbString():tt(f).toHslString()}function h(){new EyeDropper().open().then(m=>{t(0,n=m.sRGBHex)})}const c=!!window.EyeDropper;function u(){o("selected",i),o("close")}const d=f=>t(0,n=f.currentTarget.value),_=f=>t(1,r=f);return s.$$set=f=>{"color"in f&&t(0,n=f.color),"current_mode"in f&&t(1,r=f.current_mode)},s.$$.update=()=>{s.$$.dirty&3&&t(2,i=l(n,r)),s.$$.dirty&4&&i&&o("selected",i)},[n,r,i,a,h,c,u,d,_]}class oy extends Y1{constructor(e){super(),Q1(this,e,ry,ny,ty,{color:0,current_mode:1})}get color(){return this.$$.ctx[0]}set color(e){this.$$set({color:e}),mh()}get current_mode(){return this.$$.ctx[1]}set current_mode(e){this.$$set({current_mode:e}),mh()}}const{SvelteComponent:ay,action_destroyer:ly,append:vo,attr:Ht,create_component:hy,destroy_component:cy,detach:uy,element:ko,flush:Ao,init:dy,insert:_y,listen:xh,mount_component:fy,run_all:gy,safe_not_equal:my,set_input_value:vh,space:py,to_number:by,transition_in:yy,transition_out:wy}=window.__gradio__svelte__internal,{createEventDispatcher:xy}=window.__gradio__svelte__internal;function vy(s){let e,t,i,n,r,o,a,l;return i=new K_({}),{c(){e=ko("div"),t=ko("span"),hy(i.$$.fragment),n=py(),r=ko("input"),Ht(t,"class","svelte-1ktybyw"),Ht(r,"type","range"),Ht(r,"min",s[1]),Ht(r,"max",s[2]),Ht(r,"step",1),Ht(r,"class","svelte-1ktybyw"),Ht(e,"class","wrap svelte-1ktybyw")},m(h,c){_y(h,e,c),vo(e,t),fy(i,t,null),vo(e,n),vo(e,r),vh(r,s[0]),o=!0,a||(l=[xh(r,"change",s[4]),xh(r,"input",s[4]),ly(da.call(null,e,s[5]))],a=!0)},p(h,[c]){(!o||c&2)&&Ht(r,"min",h[1]),(!o||c&4)&&Ht(r,"max",h[2]),c&1&&vh(r,h[0])},i(h){o||(yy(i.$$.fragment,h),o=!0)},o(h){wy(i.$$.fragment,h),o=!1},d(h){h&&uy(e),cy(i),a=!1,gy(l)}}}function ky(s,e,t){let{selected_size:i}=e,{min:n}=e,{max:r}=e;const o=xy();function a(){i=by(this.value),t(0,i)}const l=()=>o("click_outside");return s.$$set=h=>{"selected_size"in h&&t(0,i=h.selected_size),"min"in h&&t(1,n=h.min),"max"in h&&t(2,r=h.max)},[i,n,r,o,a,l]}class Ay extends ay{constructor(e){super(),dy(this,e,ky,vy,my,{selected_size:0,min:1,max:2})}get selected_size(){return this.$$.ctx[0]}set selected_size(e){this.$$set({selected_size:e}),Ao()}get min(){return this.$$.ctx[1]}set min(e){this.$$set({min:e}),Ao()}get max(){return this.$$.ctx[2]}set max(e){this.$$set({max:e}),Ao()}}const{SvelteComponent:Sy,action_destroyer:Cy,add_flush_callback:Ys,add_render_callback:My,append:kh,attr:Ty,bind:js,binding_callbacks:Ks,check_outros:On,create_component:jn,destroy_component:Kn,detach:Ta,element:Py,empty:zy,flush:Bt,group_outros:Gn,init:Iy,insert:Pa,listen:Ey,mount_component:Zn,run_all:Ry,safe_not_equal:Fy,space:ia,toggle_class:ii,transition_in:Re,transition_out:_t}=window.__gradio__svelte__internal,{createEventDispatcher:By,tick:z2}=window.__gradio__svelte__internal;function Ah(s){let e,t,i=s[9]&&Sh(s);return{c(){i&&i.c(),e=zy()},m(n,r){i&&i.m(n,r),Pa(n,e,r),t=!0},p(n,r){n[9]?i?(i.p(n,r),r[0]&512&&Re(i,1)):(i=Sh(n),i.c(),Re(i,1),i.m(e.parentNode,e)):i&&(Gn(),_t(i,1,1,()=>{i=null}),On())},i(n){t||(Re(i),t=!0)},o(n){_t(i),t=!1},d(n){n&&Ta(e),i&&i.d(n)}}}function Sh(s){let e,t,i,n,r,o,a;function l(_){s[19](_)}function h(_){s[20](_)}let c={};s[0]!==void 0&&(c.color=s[0]),s[3]!==void 0&&(c.opacity=s[3]),e=new $1({props:c}),Ks.push(()=>js(e,"color",l)),Ks.push(()=>js(e,"opacity",h));function u(_){s[21](_)}let d={color:s[0]};return s[10]!==void 0&&(d.current_mode=s[10]),r=new oy({props:d}),Ks.push(()=>js(r,"current_mode",u)),r.$on("close",s[22]),r.$on("selected",s[23]),{c(){jn(e.$$.fragment),n=ia(),jn(r.$$.fragment)},m(_,f){Zn(e,_,f),Pa(_,n,f),Zn(r,_,f),a=!0},p(_,f){const m={};!t&&f[0]&1&&(t=!0,m.color=_[0],Ys(()=>t=!1)),!i&&f[0]&8&&(i=!0,m.opacity=_[3],Ys(()=>i=!1)),e.$set(m);const g={};f[0]&1&&(g.color=_[0]),!o&&f[0]&1024&&(o=!0,g.current_mode=_[10],Ys(()=>o=!1)),r.$set(g)},i(_){a||(Re(e.$$.fragment,_),Re(r.$$.fragment,_),a=!0)},o(_){_t(e.$$.fragment,_),_t(r.$$.fragment,_),a=!1},d(_){_&&Ta(n),Kn(e,_),Kn(r,_)}}}function Ch(s){let e,t,i;function n(o){s[24](o)}let r={colors:s[4],user_colors:s[5]==="defaults"?s[2]:null,selected_color:s[0],current_mode:s[10]};return s[9]!==void 0&&(r.color_picker=s[9]),e=new X1({props:r}),Ks.push(()=>js(e,"color_picker",n)),e.$on("select",s[25]),e.$on("edit",s[26]),e.$on("add_color",s[16]),{c(){jn(e.$$.fragment)},m(o,a){Zn(e,o,a),i=!0},p(o,a){const l={};a[0]&16&&(l.colors=o[4]),a[0]&36&&(l.user_colors=o[5]==="defaults"?o[2]:null),a[0]&1&&(l.selected_color=o[0]),a[0]&1024&&(l.current_mode=o[10]),!t&&a[0]&512&&(t=!0,l.color_picker=o[9],Ys(()=>t=!1)),e.$set(l)},i(o){i||(Re(e.$$.fragment,o),i=!0)},o(o){_t(e.$$.fragment,o),i=!1},d(o){Kn(e,o)}}}function Mh(s){let e,t,i;function n(o){s[27](o)}let r={max:100,min:1};return s[1]!==void 0&&(r.selected_size=s[1]),e=new Ay({props:r}),Ks.push(()=>js(e,"selected_size",n)),{c(){jn(e.$$.fragment)},m(o,a){Zn(e,o,a),i=!0},p(o,a){const l={};!t&&a[0]&2&&(t=!0,l.selected_size=o[1],Ys(()=>t=!1)),e.$set(l)},i(o){i||(Re(e.$$.fragment,o),i=!0)},o(o){_t(e.$$.fragment,o),i=!1},d(o){Kn(e,o)}}}function Ly(s){let e,t,i,n,r,o;My(s[18]);let a=s[5]==="defaults"&&Ah(s),l=s[6]&&Ch(s),h=s[7]&&Mh(s);return{c(){e=Py("div"),a&&a.c(),t=ia(),l&&l.c(),i=ia(),h&&h.c(),Ty(e,"class","wrap svelte-qigu2l"),ii(e,"padded",!s[9]),ii(e,"color_picker",s[9]),ii(e,"size_picker",s[7]&&s[8]==="brush"),ii(e,"eraser_picker",s[8]==="eraser")},m(c,u){Pa(c,e,u),a&&a.m(e,null),kh(e,t),l&&l.m(e,null),kh(e,i),h&&h.m(e,null),n=!0,r||(o=[Ey(window,"resize",s[18]),Cy(da.call(null,e,s[28]))],r=!0)},p(c,u){c[5]==="defaults"?a?(a.p(c,u),u[0]&32&&Re(a,1)):(a=Ah(c),a.c(),Re(a,1),a.m(e,t)):a&&(Gn(),_t(a,1,1,()=>{a=null}),On()),c[6]?l?(l.p(c,u),u[0]&64&&Re(l,1)):(l=Ch(c),l.c(),Re(l,1),l.m(e,i)):l&&(Gn(),_t(l,1,1,()=>{l=null}),On()),c[7]?h?(h.p(c,u),u[0]&128&&Re(h,1)):(h=Mh(c),h.c(),Re(h,1),h.m(e,null)):h&&(Gn(),_t(h,1,1,()=>{h=null}),On()),(!n||u[0]&512)&&ii(e,"padded",!c[9]),(!n||u[0]&512)&&ii(e,"color_picker",c[9]),(!n||u[0]&384)&&ii(e,"size_picker",c[7]&&c[8]==="brush"),(!n||u[0]&256)&&ii(e,"eraser_picker",c[8]==="eraser")},i(c){n||(Re(a),Re(l),Re(h),n=!0)},o(c){_t(a),_t(l),_t(h),n=!1},d(c){c&&Ta(e),a&&a.d(),l&&l.d(),h&&h.d(),r=!1,Ry(o)}}}function $y(s,e){let t;return function(...i){clearTimeout(t),t=setTimeout(()=>s(...i),e)}}function Oy(s,e,t){let{colors:i}=e,{selected_color:n}=e,{color_mode:r=void 0}=e,{recent_colors:o=[]}=e,{selected_size:a}=e,{selected_opacity:l}=e,{show_swatch:h}=e,{show_size:c}=e,{mode:u="brush"}=e,d=!1,_="hex",f=null;const m=By();function g({index:E,color:$,opacity:ye},Pe){Pe==="user"&&!$&&(f=E,t(9,d=!0)),$&&(t(0,n=$),ye!==void 0&&t(3,l=ye),Pe==="core"&&t(9,d=!1))}function p(E){f!==null&&t(2,o[f]=E,o)}let y=0,b=0,{preview:w=!1}=e;function A(){w||t(17,w=!0),S()}function v(){t(17,w=!1)}const S=$y(v,1e3);function R(E){t(0,n=E)}function C(){o.length>=5&&o.pop(),!o.some(E=>Array.isArray(E)?E[0]===n&&E[1]===l:E===n)&&(o.push([n,l]),t(2,o))}function M(){t(12,b=window.innerHeight),t(11,y=window.innerWidth)}function k(E){n=E,t(0,n)}function P(E){l=E,t(3,l)}function L(E){_=E,t(10,_)}const B=()=>t(9,d=!1),H=({detail:E})=>R(E);function F(E){d=E,t(9,d)}const O=({detail:E})=>g(E,"core"),Ie=({detail:E})=>g(E,"user");function q(E){a=E,t(1,a)}const te=()=>m("click_outside");return s.$$set=E=>{"colors"in E&&t(4,i=E.colors),"selected_color"in E&&t(0,n=E.selected_color),"color_mode"in E&&t(5,r=E.color_mode),"recent_colors"in E&&t(2,o=E.recent_colors),"selected_size"in E&&t(1,a=E.selected_size),"selected_opacity"in E&&t(3,l=E.selected_opacity),"show_swatch"in E&&t(6,h=E.show_swatch),"show_size"in E&&t(7,c=E.show_size),"mode"in E&&t(8,u=E.mode),"preview"in E&&t(17,w=E.preview)},s.$$.update=()=>{s.$$.dirty[0]&1&&p(n),s.$$.dirty[0]&3&&A()},[n,a,o,l,i,r,h,c,u,d,_,y,b,m,g,R,C,w,M,k,P,L,B,H,F,O,Ie,q,te]}class yd extends Sy{constructor(e){super(),Iy(this,e,Oy,Ly,Fy,{colors:4,selected_color:0,color_mode:5,recent_colors:2,selected_size:1,selected_opacity:3,show_swatch:6,show_size:7,mode:8,preview:17},null,[-1,-1])}get colors(){return this.$$.ctx[4]}set colors(e){this.$$set({colors:e}),Bt()}get selected_color(){return this.$$.ctx[0]}set selected_color(e){this.$$set({selected_color:e}),Bt()}get color_mode(){return this.$$.ctx[5]}set color_mode(e){this.$$set({color_mode:e}),Bt()}get recent_colors(){return this.$$.ctx[2]}set recent_colors(e){this.$$set({recent_colors:e}),Bt()}get selected_size(){return this.$$.ctx[1]}set selected_size(e){this.$$set({selected_size:e}),Bt()}get selected_opacity(){return this.$$.ctx[3]}set selected_opacity(e){this.$$set({selected_opacity:e}),Bt()}get show_swatch(){return this.$$.ctx[6]}set show_swatch(e){this.$$set({show_swatch:e}),Bt()}get show_size(){return this.$$.ctx[7]}set show_size(e){this.$$set({show_size:e}),Bt()}get mode(){return this.$$.ctx[8]}set mode(e){this.$$set({mode:e}),Bt()}get preview(){return this.$$.ctx[17]}set preview(e){this.$$set({preview:e}),Bt()}}const{SvelteComponent:Gy,add_flush_callback:ci,append:si,attr:So,bind:ui,binding_callbacks:di,check_outros:He,create_component:nt,destroy_component:rt,detach:Gt,element:Co,empty:lr,flush:$e,group_outros:qe,init:Dy,insert:Dt,mount_component:ot,safe_not_equal:Ny,space:xt,toggle_class:Th,transition_in:N,transition_out:V}=window.__gradio__svelte__internal,{createEventDispatcher:Uy}=window.__gradio__svelte__internal;function Ph(s){let e,t;return e=new st({props:{Icon:Bc,label:"Image",highlight:s[8]==="image",size:"medium",padded:!1,transparent:!0}}),e.$on("click",s[23]),{c(){nt(e.$$.fragment)},m(i,n){ot(e,i,n),t=!0},p(i,n){const r={};n[0]&256&&(r.highlight=i[8]==="image"),e.$set(r)},i(i){t||(N(e.$$.fragment,i),t=!0)},o(i){V(e.$$.fragment,i),t=!1},d(i){rt(e,i)}}}function zh(s){let e,t;return e=new st({props:{Icon:U_,label:"Brush",highlight:s[8]==="draw",size:"medium",padded:!1,transparent:!0}}),e.$on("click",s[24]),{c(){nt(e.$$.fragment)},m(i,n){ot(e,i,n),t=!0},p(i,n){const r={};n[0]&256&&(r.highlight=i[8]==="draw"),e.$set(r)},i(i){t||(N(e.$$.fragment,i),t=!0)},o(i){V(e.$$.fragment,i),t=!1},d(i){rt(e,i)}}}function Ih(s){let e,t;return e=new st({props:{Icon:wf,label:"Erase",highlight:s[8]==="erase",size:"medium",padded:!1,transparent:!0}}),e.$on("click",s[25]),{c(){nt(e.$$.fragment)},m(i,n){ot(e,i,n),t=!0},p(i,n){const r={};n[0]&256&&(r.highlight=i[8]==="erase"),e.$set(r)},i(i){t||(N(e.$$.fragment,i),t=!0)},o(i){V(e.$$.fragment,i),t=!1},d(i){rt(e,i)}}}function Eh(s){let e,t,i,n;const r=[Hy,Wy],o=[];function a(l,h){return l[10]?0:1}return e=a(s),t=o[e]=r[e](s),{c(){t.c(),i=lr()},m(l,h){o[e].m(l,h),Dt(l,i,h),n=!0},p(l,h){let c=e;e=a(l),e===c?o[e].p(l,h):(qe(),V(o[c],1,1,()=>{o[c]=null}),He(),t=o[e],t?t.p(l,h):(t=o[e]=r[e](l),t.c()),N(t,1),t.m(i.parentNode,i))},i(l){n||(N(t),n=!0)},o(l){V(t),n=!1},d(l){l&&Gt(i),o[e].d(l)}}}function Wy(s){let e,t,i,n,r=s[17]&&Rh(s),o=s[15]&&Fh(s),a=s[16]&&Bh(s);return{c(){r&&r.c(),e=xt(),o&&o.c(),t=xt(),a&&a.c(),i=lr()},m(l,h){r&&r.m(l,h),Dt(l,e,h),o&&o.m(l,h),Dt(l,t,h),a&&a.m(l,h),Dt(l,i,h),n=!0},p(l,h){l[17]?r?(r.p(l,h),h[0]&131072&&N(r,1)):(r=Rh(l),r.c(),N(r,1),r.m(e.parentNode,e)):r&&(qe(),V(r,1,1,()=>{r=null}),He()),l[15]?o?(o.p(l,h),h[0]&32768&&N(o,1)):(o=Fh(l),o.c(),N(o,1),o.m(t.parentNode,t)):o&&(qe(),V(o,1,1,()=>{o=null}),He()),l[16]?a?(a.p(l,h),h[0]&65536&&N(a,1)):(a=Bh(l),a.c(),N(a,1),a.m(i.parentNode,i)):a&&(qe(),V(a,1,1,()=>{a=null}),He())},i(l){n||(N(r),N(o),N(a),n=!0)},o(l){V(r),V(o),V(a),n=!1},d(l){l&&(Gt(e),Gt(t),Gt(i)),r&&r.d(l),o&&o.d(l),a&&a.d(l)}}}function Hy(s){let e,t,i,n=s[19]&&Lh(s),r=s[18]&&$h(s);return{c(){n&&n.c(),e=xt(),r&&r.c(),t=lr()},m(o,a){n&&n.m(o,a),Dt(o,e,a),r&&r.m(o,a),Dt(o,t,a),i=!0},p(o,a){o[19]?n?(n.p(o,a),a[0]&524288&&N(n,1)):(n=Lh(o),n.c(),N(n,1),n.m(e.parentNode,e)):n&&(qe(),V(n,1,1,()=>{n=null}),He()),o[18]?r?(r.p(o,a),a[0]&262144&&N(r,1)):(r=$h(o),r.c(),N(r,1),r.m(t.parentNode,t)):r&&(qe(),V(r,1,1,()=>{r=null}),He())},i(o){i||(N(n),N(r),i=!0)},o(o){V(n),V(r),i=!1},d(o){o&&(Gt(e),Gt(t)),n&&n.d(o),r&&r.d(o)}}}function Rh(s){let e,t;return e=new st({props:{Icon:u_,label:"Upload",highlight:s[9]==="upload",size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[28]),{c(){nt(e.$$.fragment)},m(i,n){ot(e,i,n),t=!0},p(i,n){const r={};n[0]&512&&(r.highlight=i[9]==="upload"),e.$set(r)},i(i){t||(N(e.$$.fragment,i),t=!0)},o(i){V(e.$$.fragment,i),t=!1},d(i){rt(e,i)}}}function Fh(s){let e,t;return e=new st({props:{Icon:d_,label:"Paste",highlight:s[9]==="paste",size:"large",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[29]),{c(){nt(e.$$.fragment)},m(i,n){ot(e,i,n),t=!0},p(i,n){const r={};n[0]&512&&(r.highlight=i[9]==="paste"),e.$set(r)},i(i){t||(N(e.$$.fragment,i),t=!0)},o(i){V(e.$$.fragment,i),t=!1},d(i){rt(e,i)}}}function Bh(s){let e,t;return e=new st({props:{Icon:f_,label:"Webcam",highlight:s[9]==="webcam",size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[30]),{c(){nt(e.$$.fragment)},m(i,n){ot(e,i,n),t=!0},p(i,n){const r={};n[0]&512&&(r.highlight=i[9]==="webcam"),e.$set(r)},i(i){t||(N(e.$$.fragment,i),t=!0)},o(i){V(e.$$.fragment,i),t=!1},d(i){rt(e,i)}}}function Lh(s){let e,t;return e=new st({props:{Icon:_f,label:"Crop",highlight:s[9]==="crop",size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[26]),{c(){nt(e.$$.fragment)},m(i,n){ot(e,i,n),t=!0},p(i,n){const r={};n[0]&512&&(r.highlight=i[9]==="crop"),e.$set(r)},i(i){t||(N(e.$$.fragment,i),t=!0)},o(i){V(e.$$.fragment,i),t=!1},d(i){rt(e,i)}}}function $h(s){let e,t;return e=new st({props:{Icon:wg,label:"Resize",highlight:s[9]==="size",size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[27]),{c(){nt(e.$$.fragment)},m(i,n){ot(e,i,n),t=!0},p(i,n){const r={};n[0]&512&&(r.highlight=i[9]==="size"),e.$set(r)},i(i){t||(N(e.$$.fragment,i),t=!0)},o(i){V(e.$$.fragment,i),t=!1},d(i){rt(e,i)}}}function Oh(s){let e,t,i,n,r,o;e=new st({props:{Icon:rf,label:"Color",color:s[2],size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[31]),i=new st({props:{Icon:Ec,label:"Brush Size",highlight:s[9]==="size",size:"medium",padded:!1,transparent:!0,offset:0}}),i.$on("click",s[32]);let a=(s[5]||s[6])&&Gh(s);return{c(){nt(e.$$.fragment),t=xt(),nt(i.$$.fragment),n=xt(),a&&a.c(),r=lr()},m(l,h){ot(e,l,h),Dt(l,t,h),ot(i,l,h),Dt(l,n,h),a&&a.m(l,h),Dt(l,r,h),o=!0},p(l,h){const c={};h[0]&4&&(c.color=l[2]),e.$set(c);const u={};h[0]&512&&(u.highlight=l[9]==="size"),i.$set(u),l[5]||l[6]?a?(a.p(l,h),h[0]&96&&N(a,1)):(a=Gh(l),a.c(),N(a,1),a.m(r.parentNode,r)):a&&(qe(),V(a,1,1,()=>{a=null}),He())},i(l){o||(N(e.$$.fragment,l),N(i.$$.fragment,l),N(a),o=!0)},o(l){V(e.$$.fragment,l),V(i.$$.fragment,l),V(a),o=!1},d(l){l&&(Gt(t),Gt(n),Gt(r)),rt(e,l),rt(i,l),a&&a.d(l)}}}function Gh(s){let e,t,i,n,r,o;function a(d){s[33](d)}function l(d){s[34](d)}function h(d){s[35](d)}function c(d){s[36](d)}let u={colors:s[11].colors,color_mode:s[11].color_mode,recent_colors:s[20],show_swatch:s[5],show_size:s[6],mode:"brush"};return s[0]!==void 0&&(u.selected_size=s[0]),s[2]!==void 0&&(u.selected_color=s[2]),s[3]!==void 0&&(u.selected_opacity=s[3]),s[4]!==void 0&&(u.preview=s[4]),e=new yd({props:u}),di.push(()=>ui(e,"selected_size",a)),di.push(()=>ui(e,"selected_color",l)),di.push(()=>ui(e,"selected_opacity",h)),di.push(()=>ui(e,"preview",c)),e.$on("click_outside",s[37]),{c(){nt(e.$$.fragment)},m(d,_){ot(e,d,_),o=!0},p(d,_){const f={};_[0]&2048&&(f.colors=d[11].colors),_[0]&2048&&(f.color_mode=d[11].color_mode),_[0]&32&&(f.show_swatch=d[5]),_[0]&64&&(f.show_size=d[6]),!t&&_[0]&1&&(t=!0,f.selected_size=d[0],ci(()=>t=!1)),!i&&_[0]&4&&(i=!0,f.selected_color=d[2],ci(()=>i=!1)),!n&&_[0]&8&&(n=!0,f.selected_opacity=d[3],ci(()=>n=!1)),!r&&_[0]&16&&(r=!0,f.preview=d[4],ci(()=>r=!1)),e.$set(f)},i(d){o||(N(e.$$.fragment,d),o=!0)},o(d){V(e.$$.fragment,d),o=!1},d(d){rt(e,d)}}}function Dh(s){let e,t;return e=new st({props:{Icon:Ec,label:"Eraser Size",highlight:s[9]==="size",size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[38]),{c(){nt(e.$$.fragment)},m(i,n){ot(e,i,n),t=!0},p(i,n){const r={};n[0]&512&&(r.highlight=i[9]==="size"),e.$set(r)},i(i){t||(N(e.$$.fragment,i),t=!0)},o(i){V(e.$$.fragment,i),t=!1},d(i){rt(e,i)}}}function Nh(s){let e,t,i,n,r,o;function a(d){s[39](d)}function l(d){s[40](d)}function h(d){s[41](d)}function c(d){s[42](d)}let u={colors:[],show_swatch:!1,show_size:!0,mode:"eraser"};return s[1]!==void 0&&(u.selected_size=s[1]),s[2]!==void 0&&(u.selected_color=s[2]),s[3]!==void 0&&(u.selected_opacity=s[3]),s[4]!==void 0&&(u.preview=s[4]),e=new yd({props:u}),di.push(()=>ui(e,"selected_size",a)),di.push(()=>ui(e,"selected_color",l)),di.push(()=>ui(e,"selected_opacity",h)),di.push(()=>ui(e,"preview",c)),e.$on("click_outside",s[43]),{c(){nt(e.$$.fragment)},m(d,_){ot(e,d,_),o=!0},p(d,_){const f={};!t&&_[0]&2&&(t=!0,f.selected_size=d[1],ci(()=>t=!1)),!i&&_[0]&4&&(i=!0,f.selected_color=d[2],ci(()=>i=!1)),!n&&_[0]&8&&(n=!0,f.selected_opacity=d[3],ci(()=>n=!1)),!r&&_[0]&16&&(r=!0,f.preview=d[4],ci(()=>r=!1)),e.$set(f)},i(d){o||(N(e.$$.fragment,d),o=!0)},o(d){V(e.$$.fragment,d),o=!1},d(d){rt(e,d)}}}function qy(s){let e,t,i,n,r,o,a,l,h,c,u=s[13].length>0&&Ph(s),d=s[11]&&zh(s),_=s[12]&&Ih(s),f=s[8]==="image"&&Eh(s),m=s[8]==="draw"&&s[11]&&Oh(s),g=s[8]==="erase"&&s[12]&&Dh(s),p=s[7]&&Nh(s);return{c(){e=Co("div"),t=Co("div"),u&&u.c(),i=xt(),d&&d.c(),n=xt(),_&&_.c(),r=xt(),o=Co("div"),f&&f.c(),a=xt(),m&&m.c(),l=xt(),g&&g.c(),h=xt(),p&&p.c(),So(t,"class","half-container svelte-1m5h684"),So(o,"class","half-container right svelte-1m5h684"),Th(o,"hide",s[8]==="pan"||s[8]==="image"&&!s[10]&&s[13].length===0||s[8]==="image"&&s[10]&&s[14].length===0),So(e,"class","toolbar-wrap svelte-1m5h684")},m(y,b){Dt(y,e,b),si(e,t),u&&u.m(t,null),si(t,i),d&&d.m(t,null),si(t,n),_&&_.m(t,null),si(e,r),si(e,o),f&&f.m(o,null),si(o,a),m&&m.m(o,null),si(o,l),g&&g.m(o,null),si(o,h),p&&p.m(o,null),c=!0},p(y,b){y[13].length>0?u?(u.p(y,b),b[0]&8192&&N(u,1)):(u=Ph(y),u.c(),N(u,1),u.m(t,i)):u&&(qe(),V(u,1,1,()=>{u=null}),He()),y[11]?d?(d.p(y,b),b[0]&2048&&N(d,1)):(d=zh(y),d.c(),N(d,1),d.m(t,n)):d&&(qe(),V(d,1,1,()=>{d=null}),He()),y[12]?_?(_.p(y,b),b[0]&4096&&N(_,1)):(_=Ih(y),_.c(),N(_,1),_.m(t,null)):_&&(qe(),V(_,1,1,()=>{_=null}),He()),y[8]==="image"?f?(f.p(y,b),b[0]&256&&N(f,1)):(f=Eh(y),f.c(),N(f,1),f.m(o,a)):f&&(qe(),V(f,1,1,()=>{f=null}),He()),y[8]==="draw"&&y[11]?m?(m.p(y,b),b[0]&2304&&N(m,1)):(m=Oh(y),m.c(),N(m,1),m.m(o,l)):m&&(qe(),V(m,1,1,()=>{m=null}),He()),y[8]==="erase"&&y[12]?g?(g.p(y,b),b[0]&4352&&N(g,1)):(g=Dh(y),g.c(),N(g,1),g.m(o,h)):g&&(qe(),V(g,1,1,()=>{g=null}),He()),y[7]?p?(p.p(y,b),b[0]&128&&N(p,1)):(p=Nh(y),p.c(),N(p,1),p.m(o,null)):p&&(qe(),V(p,1,1,()=>{p=null}),He()),(!c||b[0]&25856)&&Th(o,"hide",y[8]==="pan"||y[8]==="image"&&!y[10]&&y[13].length===0||y[8]==="image"&&y[10]&&y[14].length===0)},i(y){c||(N(u),N(d),N(_),N(f),N(m),N(g),N(p),c=!0)},o(y){V(u),V(d),V(_),V(f),V(m),V(g),V(p),c=!1},d(y){y&&Gt(e),u&&u.d(),d&&d.d(),_&&_.d(),f&&f.d(),m&&m.d(),g&&g.d(),p&&p.d()}}}function Vy(s,e,t){let i,n,r,o,a,{tool:l="image"}=e,{subtool:h=null}=e,{background:c=!1}=e,{brush_options:u}=e,{selected_size:d=u&&typeof u.default_size=="number"?u.default_size:25}=e,{eraser_options:_}=e,{selected_eraser_size:f=_&&typeof _.default_size=="number"?_.default_size:25}=e,{selected_color:m=u&&(()=>{const z=u.default_color;return Array.isArray(z)?z[0]:z})()}=e,{selected_opacity:g=u&&(()=>{const z=u.default_color;if(Array.isArray(z))return z[1];const ue=tt(z);return ue.getAlpha()<1?ue.getAlpha():1})()}=e,{preview:p=!1}=e,{show_brush_color:y=!1}=e,{show_brush_size:b=!1}=e,{show_eraser_size:w=!1}=e,{sources:A}=e,{transforms:v}=e,S=[];const R=Uy();function C(z,ue){z.stopPropagation(),R("tool_change",{tool:ue})}function M(z,ue){z.stopPropagation(),R("subtool_change",{tool:l,subtool:ue})}const k=z=>C(z,"image"),P=z=>C(z,"draw"),L=z=>C(z,"erase"),B=z=>M(z,"crop"),H=z=>M(z,"size"),F=z=>M(z,"upload"),O=z=>M(z,"paste"),Ie=z=>M(z,"webcam"),q=z=>M(z,"color"),te=z=>M(z,"size");function E(z){d=z,t(0,d)}function $(z){m=z,t(2,m)}function ye(z){g=z,t(3,g)}function Pe(z){p=z,t(4,p)}const ge=z=>{z.stopPropagation(),t(4,p=!1),t(5,y=!1),t(6,b=!1),M(z,null)},me=z=>M(z,"size");function xe(z){f=z,t(1,f)}function ve(z){m=z,t(2,m)}function ke(z){g=z,t(3,g)}function ze(z){p=z,t(4,p)}const at=z=>{z.stopPropagation(),t(4,p=!1),t(7,w=!1),M(z,null)};return s.$$set=z=>{"tool"in z&&t(8,l=z.tool),"subtool"in z&&t(9,h=z.subtool),"background"in z&&t(10,c=z.background),"brush_options"in z&&t(11,u=z.brush_options),"selected_size"in z&&t(0,d=z.selected_size),"eraser_options"in z&&t(12,_=z.eraser_options),"selected_eraser_size"in z&&t(1,f=z.selected_eraser_size),"selected_color"in z&&t(2,m=z.selected_color),"selected_opacity"in z&&t(3,g=z.selected_opacity),"preview"in z&&t(4,p=z.preview),"show_brush_color"in z&&t(5,y=z.show_brush_color),"show_brush_size"in z&&t(6,b=z.show_brush_size),"show_eraser_size"in z&&t(7,w=z.show_eraser_size),"sources"in z&&t(13,A=z.sources),"transforms"in z&&t(14,v=z.transforms)},s.$$.update=()=>{s.$$.dirty[0]&768&&t(6,b=l==="draw"&&h==="size"),s.$$.dirty[0]&768&&t(5,y=l==="draw"&&h==="color"),s.$$.dirty[0]&768&&t(7,w=l==="erase"&&h==="size"),s.$$.dirty[0]&16384&&t(19,i=v.includes("crop")),s.$$.dirty[0]&16384&&t(18,n=v.includes("resize")),s.$$.dirty[0]&8192&&t(17,r=A.includes("upload")),s.$$.dirty[0]&8192&&t(16,o=A.includes("webcam")),s.$$.dirty[0]&8192&&t(15,a=A.includes("clipboard"))},[d,f,m,g,p,y,b,w,l,h,c,u,_,A,v,a,o,r,n,i,S,C,M,k,P,L,B,H,F,O,Ie,q,te,E,$,ye,Pe,ge,me,xe,ve,ke,ze,at]}class Xy extends Gy{constructor(e){super(),Dy(this,e,Vy,qy,Ny,{tool:8,subtool:9,background:10,brush_options:11,selected_size:0,eraser_options:12,selected_eraser_size:1,selected_color:2,selected_opacity:3,preview:4,show_brush_color:5,show_brush_size:6,show_eraser_size:7,sources:13,transforms:14},null,[-1,-1])}get tool(){return this.$$.ctx[8]}set tool(e){this.$$set({tool:e}),$e()}get subtool(){return this.$$.ctx[9]}set subtool(e){this.$$set({subtool:e}),$e()}get background(){return this.$$.ctx[10]}set background(e){this.$$set({background:e}),$e()}get brush_options(){return this.$$.ctx[11]}set brush_options(e){this.$$set({brush_options:e}),$e()}get selected_size(){return this.$$.ctx[0]}set selected_size(e){this.$$set({selected_size:e}),$e()}get eraser_options(){return this.$$.ctx[12]}set eraser_options(e){this.$$set({eraser_options:e}),$e()}get selected_eraser_size(){return this.$$.ctx[1]}set selected_eraser_size(e){this.$$set({selected_eraser_size:e}),$e()}get selected_color(){return this.$$.ctx[2]}set selected_color(e){this.$$set({selected_color:e}),$e()}get selected_opacity(){return this.$$.ctx[3]}set selected_opacity(e){this.$$set({selected_opacity:e}),$e()}get preview(){return this.$$.ctx[4]}set preview(e){this.$$set({preview:e}),$e()}get show_brush_color(){return this.$$.ctx[5]}set show_brush_color(e){this.$$set({show_brush_color:e}),$e()}get show_brush_size(){return this.$$.ctx[6]}set show_brush_size(e){this.$$set({show_brush_size:e}),$e()}get show_eraser_size(){return this.$$.ctx[7]}set show_eraser_size(e){this.$$set({show_eraser_size:e}),$e()}get sources(){return this.$$.ctx[13]}set sources(e){this.$$set({sources:e}),$e()}get transforms(){return this.$$.ctx[14]}set transforms(e){this.$$set({transforms:e}),$e()}}var sa=`in vec2 aPosition;
out vec2 vTextureCoord;

uniform vec4 uInputSize;
uniform vec4 uOutputFrame;
uniform vec4 uOutputTexture;

vec4 filterVertexPosition( void )
{
    vec2 position = aPosition * uOutputFrame.zw + uOutputFrame.xy;
    
    position.x = position.x * (2.0 / uOutputTexture.x) - 1.0;
    position.y = position.y * (2.0*uOutputTexture.z / uOutputTexture.y) - uOutputTexture.z;

    return vec4(position, 0.0, 1.0);
}

vec2 filterTextureCoord( void )
{
    return aPosition * (uOutputFrame.zw * uInputSize.zw);
}

void main(void)
{
    gl_Position = filterVertexPosition();
    vTextureCoord = filterTextureCoord();
}
`,na=`struct GlobalFilterUniforms {
  uInputSize:vec4<f32>,
  uInputPixel:vec4<f32>,
  uInputClamp:vec4<f32>,
  uOutputFrame:vec4<f32>,
  uGlobalFrame:vec4<f32>,
  uOutputTexture:vec4<f32>,
};

@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;

struct VSOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) uv : vec2<f32>
  };

fn filterVertexPosition(aPosition:vec2<f32>) -> vec4<f32>
{
    var position = aPosition * gfu.uOutputFrame.zw + gfu.uOutputFrame.xy;

    position.x = position.x * (2.0 / gfu.uOutputTexture.x) - 1.0;
    position.y = position.y * (2.0*gfu.uOutputTexture.z / gfu.uOutputTexture.y) - gfu.uOutputTexture.z;

    return vec4(position, 0.0, 1.0);
}

fn filterTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>
{
    return aPosition * (gfu.uOutputFrame.zw * gfu.uInputSize.zw);
}

fn globalTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>
{
  return  (aPosition.xy / gfu.uGlobalFrame.zw) + (gfu.uGlobalFrame.xy / gfu.uGlobalFrame.zw);  
}

fn getSize() -> vec2<f32>
{
  return gfu.uGlobalFrame.zw;
}
  
@vertex
fn mainVertex(
  @location(0) aPosition : vec2<f32>, 
) -> VSOutput {
  return VSOutput(
   filterVertexPosition(aPosition),
   filterTextureCoord(aPosition)
  );
}`,Yy=`
in vec2 vTextureCoord;
out vec4 finalColor;

uniform sampler2D uTexture;
uniform vec2 uOffset;

void main(void)
{
    vec4 color = vec4(0.0);

    // Sample top left pixel
    color += texture(uTexture, vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y + uOffset.y));

    // Sample top right pixel
    color += texture(uTexture, vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y + uOffset.y));

    // Sample bottom right pixel
    color += texture(uTexture, vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y - uOffset.y));

    // Sample bottom left pixel
    color += texture(uTexture, vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y - uOffset.y));

    // Average
    color *= 0.25;

    finalColor = color;
}`,jy=`struct KawaseBlurUniforms {
  uOffset:vec2<f32>,
};

@group(0) @binding(1) var uTexture: texture_2d<f32>; 
@group(0) @binding(2) var uSampler: sampler;
@group(1) @binding(0) var<uniform> kawaseBlurUniforms : KawaseBlurUniforms;

@fragment
fn mainFragment(
  @builtin(position) position: vec4<f32>,
  @location(0) uv : vec2<f32>
) -> @location(0) vec4<f32> {
  let uOffset = kawaseBlurUniforms.uOffset;
  var color: vec4<f32> = vec4<f32>(0.0);

  // Sample top left pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x - uOffset.x, uv.y + uOffset.y));
  // Sample top right pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x + uOffset.x, uv.y + uOffset.y));
  // Sample bottom right pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x + uOffset.x, uv.y - uOffset.y));
  // Sample bottom left pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x - uOffset.x, uv.y - uOffset.y));
  // Average
  color *= 0.25;

  return color;
}`,Ky=`
precision highp float;
in vec2 vTextureCoord;
out vec4 finalColor;

uniform sampler2D uTexture;
uniform vec2 uOffset;

uniform vec4 uInputClamp;

void main(void)
{
    vec4 color = vec4(0.0);

    // Sample top left pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y + uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Sample top right pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y + uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Sample bottom right pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y - uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Sample bottom left pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y - uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Average
    color *= 0.25;

    finalColor = color;
}
`,Zy=`struct KawaseBlurUniforms {
  uOffset:vec2<f32>,
};

struct GlobalFilterUniforms {
  uInputSize:vec4<f32>,
  uInputPixel:vec4<f32>,
  uInputClamp:vec4<f32>,
  uOutputFrame:vec4<f32>,
  uGlobalFrame:vec4<f32>,
  uOutputTexture:vec4<f32>,
};

@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;

@group(0) @binding(1) var uTexture: texture_2d<f32>; 
@group(0) @binding(2) var uSampler: sampler;
@group(1) @binding(0) var<uniform> kawaseBlurUniforms : KawaseBlurUniforms;

@fragment
fn mainFragment(
  @builtin(position) position: vec4<f32>,
  @location(0) uv : vec2<f32>
) -> @location(0) vec4<f32> {
  let uOffset = kawaseBlurUniforms.uOffset;
  var color: vec4<f32> = vec4(0.0);

  // Sample top left pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x - uOffset.x, uv.y + uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Sample top right pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x + uOffset.x, uv.y + uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Sample bottom right pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x + uOffset.x, uv.y - uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Sample bottom left pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x - uOffset.x, uv.y - uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Average
  color *= 0.25;
    
  return color;
}`,Qy=Object.defineProperty,Jy=(s,e,t)=>e in s?Qy(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,Ti=(s,e,t)=>(Jy(s,typeof e!="symbol"?e+"":e,t),t);const wd=class xd extends No{constructor(...e){let t=e[0]??{};(typeof t=="number"||Array.isArray(t))&&(K("6.0.0","KawaseBlurFilter constructor params are now options object. See params: { strength, quality, clamp, pixelSize }"),t={strength:t},e[1]!==void 0&&(t.quality=e[1]),e[2]!==void 0&&(t.clamp=e[2])),t={...xd.DEFAULT_OPTIONS,...t};const i=mi.from({vertex:{source:na,entryPoint:"mainVertex"},fragment:{source:t?.clamp?Zy:jy,entryPoint:"mainFragment"}}),n=_s.from({vertex:sa,fragment:t?.clamp?Ky:Yy,name:"kawase-blur-filter"});super({gpuProgram:i,glProgram:n,resources:{kawaseBlurUniforms:{uOffset:{value:new Float32Array(2),type:"vec2<f32>"}}}}),Ti(this,"uniforms"),Ti(this,"_pixelSize",{x:0,y:0}),Ti(this,"_clamp"),Ti(this,"_kernels",[]),Ti(this,"_blur"),Ti(this,"_quality"),this.uniforms=this.resources.kawaseBlurUniforms.uniforms,this.pixelSize=t.pixelSize??{x:1,y:1},Array.isArray(t.strength)?this.kernels=t.strength:typeof t.strength=="number"&&(this._blur=t.strength,this.quality=t.quality??3),this._clamp=!!t.clamp}apply(e,t,i,n){const r=this.pixelSizeX/t.source.width,o=this.pixelSizeY/t.source.height;let a;if(this._quality===1||this._blur===0)a=this._kernels[0]+.5,this.uniforms.uOffset[0]=a*r,this.uniforms.uOffset[1]=a*o,e.applyFilter(this,t,i,n);else{const l=Zs.getSameSizeTexture(t);let h=t,c=l,u;const d=this._quality-1;for(let _=0;_<d;_++)a=this._kernels[_]+.5,this.uniforms.uOffset[0]=a*r,this.uniforms.uOffset[1]=a*o,e.applyFilter(this,h,c,!0),u=h,h=c,c=u;a=this._kernels[d]+.5,this.uniforms.uOffset[0]=a*r,this.uniforms.uOffset[1]=a*o,e.applyFilter(this,h,i,n),Zs.returnTexture(l)}}get strength(){return this._blur}set strength(e){this._blur=e,this._generateKernels()}get quality(){return this._quality}set quality(e){this._quality=Math.max(1,Math.round(e)),this._generateKernels()}get kernels(){return this._kernels}set kernels(e){Array.isArray(e)&&e.length>0?(this._kernels=e,this._quality=e.length,this._blur=Math.max(...e)):(this._kernels=[0],this._quality=1)}get pixelSize(){return this._pixelSize}set pixelSize(e){if(typeof e=="number"){this.pixelSizeX=this.pixelSizeY=e;return}if(Array.isArray(e)){this.pixelSizeX=e[0],this.pixelSizeY=e[1];return}this._pixelSize=e}get pixelSizeX(){return this.pixelSize.x}set pixelSizeX(e){this.pixelSize.x=e}get pixelSizeY(){return this.pixelSize.y}set pixelSizeY(e){this.pixelSize.y=e}get clamp(){return this._clamp}_updatePadding(){this.padding=Math.ceil(this._kernels.reduce((e,t)=>e+t+.5,0))}_generateKernels(){const e=this._blur,t=this._quality,i=[e];if(e>0){let n=e;const r=e/t;for(let o=1;o<t;o++)n-=r,i.push(n)}this._kernels=i,this._updatePadding()}};Ti(wd,"DEFAULT_OPTIONS",{strength:4,quality:3,clamp:!1,pixelSize:{x:1,y:1}});let ew=wd;var tw=`precision highp float;
in vec2 vTextureCoord;
out vec4 finalColor;

uniform sampler2D uTexture;
uniform float uAlpha;
uniform vec3 uColor;
uniform vec2 uOffset;

uniform vec4 uInputSize;

void main(void){
    vec4 sample = texture(uTexture, vTextureCoord - uOffset * uInputSize.zw);

    // Premultiply alpha
    sample.rgb = uColor.rgb * sample.a;

    // alpha user alpha
    sample *= uAlpha;

    finalColor = sample;
}`,iw=`struct DropShadowUniforms {
  uAlpha: f32,
  uColor: vec3<f32>,
  uOffset: vec2<f32>,
};

struct GlobalFilterUniforms {
  uInputSize:vec4<f32>,
  uInputPixel:vec4<f32>,
  uInputClamp:vec4<f32>,
  uOutputFrame:vec4<f32>,
  uGlobalFrame:vec4<f32>,
  uOutputTexture:vec4<f32>,
};

@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;

@group(0) @binding(1) var uTexture: texture_2d<f32>; 
@group(0) @binding(2) var uSampler: sampler;
@group(1) @binding(0) var<uniform> dropShadowUniforms : DropShadowUniforms;

@fragment
fn mainFragment(
  @builtin(position) position: vec4<f32>,
  @location(0) uv : vec2<f32>
) -> @location(0) vec4<f32> {
  var color: vec4<f32> = textureSample(uTexture, uSampler, uv - dropShadowUniforms.uOffset * gfu.uInputSize.zw);

  // Premultiply alpha
  color = vec4<f32>(vec3<f32>(dropShadowUniforms.uColor.rgb * color.a), color.a);
  // alpha user alpha
  color *= dropShadowUniforms.uAlpha;

  return color;
}`,sw=Object.defineProperty,nw=(s,e,t)=>e in s?sw(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,is=(s,e,t)=>(nw(s,typeof e!="symbol"?e+"":e,t),t);const vd=class kd extends No{constructor(e){e={...kd.DEFAULT_OPTIONS,...e};const t=mi.from({vertex:{source:na,entryPoint:"mainVertex"},fragment:{source:iw,entryPoint:"mainFragment"}}),i=_s.from({vertex:sa,fragment:tw,name:"drop-shadow-filter"});super({gpuProgram:t,glProgram:i,resources:{dropShadowUniforms:{uAlpha:{value:e.alpha,type:"f32"},uColor:{value:new Float32Array(3),type:"vec3<f32>"},uOffset:{value:e.offset,type:"vec2<f32>"}}},resolution:e.resolution}),is(this,"uniforms"),is(this,"shadowOnly",!1),is(this,"_color"),is(this,"_blurFilter"),is(this,"_basePass"),this.uniforms=this.resources.dropShadowUniforms.uniforms,this._color=new fe,this.color=e.color??0,this._blurFilter=new ew({strength:e.kernels??e.blur,quality:e.kernels?void 0:e.quality}),this._basePass=new No({gpuProgram:mi.from({vertex:{source:na,entryPoint:"mainVertex"},fragment:{source:`
                    @group(0) @binding(1) var uTexture: texture_2d<f32>; 
                    @group(0) @binding(2) var uSampler: sampler;
                    @fragment
                    fn mainFragment(
                        @builtin(position) position: vec4<f32>,
                        @location(0) uv : vec2<f32>
                    ) -> @location(0) vec4<f32> {
                        return textureSample(uTexture, uSampler, uv);
                    }
                    `,entryPoint:"mainFragment"}}),glProgram:_s.from({vertex:sa,fragment:`
                in vec2 vTextureCoord;
                out vec4 finalColor;
                uniform sampler2D uTexture;

                void main(void){
                    finalColor = texture(uTexture, vTextureCoord);
                }
                `,name:"drop-shadow-filter"}),resources:{}}),Object.assign(this,e)}apply(e,t,i,n){const r=Zs.getSameSizeTexture(t);e.applyFilter(this,t,r,!0),this._blurFilter.apply(e,r,i,n),this.shadowOnly||e.applyFilter(this._basePass,t,i,!1),Zs.returnTexture(r)}get offset(){return this.uniforms.uOffset}set offset(e){this.uniforms.uOffset=e,this._updatePadding()}get offsetX(){return this.offset.x}set offsetX(e){this.offset.x=e,this._updatePadding()}get offsetY(){return this.offset.y}set offsetY(e){this.offset.y=e,this._updatePadding()}get color(){return this._color.value}set color(e){this._color.setValue(e);const[t,i,n]=this._color.toArray();this.uniforms.uColor[0]=t,this.uniforms.uColor[1]=i,this.uniforms.uColor[2]=n}get alpha(){return this.uniforms.uAlpha}set alpha(e){this.uniforms.uAlpha=e}get blur(){return this._blurFilter.strength}set blur(e){this._blurFilter.strength=e,this._updatePadding()}get quality(){return this._blurFilter.quality}set quality(e){this._blurFilter.quality=e,this._updatePadding()}get kernels(){return this._blurFilter.kernels}set kernels(e){this._blurFilter.kernels=e}get pixelSize(){return this._blurFilter.pixelSize}set pixelSize(e){typeof e=="number"&&(e={x:e,y:e}),Array.isArray(e)&&(e={x:e[0],y:e[1]}),this._blurFilter.pixelSize=e}get pixelSizeX(){return this._blurFilter.pixelSizeX}set pixelSizeX(e){this._blurFilter.pixelSizeX=e}get pixelSizeY(){return this._blurFilter.pixelSizeY}set pixelSizeY(e){this._blurFilter.pixelSizeY=e}_updatePadding(){const e=Math.max(Math.abs(this.offsetX),Math.abs(this.offsetY));this.padding=e+this.blur*2+this.quality*4}};is(vd,"DEFAULT_OPTIONS",{offset:{x:4,y:4},color:0,alpha:.5,shadowOnly:!1,kernels:void 0,blur:2,quality:3,pixelSize:{x:1,y:1},resolution:1});let rw=vd;class ow{name="image";context;current_tool;current_subtool;async setup(e,t,i){this.context=e,this.current_tool=t,this.current_subtool=i}cleanup(){}async add_image({image:e,fixed_canvas:t,border_region:i=0}){const n=new aw(this.context,e,t,i);await this.context.execute_command(n)}set_tool(e,t){this.current_tool=e,this.current_subtool=t}}class aw{sprite=null;fixed_canvas;context;background;current_canvas_size;computed_dimensions;current_scale;current_position;border_region;scaled_width;scaled_height;previous_image=null;name;constructor(e,t,i,n=0){if(this.name="AddImage",this.context=e,this.background=t,this.fixed_canvas=i,this.border_region=n,this.current_canvas_size=Ws(this.context.dimensions),this.current_scale=Ws(this.context.scale),this.current_position=Ws(this.context.position),this.computed_dimensions={width:0,height:0},this.context.background_image&&this.context.background_image.texture){const r=this.context.background_image,o=r.borderRegion||0;this.previous_image={texture:this.clone_texture(r.texture),width:r.width,height:r.height,x:r.position.x,y:r.position.y,border_region:o}}}clone_texture(e){const t=ft.create({width:e.width,height:e.height,resolution:window.devicePixelRatio||1}),i=new ee(e),n=new U;return n.addChild(i),this.context.app.renderer.render(n,{renderTexture:t}),n.destroy({children:!0}),t}async start(){let e;if(this.background instanceof Y)e=this.background;else{const n=await createImageBitmap(this.background);e=Y.from(n)}this.sprite=new ee(e);const[t,i]=this.handle_image();this.computed_dimensions={width:t,height:i}}handle_image(){if(this.sprite===null)return[0,0];if(this.fixed_canvas){const e=Math.max(this.current_canvas_size.width-this.border_region*2,10),t=Math.max(this.current_canvas_size.height-this.border_region*2,10),{width:i,height:n,x:r,y:o}=lw(this.sprite.width,this.sprite.height,e,t);this.sprite.width=i,this.sprite.height=n,this.sprite.x=r+this.border_region,this.sprite.y=o+this.border_region}else{const e=this.sprite.width,t=this.sprite.height;return this.sprite.x=this.border_region,this.sprite.y=this.border_region,[e+this.border_region*2,t+this.border_region*2]}return[this.current_canvas_size.width,this.current_canvas_size.height]}async execute(){if(await this.start(),this.sprite===null)return;const{width:e,height:t}=this.computed_dimensions;await this.context.set_image_properties({scale:1,position:{x:this.context.app.screen.width/2,y:this.context.app.screen.height/2},width:this.fixed_canvas?this.current_canvas_size.width:e,height:this.fixed_canvas?this.current_canvas_size.height:t});const i=this.context.layer_manager.create_background_layer(this.fixed_canvas?this.current_canvas_size.width:e,this.fixed_canvas?this.current_canvas_size.height:t);this.sprite.zIndex=0,i.addChild(this.sprite),this.context.layer_manager.reset_layers(this.fixed_canvas?this.current_canvas_size.width:e,this.fixed_canvas?this.current_canvas_size.height:t,!0),this.border_region>0&&(this.sprite.borderRegion=this.border_region),this.context.set_background_image(this.sprite),this.context.reset()}async undo(){if(this.sprite){if(this.sprite.destroy(),this.previous_image){const e=new ee(this.previous_image.texture);e.width=this.previous_image.width,e.height=this.previous_image.height,e.position.set(this.previous_image.x,this.previous_image.y),this.previous_image.border_region>0&&(e.borderRegion=this.previous_image.border_region),await this.context.set_image_properties({scale:1,position:{x:this.context.app.screen.width/2,y:this.context.app.screen.height/2},width:this.previous_image.width,height:this.previous_image.height});const t=this.context.layer_manager.create_background_layer(this.previous_image.width,this.previous_image.height);e.zIndex=0,t.addChild(e),this.context.layer_manager.reset_layers(this.previous_image.width,this.previous_image.height,!0),this.context.set_background_image(e)}else await this.context.set_image_properties({scale:1,position:{x:this.context.app.screen.width/2,y:this.context.app.screen.height/2},width:this.current_canvas_size.width,height:this.current_canvas_size.height}),this.context.layer_manager.create_background_layer(this.current_canvas_size.width,this.current_canvas_size.height),this.context.layer_manager.reset_layers(this.current_canvas_size.width,this.current_canvas_size.height);this.context.reset()}}}function lw(s,e,t,i){const n=s/e,r=t/i;let o,a;n>r?(o=t,a=t/n):(a=i,o=i*n);const l=Math.round((t-o)/2),h=Math.round((i-a)/2);return{width:Math.round(o),height:Math.round(a),x:l,y:h}}class ra{name="zoom";min_zoom=as(!0);image_editor_context;max_zoom=10;border_padding=30;pad_bottom=0;is_pinching=!1;is_dragging=!1;is_pointer_dragging=!1;last_touch_position=null;last_pinch_distance=0;drag_start=new X;current_tool;current_subtool;local_scale=1;local_dimensions={width:0,height:0};local_position={x:0,y:0};prevent_default(e){e.preventDefault(),e.stopPropagation()}set_tool(e,t){this.current_tool=e,this.current_subtool=t}set_zoom(e){const t=this.calculate_min_zoom(this.local_dimensions.width,this.local_dimensions.height);let i;const n=e==="fit";n?i=t:i=Math.max(0,Math.min(this.max_zoom,e));const r=this.image_editor_context.app.screen,o=r.width,a=r.height;let l;l={x:o/2,y:a/2},this.zoom_to_point(i,l,!0,n)}async setup(e,t,i){this.image_editor_context=e,this.current_tool=t,this.current_subtool=i,this.pad_bottom=e.pad_bottom;const{width:n,height:r}=await this.get_container_dimensions(),o=this.calculate_min_zoom(n,r),a=Math.min(o,1);this.local_scale=a;const l=this.image_editor_context.app.screen,h=l.width/2,c=l.height/2,u=n*a,d=r*a,_=h-u/2,f=c-d/2;this.local_position={x:_,y:f},this.setup_event_listeners(),await this.image_editor_context.set_image_properties({scale:a,position:{x:_,y:f}}),this.image_editor_context.dimensions.subscribe(m=>{this.local_dimensions=m}),this.image_editor_context.scale.subscribe(m=>{this.local_scale=m}),this.image_editor_context.position.subscribe(m=>{this.local_position=m})}setup_event_listeners(){const e=this.image_editor_context.app.stage;this.image_editor_context.app.canvas.addEventListener("wheel",this.prevent_default,{passive:!1}),e.eventMode="static",e.hitArea=this.image_editor_context.app.screen;const i=this.handle_wheel.bind(this);e.addEventListener("wheel",i,{passive:!1}),"ontouchstart"in window?(e.addEventListener("touchstart",this.handle_touch_start.bind(this)),e.addEventListener("touchmove",this.handle_touch_move.bind(this)),e.addEventListener("touchend",this.handle_touch_end.bind(this))):(e.addEventListener("pointerdown",this.handle_pointer_down.bind(this)),e.addEventListener("pointermove",this.handle_pointer_move.bind(this)),e.addEventListener("pointerup",this.handle_pointer_up.bind(this)),e.addEventListener("pointerupoutside",this.handle_pointer_up.bind(this)))}handle_wheel(e){const t=e.deltaMode===0&&Math.abs(e.deltaY)<50,i=t?30:10;if(e.altKey||e.metaKey){const n=this.image_editor_context.app.stage.toLocal(e.global),r=-e.deltaY*(t?.001:5e-4),o=this.local_scale*(1+r);this.zoom_to_point(o,n,!0)}else{const n=e.deltaX,r=e.deltaY,o={x:this.local_position.x-n/100*i,y:this.local_position.y-r/100*i},a=this.image_editor_context.app.screen,l=this.local_dimensions.width*this.local_scale,h=this.local_dimensions.height*this.local_scale,c=a.width-this.border_padding*2,u=a.height-this.border_padding*2-this.pad_bottom;let d={...o};if(l<=c)d.x=(a.width-l)/2;else{const _=a.width/2,f=_,m=a.width-_-l;d.x=Math.min(Math.max(o.x,m),f)}if(h<=u)d.y=(a.height-this.pad_bottom-h)/2;else{const _=(a.height-this.pad_bottom)/2,f=_,m=a.height-this.pad_bottom-_-h;d.y=Math.min(Math.max(o.y,m),f)}this.image_editor_context.set_image_properties({scale:this.local_scale,position:d})}}cleanup(){const e=this?.image_editor_context?.app?.stage;e&&(e.removeEventListener("wheel",this.handle_wheel.bind(this)),"ontouchstart"in window?(e.removeEventListener("touchstart",this.handle_touch_start.bind(this)),e.removeEventListener("touchmove",this.handle_touch_move.bind(this)),e.removeEventListener("touchend",this.handle_touch_end.bind(this))):(e.removeEventListener("pointerdown",this.handle_pointer_down.bind(this)),e.removeEventListener("pointermove",this.handle_pointer_move.bind(this)),e.removeEventListener("pointerup",this.handle_pointer_up.bind(this)),e.removeEventListener("pointerupoutside",this.handle_pointer_up.bind(this))))}async get_container_dimensions(){const e=this.image_editor_context.image_container.getLocalBounds();return{width:e.width,height:e.height}}calculate_min_zoom(e,t){const i=this.image_editor_context.app.screen,n=i.width,r=i.height;if(!e||!n||!t||!r)return 1;const o=n-this.border_padding*2,a=r-this.border_padding*2-this.pad_bottom,l=o/e,h=a/t;return Math.min(l,h)}handle_touch_start(e){e.preventDefault();const t=e;if(t.touches&&t.touches.length===2)this.is_pinching=!0,this.last_pinch_distance=Math.hypot(t.touches[0].pageX-t.touches[1].pageX,t.touches[0].pageY-t.touches[1].pageY);else if(t.touches&&t.touches.length===1){this.is_dragging=!0;const i=this.image_editor_context.app.view.getBoundingClientRect();this.last_touch_position=new X(t.touches[0].pageX-i.left,t.touches[0].pageY-i.top)}}handle_touch_move(e){e.preventDefault();const t=e;if(this.is_pinching&&t.touches&&t.touches.length===2){const i=this.image_editor_context.app.view.getBoundingClientRect(),n=Math.hypot(t.touches[0].pageX-t.touches[1].pageX,t.touches[0].pageY-t.touches[1].pageY),r={x:(t.touches[0].pageX+t.touches[1].pageX)/2-i.left,y:(t.touches[0].pageY+t.touches[1].pageY)/2-i.top},o=n/this.last_pinch_distance;this.last_pinch_distance=n,this.zoom_to_point(this.local_scale*o,r)}else if(this.is_dragging&&t.touches&&t.touches.length===1&&this.last_touch_position){const i=this.image_editor_context.app.view.getBoundingClientRect(),n=new X(t.touches[0].pageX-i.left,t.touches[0].pageY-i.top),r=n.x-this.last_touch_position.x,o=n.y-this.last_touch_position.y;this.image_editor_context.set_image_properties({position:{x:this.local_position.x+r,y:this.local_position.y+o}}),this.last_touch_position=n}}handle_touch_end(e){e.preventDefault();const t=e;if(t.touches&&t.touches.length<2&&(this.is_pinching=!1,t.touches&&t.touches.length===1)){const i=this.image_editor_context.app.view.getBoundingClientRect();this.last_touch_position=new X(t.touches[0].pageX-i.left,t.touches[0].pageY-i.top),this.is_dragging=!0}t.touches&&t.touches.length===0&&(this.is_dragging=!1,this.last_touch_position=null,this.last_pinch_distance=0)}get_bounded_position(e){const t=this.image_editor_context.app.screen,i=this.local_dimensions.width*this.local_scale,n=this.local_dimensions.height*this.local_scale,r={x:(t.width-i)/2,y:(t.height-n-this.pad_bottom)/2};if(i<=t.width&&n<=t.height)return r;let o=e.x,a=e.y;if(i<=t.width)o=r.x;else{const l=t.width-i;o=Math.max(l,Math.min(0,e.x))}if(n<=t.height-this.pad_bottom)a=r.y;else{const l=t.height-n-this.pad_bottom;a=Math.max(l,Math.min(0,e.y))}return{x:o,y:a}}zoom_to_point(e,t,i,n){const r={x:(t.x-this.local_position.x)/this.local_scale,y:(t.y-this.local_position.y)/this.local_scale},o={x:r.x/this.local_dimensions.width,y:r.y/this.local_dimensions.height},a=this.calculate_min_zoom(this.local_dimensions.width,this.local_dimensions.height),l=Math.min(a,1);e=Math.min(Math.max(e,l),this.max_zoom);const h=this.local_dimensions.width*e,c=this.local_dimensions.height*e;let u={x:t.x-h*o.x,y:t.y-c*o.y};if(e===l||n){const d=this.image_editor_context.app.screen.width,_=this.image_editor_context.app.screen.height;u={x:(d-h)/2,y:(_-this.pad_bottom-c)/2}}this.image_editor_context.set_image_properties({scale:e,position:u,animate:typeof i=="boolean"?!i:e===l}),this.min_zoom.set(e===l)}handle_pointer_down(e){e.button===0&&this.current_tool==="pan"&&(this.is_pointer_dragging=!0,this.drag_start.copyFrom(e.global),this.drag_start.x-=this.local_position.x,this.drag_start.y-=this.local_position.y)}handle_pointer_move(e){if(this.is_pointer_dragging&&this.current_tool==="pan"){const t={x:e.global.x-this.drag_start.x,y:e.global.y-this.drag_start.y},i=this.image_editor_context.app.screen,n=this.local_dimensions.width*this.local_scale,r=this.local_dimensions.height*this.local_scale,o=i.width-this.border_padding*2,a=i.height-this.border_padding*2-this.pad_bottom;let l={...t};n<=o?l.x=(i.width-n)/2:l.x=t.x,r<=a?l.y=(i.height-this.pad_bottom-r)/2:l.y=t.y,this.image_editor_context.set_image_properties({scale:this.local_scale,position:l})}}handle_pointer_up(e){if(this.is_pointer_dragging&&this.current_tool==="pan"){this.is_pointer_dragging=!1;const t={x:e.global.x-this.drag_start.x,y:e.global.y-this.drag_start.y},i=this.image_editor_context.app.screen,n=this.local_dimensions.width*this.local_scale,r=this.local_dimensions.height*this.local_scale,o=i.width-this.border_padding*2,a=i.height-this.border_padding*2-this.pad_bottom;let l={...t};if(n<=o)l.x=(i.width-n)/2;else{const h=i.width/2,c=h,u=i.width-h-n;l.x=Math.min(Math.max(t.x,u),c)}if(r<=a)l.y=(i.height-this.pad_bottom-r)/2;else{const h=(i.height-this.pad_bottom)/2,c=h,u=i.height-this.pad_bottom-h-r;l.y=Math.min(Math.max(t.y,u),c)}this.image_editor_context.set_image_properties({scale:this.local_scale,position:l,animate:!0})}}}class hw{layers=[];active_layer=null;active_layer_id=null;draw_textures=new Map;layer_store=as({active_layer:"",layers:[]});background_layer=null;image_container;app;fixed_canvas;dark;border_region;layer_options;constructor(e,t,i,n,r,o){this.image_container=e,this.app=t,this.fixed_canvas=i,this.dark=n,this.border_region=r,this.layer_options=o}toggle_layer_visibility(e){const t=this.layers.find(i=>i.id===e);t&&(t.container.visible=!t.container.visible,t.visible=t.container.visible,this.layer_store.update(i=>({active_layer:i.active_layer,layers:this.layers})))}create_background_layer(e,t){this.background_layer&&this.background_layer.destroy();const i=new U;this.background_layer=i;const n=ft.create({width:e,height:t,resolution:window.devicePixelRatio,antialias:!0,scaleMode:sh.NEAREST}),r=new ee(n);i.addChild(r);const o=new we;return o.clear(),o.rect(0,0,e,t).fill({color:this.dark?3355443:16777215,alpha:1}),this.app.renderer.render({container:o,target:n,clear:!0}),this.image_container.addChild(i),i.zIndex=-1,this.update_layer_order(),i}set_layer_options(e,t,i){this.layer_options=e,this.reset_layers(t,i)}async create_background_layer_from_url(e,t,i){const n=this.create_background_layer(t||this.image_container.width,i||this.image_container.height);try{const r=await Y.from(e),o=new ee(r),a=o.texture.width,l=o.texture.height,h=t||this.image_container.width,c=i||this.image_container.height;if(this.fixed_canvas){const u=Math.max(h-this.border_region*2,10),d=Math.max(c-this.border_region*2,10),_=a/l,f=u/d;let m,g,p=this.border_region,y=this.border_region;a<=u&&l<=d?(m=a,g=l):_>f?(m=u,g=u/_):(g=d,m=d*_),p+=Math.round((u-m)/2),y+=Math.round((d-g)/2),o.width=m,o.height=g,o.position.set(p,y)}else{o.position.set(this.border_region,this.border_region),this.background_layer&&this.background_layer.destroy();const u=a+this.border_region*2,d=l+this.border_region*2,_=this.create_background_layer(u,d);return o.width=a,o.height=l,_.addChild(o),_}return n.addChild(o),n}catch(r){return console.error("Error loading image from URL:",r),n}}create_layer({width:e,height:t,layer_name:i,user_created:n,layer_id:r=void 0,make_active:o=!1}){const a=new U,l=r||Math.random().toString(36).substring(2,15),h=i||`Layer ${this.layers.length+1}`;this.layers.push({name:h,id:l,container:a,user_created:n,visible:!0}),this.image_container.addChild(a);const c=ft.create({width:e,height:t,resolution:window.devicePixelRatio,antialias:!0,scaleMode:sh.NEAREST}),u=new ee(c);a.addChild(u);const d=new we;return d.clear(),d.beginFill(0,0),d.drawRect(0,0,e,t),d.endFill(),this.app.renderer.render({container:d,target:c,clear:!0}),this.draw_textures.set(a,c),this.update_layer_order(),o&&this.set_active_layer(l),this.layer_store.set({active_layer:this.active_layer_id||"",layers:this.layers}),a}async add_layer_from_url(e){const{width:t,height:i}=this.image_container.getLocalBounds(),n=this.create_layer({width:t,height:i,layer_name:"Layer 1",user_created:!0}),r=this.layers.findIndex(a=>a.container===n);if(r===-1)return console.error("Could not find newly created layer"),"";const o=this.layers[r].id;try{const a=await Ei.load(e),l=this.draw_textures.get(n);if(!l)return console.error("No draw texture found for layer"),o;const h=new ee(a),c=h.width,u=h.height;let d=this.border_region,_=this.border_region;const f=this.fixed_canvas?t-this.border_region*2:t,m=this.fixed_canvas?i-this.border_region*2:i;if((c<f||u<m)&&(d=Math.floor((f-c)/2),_=Math.floor((m-u)/2)),h.position.set(d,_),c>f||u>m){const g=c/u,p=f/m;let y,b;g>p?(y=f,b=f/g):(b=m,y=m*g),h.width=y,h.height=b,d=this.border_region+Math.floor((f-y)/2),_=this.border_region+Math.floor((m-b)/2),h.position.set(d,_)}return this.app.renderer.render(h,{renderTexture:l}),this.set_active_layer(o),o}catch(a){return console.error("Error loading image from URL:",a),o}}get_active_layer(){return this.active_layer}set_active_layer(e){this.layers.some(t=>t.id===e)&&(this.active_layer=this.layers.find(t=>t.id===e)?.container||this.layers[0]?.container||null,this.active_layer_id=e,this.layer_store.set({active_layer:e,layers:this.layers}))}get_layers(){return this.layers}get_layer_textures(e){const t=this.layers.find(i=>i.id===e);if(t){const i=this.draw_textures.get(t.container);if(i)return{draw:i}}return null}delete_layer(e){const t=this.layers.findIndex(i=>i.id===e);if(t>-1){const i=this.draw_textures.get(this.layers[t].container);if(i&&(i.destroy(),this.draw_textures.delete(this.layers[t].container)),this.layers[t].container.destroy(),this.active_layer===this.layers[t].container){const n=this.layers[Math.max(0,t-1)]||null;this.active_layer=n?.container||null,this.active_layer_id=n?.id||null}this.layers=this.layers.filter(n=>n.id!==e),this.layer_store.update(n=>({active_layer:n.active_layer===e?this.layers[this.layers.length-1]?.id:n.active_layer,layers:this.layers})),this.update_layer_order()}}update_layer_order(){this.background_layer&&(this.background_layer.zIndex=-1),this.layers.forEach((e,t)=>{e.container.zIndex=t})}move_layer(e,t){const i=this.layers.findIndex(n=>n.id===e);if(i>-1){const n=t==="up"?i-1:i+1;this.layers=this.layers.map((r,o)=>o===i?this.layers[n]:o===n?this.layers[i]:r),this.update_layer_order(),this.layer_store.update(r=>({active_layer:e,layers:this.layers}))}}resize_all_layers(e,t,i,n,r,o){new Map(this.layers.map(_=>[_.id,_]));const a=this.background_layer,l=()=>{let _=0,f=0;const m=e-r,g=t-o;return n.includes("left")?_=0:n.includes("right")?_=m:_=Math.floor(m/2),n.includes("top")?f=0:n.includes("bottom")?f=g:f=Math.floor(g/2),{offsetX:_,offsetY:f}};this.background_layer=this._resize_background_layer(a,e,t,i,l);const h=[],c=this.layers.map(_=>({id:_.id,name:_.name,user_created:_.user_created,texture:this.draw_textures.get(_.container),container:_.container}));this.layers=[],this.draw_textures.clear();for(const _ of c){const f=this._resize_single_layer(_,e,t,i,l);f&&h.push(f)}const u=Ws(this.layer_store).active_layer;!h.some(_=>_.id===u)&&h.length>0?this.set_active_layer(h[0].id):h.length===0?this.layer_store.update(_=>({..._,active_layer:""})):this.layer_store.update(_=>({..._})),this.update_layer_order(),setTimeout(()=>{Ei.cache.reset(),this.app.renderer.textureGC.run()},100)}_resize_background_layer(e,t,i,n,r){if(!e)return this.create_background_layer(t,i);let o=e.children.find(l=>l instanceof ee&&l.texture!==e.children[0]?.texture);const a=this.create_background_layer(t,i);if(o){const l=new ee(o.texture);if(l.width=o.width,l.height=o.height,n)l.width=t,l.height=i,l.position.set(0,0);else{const{offsetX:h,offsetY:c}=r();l.position.set(o.x+h,o.y+c)}a.addChild(l)}return a}_resize_single_layer(e,t,i,n,r){if(!e.texture)return console.warn(`No texture found for layer ${e.id}, skipping cleanup.`),e.container&&!e.container.destroyed&&(this.image_container.children.includes(e.container)&&this.image_container.removeChild(e.container),e.container.destroy({children:!0})),null;const o=this.create_layer({width:t,height:i,layer_name:e.name,user_created:e.user_created}),a=this.layers[this.layers.length-1];a.id=e.id;const l=this.draw_textures.get(o);if(!l){if(console.error(`Failed to get texture for newly created layer ${a.id}. Cleaning up.`),o&&!o.destroyed){this.image_container.children.includes(o)&&this.image_container.removeChild(o),o.destroy({children:!0});const c=this.layers.findIndex(u=>u.container===o);c>-1&&this.layers.splice(c,1),this.draw_textures.delete(o)}return e.texture&&!e.texture.destroyed&&e.texture.destroy(!0),e.container&&!e.container.destroyed&&e.container.destroy({children:!0}),null}this.app.renderer.clear({target:l,clearColor:[0,0,0,0]});const h=new ee(e.texture);if(n)h.width=t,h.height=i,h.position.set(0,0);else{const{offsetX:c,offsetY:u}=r();h.position.set(c,u)}return this.app.renderer.render(h,{renderTexture:l}),h.destroy(),e.texture&&!e.texture.destroyed&&e.texture.destroy(!0),e.container&&!e.container.destroyed&&(this.image_container.children.includes(e.container)&&this.image_container.removeChild(e.container),e.container.destroy({children:!0})),a}async get_blobs(e,t){return{background:await Ln(this.app.renderer,this.background_layer,{width:e,height:t,x:0,y:0}),layers:await Promise.all(this.layers.map(async n=>{const r=await Ln(this.app.renderer,n.container,{width:e,height:t,x:0,y:0});return r||null})),composite:await Ln(this.app.renderer,this.image_container)}}reset_layers(e,t,i=!1){const n=i?this.layers.map(r=>[r.name,r.id]):this.layer_options.layers.map(r=>[r,void 0]);this.layers.forEach(r=>{this.delete_layer(r.id)});for(const[r,o]of n)this.create_layer({width:e,height:t,layer_name:r,user_created:!this.layer_options.layers.find(a=>a===r),layer_id:o});if(!i)this.active_layer=this.layers[0].container,this.active_layer_id=this.layers[0].id;else if(this.active_layer=this.layers.find(r=>r.id===this.active_layer_id)?.container||this.layers[0]?.container,!this.active_layer)return;this.layer_store.update(r=>({active_layer:this.active_layer_id||this.layers[0].id,layers:this.layers}))}init_layers(e,t){for(const i of this.layers)this.delete_layer(i.id);for(const i of this.layer_options.layers)this.create_layer({width:e,height:t,layer_name:i,user_created:!1});this.active_layer=this.layers[0].container,this.active_layer_id=this.layers[0].id,this.layer_store.update(i=>({active_layer:this.layers[0].id,layers:this.layers}))}}class cw{constructor(e,t){this.context=e,this.width=t.width,this.height=t.height,this.layer_name=t.layer_name||`Layer ${this.context.layer_manager.get_layers().length+1}`,this.user_created=t.user_created,this.layer_id=t.layer_id||Math.random().toString(36).substring(2,15),this.make_active=t.make_active||!1,this.name="AddLayer";const n=this.context.layer_manager.get_layers().find(r=>r.container===this.context.layer_manager.get_active_layer());this.previous_active_layer=n?.id||null}layer_id;layer_name;width;height;user_created;make_active;previous_active_layer=null;name;async execute(){this.context.layer_manager.create_layer({width:this.width,height:this.height,layer_name:this.layer_name,user_created:this.user_created,layer_id:this.layer_id,make_active:this.make_active})}async undo(){this.context.layer_manager.delete_layer(this.layer_id),this.previous_active_layer&&this.context.layer_manager.set_active_layer(this.previous_active_layer)}}class uw{constructor(e,t){this.context=e,this.name="RemoveLayer";const i=this.context.layer_manager.get_layers(),n=i.find(a=>a.id===t);if(!n)throw new Error(`Layer with ID ${t} not found`);const r=this.context.layer_manager.get_active_layer(),o=n.container===r;if(o){const a=i.findIndex(h=>h.id===t),l=i[Math.max(0,a-1)];this.previous_active_layer=l?.id||null}this.layer_data={id:n.id,name:n.name,user_created:n.user_created,visible:n.visible,was_active:o},this.captureTextureData(t)}layer_data;previous_active_layer=null;texture_copy=null;name;captureTextureData(e){const t=this.context.layer_manager.get_layer_textures(e);if(t)try{const i=t.draw,n=ft.create({width:i.width,height:i.height,resolution:window.devicePixelRatio||1}),r=new ee(i);this.context.app.renderer.render(r,{renderTexture:n}),this.texture_copy=n,r.destroy()}catch(i){console.error("Failed to copy layer texture:",i),this.texture_copy=null}}async execute(){this.context.layer_manager.delete_layer(this.layer_data.id)}async undo(){const e=Ws(this.context.dimensions);if(this.context.layer_manager.create_layer({width:e.width,height:e.height,layer_name:this.layer_data.name,user_created:this.layer_data.user_created,layer_id:this.layer_data.id,make_active:this.layer_data.was_active}),this.texture_copy)try{const t=this.context.layer_manager.get_layer_textures(this.layer_data.id);if(t){const i=new ee(this.texture_copy);this.context.app.renderer.render(i,{renderTexture:t.draw}),i.destroy()}}catch(t){console.error("Failed to restore layer content:",t)}this.layer_data.visible||this.context.layer_manager.toggle_layer_visibility(this.layer_data.id),!this.layer_data.was_active&&this.previous_active_layer&&this.context.layer_manager.set_active_layer(this.previous_active_layer)}destroy(){this.texture_copy&&(this.texture_copy.destroy(),this.texture_copy=null)}}class dw{constructor(e,t,i){this.context=e,this.layer_id=t,this.direction=i,this.name="ReorderLayer";const n=this.context.layer_manager.get_layers();this.original_order=n.map(a=>a.id);const r=n.findIndex(a=>a.id===t);if(r===-1)throw new Error(`Layer with ID ${t} not found`);const o=i==="up"?r-1:r+1;o<0||o>=n.length?this.new_order=[...this.original_order]:(this.new_order=[...this.original_order],[this.new_order[r],this.new_order[o]]=[this.new_order[o],this.new_order[r]])}original_order;new_order;layer_id;direction;name;async execute(){this.context.layer_manager.move_layer(this.layer_id,this.direction)}async undo(){const e=this.context.layer_manager.get_layers();if(this.original_order.join(",")===e.map(r=>r.id).join(","))return;const t=e.findIndex(r=>r.id===this.layer_id);if(t===-1)return;const i=this.original_order.indexOf(this.layer_id),n=t>i?"up":"down";this.context.layer_manager.move_layer(this.layer_id,n)}}const _w={image:()=>new ow,zoom:()=>new ra},Mo={stiffness:.45,damping:.8};class fw{state;scale;position;subscribers;constructor(e){if(!(e instanceof oa))throw new Error("EditorState must be created by ImageEditor");this.scale=1,this.position={x:0,y:0},this.subscribers=new Set,this.state=Object.freeze({get position(){return{...this.position}},get scale(){return this.scale},get current_tool(){return this.current_tool},get current_subtool(){return this.current_subtool},subscribe:this.subscribe.bind(this)})}_set_position(e,t){const i={...this.position};this.position={x:e,y:t},this._notify_subscribers("position",i,this.position)}_set_scale(e){const t=this.scale;this.scale=e,this._notify_subscribers("scale",t,e)}_notify_subscribers(e,t,i){this.subscribers.forEach(n=>{n({property:e,oldValue:t,newValue:i,timestamp:Date.now()})})}subscribe(e){return this.subscribers.add(e),()=>this.subscribers.delete(e)}}class oa{ready;background_image_present=as(!1);min_zoom=as(!0);app;ui_container;image_container;command_manager;layer_manager;tools=new Map;current_tool;current_subtool;target_element;width;height;dimensions;scale;position;state;scale_value=1;position_value={x:0,y:0};dimensions_value={width:0,height:0};layers=as({active_layer:"",layers:[]});outline_container;outline_graphics;background_image;ready_resolve;event_callbacks=new Map;fixed_canvas;dark;border_region;layer_options;overlay_container;overlay_graphics;pad_bottom;theme_mode;constructor(e){this.pad_bottom=e.pad_bottom||0,this.dark=e.dark||!1,this.theme_mode=e.theme_mode||"dark",this.target_element=e.target_element,this.width=e.width,this.height=e.height,this.command_manager=new ja,this.ready=new Promise(t=>{this.ready_resolve=t}),this.fixed_canvas=e.fixed_canvas||!1,this.tools=new Map(e.tools.map(t=>typeof t=="string"?[t,_w[t]()]:[t.name,t]));for(const t of this.tools.values())t?.on&&t.on("change",()=>{this.notify("change")});this.dimensions=mr({width:this.width,height:this.height},Mo),this.scale=mr(1,Mo),this.position=mr({x:0,y:0},Mo),this.state=new fw(this),this.border_region=e.border_region||0,this.layer_options=e.layer_options||{allow_additional_layers:!0,layers:["Layer 1"],disabled:!1},this.scale.subscribe(t=>{this.state._set_scale(t)}),this.position.subscribe(t=>{this.state._set_position(t.x,t.y)}),this.init()}get context(){const e=this;return{app:this.app,ui_container:this.ui_container,image_container:this.image_container,get background_image(){return e.background_image},pad_bottom:this.pad_bottom,command_manager:this.command_manager,layer_manager:this.layer_manager,dimensions:{subscribe:this.dimensions.subscribe},scale:{subscribe:this.scale.subscribe},position:{subscribe:this.position.subscribe},set_image_properties:this.set_image_properties.bind(this),execute_command:this.execute_command.bind(this),resize_canvas:this.resize_canvas.bind(this),reset:this.reset.bind(this),set_background_image:this.set_background_image.bind(this)}}async init(){const e=this.target_element.getBoundingClientRect();new ResizeObserver(n=>{n.forEach(r=>{this.resize_canvas(r.contentBoxSize[0].inlineSize,r.contentBoxSize[0].blockSize)})}).observe(this.target_element),this.app=new Lu,await this.app.init({width:e.width,height:e.height,backgroundAlpha:this.dark?0:1,backgroundColor:this.theme_mode==="dark"?"#27272a":"#ffffff",resolution:window.devicePixelRatio,autoDensity:!0,antialias:!0,powerPreference:"high-performance"});const t=this.app.canvas;t.style.background="transparent",await this.setup_containers(),this.layer_manager.create_background_layer(this.width,this.height),this.layer_manager.init_layers(this.width,this.height);for(const n of this.tools.values())await n.setup(this.context,this.current_tool,this.current_subtool);const i=this.tools.get("zoom");i&&i.min_zoom.subscribe(n=>{this.min_zoom.set(n)}),this.target_element.appendChild(t),this.target_element.style.background="transparent",this.dimensions.subscribe(n=>{this.dimensions_value=n,this.image_container.width=n.width,this.image_container.height=n.height}),this.scale.subscribe(n=>{this.scale_value=n}),this.position.subscribe(n=>{this.position_value=n}),this.app.ticker.add(()=>{this.image_container.scale.set(this.scale_value),this.image_container.position.set(this.position_value.x,this.position_value.y);const n=this.dimensions_value.width*this.scale_value,r=this.dimensions_value.height*this.scale_value,o=Math.round(this.image_container.position.x-this.outline_container.position.x),a=Math.round(this.image_container.position.y-this.outline_container.position.y);if(this.overlay_container.position.set(this.outline_container.position.x,this.outline_container.position.y),this.outline_graphics.clear(),this.outline_graphics.rect(o,a,n,r).fill({color:this.dark?3355443:16777215,alpha:1}),this.border_region>0){const l=this.border_region*this.scale_value,h=o+l-1,c=a+l-1,u=n-l*2+1,d=r-l*2+1;this.overlay_graphics.clear(),this.overlay_graphics.rect(h,c,u,d).stroke({color:10066329,width:1,alpha:0,pixelLine:!0});const _=5,m=_+5,g=10066329;for(let p=h;p<h+u;p+=m)this.overlay_graphics.rect(p,c,Math.min(_,h+u-p),1).fill({color:g,alpha:.7}),this.overlay_graphics.rect(p,c+d,Math.min(_,h+u-p),1).fill({color:g,alpha:.7});for(let p=c;p<c+d;p+=m)this.overlay_graphics.rect(h,p,1,Math.min(_,c+d-p)).fill({color:g,alpha:.7}),this.overlay_graphics.rect(h+u,p,1,Math.min(_,c+d-p)).fill({color:g,alpha:.7})}else this.overlay_graphics.clear()}),this.ready_resolve()}async setup_containers(){if(this.image_container=new U({eventMode:"static",sortableChildren:!0}),this.image_container.width=this.width,this.image_container.height=this.height,this.app.stage.sortableChildren=!0,this.app.stage.alpha=1,this.app.stage.addChild(this.image_container),this.image_container.scale.set(1),this.ui_container=new U({eventMode:"static"}),this.app.stage.addChild(this.ui_container),this.ui_container.width=this.width,this.ui_container.height=this.height,this.outline_container=new U,this.outline_container.zIndex=-10,this.outline_graphics=new we,this.outline_graphics.rect(0,0,this.width,this.height).fill({color:this.dark?3355443:16777215,alpha:0}),!this.dark){const i=new rw({alpha:.1,blur:2,color:0,offset:{x:0,y:0},quality:4,shadowOnly:!1});this.outline_graphics.filters=[i]}this.outline_container.addChild(this.outline_graphics),this.app.stage.addChild(this.outline_container),this.overlay_container=new U,this.app.stage.addChild(this.overlay_container),this.overlay_container.width=this.width,this.overlay_container.height=this.height,this.overlay_container.zIndex=999,this.overlay_container.eventMode="static",this.overlay_container.interactiveChildren=!0,this.overlay_graphics=new we,this.overlay_container.addChild(this.overlay_graphics),this.overlay_graphics.rect(0,0,this.width,this.height).fill({color:0,alpha:0});const e=this.app.screen.width/2,t=this.app.screen.height/2;this.image_container.position.set(e,t),this.outline_container.position.set(e,t),this.layer_manager=new hw(this.image_container,this.app,this.fixed_canvas,this.dark,this.border_region,this.layer_options),this.layers=this.layer_manager.layer_store}resize_canvas(e,t){this.app.renderer&&this.app.renderer.resize(e,t);const i=e/2,n=t/2;this.image_container&&this.image_container.position.set(i,n),this.outline_container&&this.outline_container.position.set(i,n)}reset(){const e=this.tools.get("zoom");e&&(e.cleanup(),e.setup(this.context,this.current_tool,this.current_subtool)),this.tools.get("brush")&&this.set_tool("draw")}async set_image_properties(e){let t=typeof e.animate<"u"?!e.animate:!0;if(e.position){const i=this.position.set(e.position,{hard:t});t&&await i}if(e.scale){const i=this.scale.set(e.scale,{hard:t});t&&await i}if(e.width&&e.height){this.width=e.width,this.height=e.height;const i=this.dimensions.set({width:e.width,height:e.height},{hard:t});t&&await i}}async execute_command(e){await this.command_manager.execute(e)}undo(){this.command_manager.undo(),this.notify("change")}redo(){this.command_manager.redo(),this.notify("change")}async add_image({image:e,resize:t=!0}){await this.tools.get("image").add_image({image:e,fixed_canvas:this.fixed_canvas,border_region:this.border_region})}async add_image_from_url(e){this.command_manager.reset();const t=this.tools.get("image"),i=await Ei.load(e);await t.add_image({image:i,fixed_canvas:this.fixed_canvas,border_region:this.border_region});const n=this.tools.get("resize");n&&typeof n.set_border_region=="function"&&n.set_border_region(this.border_region)}set_tool(e){this.current_tool=e;for(const t of this.tools.values())t.set_tool(this.current_tool,this.current_subtool)}set_subtool(e){this.current_subtool=e;for(const t of this.tools.values())t.set_tool(this.current_tool,this.current_subtool)}set_background_image(e){this.background_image=e}async reset_canvas(){this.layer_manager.reset_layers(this.width,this.height),this.background_image=void 0,this.background_image_present.set(!1),await this.set_image_properties({width:this.width,height:this.height,scale:1,position:{x:0,y:0},animate:!1}),this.command_manager=new ja,this.layer_manager.create_background_layer(this.width,this.height);for(const t of this.tools.values())t.cleanup(),t.setup(this.context,this.current_tool,this.current_subtool);const e=this.tools.get("zoom");e&&e.min_zoom.subscribe(t=>{this.min_zoom.set(t)}),this.notify("change")}add_layer(){const e=new cw(this.context,{width:this.width,height:this.height,user_created:!0,make_active:!0});this.command_manager.execute(e),this.notify("change")}async add_layers_from_url(e){this.command_manager.reset();const t=this.layer_manager.get_layers();if(t.forEach(n=>this.layer_manager.delete_layer(n.id)),e===void 0||e.length===0){this.layer_manager.create_layer({width:this.width,height:this.height,layer_name:void 0,user_created:!1});return}for await(const n of e)await this.layer_manager.add_layer_from_url(n);this.layer_manager.set_active_layer(t[0].id);const i=this.tools.get("resize");i&&typeof i.set_border_region=="function"&&i.set_border_region(this.border_region),this.notify("change"),this.notify("input")}set_layer(e){this.layer_manager.set_active_layer(e),this.notify("change")}move_layer(e,t){const i=new dw(this.context,e,t);this.command_manager.execute(i),this.notify("change")}delete_layer(e){const t=new uw(this.context,e);this.command_manager.execute(t),this.notify("change")}modify_canvas_size(e,t,i,n){const r=this.width,o=this.height;this.layer_manager.resize_all_layers(e,t,n,i,r,o),this.width=e,this.height=t,this.set_image_properties({width:e,height:t,scale:1,position:{x:0,y:0},animate:!1}),this.notify("change")}async get_blobs(){return await this.layer_manager.get_blobs(this.width,this.height)}on(e,t){this.event_callbacks.set(e,[...this.event_callbacks.get(e)||[],t])}off(e,t){this.event_callbacks.set(e,this.event_callbacks.get(e)?.filter(i=>i!==t)||[])}notify(e){for(const t of this.event_callbacks.get(e)||[])t()}destroy(){this.app&&this.app?.destroy()}resize(e,t){this.set_image_properties({width:e,height:t}),this.reset()}async get_crop_bounds(){const e=this.tools.get("crop"),t=e.get_crop_bounds();return{image:await e.get_image(),...t}}get background_image_sprite(){return this.background_image}set_layer_options(e){this.layer_options=e,this.layer_manager.set_layer_options(e,this.width,this.height)}toggle_layer_visibility(e){this.layer_manager.toggle_layer_visibility(e)}}class gw{name="crop";image_editor_context;current_tool="image";current_subtool="crop";CORNER_SIZE=25;LINE_THICKNESS=5;HANDLE_COLOR=0;HIT_AREA_SIZE=40;is_dragging=!1;selected_handle=null;active_corner_index=-1;active_edge_index=-1;last_pointer_position=null;crop_bounds={x:0,y:0,width:0,height:0};crop_ui_container=null;crop_mask=null;dimensions={width:0,height:0};position={x:0,y:0};scale=1;is_dragging_window=!1;drag_start_position=null;drag_start_bounds=null;background_image_watcher=null;has_been_manually_changed=!1;event_callbacks=new Map;async setup(e,t,i){this.image_editor_context=e,this.current_tool=t,this.current_subtool=i,e.dimensions.subscribe(r=>{this.dimensions=r,this.crop_bounds={x:0,y:0,width:r.width,height:r.height},this.crop_ui_container&&this.update_crop_ui(),this.set_crop_mask(),this.update_crop_mask()}),e.position.subscribe(r=>{this.position=r}),e.scale.subscribe(r=>{this.scale=r}),this.background_image_watcher=()=>{this.crop_mask&&this.current_tool==="image"&&this.current_subtool==="crop"&&this.image_editor_context.background_image&&this.image_editor_context.background_image.setMask(this.crop_mask)},this.image_editor_context.app.ticker.add(this.background_image_watcher),await this.init_crop_ui(),this.setup_event_listeners();const n=t==="image"&&i==="crop";this.crop_ui_container&&(this.crop_ui_container.visible=n)}cleanup(){this.crop_ui_container&&this.image_editor_context.app.stage.removeChild(this.crop_ui_container),this.crop_mask&&(this.crop_mask.parent&&this.crop_mask.parent.removeChild(this.crop_mask),this.image_editor_context.background_image&&this.image_editor_context.background_image.mask===this.crop_mask&&(this.image_editor_context.background_image.mask=null),this.image_editor_context.image_container.mask===this.crop_mask&&(this.image_editor_context.image_container.mask=null)),this.crop_mask=null,this.crop_ui_container=null,this.background_image_watcher&&(this.image_editor_context.app.ticker.remove(this.background_image_watcher),this.background_image_watcher=null),this.cleanup_event_listeners()}set_tool(e,t){this.current_tool=e,this.current_subtool=t;const i=e==="image"&&t==="crop";this.crop_ui_container&&(this.crop_ui_container.visible=i),i&&this.crop_mask&&this.update_crop_mask()}set_crop_mask(){this.crop_mask&&(this.crop_mask.parent&&this.crop_mask.parent.removeChild(this.crop_mask),this.image_editor_context.image_container.mask===this.crop_mask&&(this.image_editor_context.image_container.mask=null),this.image_editor_context.background_image&&this.image_editor_context.background_image.mask===this.crop_mask&&(this.image_editor_context.background_image.mask=null)),this.crop_mask=new we,this.image_editor_context.image_container.addChild(this.crop_mask)}async init_crop_ui(){this.image_editor_context.background_image?.getLocalBounds(),this.crop_ui_container=this.make_crop_ui(this.dimensions.width*this.scale,this.dimensions.height*this.scale),this.crop_ui_container.position.set(this.position.x,this.position.y),this.crop_ui_container.visible=!1,this.image_editor_context.app.stage.addChild(this.crop_ui_container),this.set_crop_mask(),this.image_editor_context.app.ticker.add(this.update_crop_ui.bind(this)),this.update_crop_mask()}make_crop_ui(e,t){const i=new U;i.eventMode="static",i.interactiveChildren=!0;const n=new we().rect(0,0,e,t).stroke({width:1,color:0,alignment:0,alpha:.5});return n.eventMode="static",n.cursor="move",n.hitArea=new se(0,0,e,t),n.on("pointerdown",this.handle_window_drag_start.bind(this)),i.addChild(n),this.create_corner_handles(i,e,t),this.create_edge_handles(i,e,t),i}create_handle(e=!1){const t=new U;t.eventMode="static";const i=new we;e?i.rect(0,0,this.CORNER_SIZE*1.5,this.LINE_THICKNESS).fill(this.HANDLE_COLOR):i.rect(0,0,this.CORNER_SIZE,this.LINE_THICKNESS).rect(0,0,this.LINE_THICKNESS,this.CORNER_SIZE).fill(this.HANDLE_COLOR),t.addChild(i);const n=e?this.HIT_AREA_SIZE*1.5:this.HIT_AREA_SIZE;return t.hitArea=new se(-n/2+this.LINE_THICKNESS,-n/2+this.LINE_THICKNESS,n,n),t}create_edge_handles(e,t,i){[-1,1].forEach((n,r)=>{const o=this.create_handle(!0);o.rotation=0,o.on("pointerdown",a=>{this.handle_pointer_down(a,o,-1,r)}),o.x=t/2-this.CORNER_SIZE*3/2,o.y=n<0?-this.LINE_THICKNESS:i,o.cursor="ns-resize",e.addChild(o)}),[-1,1].forEach((n,r)=>{const o=this.create_handle(!0);o.rotation=Math.PI/2,o.on("pointerdown",a=>{this.handle_pointer_down(a,o,-1,r+2)}),o.x=n<0?-this.LINE_THICKNESS:t,o.y=i/2-this.CORNER_SIZE*3/2,o.cursor="ew-resize",e.addChild(o)})}create_corner_handles(e,t,i){[{x:0,y:0,xScale:1,yScale:1,cursor:"nwse-resize"},{x:t,y:0,xScale:-1,yScale:1,cursor:"nesw-resize"},{x:0,y:i,xScale:1,yScale:-1,cursor:"nesw-resize"},{x:t,y:i,xScale:-1,yScale:-1,cursor:"nwse-resize"}].forEach(({x:r,y:o,xScale:a,yScale:l,cursor:h},c)=>{const u=this.create_handle(!1);u.x=r-(a<0?-this.LINE_THICKNESS:this.LINE_THICKNESS),u.y=o-(l<0?-this.LINE_THICKNESS:this.LINE_THICKNESS),u.scale.set(a,l),u.on("pointerdown",d=>{this.handle_pointer_down(d,u,c,-1)}),u.cursor=h,e.addChild(u)})}handle_pointer_down(e,t,i,n){if(this.current_subtool!=="crop")return;e.stopPropagation(),this.is_dragging=!0,this.selected_handle=t,this.active_corner_index=i,this.active_edge_index=n;const r=this.image_editor_context.image_container.toLocal(e.global);this.last_pointer_position=new X(r.x,r.y)}update_crop_bounds(e){this.has_been_manually_changed=!0;const t=new X(e.x,e.y),i={x:this.crop_bounds.x,y:this.crop_bounds.y,width:this.crop_bounds.width,height:this.crop_bounds.height};if(this.active_corner_index!==-1)switch(this.active_corner_index){case 0:const n=Math.max(20,i.width-t.x),r=i.width-n;this.crop_bounds.width=n,this.crop_bounds.x=i.x+r;const o=Math.max(20,i.height-t.y),a=i.height-o;this.crop_bounds.height=o,this.crop_bounds.y=i.y+a;break;case 1:this.crop_bounds.width=Math.max(20,i.width+t.x);const l=Math.max(20,i.height-t.y),h=i.height-l;this.crop_bounds.height=l,this.crop_bounds.y=i.y+h;break;case 2:const c=Math.max(20,i.width-t.x),u=i.width-c;this.crop_bounds.width=c,this.crop_bounds.x=i.x+u,this.crop_bounds.height=Math.max(20,i.height+t.y);break;case 3:this.crop_bounds.width=Math.max(20,i.width+t.x),this.crop_bounds.height=Math.max(20,i.height+t.y);break}else if(this.active_edge_index!==-1)switch(this.active_edge_index){case 0:const n=Math.max(20,i.height-t.y),r=i.height-n;this.crop_bounds.height=n,this.crop_bounds.y=i.y+r;break;case 1:this.crop_bounds.height=Math.max(20,i.height+t.y);break;case 2:const o=Math.max(20,i.width-t.x),a=i.width-o;this.crop_bounds.width=o,this.crop_bounds.x=i.x+a;break;case 3:this.crop_bounds.width=Math.max(20,i.width+t.x);break}this.constrain_crop_bounds(),this.update_crop_ui(),this.update_crop_mask(),this.crop_mask&&this.image_editor_context.background_image&&this.image_editor_context.background_image.setMask(this.crop_mask)}constrain_crop_bounds(){this.crop_bounds.x,this.crop_bounds.y,this.crop_bounds.width,this.crop_bounds.height,this.crop_bounds.width=Math.max(20,Math.min(this.crop_bounds.width,this.dimensions.width)),this.crop_bounds.height=Math.max(20,Math.min(this.crop_bounds.height,this.dimensions.height));const e=this.crop_bounds.x;if(this.crop_bounds.x=Math.max(0,Math.min(this.crop_bounds.x,this.dimensions.width-this.crop_bounds.width)),this.crop_bounds.x!==e&&this.active_corner_index===0||this.active_edge_index===2){const i=this.crop_bounds.x-e;this.crop_bounds.width-=i}const t=this.crop_bounds.y;if(this.crop_bounds.y=Math.max(0,Math.min(this.crop_bounds.y,this.dimensions.height-this.crop_bounds.height)),this.crop_bounds.y!==t&&(this.active_corner_index===0||this.active_corner_index===1||this.active_edge_index===0)){const i=this.crop_bounds.y-t;this.crop_bounds.height-=i}this.crop_bounds.width=Math.max(20,Math.min(this.crop_bounds.width,this.dimensions.width-this.crop_bounds.x)),this.crop_bounds.height=Math.max(20,Math.min(this.crop_bounds.height,this.dimensions.height-this.crop_bounds.y))}update_crop_mask(){if(!this.crop_mask)return;this.crop_mask.clear();const{width:e,height:t}=this.image_editor_context.background_image?.getLocalBounds()??{width:0,height:0};this.crop_mask.rect(0,0,e,t).fill({color:0,alpha:.4}).rect(this.crop_bounds.x,this.crop_bounds.y,this.crop_bounds.width,this.crop_bounds.height).cut(),this.image_editor_context.background_image&&(this.image_editor_context.background_image.mask=null),this.image_editor_context.image_container.mask===this.crop_mask&&(this.image_editor_context.image_container.mask=null)}update_crop_ui(){if(!this.crop_mask||!this.crop_ui_container)return;this.crop_ui_container.position.set(this.position.x+this.crop_bounds.x*this.scale,this.position.y+this.crop_bounds.y*this.scale);const e=this.crop_bounds.width*this.scale,t=this.crop_bounds.height*this.scale,i=this.crop_ui_container.getChildAt(0);i.clear().rect(0,0,e,t).stroke({width:1,color:0,alignment:0,alpha:.5}),i.hitArea=new se(0,0,e,t),this.update_handle_positions(e,t)}update_handle_positions(e,t){if(!this.crop_ui_container)return;const i=this.crop_ui_container.children.slice(1),n=i.slice(0,4),r=[{x:0,y:0},{x:e,y:0},{x:0,y:t},{x:e,y:t}];n.forEach((a,l)=>{const h=l%2===0?1:-1,c=l<2?1:-1;a.position.set(r[l].x-(h<0?-this.LINE_THICKNESS:this.LINE_THICKNESS),r[l].y-(c<0?-this.LINE_THICKNESS:this.LINE_THICKNESS))}),i.slice(4).forEach((a,l)=>{l<2?a.position.set(e/2-this.CORNER_SIZE*1.5/2,l===0?-this.LINE_THICKNESS:t):a.position.set(l===2?0:e+this.LINE_THICKNESS,t/2-this.CORNER_SIZE*1.5/2)})}setup_event_listeners(){const e=this.image_editor_context.app.stage;e.eventMode="static",e.on("pointermove",this.handle_pointer_move.bind(this)),e.on("pointerup",this.handle_pointer_up.bind(this)),e.on("pointerupoutside",this.handle_pointer_up.bind(this))}cleanup_event_listeners(){const e=this.image_editor_context.app.stage;e.off("pointermove",this.handle_pointer_move.bind(this)),e.off("pointerup",this.handle_pointer_up.bind(this)),e.off("pointerupoutside",this.handle_pointer_up.bind(this))}handle_pointer_move(e){if(this.current_subtool==="crop"){if(this.is_dragging_window&&this.drag_start_position&&this.drag_start_bounds){const t=this.image_editor_context.image_container.toLocal(e.global),i=new X(t.x-this.drag_start_position.x,t.y-this.drag_start_position.y);this.crop_bounds={x:this.drag_start_bounds.x+i.x,y:this.drag_start_bounds.y+i.y,width:this.drag_start_bounds.width,height:this.drag_start_bounds.height},this.constrain_crop_bounds(),this.update_crop_mask(),this.crop_mask&&this.image_editor_context.background_image&&this.image_editor_context.background_image.setMask(this.crop_mask);return}if(this.is_dragging&&this.selected_handle&&this.last_pointer_position){const t=this.image_editor_context.image_container.toLocal(e.global),i=new X(t.x,t.y),n=new X(i.x-this.last_pointer_position.x,i.y-this.last_pointer_position.y);this.update_crop_bounds(n),this.last_pointer_position=i,this.update_crop_mask()}}}handle_pointer_up(){this.current_subtool==="crop"&&(this.is_dragging=!1,this.is_dragging_window=!1,this.selected_handle=null,this.last_pointer_position=null,this.drag_start_position=null,this.drag_start_bounds=null,this.active_corner_index=-1,this.active_edge_index=-1,this.notify("change"))}handle_window_drag_start(e){if(this.current_subtool!=="crop")return;e.stopPropagation(),this.is_dragging_window=!0,this.has_been_manually_changed=!0;const t=this.image_editor_context.image_container.toLocal(e.global);this.drag_start_position=new X(t.x,t.y),this.drag_start_bounds={...this.crop_bounds}}get crop_manually_changed(){return this.has_been_manually_changed}on(e,t){this.event_callbacks.set(e,[...this.event_callbacks.get(e)||[],t])}off(e,t){this.event_callbacks.set(e,this.event_callbacks.get(e)?.filter(i=>i!==t)||[])}notify(e){for(const t of this.event_callbacks.get(e)||[])t()}get_crop_bounds(){const{width:e,height:t}=this.image_editor_context.background_image?.getLocalBounds()??{width:0,height:0};return{x:this.crop_bounds.x,y:this.crop_bounds.y,crop_dimensions:{width:this.crop_bounds.width,height:this.crop_bounds.height},image_dimensions:{width:e,height:t}}}get_image(){if(!this.image_editor_context.background_image)return Promise.resolve(null);const e=new U,t=new ee(this.image_editor_context.background_image.texture);return e.addChild(t),Ln(this.image_editor_context.app.renderer,e,this.crop_bounds)}}class mw{constructor(e,t,i,n){this.context=e,this.name="Resize",this.original_width=t.width,this.original_height=t.height,this.original_x=t.x,this.original_y=t.y,this.new_width=i.width,this.new_height=i.height,this.new_x=i.x,this.new_y=i.y,this.reset_ui=n}original_width;original_height;original_x;original_y;new_width;new_height;new_x;new_y;reset_ui;name;async execute(){this.context.background_image&&(this.context.background_image.width=this.new_width,this.context.background_image.height=this.new_height,this.context.background_image.position.set(this.new_x,this.new_y),this.reset_ui())}async undo(){this.context.background_image&&(this.context.background_image.width=this.original_width,this.context.background_image.height=this.original_height,this.context.background_image.position.set(this.original_x,this.original_y),this.reset_ui())}}class pw{name="resize";image_editor_context;current_tool="image";current_subtool="size";current_cursor="unset";CORNER_SIZE=25;LINE_THICKNESS=5;HANDLE_COLOR=0;HIT_AREA_SIZE=40;borderRegion=0;is_dragging=!1;selected_handle=null;active_corner_index=-1;active_edge_index=-1;last_pointer_position=null;resize_ui_container=null;dimensions={width:0,height:0};position={x:0,y:0};scale=1;is_moving=!1;dom_mousedown_handler=null;dom_mousemove_handler=null;dom_mouseup_handler=null;event_callbacks=new Map;last_scale=1;original_state=null;async setup(e,t,i){if(this.image_editor_context=e,this.current_tool=t,this.current_subtool=i,e.background_image)if(e.background_image.borderRegion!==void 0)this.borderRegion=e.background_image.borderRegion;else{const n=e.background_image,r=e.image_container,o=Math.abs(n.position.x-(r.width-n.width)/2)<2,a=Math.abs(n.position.y-(r.height-n.height)/2)<2;(!o||!a)&&(this.borderRegion=Math.max(n.position.x,n.position.y),e.background_image.borderRegion=this.borderRegion)}if(e.dimensions.subscribe(n=>{this.dimensions=n,this.resize_ui_container&&this.update_resize_ui()}),e.position.subscribe(n=>{this.position=n,this.resize_ui_container&&this.current_tool==="image"&&this.current_subtool==="size"&&this.update_resize_ui()}),e.scale.subscribe(n=>{this.scale=n,this.resize_ui_container&&this.current_tool==="image"&&this.current_subtool==="size"&&this.update_resize_ui()}),await this.init_resize_ui(),this.setup_event_listeners(),this.current_subtool==="size"&&this.image_editor_context.background_image){const n=this.image_editor_context.background_image,r=this.image_editor_context.image_container,o=n.width,a=n.height,l=r.getLocalBounds(),h=l.width,c=l.height,u=-(o-h),d=-(a-c),_=0,f=0,m=o>h&&(n.position.x<u||n.position.x>_),g=a>c&&(n.position.y<d||n.position.y>f);(m||g)&&this.apply_boundary_constraints()}}cleanup(){this.resize_ui_container&&this.image_editor_context.app.stage.removeChild(this.resize_ui_container),this.cleanup_event_listeners()}set_tool(e,t){if(this.current_tool=e,this.current_subtool=t,e==="image"&&t==="size"){if(this.image_editor_context.background_image){const i=this.image_editor_context.background_image.borderRegion;typeof i=="number"&&i>0&&(this.borderRegion=i)}if(this.show_resize_ui(),this.image_editor_context.background_image){const i=this.image_editor_context.background_image,n=this.image_editor_context.image_container,r=i.width,o=i.height,a=n.getLocalBounds(),l=a.width,h=a.height,c=l-this.borderRegion*2,u=h-this.borderRegion*2,d=r>c?-(r-c)+this.borderRegion:this.borderRegion,_=r>c?this.borderRegion:l-r-this.borderRegion,f=o>u?-(o-u)+this.borderRegion:this.borderRegion,m=o>u?this.borderRegion:h-o-this.borderRegion,g=i.position.x<d||i.position.x>_,p=i.position.y<f||i.position.y>m;(g||p)&&this.apply_boundary_constraints()}}else this.hide_resize_ui()}async init_resize_ui(){this.resize_ui_container=this.make_resize_ui(this.dimensions.width*this.scale,this.dimensions.height*this.scale),this.resize_ui_container.position.set(this.position.x,this.position.y),this.resize_ui_container.visible=!1,this.image_editor_context.app.stage.addChild(this.resize_ui_container)}make_resize_ui(e,t){const i=new U;i.eventMode="static",i.interactiveChildren=!0;const n=new we().rect(0,0,e,t).stroke({width:1,color:0,alignment:0,alpha:.3}),r=new we;r.rect(0,0,e,t).stroke({width:1,color:0,alpha:.7,pixelLine:!0});const o=5,l=o+5;for(let c=0;c<e;c+=l*2)r.rect(c,0,Math.min(o,e-c),1).fill(0),r.rect(c,t-1,Math.min(o,e-c),1).fill(0);for(let c=0;c<t;c+=l*2)r.rect(0,c,1,Math.min(o,t-c)).fill(0),r.rect(e-1,c,1,Math.min(o,t-c)).fill(0);i.addChild(n),i.addChild(r);const h=new we().rect(0,0,e,t).fill(16777215,0);return h.eventMode="static",h.cursor="move",i.addChild(h),this.create_corner_handles(i,e,t),this.create_edge_handles(i,e,t),i}create_handle(e=!1){const t=new U;t.eventMode="static",t.cursor="pointer";const i=new we,n=e?8:10;i.rect(-n/2,-n/2,n,n).fill(16777215).stroke({width:1,color:this.HANDLE_COLOR}),t.addChild(i);const r=e?this.HIT_AREA_SIZE*1.5:this.HIT_AREA_SIZE;return t.hitArea=new se(-r/2,-r/2,r,r),t}create_edge_handles(e,t,i){[-1,1].forEach((n,r)=>{const o=this.create_handle(!0);o.rotation=0,o.on("pointerdown",a=>{this.handle_pointer_down(a,o,-1,r)}),o.x=t/2,o.y=n<0?0:i,o.cursor="ns-resize",e.addChild(o)}),[-1,1].forEach((n,r)=>{const o=this.create_handle(!0);o.rotation=0,o.on("pointerdown",a=>{this.handle_pointer_down(a,o,-1,r+2)}),o.x=n<0?0:t,o.y=i/2,o.cursor="ew-resize",e.addChild(o)})}create_corner_handles(e,t,i){[{x:0,y:0,cursor:"nwse-resize"},{x:t,y:0,cursor:"nesw-resize"},{x:0,y:i,cursor:"nesw-resize"},{x:t,y:i,cursor:"nwse-resize"}].forEach(({x:r,y:o,cursor:a},l)=>{const h=this.create_handle(!1);h.x=r,h.y=o,h.on("pointerdown",c=>{this.handle_pointer_down(c,h,l,-1)}),h.cursor=a,e.addChild(h)})}handle_pointer_down(e,t,i,n){if(this.current_subtool!=="size")return;e.stopPropagation(),this.is_dragging=!0,this.selected_handle=t,this.active_corner_index=i,this.active_edge_index=n;const r=this.image_editor_context.image_container.toLocal(e.global);if(this.last_pointer_position=new X(r.x,r.y),this.image_editor_context.background_image){const o=this.image_editor_context.background_image;this.original_state={width:o.width,height:o.height,x:o.position.x,y:o.position.y}}}maintain_aspect_ratio(e,t,i,n){const r=Math.abs(n.x),o=Math.abs(n.y);return r>o*1.2?t=e/i:o>r*1.2?e=t*i:e/i>t?t=e/i:e=t*i,{width:e,height:t}}limit_dimensions(e,t,i){const n=this.dimensions.width,r=this.dimensions.height;let o=e,a=t;return o>n&&(i&&this.active_corner_index!==-1&&(a=n/o*a),o=n),a>r&&(i&&this.active_corner_index!==-1&&(o=r/a*o),a=r),o=Math.max(20,o),a=Math.max(20,a),{width:o,height:a}}calculate_position_deltas(e,t,i,n){let r=0,o=0;return this.active_corner_index===0||this.active_edge_index===2?r=e-i:this.active_corner_index===2&&(r=e-i,o=0),(this.active_corner_index===0||this.active_corner_index===1||this.active_edge_index===0)&&(o=t-n),{x:r,y:o}}apply_boundary_constraints(){if(!this.image_editor_context.background_image)return;const e=this.image_editor_context.background_image,t=this.image_editor_context.image_container;let i=e.position.x,n=e.position.y;const r=t.width,o=t.height,a=e.width,l=e.height,h=r-this.borderRegion*2,c=o-this.borderRegion*2;if(a>h){const u=-(a-h)+this.borderRegion,d=this.borderRegion;i=Math.max(u,Math.min(d,i))}else{const u=this.borderRegion,d=r-a-this.borderRegion;(i<u||i>d)&&(i=Math.max(u,Math.min(d,i)))}if(l>c){const u=-(l-c)+this.borderRegion,d=this.borderRegion;n=Math.max(u,Math.min(d,n))}else{const u=this.borderRegion,d=o-l-this.borderRegion;(n<u||n>d)&&(n=Math.max(u,Math.min(d,n)))}if(e.position.set(i,n),this.resize_ui_container){const u=t.position.x+i*this.scale,d=t.position.y+n*this.scale;this.resize_ui_container.position.set(u,d)}}update_resize_bounds(e,t=!1){const i=this.image_editor_context.background_image;if(!i)return;const n=i.width,r=i.height,o=i.position.x,a=i.position.y,l=n/r;let h;if(this.active_corner_index!==-1){switch(this.active_corner_index){case 0:h=new X(o-e.x,a-e.y);break;case 1:h=new X(o+n+e.x,a-e.y);break;case 2:h=new X(o-e.x,a+r+e.y);break;case 3:h=new X(o+n+e.x,a+r+e.y);break;default:return}const c=this.handle_direct_corner_resize(h,n,r,o,a,l,t);i.width=c.width,i.height=c.height,i.position.set(c.x,c.y)}else if(this.active_edge_index!==-1){switch(this.active_edge_index){case 0:h=new X(o+n/2,a-e.y);break;case 1:h=new X(o+n/2,a+r+e.y);break;case 2:h=new X(o-e.x,a+r/2);break;case 3:h=new X(o+n+e.x,a+r/2);break;default:return}const c=this.handle_direct_edge_resize(h,n,r,o,a);i.width=c.width,i.height=c.height,i.position.set(c.x,c.y)}this.update_resize_ui()}update_resize_ui(){if(!this.resize_ui_container||!this.image_editor_context.background_image||!this.image_editor_context.background_image.position)return;const e=this.image_editor_context.background_image,i=this.image_editor_context.image_container.getGlobalPosition(),n=e.position.x*this.scale,r=e.position.y*this.scale;this.resize_ui_container.position.set(i.x+n,i.y+r);const o=e.width*this.scale,a=e.height*this.scale;this.resize_ui_container.getChildAt(0).clear().rect(0,0,o,a).stroke({width:1,color:0,alignment:0,alpha:.3});const h=this.resize_ui_container.getChildAt(1);if(h.clear(),h.rect(0,0,o,a).stroke({width:1,color:0,alpha:.7,pixelLine:!0}),!this.is_dragging&&!this.is_moving&&Math.abs(this.scale-this.last_scale)<.001){for(let f=0;f<o;f+=10*2)h.rect(f,0,Math.min(5,o-f),1).fill(0),h.rect(f,a-1,Math.min(5,o-f),1).fill(0);for(let f=0;f<a;f+=10*2)h.rect(0,f,1,Math.min(5,a-f)).fill(0),h.rect(o-1,f,1,Math.min(5,a-f)).fill(0)}this.last_scale=this.scale,this.resize_ui_container.getChildAt(2).clear().rect(0,0,o,a).fill(16777215,0),this.update_handle_positions(o,a)}update_handle_positions(e,t){if(!this.resize_ui_container)return;const i=this.resize_ui_container.children.slice(3);this.resize_ui_container.getChildAt(2).clear().rect(0,0,e,t).fill(16777215,0);const r=i.slice(0,4),o=[{x:0,y:0},{x:e,y:0},{x:0,y:t},{x:e,y:t}];r.forEach((l,h)=>{l.position.set(o[h].x,o[h].y)}),i.slice(4).forEach((l,h)=>{h<2?l.position.set(e/2,h===0?0:t):l.position.set(h===2?0:e,t/2)})}setup_event_listeners(){const e=this.image_editor_context.app.stage;e.eventMode="static",e.on("pointermove",this.handle_pointer_move.bind(this)),e.on("pointerup",this.handle_pointer_up.bind(this)),e.on("pointerupoutside",this.handle_pointer_up.bind(this)),this.setup_dom_event_listeners()}setup_dom_event_listeners(){this.cleanup_dom_event_listeners();const e=this.image_editor_context.app.canvas;this.dom_mousedown_handler=t=>{if(this.current_subtool!=="size"||this.is_dragging||this.selected_handle||!this.image_editor_context.background_image)return;const i=this.image_editor_context.background_image,r=this.image_editor_context.image_container.getBounds(),o=e.getBoundingClientRect(),a=t.clientX-o.left,l=t.clientY-o.top,h=(a-r.x)/this.scale,c=(l-r.y)/this.scale;h>=i.position.x&&h<=i.position.x+i.width&&c>=i.position.y&&c<=i.position.y+i.height&&(this.is_moving=!0,this.last_pointer_position=new X(h,c),this.original_state={width:i.width,height:i.height,x:i.position.x,y:i.position.y},t.preventDefault(),t.stopPropagation())},this.dom_mousemove_handler=t=>{if(this.current_subtool!=="size"||!this.is_moving||!this.last_pointer_position||!this.image_editor_context.background_image)return;const n=this.image_editor_context.image_container.getBounds(),r=e.getBoundingClientRect(),o=t.clientX-r.left,a=t.clientY-r.top,l=(o-n.x)/this.scale,h=(a-n.y)/this.scale,c=new X(l,h);this.handle_image_dragging(c),this.last_pointer_position=c,t.preventDefault(),t.stopPropagation()},this.dom_mouseup_handler=t=>{if(this.current_subtool==="size"){if(this.original_state&&this.image_editor_context.background_image){const i=this.image_editor_context.background_image,n={width:i.width,height:i.height,x:i.position.x,y:i.position.y};if(this.original_state.width!==n.width||this.original_state.height!==n.height||this.original_state.x!==n.x||this.original_state.y!==n.y){const r=new mw(this.image_editor_context,this.original_state,n,this.update_resize_ui.bind(this));this.image_editor_context.execute_command(r)}this.original_state=null}this.is_moving=!1,this.last_pointer_position=null,this.update_resize_ui(),t.preventDefault(),t.stopPropagation()}},e.addEventListener("mousedown",this.dom_mousedown_handler),window.addEventListener("mousemove",this.dom_mousemove_handler),window.addEventListener("mouseup",this.dom_mouseup_handler)}cleanup_dom_event_listeners(){const e=this.image_editor_context.app.canvas;this.dom_mousedown_handler&&(e.removeEventListener("mousedown",this.dom_mousedown_handler),this.dom_mousedown_handler=null),this.dom_mousemove_handler&&(window.removeEventListener("mousemove",this.dom_mousemove_handler),this.dom_mousemove_handler=null),this.dom_mouseup_handler&&(window.removeEventListener("mouseup",this.dom_mouseup_handler),this.dom_mouseup_handler=null)}cleanup_event_listeners(){const e=this.image_editor_context.app.stage;e.off("pointermove",this.handle_pointer_move.bind(this)),e.off("pointerup",this.handle_pointer_up.bind(this)),e.off("pointerupoutside",this.handle_pointer_up.bind(this)),this.cleanup_dom_event_listeners()}handle_pointer_move(e){if(this.current_subtool!=="size")return;const t=this.image_editor_context.image_container.toLocal(e.global),i=new X(t.x,t.y);if(this.is_moving&&this.last_pointer_position)this.handle_image_dragging(i),this.last_pointer_position=i;else if(this.is_dragging&&this.selected_handle){if(!this.image_editor_context.background_image)return;const n=this.image_editor_context.background_image,r=this.active_corner_index!==-1&&!e.shiftKey,o=n.width,a=n.height,l=n.position.x,h=n.position.y,c=o/a;let u;if(this.active_corner_index!==-1)u=this.handle_direct_corner_resize(i,o,a,l,h,c,r);else if(this.active_edge_index!==-1)u=this.handle_direct_edge_resize(i,o,a,l,h);else return;u=this.limit_dimensions_to_container(u,l,h,o,a,r),n.width=u.width,n.height=u.height,n.position.set(u.x,u.y),this.update_resize_ui(),this.last_pointer_position=i}}handle_pointer_up(){this.current_subtool==="size"&&(this.is_dragging=!1,this.is_moving=!1,this.selected_handle=null,this.last_pointer_position=null,this.active_corner_index=-1,this.active_edge_index=-1,this.update_resize_ui(),this.notify("change"))}handle_image_dragging(e){if(!this.last_pointer_position||!this.image_editor_context.background_image)return;const t=this.image_editor_context.background_image,i=this.image_editor_context.image_container.getLocalBounds(),n=e.x-this.last_pointer_position.x,r=e.y-this.last_pointer_position.y;let o=t.position.x+n,a=t.position.y+r;const l=i.width,h=i.height,c=t.width,u=t.height,d=l-this.borderRegion*2,_=h-this.borderRegion*2;if(c>d){const f=-(c-d)+this.borderRegion,m=this.borderRegion;o=Math.max(f,Math.min(m,o))}else{const f=this.borderRegion,m=l-c-this.borderRegion;o=Math.max(f,Math.min(m,o))}if(u>_){const f=-(u-_)+this.borderRegion,m=this.borderRegion;a=Math.max(f,Math.min(m,a))}else{const f=this.borderRegion,m=h-u-this.borderRegion;a=Math.max(f,Math.min(m,a))}t.position.set(o,a),this.update_resize_ui()}handle_direct_corner_resize(e,t,i,n,r,o,a){let l=0,h=0,c=n,u=r;switch(this.active_corner_index){case 0:l=n+t-e.x,h=r+i-e.y;break;case 1:l=e.x-n,h=r+i-e.y;break;case 2:l=n+t-e.x,h=e.y-r;break;case 3:l=e.x-n,h=e.y-r;break}l=Math.max(20,l),h=Math.max(20,h);let d=l,_=h;if(a){const f={x:e.x-(n+t/2),y:e.y-(r+i/2)},m=Math.abs(Math.atan2(f.y,f.x));m<Math.PI/4||m>3*Math.PI/4?_=d/o:d=_*o}switch(this.active_corner_index){case 0:c=n+t-d,u=r+i-_;break;case 1:u=r+i-_;break;case 2:c=n+t-d;break}return{width:d,height:_,x:c,y:u}}handle_direct_edge_resize(e,t,i,n,r){let o=t,a=i,l=n,h=r;switch(this.active_edge_index){case 0:a=Math.max(20,r+i-e.y),h=e.y;break;case 1:a=Math.max(20,e.y-r);break;case 2:o=Math.max(20,n+t-e.x),l=e.x;break;case 3:o=Math.max(20,e.x-n);break}return{width:o,height:a,x:l,y:h}}limit_dimensions_to_container(e,t,i,n,r,o){let{width:a,height:l,x:h,y:c}=e;const u=this.dimensions.width-this.borderRegion*2,d=this.dimensions.height-this.borderRegion*2,_=a,f=l,m=this.apply_size_limits(a,l,u,d,o,_/f);a=m.width,l=m.height;const g=this.adjust_position_for_resizing(a,l,t,i,n,r);return h=g.x,c=g.y,this.apply_border_constraints(a,l,h,c,o,_/f)}apply_size_limits(e,t,i,n,r,o){let a=e,l=t;const h=a>i,c=l>n;if(h&&c){const u=i/a,d=n/l;u<d?(a=i,r&&(l=a/o)):(l=n,r&&(a=l*o))}else h?(a=i,r&&(l=a/o)):c&&(l=n,r&&(a=l*o));return{width:a,height:l}}adjust_position_for_resizing(e,t,i,n,r,o){let a=i,l=n;switch(this.active_corner_index){case 0:a=i+r-e,l=n+o-t;break;case 1:l=n+o-t;break;case 2:a=i+r-e;break}if(this.active_edge_index!==-1)switch(this.active_edge_index){case 0:l=n+o-t;break;case 2:a=i+r-e;break}return{x:a,y:l}}apply_border_constraints(e,t,i,n,r,o){const a=this.apply_top_left_constraints(e,t,i,n,r,o),l=this.apply_bottom_right_constraints(a.width,a.height,a.x,a.y,r,o);return l.width=Math.max(20,l.width),l.height=Math.max(20,l.height),l}apply_top_left_constraints(e,t,i,n,r,o){let a=e,l=t,h=i,c=n;return h<this.borderRegion&&((this.active_corner_index===0||this.active_corner_index===2||this.active_edge_index===2)&&(a-=this.borderRegion-h,r&&(l=a/o)),h=this.borderRegion),c<this.borderRegion&&((this.active_corner_index===0||this.active_corner_index===1||this.active_edge_index===0)&&(l-=this.borderRegion-c,r&&(a=l*o)),c=this.borderRegion),{width:a,height:l,x:h,y:c}}apply_bottom_right_constraints(e,t,i,n,r,o){let a=e,l=t,h=i,c=n;if(h+a>this.dimensions.width-this.borderRegion)if(this.active_corner_index===1||this.active_corner_index===3||this.active_edge_index===3)a=this.dimensions.width-this.borderRegion-h,r&&(l=a/o);else{const d=h+a-(this.dimensions.width-this.borderRegion);h=Math.max(this.borderRegion,h-d)}if(c+l>this.dimensions.height-this.borderRegion)if(this.active_corner_index===2||this.active_corner_index===3||this.active_edge_index===1)l=this.dimensions.height-this.borderRegion-c,r&&(a=l*o);else{const d=c+l-(this.dimensions.height-this.borderRegion);c=Math.max(this.borderRegion,c-d)}return{width:a,height:l,x:h,y:c}}show_resize_ui(){if(this.resize_ui_container&&(this.resize_ui_container.visible=!0,this.update_resize_ui()),this.image_editor_context.background_image){const e=this.image_editor_context.background_image;this.current_cursor=e.cursor,e.cursor="move"}this.setup_dom_event_listeners()}hide_resize_ui(){if(this.resize_ui_container&&(this.resize_ui_container.visible=!1),this.image_editor_context.background_image){const e=this.image_editor_context.background_image;e.cursor=this.current_cursor}this.cleanup_dom_event_listeners()}on(e,t){this.event_callbacks.set(e,[...this.event_callbacks.get(e)||[],t])}off(e,t){this.event_callbacks.set(e,this.event_callbacks.get(e)?.filter(i=>i!==t)||[])}notify(e){for(const t of this.event_callbacks.get(e)||[])t()}set_border_region(e){this.borderRegion=e,this.image_editor_context?.background_image&&(this.image_editor_context.background_image.borderRegion=e),this.resize_ui_container&&this.update_resize_ui(),this.current_subtool==="size"&&this.image_editor_context?.background_image&&this.apply_boundary_constraints()}}function bw(s,e){(s instanceof U||"cursor"in s)&&(s.cursor=e)}function Ad(s,e){for(const t of s)bw(t,e),t instanceof U&&t.children.length>0&&Ad(t.children,e)}function yw(s){return s!==null&&window.clearTimeout(s),null}class ww{constructor(e,t,i){this.image_editor_context=e,this.state=t,this.scale=i,this.initialize_cursor(),this.initialize_brush_preview()}cursor_graphics=null;cursor_container=null;brush_preview_container=null;brush_preview_graphics=null;is_preview_visible=!1;is_cursor_over_image=!1;cursor_position_check_timeout=null;is_brush_or_erase_active=!1;_bound_update_cursor=null;_bound_check_cursor_over_image=null;is_over_image(){return this.is_cursor_over_image}setup_event_listeners(){this.cleanup_event_listeners(),this._bound_update_cursor=this.update_cursor_position.bind(this),this._bound_check_cursor_over_image=this.check_cursor_over_image.bind(this);const e=this.image_editor_context.app.stage;e.on("pointermove",this._bound_update_cursor),e.on("pointermove",this._bound_check_cursor_over_image),this.image_editor_context.image_container.on("pointerenter",this.on_image_container_pointer_enter.bind(this)),this.image_editor_context.image_container.on("pointerleave",this.on_image_container_pointer_leave.bind(this))}cleanup_event_listeners(){const e=this.image_editor_context.app.stage;this._bound_update_cursor&&(e.off("pointermove",this._bound_update_cursor),this._bound_update_cursor=null),this._bound_check_cursor_over_image&&(e.off("pointermove",this._bound_check_cursor_over_image),this._bound_check_cursor_over_image=null),this.image_editor_context.image_container.off("pointerenter"),this.image_editor_context.image_container.off("pointerleave"),this.cursor_position_check_timeout=yw(this.cursor_position_check_timeout)}initialize_cursor(){this.cursor_container&&(this.cursor_container.parent&&this.cursor_container.parent.removeChild(this.cursor_container),this.cursor_container.destroy({children:!0}),this.cursor_container=null),this.cursor_container=new U,this.image_editor_context.ui_container.addChild(this.cursor_container),this.cursor_graphics=new we,this.cursor_container.addChild(this.cursor_graphics),this.update_cursor_appearance()}update_cursor_appearance(){if(!this.cursor_graphics)return;this.cursor_graphics.clear();const e=this.state.mode==="draw"?tt(this.state.color).toString():16777215;this.cursor_graphics.circle(0,0,this.state.brush_size*this.scale).stroke({width:1.5,color:e,alpha:.8}),this.cursor_graphics.circle(0,0,1).fill({color:e,alpha:.8})}update_cursor_position(e){if(!this.cursor_container)return;const t=this.image_editor_context.image_container.toLocal(e.global),i=this.image_editor_context.ui_container.toLocal(this.image_editor_context.image_container.toGlobal(t));this.cursor_container.position.set(i.x,i.y)}check_cursor_over_image(e){this.cursor_position_check_timeout!==null&&(window.clearTimeout(this.cursor_position_check_timeout),this.cursor_position_check_timeout=null);const i=this.image_editor_context.image_container.getBounds(),n=this.is_cursor_over_image;this.is_cursor_over_image=e.global.x>=i.x&&e.global.x<=i.x+i.width&&e.global.y>=i.y&&e.global.y<=i.y+i.height,n!==this.is_cursor_over_image&&this.update_cursor_and_preview_visibility()}update_cursor_size(){this.update_cursor_appearance()}set_active(e){this.is_brush_or_erase_active=e,this.update_cursor_and_preview_visibility()}update_cursor_visibility(){this.cursor_container&&(this.cursor_container.visible=this.is_cursor_over_image&&this.is_brush_or_erase_active)}preview_brush(e){this.is_preview_visible=e,this.brush_preview_container&&(this.brush_preview_container.visible=e&&this.is_brush_or_erase_active),e&&(this.update_brush_preview(),this.update_brush_preview_position())}initialize_brush_preview(){this.brush_preview_container&&(this.brush_preview_container.parent&&this.brush_preview_container.parent.removeChild(this.brush_preview_container),this.brush_preview_container.destroy({children:!0}),this.brush_preview_container=null),this.brush_preview_container=new U,this.image_editor_context.ui_container.addChild(this.brush_preview_container),this.brush_preview_graphics=new we,this.brush_preview_container.addChild(this.brush_preview_graphics),this.brush_preview_container.visible=!1}update_brush_preview(){if(!this.brush_preview_graphics)return;this.brush_preview_graphics.clear();const e=this.state.brush_size*this.scale,t=this.state.mode==="draw"?tt(this.state.color).setAlpha(this.state.opacity).toString():16777215;this.brush_preview_graphics.circle(0,0,e).fill({color:t,alpha:this.state.mode==="draw"?this.state.opacity:.3}),this.brush_preview_graphics.circle(0,0,e+1).stroke({width:1,color:0,alpha:.5})}update_brush_preview_position(){if(!this.brush_preview_container)return;const e=this.image_editor_context.image_container,t=e.width/2,i=e.height/2,n=e.toGlobal({x:t,y:i}),r=this.image_editor_context.ui_container.toLocal(n);this.brush_preview_container.position.set(r.x,r.y)}on_image_container_pointer_enter(){this.is_cursor_over_image=!0,this.update_cursor_and_preview_visibility()}on_image_container_pointer_leave(){this.is_cursor_over_image=!1,this.update_cursor_and_preview_visibility()}update_cursor_and_preview_visibility(){this.update_cursor_visibility(),this.brush_preview_container&&(this.brush_preview_container.visible=this.is_preview_visible&&this.is_brush_or_erase_active)}cleanup(){this.is_brush_or_erase_active=!1,this.cursor_container&&(this.cursor_container.parent&&this.cursor_container.parent.removeChild(this.cursor_container),this.cursor_container.destroy({children:!0}),this.cursor_container=null),this.brush_preview_container&&(this.brush_preview_container.parent&&this.brush_preview_container.parent.removeChild(this.brush_preview_container),this.brush_preview_container.destroy({children:!0}),this.brush_preview_container=null),this.cursor_position_check_timeout!==null&&(window.clearTimeout(this.cursor_position_check_timeout),this.cursor_position_check_timeout=null)}update_state(e,t){this.state=e,this.scale=t,this.update_cursor_appearance(),this.is_preview_visible&&this.update_brush_preview()}}class xw{constructor(e,t,i,n){this.context=e,this.name="Draw",this.layer_id=t,this.original_texture=this.createTextureFrom(i),this.final_texture=this.createTextureFrom(n)}layer_id;original_texture;final_texture;name;createTextureFrom(e){const t=ft.create({width:e.width,height:e.height,resolution:window.devicePixelRatio||1}),i=new ee(e),n=new U;return n.addChild(i),this.context.app.renderer.render(n,{renderTexture:t}),n.destroy({children:!0}),t}async execute(){const e=this.context.layer_manager.get_layer_textures(this.layer_id);if(!e)return;const t=new ee(this.final_texture),i=new U;i.addChild(t),this.context.app.renderer.render(i,{renderTexture:e.draw}),i.destroy({children:!0})}async undo(){const e=this.context.layer_manager.get_layer_textures(this.layer_id);if(!e)return;const t=new ee(this.original_texture),i=new U;i.addChild(t),this.context.app.renderer.render(i,{renderTexture:e.draw}),i.destroy({children:!0})}}class vw{constructor(e,t){this.image_editor_context=e,this.app=t,this.dimensions={width:this.image_editor_context.image_container.width,height:this.image_editor_context.image_container.height}}stroke_texture=null;erase_texture=null;display_container=null;stroke_container=null;stroke_graphics=null;preview_sprite=null;erase_graphics=null;dimensions;is_new_stroke=!0;current_opacity=1;current_mode="draw";original_layer_texture=null;active_layer_id=null;initialize_textures(){this.cleanup_textures();const e=this.image_editor_context.image_container.getLocalBounds();this.dimensions={width:e.width,height:e.height},this.stroke_texture=ft.create({width:this.dimensions.width,height:this.dimensions.height,resolution:window.devicePixelRatio||1}),this.erase_texture=ft.create({width:this.dimensions.width,height:this.dimensions.height,resolution:window.devicePixelRatio||1}),this.display_container=new U;const t=this.image_editor_context.layer_manager.get_active_layer();t?t.addChild(this.display_container):this.image_editor_context.image_container.addChild(this.display_container),this.stroke_container=new U,this.stroke_graphics=new we,this.stroke_container.addChild(this.stroke_graphics),this.erase_graphics=new we,this.preview_sprite=new ee(this.stroke_texture),this.preview_sprite.alpha=0,this.display_container.addChild(this.preview_sprite);const i=new U;this.app.renderer.render(i,{renderTexture:this.stroke_texture}),this.app.renderer.render(i,{renderTexture:this.erase_texture}),i.destroy(),this.is_new_stroke=!0,this.current_opacity=1}reinitialize(){(this.image_editor_context.image_container.width!==this.dimensions.width||this.image_editor_context.image_container.height!==this.dimensions.height)&&this.initialize_textures()}cleanup_textures(){this.stroke_texture&&(this.stroke_texture.destroy(),this.stroke_texture=null),this.erase_texture&&(this.erase_texture.destroy(),this.erase_texture=null),this.display_container&&(this.display_container.parent&&this.display_container.parent.removeChild(this.display_container),this.display_container.destroy({children:!0}),this.display_container=null),this.original_layer_texture&&(this.original_layer_texture.destroy(),this.original_layer_texture=null),this.stroke_container=null,this.stroke_graphics=null,this.preview_sprite=null,this.erase_graphics=null,this.active_layer_id=null}preserve_canvas_state(){const e=this.image_editor_context.layer_manager.get_active_layer();if(!e)return;const i=this.image_editor_context.layer_manager.get_layers().find(a=>a.container===e);if(!i)return;this.active_layer_id=i.id;const n=this.image_editor_context.layer_manager.get_layer_textures(i.id);if(!n)return;this.original_layer_texture&&this.original_layer_texture.destroy(),this.original_layer_texture=ft.create({width:this.dimensions.width,height:this.dimensions.height,resolution:window.devicePixelRatio||1});const r=new ee(n.draw),o=new U;o.addChild(r),this.app.renderer.render(o,{renderTexture:this.original_layer_texture}),o.destroy({children:!0}),this.is_new_stroke=!0}reset_eraser_mask(){!this.erase_graphics||!this.erase_texture||(this.erase_graphics.clear(),this.erase_graphics.beginFill(16777215,1),this.erase_graphics.drawRect(0,0,this.dimensions.width,this.dimensions.height),this.erase_graphics.endFill(),this.app.renderer.render(this.erase_graphics,{renderTexture:this.erase_texture}))}commit_stroke(){if(!this.stroke_texture||!this.preview_sprite||!this.stroke_graphics||!this.original_layer_texture||!this.active_layer_id)return;this.preview_sprite.visible=!1;const e=this.image_editor_context.layer_manager.get_active_layer();if(!e)return;const i=this.image_editor_context.layer_manager.get_layers().find(u=>u.container===e);if(!i||i.id!==this.active_layer_id)return;const n=this.image_editor_context.layer_manager.get_layer_textures(i.id);if(!n)return;const r=this.current_opacity;if(this.current_mode==="draw"){const u=new U,d=new ee(n.draw);u.addChild(d);const _=new ee(this.stroke_texture);_.alpha=r,u.addChild(_),this.app.renderer.render(u,{renderTexture:n.draw}),u.destroy({children:!0})}else{if(!this.stroke_texture)return;const u=new U,d=new ee(n.draw);u.addChild(d);const _=new ee(this.stroke_texture);u.setMask({mask:_,inverse:!0}),this.app.renderer.render(u,{renderTexture:n.draw}),u.destroy({children:!0})}const o=ft.create({width:this.dimensions.width,height:this.dimensions.height,resolution:window.devicePixelRatio||1}),a=new U,l=new ee(n.draw);a.addChild(l),this.app.renderer.render(a,{renderTexture:o}),a.destroy({children:!0});const h=new xw(this.image_editor_context,this.active_layer_id,this.original_layer_texture,o);this.stroke_graphics&&this.stroke_graphics.clear();const c=new U;this.app.renderer.render(c,{renderTexture:this.stroke_texture}),c.destroy(),this.is_new_stroke=!0,o.destroy(),this.original_layer_texture=null,this.active_layer_id=null,this.image_editor_context.command_manager.execute(h)}calculateDistance(e,t,i,n){return Math.sqrt(Math.pow(i-e,2)+Math.pow(n-t,2))}draw_segment(e,t,i,n,r,o,a,l){if(!this.stroke_graphics||!this.stroke_texture||!this.stroke_container||!this.preview_sprite)return;this.is_new_stroke&&!this.original_layer_texture&&this.preserve_canvas_state(),this.current_mode=l,this.current_opacity=l==="draw"?Math.min(Math.max(a,0),1):.5;const h=r;if(this.is_new_stroke){this.stroke_graphics.clear();const u=new U;this.app.renderer.render(u,{renderTexture:this.stroke_texture}),u.destroy(),this.is_new_stroke=!1}if(l==="draw"){let u=16777215;try{o.startsWith("#")&&(u=parseInt(o.replace("#","0x"),16))}catch{u=16777215}this.stroke_graphics.setFillStyle({color:u,alpha:1})}else this.stroke_graphics.setFillStyle({color:16777215,alpha:1});const c=this.calculateDistance(e,t,i,n);if(c<.1)this.stroke_graphics.circle(e,t,h).fill();else{const u=Math.max(h/3,2),d=Math.max(Math.ceil(c/u),2);for(let _=0;_<d;_++){const f=_/(d-1),m=e+(i-e)*f,g=t+(n-t)*f;this.stroke_graphics.circle(m,g,h).fill()}}if(this.stroke_graphics.endFill(),this.app.renderer.render(this.stroke_container,{renderTexture:this.stroke_texture}),l==="draw")this.preview_sprite.texture=this.stroke_texture,this.preview_sprite.alpha=this.current_opacity,this.preview_sprite.tint=16777215;else{const u=this.image_editor_context.layer_manager.get_active_layer();if(!u)return;const _=this.image_editor_context.layer_manager.get_layers().find(A=>A.container===u);if(!_)return;const f=this.image_editor_context.layer_manager.get_layer_textures(_.id);if(!f)return;const m=ft.create({width:this.dimensions.width,height:this.dimensions.height,resolution:window.devicePixelRatio||1}),g=new U,p=new ee(f.draw);g.addChild(p),this.app.renderer.render(g,{renderTexture:m}),g.destroy({children:!0});const y=new U,b=new ee(this.stroke_texture),w=new we;w.setFillStyle({color:16777215,alpha:.5}),w.rect(0,0,this.dimensions.width,this.dimensions.height).fill(),w.setMask({mask:b,inverse:!1}),y.addChild(w),this.app.renderer.render(y,{renderTexture:m}),this.preview_sprite.texture=m,this.preview_sprite.alpha=1,y.destroy({children:!0}),m.destroy()}this.preview_sprite.visible=!0}get_dimensions(){return this.dimensions}cleanup(){this.cleanup_textures()}}class kw{name="brush";image_editor_context;current_tool;current_subtool;state={opacity:1,brush_size:10,color:"#000000",mode:"draw"};brush_size=10;eraser_size=20;is_drawing=!1;last_x=0;last_y=0;scale=1;_bound_pointer_down=null;_bound_pointer_move=null;_bound_pointer_up=null;event_callbacks=new Map;brush_cursor=null;brush_textures=null;async setup(e,t,i){this.image_editor_context=e,this.current_tool=t,this.current_subtool=i,this.state.mode=t==="erase"?"erase":"draw",this.state.mode==="draw"?this.state.brush_size=this.brush_size:this.state.brush_size=this.eraser_size,e.scale.subscribe(n=>{this.scale=n,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale)}),this.brush_cursor=new ww(this.image_editor_context,this.state,this.scale),this.brush_cursor.set_active(t==="draw"||t==="erase"),this.brush_textures=new vw(this.image_editor_context,e.app),this.brush_textures.initialize_textures(),this.setup_event_listeners(),this.handle_cursors(t)}handle_cursors(e){Ad(this.image_editor_context.image_container.children,"none")}set_tool(e,t){this.current_tool=e,this.current_subtool=t,this.current_tool!=="erase"&&this.current_tool!=="draw"&&this.commit_pending_changes(),this.brush_cursor&&this.brush_cursor.set_active(e==="draw"||e==="erase");const i=e==="erase"?"erase":"draw";(e==="erase"||e==="draw")&&this.brush_textures?.initialize_textures(),this.state.mode!==i&&(this.state.mode=i,this.state.mode==="draw"?this.state.brush_size=this.brush_size:this.state.brush_size=this.eraser_size,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale))}commit_pending_changes(){this.is_drawing&&this.on_pointer_up()}on_pointer_down(e){if(!this.image_editor_context.layer_manager.get_active_layer()?.visible||this.current_tool!=="erase"&&this.current_tool!=="draw"||this.brush_cursor&&!this.brush_cursor.is_over_image())return;this.brush_textures&&this.brush_textures.preserve_canvas_state();const i=this.image_editor_context.image_container.toLocal(e.global);this.is_drawing=!0,this.last_x=i.x,this.last_y=i.y,this.brush_textures&&this.brush_textures.draw_segment(i.x,i.y,i.x,i.y,this.state.brush_size,this.state.color,this.state.opacity,this.state.mode)}on_pointer_move(e){if(this.brush_cursor&&this.brush_cursor.update_cursor_position(e),!this.is_drawing)return;const t=this.image_editor_context.image_container.toLocal(e.global);this.brush_textures&&this.brush_textures.draw_segment(this.last_x,this.last_y,t.x,t.y,this.state.brush_size,this.state.color,this.state.opacity,this.state.mode),this.last_x=t.x,this.last_y=t.y}on_pointer_up(){this.is_drawing&&(this.is_drawing=!1,this.brush_textures&&this.brush_textures.commit_stroke(),this.notify("change"))}setup_event_listeners(){this.cleanup_event_listeners(),this._bound_pointer_down=this.on_pointer_down.bind(this),this._bound_pointer_move=this.on_pointer_move.bind(this),this._bound_pointer_up=this.on_pointer_up.bind(this);const e=this.image_editor_context.image_container;e.eventMode="static",e.interactiveChildren=!0;const t=this.image_editor_context.app.stage;t.eventMode="static",t.on("pointerdown",this._bound_pointer_down),t.on("pointermove",this._bound_pointer_move),t.on("pointerup",this._bound_pointer_up),t.on("pointerupoutside",this._bound_pointer_up),this.brush_cursor&&this.brush_cursor.setup_event_listeners()}cleanup_event_listeners(){const e=this.image_editor_context.app.stage;this._bound_pointer_down&&(e.off("pointerdown",this._bound_pointer_down),this._bound_pointer_down=null),this._bound_pointer_move&&(e.off("pointermove",this._bound_pointer_move),this._bound_pointer_move=null),this._bound_pointer_up&&(e.off("pointerup",this._bound_pointer_up),e.off("pointerupoutside",this._bound_pointer_up),this._bound_pointer_up=null),this.brush_cursor&&this.brush_cursor.cleanup_event_listeners()}set_brush_size(e){this.brush_size=e,this.state.mode==="draw"&&(this.state.brush_size=e,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale))}set_brush_color(e){const t=tt(e).toHexString();this.state.color=t,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale)}set_brush_opacity(e){const t=Math.max(0,Math.min(1,e));this.state.opacity=t,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale)}set_eraser_size(e){this.eraser_size=e,this.state.mode==="erase"&&(this.state.brush_size=e,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale))}get_current_size(){return this.state.mode==="draw"?this.brush_size:this.eraser_size}preview_brush(e){this.brush_cursor&&this.brush_cursor.preview_brush(e)}cleanup(){this.commit_pending_changes(),this.cleanup_event_listeners(),this.brush_textures&&(this.brush_textures.cleanup(),this.brush_textures=null),this.brush_cursor&&(this.brush_cursor.cleanup(),this.brush_cursor=null)}on(e,t){this.event_callbacks.set(e,[...this.event_callbacks.get(e)||[],t])}off(e,t){this.event_callbacks.set(e,this.event_callbacks.get(e)?.filter(i=>i!==t)||[])}notify(e){for(const t of this.event_callbacks.get(e)||[])t()}}const{SvelteComponent:Aw,action_destroyer:Sw,append:gt,attr:At,check_outros:Qt,create_component:Gi,destroy_component:Di,detach:As,element:gi,empty:Cw,ensure_array_like:Uh,flush:zn,group_outros:Jt,init:Mw,insert:Ss,is_function:Tw,listen:Sd,mount_component:Ni,noop:Cd,outro_and_destroy_block:Pw,run_all:zw,safe_not_equal:Iw,set_data:Md,space:Oi,stop_propagation:Td,subscribe:Ew,text:Pd,toggle_class:Qn,transition_in:Q,transition_out:oe,update_keyed_each:Rw}=window.__gradio__svelte__internal,{createEventDispatcher:Fw}=window.__gradio__svelte__internal;function Wh(s,e,t){const i=s.slice();return i[20]=e[t].id,i[21]=e[t].name,i[22]=e[t].user_created,i[23]=e[t].visible,i[25]=t,i}function Hh(s){let e,t,i=(s[0]?"Layers":s[4].layers.find(s[10])?.name)+"",n,r,o,a,l,h,c,u,d;a=new Mf({});let _=s[0]&&qh(s);return{c(){e=gi("div"),t=gi("button"),n=Pd(i),r=Oi(),o=gi("span"),Gi(a.$$.fragment),l=Oi(),_&&_.c(),At(o,"class","icon svelte-1peog37"),At(t,"class","layer-title-button svelte-1peog37"),At(t,"aria-label","Show Layers"),At(e,"class","layer-wrap svelte-1peog37"),Qn(e,"closed",!s[0])},m(f,m){Ss(f,e,m),gt(e,t),gt(t,n),gt(t,r),gt(t,o),Ni(a,o,null),gt(e,l),_&&_.m(e,null),c=!0,u||(d=[Sd(t,"click",Td(s[11])),Sw(h=da.call(null,e,s[19]))],u=!0)},p(f,m){(!c||m&17)&&i!==(i=(f[0]?"Layers":f[4].layers.find(f[10])?.name)+"")&&Md(n,i),f[0]?_?(_.p(f,m),m&1&&Q(_,1)):(_=qh(f),_.c(),Q(_,1),_.m(e,null)):_&&(Jt(),oe(_,1,1,()=>{_=null}),Qt()),h&&Tw(h.update)&&m&1&&h.update.call(null,f[19]),(!c||m&1)&&Qn(e,"closed",!f[0])},i(f){c||(Q(a.$$.fragment,f),Q(_),c=!0)},o(f){oe(a.$$.fragment,f),oe(_),c=!1},d(f){f&&As(e),Di(a),_&&_.d(),u=!1,zw(d)}}}function qh(s){let e,t=[],i=new Map,n,r,o=Uh(s[4].layers);const a=h=>h[25];for(let h=0;h<o.length;h+=1){let c=Wh(s,o,h),u=a(c);i.set(u,t[h]=Kh(u,c))}let l=s[2]&&Zh(s);return{c(){e=gi("ul");for(let h=0;h<t.length;h+=1)t[h].c();n=Oi(),l&&l.c(),At(e,"class","svelte-1peog37")},m(h,c){Ss(h,e,c);for(let u=0;u<t.length;u+=1)t[u]&&t[u].m(e,null);gt(e,n),l&&l.m(e,null),r=!0},p(h,c){c&944&&(o=Uh(h[4].layers),Jt(),t=Rw(t,c,a,1,h,o,i,e,Pw,Kh,n,Wh),Qt()),h[2]?l?(l.p(h,c),c&4&&Q(l,1)):(l=Zh(h),l.c(),Q(l,1),l.m(e,null)):l&&(Jt(),oe(l,1,1,()=>{l=null}),Qt())},i(h){if(!r){for(let c=0;c<o.length;c+=1)Q(t[c]);Q(l),r=!0}},o(h){for(let c=0;c<t.length;c+=1)oe(t[c]);oe(l),r=!1},d(h){h&&As(e);for(let c=0;c<t.length;c+=1)t[c].d();l&&l.d()}}}function Bw(s){let e,t;function i(...n){return s[13](s[20],...n)}return e=new Ve({props:{Icon:ng,size:"small"}}),e.$on("click",i),{c(){Gi(e.$$.fragment)},m(n,r){Ni(e,n,r),t=!0},p(n,r){s=n},i(n){t||(Q(e.$$.fragment,n),t=!0)},o(n){oe(e.$$.fragment,n),t=!1},d(n){Di(e,n)}}}function Lw(s){let e,t;function i(...n){return s[12](s[20],...n)}return e=new Ve({props:{Icon:dg,size:"small"}}),e.$on("click",i),{c(){Gi(e.$$.fragment)},m(n,r){Ni(e,n,r),t=!0},p(n,r){s=n},i(n){t||(Q(e.$$.fragment,n),t=!0)},o(n){oe(e.$$.fragment,n),t=!1},d(n){Di(e,n)}}}function Vh(s){let e,t,i,n,r=s[25]>0&&Xh(s),o=s[25]<s[4].layers.length-1&&Yh(s),a=s[4].layers.length>1&&s[22]&&jh(s);return{c(){e=gi("div"),r&&r.c(),t=Oi(),o&&o.c(),i=Oi(),a&&a.c(),At(e,"class","svelte-1peog37")},m(l,h){Ss(l,e,h),r&&r.m(e,null),gt(e,t),o&&o.m(e,null),gt(e,i),a&&a.m(e,null),n=!0},p(l,h){l[25]>0?r?(r.p(l,h),h&16&&Q(r,1)):(r=Xh(l),r.c(),Q(r,1),r.m(e,t)):r&&(Jt(),oe(r,1,1,()=>{r=null}),Qt()),l[25]<l[4].layers.length-1?o?(o.p(l,h),h&16&&Q(o,1)):(o=Yh(l),o.c(),Q(o,1),o.m(e,i)):o&&(Jt(),oe(o,1,1,()=>{o=null}),Qt()),l[4].layers.length>1&&l[22]?a?(a.p(l,h),h&16&&Q(a,1)):(a=jh(l),a.c(),Q(a,1),a.m(e,null)):a&&(Jt(),oe(a,1,1,()=>{a=null}),Qt())},i(l){n||(Q(r),Q(o),Q(a),n=!0)},o(l){oe(r),oe(o),oe(a),n=!1},d(l){l&&As(e),r&&r.d(),o&&o.d(),a&&a.d()}}}function Xh(s){let e,t;function i(...n){return s[15](s[20],...n)}return e=new Ve({props:{Icon:T_,size:"x-small"}}),e.$on("click",i),{c(){Gi(e.$$.fragment)},m(n,r){Ni(e,n,r),t=!0},p(n,r){s=n},i(n){t||(Q(e.$$.fragment,n),t=!0)},o(n){oe(e.$$.fragment,n),t=!1},d(n){Di(e,n)}}}function Yh(s){let e,t;function i(...n){return s[16](s[20],...n)}return e=new Ve({props:{Icon:B_,size:"x-small"}}),e.$on("click",i),{c(){Gi(e.$$.fragment)},m(n,r){Ni(e,n,r),t=!0},p(n,r){s=n},i(n){t||(Q(e.$$.fragment,n),t=!0)},o(n){oe(e.$$.fragment,n),t=!1},d(n){Di(e,n)}}}function jh(s){let e,t;function i(...n){return s[17](s[20],...n)}return e=new Ve({props:{Icon:m_,size:"x-small"}}),e.$on("click",i),{c(){Gi(e.$$.fragment)},m(n,r){Ni(e,n,r),t=!0},p(n,r){s=n},i(n){t||(Q(e.$$.fragment,n),t=!0)},o(n){oe(e.$$.fragment,n),t=!1},d(n){Di(e,n)}}}function Kh(s,e){let t,i,n,r,o,a=e[21]+"",l,h,c,u,d,_;const f=[Lw,Bw],m=[];function g(b,w){return b[23]?1:0}i=g(e),n=m[i]=f[i](e);function p(){return e[14](e[20])}let y=e[4].layers.length>1&&Vh(e);return{key:s,first:null,c(){t=gi("li"),n.c(),r=Oi(),o=gi("button"),l=Pd(a),c=Oi(),y&&y.c(),At(o,"aria-label",h=`layer-${e[25]+1}`),At(o,"class","svelte-1peog37"),Qn(o,"selected_layer",e[4].active_layer===e[20]),At(t,"class","svelte-1peog37"),this.first=t},m(b,w){Ss(b,t,w),m[i].m(t,null),gt(t,r),gt(t,o),gt(o,l),gt(t,c),y&&y.m(t,null),u=!0,d||(_=Sd(o,"click",Td(p)),d=!0)},p(b,w){e=b;let A=i;i=g(e),i===A?m[i].p(e,w):(Jt(),oe(m[A],1,1,()=>{m[A]=null}),Qt(),n=m[i],n?n.p(e,w):(n=m[i]=f[i](e),n.c()),Q(n,1),n.m(t,r)),(!u||w&16)&&a!==(a=e[21]+"")&&Md(l,a),(!u||w&16&&h!==(h=`layer-${e[25]+1}`))&&At(o,"aria-label",h),(!u||w&16)&&Qn(o,"selected_layer",e[4].active_layer===e[20]),e[4].layers.length>1?y?(y.p(e,w),w&16&&Q(y,1)):(y=Vh(e),y.c(),Q(y,1),y.m(t,null)):y&&(Jt(),oe(y,1,1,()=>{y=null}),Qt())},i(b){u||(Q(n),Q(y),u=!0)},o(b){oe(n),oe(y),u=!1},d(b){b&&As(t),m[i].d(),y&&y.d(),d=!1,_()}}}function Zh(s){let e,t,i;return t=new Ve({props:{Icon:$c,label:"Add Layer",size:"x-small"}}),t.$on("click",s[18]),{c(){e=gi("li"),Gi(t.$$.fragment),At(e,"class","add-layer svelte-1peog37")},m(n,r){Ss(n,e,r),Ni(t,e,null),i=!0},p:Cd,i(n){i||(Q(t.$$.fragment,n),i=!0)},o(n){oe(t.$$.fragment,n),i=!1},d(n){n&&As(e),Di(t)}}}function $w(s){let e,t,i=s[3]&&Hh(s);return{c(){i&&i.c(),e=Cw()},m(n,r){i&&i.m(n,r),Ss(n,e,r),t=!0},p(n,[r]){n[3]?i?(i.p(n,r),r&8&&Q(i,1)):(i=Hh(n),i.c(),Q(i,1),i.m(e.parentNode,e)):i&&(Jt(),oe(i,1,1,()=>{i=null}),Qt())},i(n){t||(Q(i),t=!0)},o(n){oe(i),t=!1},d(n){n&&As(e),i&&i.d(n)}}}function Ow(s,e,t){let i,n=Cd,r=()=>(n(),n=Ew(a,C=>t(4,i=C)),a);s.$$.on_destroy.push(()=>n());const o=Fw();let{layers:a}=e;r();let{enable_additional_layers:l=!0}=e,{enable_layers:h=!0}=e,{show_layers:c=!1}=e;function u(){o("new_layer")}function d(C){o("change_layer",C),t(0,c=!1)}function _(C,M){o("move_layer",{id:C,direction:M})}function f(C){o("delete_layer",C)}const m=C=>C.id===i.active_layer,g=()=>t(0,c=!c),p=(C,M)=>{M.stopPropagation(),o("toggle_layer_visibility",C)},y=(C,M)=>{M.stopPropagation(),o("toggle_layer_visibility",C)},b=C=>d(C),w=(C,M)=>{M.stopPropagation(),_(C,"up")},A=(C,M)=>{M.stopPropagation(),_(C,"down")},v=(C,M)=>{M.stopPropagation(),f(C)},S=C=>{C.stopPropagation(),u()},R=()=>t(0,c=!1);return s.$$set=C=>{"layers"in C&&r(t(1,a=C.layers)),"enable_additional_layers"in C&&t(2,l=C.enable_additional_layers),"enable_layers"in C&&t(3,h=C.enable_layers),"show_layers"in C&&t(0,c=C.show_layers)},[c,a,l,h,i,o,u,d,_,f,m,g,p,y,b,w,A,v,S,R]}class Gw extends Aw{constructor(e){super(),Mw(this,e,Ow,$w,Iw,{layers:1,enable_additional_layers:2,enable_layers:3,show_layers:0})}get layers(){return this.$$.ctx[1]}set layers(e){this.$$set({layers:e}),zn()}get enable_additional_layers(){return this.$$.ctx[2]}set enable_additional_layers(e){this.$$set({enable_additional_layers:e}),zn()}get enable_layers(){return this.$$.ctx[3]}set enable_layers(e){this.$$set({enable_layers:e}),zn()}get show_layers(){return this.$$.ctx[0]}set show_layers(e){this.$$set({show_layers:e}),zn()}}const{SvelteComponent:Dw,append:Nw,attr:Qh,bubble:Bs,check_outros:Uw,create_component:Ww,destroy_component:Hw,detach:qw,element:Jh,flush:In,group_outros:Vw,init:Xw,insert:Yw,mount_component:jw,safe_not_equal:Kw,transition_in:Dn,transition_out:aa}=window.__gradio__svelte__internal;function ec(s){let e,t;return e=new Gw({props:{layers:s[0],enable_additional_layers:s[1],enable_layers:s[2],show_layers:s[3]}}),e.$on("new_layer",s[4]),e.$on("change_layer",s[5]),e.$on("move_layer",s[6]),e.$on("delete_layer",s[7]),e.$on("toggle_layer_visibility",s[8]),{c(){Ww(e.$$.fragment)},m(i,n){jw(e,i,n),t=!0},p(i,n){const r={};n&1&&(r.layers=i[0]),n&2&&(r.enable_additional_layers=i[1]),n&4&&(r.enable_layers=i[2]),n&8&&(r.show_layers=i[3]),e.$set(r)},i(i){t||(Dn(e.$$.fragment,i),t=!0)},o(i){aa(e.$$.fragment,i),t=!1},d(i){Hw(e,i)}}}function Zw(s){let e,t,i,n=s[2]&&ec(s);return{c(){e=Jh("div"),t=Jh("div"),n&&n.c(),Qh(t,"class","toolbar-wrap svelte-nkklfl"),Qh(e,"class","toolbar-wrap-wrap svelte-nkklfl")},m(r,o){Yw(r,e,o),Nw(e,t),n&&n.m(t,null),i=!0},p(r,[o]){r[2]?n?(n.p(r,o),o&4&&Dn(n,1)):(n=ec(r),n.c(),Dn(n,1),n.m(t,null)):n&&(Vw(),aa(n,1,1,()=>{n=null}),Uw())},i(r){i||(Dn(n),i=!0)},o(r){aa(n),i=!1},d(r){r&&qw(e),n&&n.d()}}}function Qw(s,e,t){let{layers:i}=e,{enable_additional_layers:n=!0}=e,{enable_layers:r=!0}=e,{show_layers:o=!1}=e;function a(d){Bs.call(this,s,d)}function l(d){Bs.call(this,s,d)}function h(d){Bs.call(this,s,d)}function c(d){Bs.call(this,s,d)}function u(d){Bs.call(this,s,d)}return s.$$set=d=>{"layers"in d&&t(0,i=d.layers),"enable_additional_layers"in d&&t(1,n=d.enable_additional_layers),"enable_layers"in d&&t(2,r=d.enable_layers),"show_layers"in d&&t(3,o=d.show_layers)},[i,n,r,o,a,l,h,c,u]}class Jw extends Dw{constructor(e){super(),Xw(this,e,Qw,Zw,Kw,{layers:0,enable_additional_layers:1,enable_layers:2,show_layers:3})}get layers(){return this.$$.ctx[0]}set layers(e){this.$$set({layers:e}),In()}get enable_additional_layers(){return this.$$.ctx[1]}set enable_additional_layers(e){this.$$set({enable_additional_layers:e}),In()}get enable_layers(){return this.$$.ctx[2]}set enable_layers(e){this.$$set({enable_layers:e}),In()}get show_layers(){return this.$$.ctx[3]}set show_layers(e){this.$$set({show_layers:e}),In()}}const{SvelteComponent:ex,append:Ot,attr:kt,check_outros:tc,create_component:qt,destroy_component:Vt,destroy_each:tx,detach:Ze,element:jt,ensure_array_like:ic,flush:ni,group_outros:sc,init:ix,insert:Qe,listen:Jn,mount_component:Xt,noop:sx,run_all:nx,safe_not_equal:rx,set_data:ox,space:ut,stop_propagation:zd,text:nc,transition_in:Oe,transition_out:Je}=window.__gradio__svelte__internal,{createEventDispatcher:ax}=window.__gradio__svelte__internal;function rc(s,e,t){const i=s.slice();return i[24]=e[t],i}function oc(s){let e,t;return e=new Ve({props:{Icon:p_,label:"Download"}}),e.$on("click",s[14]),{c(){qt(e.$$.fragment)},m(i,n){Xt(e,i,n),t=!0},p:sx,i(i){t||(Oe(e.$$.fragment,i),t=!0)},o(i){Je(e.$$.fragment,i),t=!1},d(i){Vt(e,i)}}}function ac(s){let e,t,i,n,r,o,a,l=ic([.25,.5,1,2,4]),h=[];for(let c=0;c<5;c+=1)h[c]=lc(rc(s,l,c));return{c(){e=jt("div"),t=jt("ul"),i=jt("li"),n=jt("button"),n.textContent="Fit to screen",r=ut();for(let c=0;c<5;c+=1)h[c].c();kt(n,"class","svelte-eqjom0"),kt(i,"class","svelte-eqjom0"),kt(t,"class","svelte-eqjom0"),kt(e,"class","zoom-controls svelte-eqjom0")},m(c,u){Qe(c,e,u),Ot(e,t),Ot(t,i),Ot(i,n),Ot(t,r);for(let d=0;d<5;d+=1)h[d]&&h[d].m(t,null);o||(a=Jn(n,"click",zd(s[18])),o=!0)},p(c,u){if(u&2048){l=ic([.25,.5,1,2,4]);let d;for(d=0;d<5;d+=1){const _=rc(c,l,d);h[d]?h[d].p(_,u):(h[d]=lc(_),h[d].c(),h[d].m(t,null))}for(;d<5;d+=1)h[d].d(1)}},d(c){c&&Ze(e),tx(h,c),o=!1,a()}}}function lc(s){let e,t,i,n,r;function o(){return s[19](s[24])}return{c(){e=jt("li"),t=jt("button"),t.textContent=`${s[24]*100}%`,i=ut(),kt(t,"class","svelte-eqjom0"),kt(e,"class","svelte-eqjom0")},m(a,l){Qe(a,e,l),Ot(e,t),Ot(e,i),n||(r=Jn(t,"click",zd(o)),n=!0)},p(a,l){s=a},d(a){a&&Ze(e),n=!1,r()}}}function hc(s){let e,t;return e=new Ve({props:{disabled:!s[0],Icon:Lc,label:"Save changes",color:"var(--color-accent)"}}),e.$on("click",s[22]),{c(){qt(e.$$.fragment)},m(i,n){Xt(e,i,n),t=!0},p(i,n){const r={};n&1&&(r.disabled=!i[0]),e.$set(r)},i(i){t||(Oe(e.$$.fragment,i),t=!0)},o(i){Je(e.$$.fragment,i),t=!1},d(i){Vt(e,i)}}}function lx(s){let e,t,i,n,r,o,a,l,h,c,u,d,_,f,m,g,p,y,b,w,A,v,S,R,C=s[4]&&oc(s);t=new Ve({props:{Icon:Bf,label:"Pan",highlight:s[2]==="pan",size:"small",padded:!1,transparent:!0,disabled:s[3]}}),t.$on("click",s[15]),n=new Ve({props:{Icon:Lg,label:"Zoom out"}}),n.$on("click",s[16]),o=new Ve({props:{Icon:Tg,label:"Zoom in"}}),o.$on("click",s[17]);let M=s[7]&&ac(s);g=new Ve({props:{Icon:y_,label:"Undo",disabled:!s[5]}}),g.$on("click",s[20]),y=new Ve({props:{Icon:Kf,label:"Redo",disabled:!s[6]}}),y.$on("click",s[21]);let k=s[1]&&hc(s);return A=new Ve({props:{Icon:b_,label:"Clear canvas"}}),A.$on("click",s[23]),{c(){C&&C.c(),e=ut(),qt(t.$$.fragment),i=ut(),qt(n.$$.fragment),r=ut(),qt(o.$$.fragment),a=ut(),l=jt("div"),h=jt("span"),c=nc(s[8]),u=nc("%"),d=ut(),M&&M.c(),_=ut(),f=jt("div"),m=ut(),qt(g.$$.fragment),p=ut(),qt(y.$$.fragment),b=ut(),k&&k.c(),w=ut(),qt(A.$$.fragment),kt(h,"role","button"),kt(h,"tabindex","0"),kt(h,"class","svelte-eqjom0"),kt(l,"class","zoom-number svelte-eqjom0"),kt(f,"class","separator svelte-eqjom0")},m(P,L){C&&C.m(P,L),Qe(P,e,L),Xt(t,P,L),Qe(P,i,L),Xt(n,P,L),Qe(P,r,L),Xt(o,P,L),Qe(P,a,L),Qe(P,l,L),Ot(l,h),Ot(h,c),Ot(h,u),Ot(l,d),M&&M.m(l,null),Qe(P,_,L),Qe(P,f,L),Qe(P,m,L),Xt(g,P,L),Qe(P,p,L),Xt(y,P,L),Qe(P,b,L),k&&k.m(P,L),Qe(P,w,L),Xt(A,P,L),v=!0,S||(R=[Jn(h,"click",s[10]),Jn(h,"keydown",s[12])],S=!0)},p(P,L){P[4]?C?(C.p(P,L),L&16&&Oe(C,1)):(C=oc(P),C.c(),Oe(C,1),C.m(e.parentNode,e)):C&&(sc(),Je(C,1,1,()=>{C=null}),tc());const B={};L&4&&(B.highlight=P[2]==="pan"),L&8&&(B.disabled=P[3]),t.$set(B),(!v||L&256)&&ox(c,P[8]),P[7]?M?M.p(P,L):(M=ac(P),M.c(),M.m(l,null)):M&&(M.d(1),M=null);const H={};L&32&&(H.disabled=!P[5]),g.$set(H);const F={};L&64&&(F.disabled=!P[6]),y.$set(F),P[1]?k?(k.p(P,L),L&2&&Oe(k,1)):(k=hc(P),k.c(),Oe(k,1),k.m(w.parentNode,w)):k&&(sc(),Je(k,1,1,()=>{k=null}),tc())},i(P){v||(Oe(C),Oe(t.$$.fragment,P),Oe(n.$$.fragment,P),Oe(o.$$.fragment,P),Oe(g.$$.fragment,P),Oe(y.$$.fragment,P),Oe(k),Oe(A.$$.fragment,P),v=!0)},o(P){Je(C),Je(t.$$.fragment,P),Je(n.$$.fragment,P),Je(o.$$.fragment,P),Je(g.$$.fragment,P),Je(y.$$.fragment,P),Je(k),Je(A.$$.fragment,P),v=!1},d(P){P&&(Ze(e),Ze(i),Ze(r),Ze(a),Ze(l),Ze(_),Ze(f),Ze(m),Ze(p),Ze(b),Ze(w)),C&&C.d(P),Vt(t,P),Vt(n,P),Vt(o,P),M&&M.d(),Vt(g,P),Vt(y,P),k&&k.d(P),Vt(A,P),S=!1,nx(R)}}}function hx(s){let e,t;return e=new w_({props:{$$slots:{default:[lx]},$$scope:{ctx:s}}}),{c(){qt(e.$$.fragment)},m(i,n){Xt(e,i,n),t=!0},p(i,[n]){const r={};n&134218239&&(r.$$scope={dirty:n,ctx:i}),e.$set(r)},i(i){t||(Oe(e.$$.fragment,i),t=!0)},o(i){Je(e.$$.fragment,i),t=!1},d(i){Vt(e,i)}}}function cx(s,e,t){let i,{can_save:n=!1}=e,{changeable:r=!1}=e,{current_zoom:o=1}=e,{tool:a}=e,{min_zoom:l=!0}=e,{enable_download:h=!1}=e,{can_undo:c}=e,{can_redo:u}=e;const d=ax();let _=!1;function f(k){k.stopPropagation(),t(7,_=!_)}function m(k){d("set_zoom",k),t(7,_=!1)}function g(k){k.key==="Enter"&&m(o)}const p=k=>{d("download"),k.stopPropagation()},y=k=>{k.stopPropagation(),d("pan")},b=k=>{d("zoom_out"),k.stopPropagation()},w=k=>{d("zoom_in"),k.stopPropagation()},A=()=>m("fit"),v=k=>m(k),S=k=>{d("undo"),k.stopPropagation()},R=k=>{d("redo"),k.stopPropagation()},C=k=>{d("save"),k.stopPropagation()},M=k=>{d("remove_image"),k.stopPropagation()};return s.$$set=k=>{"can_save"in k&&t(0,n=k.can_save),"changeable"in k&&t(1,r=k.changeable),"current_zoom"in k&&t(13,o=k.current_zoom),"tool"in k&&t(2,a=k.tool),"min_zoom"in k&&t(3,l=k.min_zoom),"enable_download"in k&&t(4,h=k.enable_download),"can_undo"in k&&t(5,c=k.can_undo),"can_redo"in k&&t(6,u=k.can_redo)},s.$$.update=()=>{s.$$.dirty&8192&&t(8,i=Math.round(o*100))},[n,r,a,l,h,c,u,_,i,d,f,m,g,o,p,y,b,w,A,v,S,R,C,M]}class ux extends ex{constructor(e){super(),ix(this,e,cx,hx,rx,{can_save:0,changeable:1,current_zoom:13,tool:2,min_zoom:3,enable_download:4,can_undo:5,can_redo:6})}get can_save(){return this.$$.ctx[0]}set can_save(e){this.$$set({can_save:e}),ni()}get changeable(){return this.$$.ctx[1]}set changeable(e){this.$$set({changeable:e}),ni()}get current_zoom(){return this.$$.ctx[13]}set current_zoom(e){this.$$set({current_zoom:e}),ni()}get tool(){return this.$$.ctx[2]}set tool(e){this.$$set({tool:e}),ni()}get min_zoom(){return this.$$.ctx[3]}set min_zoom(e){this.$$set({min_zoom:e}),ni()}get enable_download(){return this.$$.ctx[4]}set enable_download(e){this.$$set({enable_download:e}),ni()}get can_undo(){return this.$$.ctx[5]}set can_undo(e){this.$$set({can_undo:e}),ni()}get can_redo(){return this.$$.ctx[6]}set can_redo(e){this.$$set({can_redo:e}),ni()}}const{SvelteComponent:dx,action_destroyer:_x,add_flush_callback:Ls,append:Pi,attr:Yt,bind:$s,binding_callbacks:zi,bubble:cc,check_outros:rs,create_component:ln,create_slot:fx,destroy_component:hn,detach:Ri,element:us,empty:gx,flush:le,get_all_dirty_from_scope:mx,get_slot_changes:px,group_outros:os,init:bx,insert:Fi,is_function:yx,mount_component:cn,noop:wx,safe_not_equal:xx,space:Bi,toggle_class:es,transition_in:re,transition_out:Te,update_slot_base:vx}=window.__gradio__svelte__internal,{onMount:kx,createEventDispatcher:Ax,tick:uc}=window.__gradio__svelte__internal;function dc(s){let e,t,i,n,r,o=s[22]!=="crop"&&_c(s),a=s[22]!=="crop"&&fc(s),l=s[1]==="image"&&s[22]==="webcam"&&gc(s),h=s[22]!=="crop"&&!s[13].disabled&&mc(s);return{c(){o&&o.c(),e=Bi(),a&&a.c(),t=Bi(),l&&l.c(),i=Bi(),h&&h.c(),n=gx()},m(c,u){o&&o.m(c,u),Fi(c,e,u),a&&a.m(c,u),Fi(c,t,u),l&&l.m(c,u),Fi(c,i,u),h&&h.m(c,u),Fi(c,n,u),r=!0},p(c,u){c[22]!=="crop"?o?(o.p(c,u),u[0]&4194304&&re(o,1)):(o=_c(c),o.c(),re(o,1),o.m(e.parentNode,e)):o&&(os(),Te(o,1,1,()=>{o=null}),rs()),c[22]!=="crop"?a?(a.p(c,u),u[0]&4194304&&re(a,1)):(a=fc(c),a.c(),re(a,1),a.m(t.parentNode,t)):a&&(os(),Te(a,1,1,()=>{a=null}),rs()),c[1]==="image"&&c[22]==="webcam"?l?(l.p(c,u),u[0]&4194306&&re(l,1)):(l=gc(c),l.c(),re(l,1),l.m(i.parentNode,i)):l&&(os(),Te(l,1,1,()=>{l=null}),rs()),c[22]!=="crop"&&!c[13].disabled?h?(h.p(c,u),u[0]&4202496&&re(h,1)):(h=mc(c),h.c(),re(h,1),h.m(n.parentNode,n)):h&&(os(),Te(h,1,1,()=>{h=null}),rs())},i(c){r||(re(o),re(a),re(l),re(h),r=!0)},o(c){Te(o),Te(a),Te(l),Te(h),r=!1},d(c){c&&(Ri(e),Ri(t),Ri(i),Ri(n)),o&&o.d(c),a&&a.d(c),l&&l.d(c),h&&h.d(c)}}}function _c(s){let e,t;return e=new ux({props:{changeable:s[5],min_zoom:s[27],current_zoom:s[26],tool:s[1],can_save:!0,enable_download:s[15],can_undo:s[2],can_redo:s[28]}}),e.$on("set_zoom",s[63]),e.$on("zoom_in",s[64]),e.$on("zoom_out",s[65]),e.$on("remove_image",s[66]),e.$on("save",s[41]),e.$on("pan",s[67]),e.$on("download",s[68]),e.$on("undo",s[44]),e.$on("redo",s[45]),{c(){ln(e.$$.fragment)},m(i,n){cn(e,i,n),t=!0},p(i,n){const r={};n[0]&32&&(r.changeable=i[5]),n[0]&134217728&&(r.min_zoom=i[27]),n[0]&67108864&&(r.current_zoom=i[26]),n[0]&2&&(r.tool=i[1]),n[0]&32768&&(r.enable_download=i[15]),n[0]&4&&(r.can_undo=i[2]),n[0]&268435456&&(r.can_redo=i[28]),e.$set(r)},i(i){t||(re(e.$$.fragment,i),t=!0)},o(i){Te(e.$$.fragment,i),t=!1},d(i){hn(e,i)}}}function fc(s){let e,t,i,n,r,o,a;function l(f){s[69](f)}function h(f){s[70](f)}function c(f){s[71](f)}function u(f){s[72](f)}function d(f){s[73](f)}let _={sources:s[6],transforms:s[7],background:s[0],show_brush_size:s[30],show_brush_color:s[31],show_eraser_size:s[29],brush_options:s[8],eraser_options:s[9],tool:s[1],subtool:s[22]};return s[18]!==void 0&&(_.selected_color=s[18]),s[19]!==void 0&&(_.selected_size=s[19]),s[21]!==void 0&&(_.selected_eraser_size=s[21]),s[20]!==void 0&&(_.selected_opacity=s[20]),s[23]!==void 0&&(_.preview=s[23]),e=new Xy({props:_}),zi.push(()=>$s(e,"selected_color",l)),zi.push(()=>$s(e,"selected_size",h)),zi.push(()=>$s(e,"selected_eraser_size",c)),zi.push(()=>$s(e,"selected_opacity",u)),zi.push(()=>$s(e,"preview",d)),e.$on("tool_change",s[74]),e.$on("subtool_change",s[75]),{c(){ln(e.$$.fragment)},m(f,m){cn(e,f,m),a=!0},p(f,m){const g={};m[0]&64&&(g.sources=f[6]),m[0]&128&&(g.transforms=f[7]),m[0]&1&&(g.background=f[0]),m[0]&1073741824&&(g.show_brush_size=f[30]),m[1]&1&&(g.show_brush_color=f[31]),m[0]&536870912&&(g.show_eraser_size=f[29]),m[0]&256&&(g.brush_options=f[8]),m[0]&512&&(g.eraser_options=f[9]),m[0]&2&&(g.tool=f[1]),m[0]&4194304&&(g.subtool=f[22]),!t&&m[0]&262144&&(t=!0,g.selected_color=f[18],Ls(()=>t=!1)),!i&&m[0]&524288&&(i=!0,g.selected_size=f[19],Ls(()=>i=!1)),!n&&m[0]&2097152&&(n=!0,g.selected_eraser_size=f[21],Ls(()=>n=!1)),!r&&m[0]&1048576&&(r=!0,g.selected_opacity=f[20],Ls(()=>r=!1)),!o&&m[0]&8388608&&(o=!0,g.preview=f[23],Ls(()=>o=!1)),e.$set(g)},i(f){a||(re(e.$$.fragment,f),a=!0)},o(f){Te(e.$$.fragment,f),a=!1},d(f){hn(e,f)}}}function gc(s){let e,t,i,n;return i=new c_({props:{upload:s[12],root:s[10],streaming:!1,mode:"image",include_audio:!1,i18n:s[11],mirror_webcam:s[14].mirror,webcam_constraints:s[14].constraints}}),i.$on("capture",s[40]),i.$on("error",s[76]),i.$on("drag",s[77]),{c(){e=us("div"),t=us("div"),ln(i.$$.fragment),Yt(t,"class","modal-inner svelte-h6gdep"),Yt(e,"class","modal svelte-h6gdep")},m(r,o){Fi(r,e,o),Pi(e,t),cn(i,t,null),n=!0},p(r,o){const a={};o[0]&4096&&(a.upload=r[12]),o[0]&1024&&(a.root=r[10]),o[0]&2048&&(a.i18n=r[11]),o[0]&16384&&(a.mirror_webcam=r[14].mirror),o[0]&16384&&(a.webcam_constraints=r[14].constraints),i.$set(a)},i(r){n||(re(i.$$.fragment,r),n=!0)},o(r){Te(i.$$.fragment,r),n=!1},d(r){r&&Ri(e),hn(i)}}}function mc(s){let e,t;return e=new Jw({props:{enable_additional_layers:s[13].allow_additional_layers,layers:s[16].layers}}),e.$on("new_layer",s[78]),e.$on("change_layer",s[79]),e.$on("move_layer",s[80]),e.$on("delete_layer",s[81]),e.$on("toggle_layer_visibility",s[82]),{c(){ln(e.$$.fragment)},m(i,n){cn(e,i,n),t=!0},p(i,n){const r={};n[0]&8192&&(r.enable_additional_layers=i[13].allow_additional_layers),n[0]&65536&&(r.layers=i[16].layers),e.$set(r)},i(i){t||(re(e.$$.fragment,i),t=!0)},o(i){Te(e.$$.fragment,i),t=!1},d(i){hn(e,i)}}}function pc(s){let e,t,i;return t=new st({props:{Icon:Lc,label:"Confirm crop",show_label:!0,size:"large",padded:!0,color:"white",background:"var(--color-green-500)",label_position:"right"}}),t.$on("click",s[42]),{c(){e=us("div"),ln(t.$$.fragment),Yt(e,"class","crop-confirm-button svelte-h6gdep")},m(n,r){Fi(n,e,r),cn(t,e,null),i=!0},p:wx,i(n){i||(re(t.$$.fragment,n),i=!0)},o(n){Te(t.$$.fragment,n),i=!1},d(n){n&&Ri(e),hn(t)}}}function Sx(s){let e,t,i,n,r,o,a,l,h,c,u,d=s[17]&&dc(s),_=s[22]==="crop"&&pc(s);const f=s[62].default,m=fx(f,s,s[61],null);return{c(){e=us("div"),d&&d.c(),t=Bi(),i=us("div"),n=Bi(),r=us("div"),o=Bi(),_&&_.c(),a=Bi(),m&&m.c(),Yt(i,"class","pixi-target svelte-h6gdep"),es(i,"visible",s[22]!=="crop"),Yt(r,"class","pixi-target-crop svelte-h6gdep"),es(r,"visible",s[22]==="crop"),Yt(e,"data-testid","image"),Yt(e,"class","image-container svelte-h6gdep"),Yt(e,"aria-label","Click to upload or drop files"),Yt(e,"aria-dropeffect","copy"),es(e,"dark-bg",s[22]==="crop")},m(g,p){Fi(g,e,p),d&&d.m(e,null),Pi(e,t),Pi(e,i),s[83](i),Pi(e,n),Pi(e,r),s[84](r),Pi(e,o),_&&_.m(e,null),Pi(e,a),m&&m.m(e,null),h=!0,c||(u=_x(l=s[33].call(null,e,{on_drag_change:s[85],on_files:s[35],accepted_types:"image/*",disable_click:s[32]})),c=!0)},p(g,p){g[17]?d?(d.p(g,p),p[0]&131072&&re(d,1)):(d=dc(g),d.c(),re(d,1),d.m(e,t)):d&&(os(),Te(d,1,1,()=>{d=null}),rs()),(!h||p[0]&4194304)&&es(i,"visible",g[22]!=="crop"),(!h||p[0]&4194304)&&es(r,"visible",g[22]==="crop"),g[22]==="crop"?_?(_.p(g,p),p[0]&4194304&&re(_,1)):(_=pc(g),_.c(),re(_,1),_.m(e,a)):_&&(os(),Te(_,1,1,()=>{_=null}),rs()),m&&m.p&&(!h||p[1]&1073741824)&&vx(m,f,g,g[61],h?px(f,g[61],p,null):mx(g[61]),null),l&&yx(l.update)&&p[0]&8|p[1]&2&&l.update.call(null,{on_drag_change:g[85],on_files:g[35],accepted_types:"image/*",disable_click:g[32]}),(!h||p[0]&4194304)&&es(e,"dark-bg",g[22]==="crop")},i(g){h||(re(d),re(_),re(m,g),h=!0)},o(g){Te(d),Te(_),Te(m,g),h=!1},d(g){g&&Ri(e),d&&d.d(),s[83](null),s[84](null),_&&_.d(),m&&m.d(g),c=!1,u()}}}function Cx(s,e,t){let i,{$$slots:n={},$$scope:r}=e;const{drag:o,open_file_upload:a}=__(),l=Ax(),h=!0,c=null;let{changeable:u=!1}=e,{sources:d=["upload","webcam","clipboard"]}=e,{transforms:_=["crop","resize"]}=e,{canvas_size:f}=e,{is_dragging:m=!1}=e,{background_image:g=!1}=e,{brush_options:p}=e,{eraser_options:y}=e,{fixed_canvas:b=!1}=e,{root:w}=e,{i18n:A}=e,{upload:v}=e,{composite:S}=e,{layers:R}=e,{background:C}=e,{border_region:M=0}=e,{layer_options:k}=e,{current_tool:P}=e,{webcam_options:L}=e,{show_download_button:B=!1}=e,{theme_mode:H}=e,F,O;function Ie(){$.set_tool(P),$.set_subtool(wi)}function q(){return k&&$&&ze}let{has_drawn:te=!1}=e;async function E(){return $?!g&&!te&&!R.length?{background:null,layers:[],composite:null}:await $.get_blobs():{background:null,layers:[],composite:null}}let $;function ye(x){$.add_image({image:x})}let Pe;async function ge(x){if(!$||!x||!q())return;let W;if(typeof x=="string")W=x;else if(x?.meta?._type==="gradio.FileData"&&x?.url)W=x.url;else{console.warn("Invalid source provided to add_image_from_url:",x);return}try{Pe=$.add_image_from_url(W);let je=ne.add_image_from_url(W);await Promise.all([Pe,je]),ne.set_tool("image"),ne.set_subtool("crop"),t(0,g=!0),l("upload"),l("input")}catch(je){console.error("Error adding image from URL:",je)}}async function me(x){if(!(!$||!x.length||!q())&&Array.isArray(x)&&x.every(W=>W?.meta?._type==="gradio.FileData"))try{await Pe,await $.add_layers_from_url(x.map(W=>W.url)),l("change"),l("input")}catch(W){console.error("Error adding layer from URL:",W)}}let xe,ve,ke=1,ze=!1,at=!0,z={width:0,height:0};async function ue(){if(!$||!ze||!ve)return;if(await uc(),F.offsetParent!==null){const W=F.getBoundingClientRect();(W.width!==z.width||W.height!==z.height)&&(ve.set_zoom("fit"),z={width:W.width,height:W.height})}}kx(()=>{let x,W;return lt().then(()=>{x=new IntersectionObserver(()=>{ue()}),W=new ResizeObserver(()=>{ue()}),x.observe(F),W.observe(F)}),()=>{x&&x.disconnect(),W&&W.disconnect(),$&&$.destroy()}});let ne,Ee,{can_undo:I=!1}=e,Ae=!1;async function lt(){t(59,xe=new kw),ve=new ra,t(16,$=new oa({target_element:F,width:f[0],height:f[1],tools:["image",ve,new pw,xe],fixed_canvas:b,border_region:M,layer_options:k,theme_mode:H})),xe.on("change",()=>{t(4,te=!0)}),t(60,Ee=new ra),ne=new oa({target_element:O,width:f[0],height:f[1],tools:["image",Ee,new gw],dark:!0,fixed_canvas:!1,border_region:0,pad_bottom:40}),$.scale.subscribe(x=>{t(26,ke=x)}),$.min_zoom.subscribe(x=>{t(27,at=x)}),$.dimensions.subscribe(x=>{z={...x}}),await Promise.all([$.ready,ne.ready]).then(()=>{ae({tool:"image"}),t(17,ze=!0),d.length>0?ae({tool:"image"}):ae({tool:"draw"}),ne.set_subtool("crop")}),$.on("change",()=>{l("change"),t(2,I=$.command_manager.history.previous!==null),t(28,Ae=$.command_manager.history.next!==null)}),C||R.length>0?(C&&await ge(C),R.length>0&&await me(R),ae({tool:"draw"})):S&&(await ge(S),ae({tool:"draw"}))}async function Ye(x){if(x==null||!d.includes("upload"))return;const W=Array.isArray(x)?x[0]:x;await $.add_image({image:W}),await ne.add_image({image:W}),ne.reset(),t(0,g=!0),ae({tool:"draw"}),l("upload"),l("input"),l("change"),t(2,I=$.command_manager.history.previous!==null),t(28,Ae=$.command_manager.history.next!==null)}function ae({tool:x}){$.set_tool(x),t(1,P=x),x==="image"&&(ne.set_tool("image"),ne.set_subtool("crop"))}function Fe({tool:x,subtool:W}){$.set_subtool(W),t(22,wi=W),W!==null&&(x==="draw"&&(W==="size"?t(30,un=!0):W==="color"&&t(31,dn=!0)),x==="erase"&&W==="size"&&t(29,ei=!0),x==="image"&&W==="paste"&&dr(),x==="image"&&W==="upload"&&uc().then(()=>{t(32,i=!1),a()}))}let ei=!1,Se,It,Nt=1,ti;function cr(){const x=p.default_color==="auto"?p.colors[0]:p.default_color;if(Array.isArray(x))t(18,Se=x[0]),t(20,Nt=x[1]);else{t(18,Se=x);const W=tt(x);W.getAlpha()<1?t(20,Nt=W.getAlpha()):t(20,Nt=1)}t(19,It=typeof p.default_size=="number"?p.default_size:25)}function ur(){t(21,ti=y.default_size==="auto"?25:y.default_size)}let un=!1,dn=!1,wi=null,Ui=!1;function _n(x){ve.set_zoom(x)}function Ms(x){ve.set_zoom(x==="in"?ke+(ke<1?.1:ke*.1):ke-(ke<1?.1:ke*.1))}async function dr(){const x=await navigator.clipboard.read();for(let W=0;W<x.length;W++){const je=x[W].types.find(xi=>xi.startsWith("image/"));if(je){const xi=await x[W].getType(je);Ye(xi)}}}function T(x){x.detail!==null&&Ye(x.detail),Fe({tool:P,subtool:null})}function _r(){l("save")}async function $d(){const{image:x}=await ne.get_crop_bounds();x&&(await $.add_image({image:x,resize:!1}),Fe({tool:"image",subtool:null}),l("change"),l("input"))}async function Ra(){const W=(await $.get_blobs()).composite;if(!W){l("download_error","Unable to generate image to download.");return}const je=URL.createObjectURL(W),xi=document.createElement("a");xi.href=je,xi.download="image.png",xi.click(),URL.revokeObjectURL(je)}function Od(){$.undo(),t(2,I=$.command_manager.history.previous!==null),t(28,Ae=$.command_manager.history.next!==null)}function Gd(){$.redo(),t(2,I=$.command_manager.history.previous!==null),t(28,Ae=$.command_manager.history.next!==null)}const Dd=x=>_n(x.detail),Nd=()=>Ms("in"),Ud=()=>Ms("out"),Wd=()=>{l("clear"),$.reset_canvas(),ae({tool:"image"}),t(0,g=!1),t(4,te=!1)},Hd=x=>{ae({tool:"pan"})},qd=()=>Ra();function Vd(x){Se=x,t(18,Se)}function Xd(x){It=x,t(19,It)}function Yd(x){ti=x,t(21,ti)}function jd(x){Nt=x,t(20,Nt)}function Kd(x){Ui=x,t(23,Ui)}const Zd=x=>ae(x.detail),Qd=x=>Fe(x.detail);function Jd(x){cc.call(this,s,x)}function e_(x){cc.call(this,s,x)}const t_=()=>{$.add_layer()},i_=x=>{$.set_layer(x.detail),P==="draw"&&ae({tool:"draw"})},s_=x=>{$.move_layer(x.detail.id,x.detail.direction)},n_=x=>{$.delete_layer(x.detail)},r_=x=>{$.toggle_layer_visibility(x.detail)};function o_(x){zi[x?"unshift":"push"](()=>{F=x,t(24,F)})}function a_(x){zi[x?"unshift":"push"](()=>{O=x,t(25,O)})}const l_=x=>t(3,m=x);return s.$$set=x=>{"changeable"in x&&t(5,u=x.changeable),"sources"in x&&t(6,d=x.sources),"transforms"in x&&t(7,_=x.transforms),"canvas_size"in x&&t(48,f=x.canvas_size),"is_dragging"in x&&t(3,m=x.is_dragging),"background_image"in x&&t(0,g=x.background_image),"brush_options"in x&&t(8,p=x.brush_options),"eraser_options"in x&&t(9,y=x.eraser_options),"fixed_canvas"in x&&t(49,b=x.fixed_canvas),"root"in x&&t(10,w=x.root),"i18n"in x&&t(11,A=x.i18n),"upload"in x&&t(12,v=x.upload),"composite"in x&&t(50,S=x.composite),"layers"in x&&t(51,R=x.layers),"background"in x&&t(52,C=x.background),"border_region"in x&&t(53,M=x.border_region),"layer_options"in x&&t(13,k=x.layer_options),"current_tool"in x&&t(1,P=x.current_tool),"webcam_options"in x&&t(14,L=x.webcam_options),"show_download_button"in x&&t(15,B=x.show_download_button),"theme_mode"in x&&t(54,H=x.theme_mode),"has_drawn"in x&&t(4,te=x.has_drawn),"can_undo"in x&&t(2,I=x.can_undo),"$$scope"in x&&t(61,r=x.$$scope)},s.$$.update=()=>{s.$$.dirty[0]&73728&&k&&q()&&($.set_layer_options(k),Ie()),s.$$.dirty[0]&196608|s.$$.dirty[1]&3670016&&C==null&&R.length==0&&S==null&&$&&ze&&($.reset_canvas(),ae({tool:"image"}),t(0,g=!1),t(4,te=!1)),s.$$.dirty[0]&4194306|s.$$.dirty[1]&536870912&&P==="image"&&wi==="crop"&&Ee.set_zoom("fit"),s.$$.dirty[0]&65540&&t(0,g=I&&$.command_manager.contains("AddImage")),s.$$.dirty[0]&768&&(p&&cr(),y&&ur()),s.$$.dirty[0]&262400|s.$$.dirty[1]&268435456&&xe?.set_brush_color((()=>{let x;if(Se==="auto"){const W=p.colors.find(je=>Array.isArray(je)?je[0]===p.default_color:je===p.default_color)||p.colors[0];x=Array.isArray(W)?W[0]:W}else x=Se;return x})()),s.$$.dirty[0]&524288|s.$$.dirty[1]&268435456&&xe?.set_brush_size(typeof It=="number"?It:25),s.$$.dirty[0]&2097152|s.$$.dirty[1]&268435456&&xe?.set_eraser_size(typeof ti=="number"?ti:25),s.$$.dirty[0]&4194371&&t(32,i=P!=="image"||P==="image"&&g||P==="image"&&wi==="webcam"||!d.includes("upload")),s.$$.dirty[0]&8388608|s.$$.dirty[1]&268435456&&xe?.preview_brush(Ui),s.$$.dirty[0]&1048576|s.$$.dirty[1]&268435456&&xe?.set_brush_opacity(Nt),s.$$.dirty[1]&2621440&&ge(S||C),s.$$.dirty[1]&1048576&&me(R)},[g,P,I,m,te,u,d,_,p,y,w,A,v,k,L,B,$,ze,Se,It,Nt,ti,wi,Ui,F,O,ke,at,Ae,ei,un,dn,i,o,l,Ye,ae,Fe,_n,Ms,T,_r,$d,Ra,Od,Gd,h,c,f,b,S,R,C,M,H,E,ye,ge,me,xe,Ee,r,n,Dd,Nd,Ud,Wd,Hd,qd,Vd,Xd,Yd,jd,Kd,Zd,Qd,Jd,e_,t_,i_,s_,n_,r_,o_,a_,l_]}class Mx extends dx{constructor(e){super(),bx(this,e,Cx,Sx,xx,{antialias:46,full_history:47,changeable:5,sources:6,transforms:7,canvas_size:48,is_dragging:3,background_image:0,brush_options:8,eraser_options:9,fixed_canvas:49,root:10,i18n:11,upload:12,composite:50,layers:51,background:52,border_region:53,layer_options:13,current_tool:1,webcam_options:14,show_download_button:15,theme_mode:54,has_drawn:4,get_blobs:55,add_image:56,add_image_from_url:57,add_layers_from_url:58,can_undo:2},null,[-1,-1,-1,-1])}get antialias(){return this.$$.ctx[46]}get full_history(){return this.$$.ctx[47]}get changeable(){return this.$$.ctx[5]}set changeable(e){this.$$set({changeable:e}),le()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),le()}get transforms(){return this.$$.ctx[7]}set transforms(e){this.$$set({transforms:e}),le()}get canvas_size(){return this.$$.ctx[48]}set canvas_size(e){this.$$set({canvas_size:e}),le()}get is_dragging(){return this.$$.ctx[3]}set is_dragging(e){this.$$set({is_dragging:e}),le()}get background_image(){return this.$$.ctx[0]}set background_image(e){this.$$set({background_image:e}),le()}get brush_options(){return this.$$.ctx[8]}set brush_options(e){this.$$set({brush_options:e}),le()}get eraser_options(){return this.$$.ctx[9]}set eraser_options(e){this.$$set({eraser_options:e}),le()}get fixed_canvas(){return this.$$.ctx[49]}set fixed_canvas(e){this.$$set({fixed_canvas:e}),le()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),le()}get i18n(){return this.$$.ctx[11]}set i18n(e){this.$$set({i18n:e}),le()}get upload(){return this.$$.ctx[12]}set upload(e){this.$$set({upload:e}),le()}get composite(){return this.$$.ctx[50]}set composite(e){this.$$set({composite:e}),le()}get layers(){return this.$$.ctx[51]}set layers(e){this.$$set({layers:e}),le()}get background(){return this.$$.ctx[52]}set background(e){this.$$set({background:e}),le()}get border_region(){return this.$$.ctx[53]}set border_region(e){this.$$set({border_region:e}),le()}get layer_options(){return this.$$.ctx[13]}set layer_options(e){this.$$set({layer_options:e}),le()}get current_tool(){return this.$$.ctx[1]}set current_tool(e){this.$$set({current_tool:e}),le()}get webcam_options(){return this.$$.ctx[14]}set webcam_options(e){this.$$set({webcam_options:e}),le()}get show_download_button(){return this.$$.ctx[15]}set show_download_button(e){this.$$set({show_download_button:e}),le()}get theme_mode(){return this.$$.ctx[54]}set theme_mode(e){this.$$set({theme_mode:e}),le()}get has_drawn(){return this.$$.ctx[4]}set has_drawn(e){this.$$set({has_drawn:e}),le()}get get_blobs(){return this.$$.ctx[55]}get add_image(){return this.$$.ctx[56]}get add_image_from_url(){return this.$$.ctx[57]}get add_layers_from_url(){return this.$$.ctx[58]}get can_undo(){return this.$$.ctx[2]}set can_undo(e){this.$$set({can_undo:e}),le()}}const Tx=/^(#\s*)(.+)$/m;function Px(s){const e=s.trim(),t=e.match(Tx);if(!t)return[!1,e||!1];const[i,,n]=t,r=n.trim();if(e===i)return[r,!1];const o=t.index!==void 0?t.index+i.length:0,l=e.substring(o).trim()||!1;return[r,l]}const{SvelteComponent:zx,add_flush_callback:Os,append:er,attr:hr,bind:Gs,binding_callbacks:ss,bubble:To,create_component:bc,destroy_component:yc,detach:Ct,element:Cs,empty:za,flush:ie,init:Ix,insert:Mt,mount_component:wc,noop:Ex,safe_not_equal:Rx,set_data:Id,space:tr,text:Ed,transition_in:xc,transition_out:vc}=window.__gradio__svelte__internal,{createEventDispatcher:Fx}=window.__gradio__svelte__internal;function kc(s){let e,t,i,n=s[3]&&s[3].length&&Ac(s),r=s[3]&&s[3].length&&s[1]&&!s[18]&&Mc(),o=s[1]&&!s[18]&&Tc();return{c(){e=Cs("div"),n&&n.c(),t=tr(),r&&r.c(),i=tr(),o&&o.c(),hr(e,"class","empty wrap svelte-a0rsm5")},m(a,l){Mt(a,e,l),n&&n.m(e,null),er(e,t),r&&r.m(e,null),er(e,i),o&&o.m(e,null)},p(a,l){a[3]&&a[3].length?n?n.p(a,l):(n=Ac(a),n.c(),n.m(e,t)):n&&(n.d(1),n=null),a[3]&&a[3].length&&a[1]&&!a[18]?r||(r=Mc(),r.c(),r.m(e,i)):r&&(r.d(1),r=null),a[1]&&!a[18]?o||(o=Tc(),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},d(a){a&&Ct(e),n&&n.d(),r&&r.d(),o&&o.d()}}}function Ac(s){let e;function t(r,o){return r[29]||r[28]?Lx:Bx}let i=t(s),n=i(s);return{c(){n.c(),e=za()},m(r,o){n.m(r,o),Mt(r,e,o)},p(r,o){i===(i=t(r))&&n?n.p(r,o):(n.d(1),n=i(r),n&&(n.c(),n.m(e.parentNode,e)))},d(r){r&&Ct(e),n.d(r)}}}function Bx(s){let e;return{c(){e=Cs("div"),e.textContent="Upload an image"},m(t,i){Mt(t,e,i)},p:Ex,d(t){t&&Ct(e)}}}function Lx(s){let e,t,i=s[29]&&Sc(s),n=s[28]&&Cc(s);return{c(){i&&i.c(),e=tr(),n&&n.c(),t=za()},m(r,o){i&&i.m(r,o),Mt(r,e,o),n&&n.m(r,o),Mt(r,t,o)},p(r,o){r[29]?i?i.p(r,o):(i=Sc(r),i.c(),i.m(e.parentNode,e)):i&&(i.d(1),i=null),r[28]?n?n.p(r,o):(n=Cc(r),n.c(),n.m(t.parentNode,t)):n&&(n.d(1),n=null)},d(r){r&&(Ct(e),Ct(t)),i&&i.d(r),n&&n.d(r)}}}function Sc(s){let e,t;return{c(){e=Cs("h2"),t=Ed(s[29]),hr(e,"class","svelte-a0rsm5")},m(i,n){Mt(i,e,n),er(e,t)},p(i,n){n[0]&536870912&&Id(t,i[29])},d(i){i&&Ct(e)}}}function Cc(s){let e,t;return{c(){e=Cs("p"),t=Ed(s[28]),hr(e,"class","svelte-a0rsm5")},m(i,n){Mt(i,e,n),er(e,t)},p(i,n){n[0]&268435456&&Id(t,i[28])},d(i){i&&Ct(e)}}}function Mc(s){let e;return{c(){e=Cs("div"),e.textContent="or",hr(e,"class","or svelte-a0rsm5")},m(t,i){Mt(t,e,i)},d(t){t&&Ct(e)}}}function Tc(s){let e;return{c(){e=Cs("div"),e.textContent="select the draw tool to start"},m(t,i){Mt(t,e,i)},d(t){t&&Ct(e)}}}function $x(s){let e,t=s[27]==="image"&&!s[26]&&kc(s);return{c(){t&&t.c(),e=za()},m(i,n){t&&t.m(i,n),Mt(i,e,n)},p(i,n){i[27]==="image"&&!i[26]?t?t.p(i,n):(t=kc(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(i){i&&Ct(e),t&&t.d(i)}}}function Ox(s){let e,t,i,n,r,o,a,l,h;e=new x_({props:{show_label:s[7],Icon:Bc,label:s[6]||s[4]("image.image")}});function c(g){s[37](g)}function u(g){s[38](g)}function d(g){s[39](g)}function _(g){s[40](g)}function f(g){s[41](g)}let m={transforms:s[14],composite:s[11],layers:s[10],background:s[12],canvas_size:s[15],changeable:s[8],sources:s[3],full_history:s[20],brush_options:s[1],eraser_options:s[2],fixed_canvas:s[16],border_region:s[19],layer_options:s[13],i18n:s[4],root:s[5],upload:s[17],webcam_options:s[21],show_download_button:s[22],theme_mode:s[9],$$slots:{default:[$x]},$$scope:{ctx:s}};return s[23]!==void 0&&(m.background_image=s[23]),s[27]!==void 0&&(m.current_tool=s[27]),s[0]!==void 0&&(m.is_dragging=s[0]),s[25]!==void 0&&(m.has_drawn=s[25]),s[26]!==void 0&&(m.can_undo=s[26]),i=new Mx({props:m}),s[36](i),ss.push(()=>Gs(i,"background_image",c)),ss.push(()=>Gs(i,"current_tool",u)),ss.push(()=>Gs(i,"is_dragging",d)),ss.push(()=>Gs(i,"has_drawn",_)),ss.push(()=>Gs(i,"can_undo",f)),i.$on("history",s[42]),i.$on("save",s[43]),i.$on("change",s[31]),i.$on("clear",s[44]),i.$on("download_error",s[45]),{c(){bc(e.$$.fragment),t=tr(),bc(i.$$.fragment)},m(g,p){wc(e,g,p),Mt(g,t,p),wc(i,g,p),h=!0},p(g,p){const y={};p[0]&128&&(y.show_label=g[7]),p[0]&80&&(y.label=g[6]||g[4]("image.image")),e.$set(y);const b={};p[0]&16384&&(b.transforms=g[14]),p[0]&2048&&(b.composite=g[11]),p[0]&1024&&(b.layers=g[10]),p[0]&4096&&(b.background=g[12]),p[0]&32768&&(b.canvas_size=g[15]),p[0]&256&&(b.changeable=g[8]),p[0]&8&&(b.sources=g[3]),p[0]&1048576&&(b.full_history=g[20]),p[0]&2&&(b.brush_options=g[1]),p[0]&4&&(b.eraser_options=g[2]),p[0]&65536&&(b.fixed_canvas=g[16]),p[0]&524288&&(b.border_region=g[19]),p[0]&8192&&(b.layer_options=g[13]),p[0]&16&&(b.i18n=g[4]),p[0]&32&&(b.root=g[5]),p[0]&131072&&(b.upload=g[17]),p[0]&2097152&&(b.webcam_options=g[21]),p[0]&4194304&&(b.show_download_button=g[22]),p[0]&512&&(b.theme_mode=g[9]),p[0]&1006895114|p[1]&262144&&(b.$$scope={dirty:p,ctx:g}),!n&&p[0]&8388608&&(n=!0,b.background_image=g[23],Os(()=>n=!1)),!r&&p[0]&134217728&&(r=!0,b.current_tool=g[27],Os(()=>r=!1)),!o&&p[0]&1&&(o=!0,b.is_dragging=g[0],Os(()=>o=!1)),!a&&p[0]&33554432&&(a=!0,b.has_drawn=g[25],Os(()=>a=!1)),!l&&p[0]&67108864&&(l=!0,b.can_undo=g[26],Os(()=>l=!1)),i.$set(b)},i(g){h||(xc(e.$$.fragment,g),xc(i.$$.fragment,g),h=!0)},o(g){vc(e.$$.fragment,g),vc(i.$$.fragment,g),h=!1},d(g){g&&Ct(t),yc(e,g),s[36](null),yc(i,g)}}}function Gx(s){return!!s}function Dx(s){return!!s}function Pc(){return new Promise(s=>setTimeout(()=>s(),30))}function Nx(s,e,t){let i,n,{brush:r}=e,{eraser:o}=e,{sources:a}=e,{i18n:l}=e,{root:h}=e,{label:c=void 0}=e,{show_label:u}=e,{changeable:d=!1}=e,{theme_mode:_}=e,{layers:f}=e,{composite:m}=e,{background:g}=e,{layer_options:p}=e,{transforms:y}=e,{accept_blobs:b}=e,{canvas_size:w}=e,{fixed_canvas:A=!1}=e,{realtime:v}=e,{upload:S}=e,{is_dragging:R}=e,{placeholder:C=void 0}=e,{border_region:M}=e,{full_history:k=null}=e,{webcam_options:P}=e,{show_download_button:L=!1}=e;const B=Fx();let H,F=!1;async function O(){let I;try{I=await H.get_blobs()}catch{return{background:null,layers:[],composite:null}}const Ae=I.background?S(await fr([new File([I.background],"background.png")]),h):Promise.resolve(null),lt=I.layers.filter(Gx).map(async(Se,It)=>S(await fr([new File([Se],`layer_${It}.png`)]),h)),Ye=I.composite?S(await fr([new File([I.composite],"composite.png")]),h):Promise.resolve(null),[ae,Fe,...ei]=await Promise.all([Ae,Ye,...lt]);return{background:Array.isArray(ae)?ae[0]:ae,layers:ei.flatMap(Se=>Array.isArray(Se)?Se:[Se]).filter(Dx),composite:Array.isArray(Fe)?Fe[0]:Fe}}function Ie(I){H&&I==null&&(H.handle_remove(),B("receive_null"))}let q=!1,te,{image_id:E=null}=e,$=!1,ye=!1;async function Pe(I){if(!v)return;if($){ye=!0;return}$=!0,await Pc();const Ae=await H.get_blobs(),lt=[];let Ye=Math.random().toString(36).substring(2);Ae.background&&lt.push([Ye,"background",new File([Ae.background],"background.png"),null]),Ae.composite&&lt.push([Ye,"composite",new File([Ae.composite],"composite.png"),null]),Ae.layers.forEach((ae,Fe)=>{ae&&lt.push([Ye,"layer",new File([ae],`layer_${Fe}.png`),Fe])}),await Promise.all(lt.map(async([ae,Fe,ei,Se])=>b({binary:!0,data:{file:ei,id:ae,type:Fe,index:Se}}))),t(32,E=Ye),B("change"),await Pc(),$=!1,ye&&(ye=!1,$=!1,Pe())}let ge;function me(I){ss[I?"unshift":"push"](()=>{H=I,t(24,H)})}function xe(I){q=I,t(23,q)}function ve(I){ge=I,t(27,ge)}function ke(I){R=I,t(0,R)}function ze(I){F=I,t(25,F)}function at(I){te=I,t(26,te)}function z(I){To.call(this,s,I)}function ue(I){To.call(this,s,I)}const ne=()=>B("clear");function Ee(I){To.call(this,s,I)}return s.$$set=I=>{"brush"in I&&t(1,r=I.brush),"eraser"in I&&t(2,o=I.eraser),"sources"in I&&t(3,a=I.sources),"i18n"in I&&t(4,l=I.i18n),"root"in I&&t(5,h=I.root),"label"in I&&t(6,c=I.label),"show_label"in I&&t(7,u=I.show_label),"changeable"in I&&t(8,d=I.changeable),"theme_mode"in I&&t(9,_=I.theme_mode),"layers"in I&&t(10,f=I.layers),"composite"in I&&t(11,m=I.composite),"background"in I&&t(12,g=I.background),"layer_options"in I&&t(13,p=I.layer_options),"transforms"in I&&t(14,y=I.transforms),"accept_blobs"in I&&t(33,b=I.accept_blobs),"canvas_size"in I&&t(15,w=I.canvas_size),"fixed_canvas"in I&&t(16,A=I.fixed_canvas),"realtime"in I&&t(34,v=I.realtime),"upload"in I&&t(17,S=I.upload),"is_dragging"in I&&t(0,R=I.is_dragging),"placeholder"in I&&t(18,C=I.placeholder),"border_region"in I&&t(19,M=I.border_region),"full_history"in I&&t(20,k=I.full_history),"webcam_options"in I&&t(21,P=I.webcam_options),"show_download_button"in I&&t(22,L=I.show_download_button),"image_id"in I&&t(32,E=I.image_id)},s.$$.update=()=>{s.$$.dirty[0]&8388608&&q&&B("upload"),s.$$.dirty[0]&7168&&Ie({layers:f,composite:m,background:g}),s.$$.dirty[0]&262144&&t(29,[i,n]=C?Px(C):[!1,!1],i,(t(28,n),t(18,C)))},[R,r,o,a,l,h,c,u,d,_,f,m,g,p,y,w,A,S,C,M,k,P,L,q,H,F,te,ge,n,i,B,Pe,E,b,v,O,me,xe,ve,ke,ze,at,z,ue,ne,Ee]}class Ux extends zx{constructor(e){super(),Ix(this,e,Nx,Ox,Rx,{brush:1,eraser:2,sources:3,i18n:4,root:5,label:6,show_label:7,changeable:8,theme_mode:9,layers:10,composite:11,background:12,layer_options:13,transforms:14,accept_blobs:33,canvas_size:15,fixed_canvas:16,realtime:34,upload:17,is_dragging:0,placeholder:18,border_region:19,full_history:20,webcam_options:21,show_download_button:22,get_data:35,image_id:32},null,[-1,-1])}get brush(){return this.$$.ctx[1]}set brush(e){this.$$set({brush:e}),ie()}get eraser(){return this.$$.ctx[2]}set eraser(e){this.$$set({eraser:e}),ie()}get sources(){return this.$$.ctx[3]}set sources(e){this.$$set({sources:e}),ie()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),ie()}get root(){return this.$$.ctx[5]}set root(e){this.$$set({root:e}),ie()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),ie()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),ie()}get changeable(){return this.$$.ctx[8]}set changeable(e){this.$$set({changeable:e}),ie()}get theme_mode(){return this.$$.ctx[9]}set theme_mode(e){this.$$set({theme_mode:e}),ie()}get layers(){return this.$$.ctx[10]}set layers(e){this.$$set({layers:e}),ie()}get composite(){return this.$$.ctx[11]}set composite(e){this.$$set({composite:e}),ie()}get background(){return this.$$.ctx[12]}set background(e){this.$$set({background:e}),ie()}get layer_options(){return this.$$.ctx[13]}set layer_options(e){this.$$set({layer_options:e}),ie()}get transforms(){return this.$$.ctx[14]}set transforms(e){this.$$set({transforms:e}),ie()}get accept_blobs(){return this.$$.ctx[33]}set accept_blobs(e){this.$$set({accept_blobs:e}),ie()}get canvas_size(){return this.$$.ctx[15]}set canvas_size(e){this.$$set({canvas_size:e}),ie()}get fixed_canvas(){return this.$$.ctx[16]}set fixed_canvas(e){this.$$set({fixed_canvas:e}),ie()}get realtime(){return this.$$.ctx[34]}set realtime(e){this.$$set({realtime:e}),ie()}get upload(){return this.$$.ctx[17]}set upload(e){this.$$set({upload:e}),ie()}get is_dragging(){return this.$$.ctx[0]}set is_dragging(e){this.$$set({is_dragging:e}),ie()}get placeholder(){return this.$$.ctx[18]}set placeholder(e){this.$$set({placeholder:e}),ie()}get border_region(){return this.$$.ctx[19]}set border_region(e){this.$$set({border_region:e}),ie()}get full_history(){return this.$$.ctx[20]}set full_history(e){this.$$set({full_history:e}),ie()}get webcam_options(){return this.$$.ctx[21]}set webcam_options(e){this.$$set({webcam_options:e}),ie()}get show_download_button(){return this.$$.ctx[22]}set show_download_button(e){this.$$set({show_download_button:e}),ie()}get get_data(){return this.$$.ctx[35]}get image_id(){return this.$$.ctx[32]}set image_id(e){this.$$set({image_id:e}),ie()}}const{SvelteComponent:Wx,add_flush_callback:zc,assign:Rd,bind:Ic,binding_callbacks:la,bubble:Hx,check_outros:qx,create_component:ps,destroy_component:bs,detach:Ia,empty:Vx,flush:j,get_spread_object:Fd,get_spread_update:Bd,group_outros:Xx,init:Yx,insert:Ea,mount_component:ys,not_equal:jx,space:Ld,transition_in:pi,transition_out:bi}=window.__gradio__svelte__internal,{tick:Kx}=window.__gradio__svelte__internal;function Zx(s){let e,t;return e=new Rc({props:{visible:s[5],variant:s[39]?"solid":"dashed",border_mode:s[35]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],height:s[10],width:s[11],allow_overflow:!0,overflow_behavior:"visible",container:s[13],scale:s[14],min_width:s[15],$$slots:{default:[Jx]},$$scope:{ctx:s}}}),{c(){ps(e.$$.fragment)},m(i,n){ys(e,i,n),t=!0},p(i,n){const r={};n[0]&32&&(r.visible=i[5]),n[1]&256&&(r.variant=i[39]?"solid":"dashed"),n[1]&16&&(r.border_mode=i[35]?"focus":"base"),n[0]&8&&(r.elem_id=i[3]),n[0]&16&&(r.elem_classes=i[4]),n[0]&1024&&(r.height=i[10]),n[0]&2048&&(r.width=i[11]),n[0]&8192&&(r.container=i[13]),n[0]&16384&&(r.scale=i[14]),n[0]&32768&&(r.min_width=i[15]),n[0]&1878655943|n[1]&255|n[2]&128&&(r.$$scope={dirty:n,ctx:i}),e.$set(r)},i(i){t||(pi(e.$$.fragment,i),t=!0)},o(i){bi(e.$$.fragment,i),t=!1},d(i){bs(e,i)}}}function Qx(s){let e,t;return e=new Rc({props:{visible:s[5],variant:"solid",border_mode:s[35]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],height:s[10],width:s[11],allow_overflow:!0,overflow_behavior:"visible",container:s[13],scale:s[14],min_width:s[15],$$slots:{default:[e2]},$$scope:{ctx:s}}}),{c(){ps(e.$$.fragment)},m(i,n){ys(e,i,n),t=!0},p(i,n){const r={};n[0]&32&&(r.visible=i[5]),n[1]&16&&(r.border_mode=i[35]?"focus":"base"),n[0]&8&&(r.elem_id=i[3]),n[0]&16&&(r.elem_classes=i[4]),n[0]&1024&&(r.height=i[10]),n[0]&2048&&(r.width=i[11]),n[0]&8192&&(r.container=i[13]),n[0]&16384&&(r.scale=i[14]),n[0]&32768&&(r.min_width=i[15]),n[0]&805376451|n[2]&128&&(r.$$scope={dirty:n,ctx:i}),e.$set(r)},i(i){t||(pi(e.$$.fragment,i),t=!0)},o(i){bi(e.$$.fragment,i),t=!1},d(i){bs(e,i)}}}function Jx(s){let e,t,i,n,r,o;const a=[{autoscroll:s[29].autoscroll},{i18n:s[29].i18n},s[1]];let l={};for(let d=0;d<a.length;d+=1)l=Rd(l,a[d]);e=new Fc({props:l}),e.$on("clear_status",s[48]);function h(d){s[50](d)}function c(d){s[51](d)}let u={border_region:s[30],canvas_size:s[26],layers:s[36],composite:s[37],background:s[38],root:s[9],sources:s[17],label:s[6],show_label:s[7],fixed_canvas:s[27],brush:s[20],eraser:s[21],changeable:s[24].includes("apply"),realtime:s[24].includes("change")||s[24].includes("input"),i18n:s[29].i18n,transforms:s[22],accept_blobs:s[25].accept_blobs,layer_options:s[23],upload:s[49],placeholder:s[19],full_history:s[2],webcam_options:s[31],show_download_button:s[8],theme_mode:s[32]};return s[35]!==void 0&&(u.is_dragging=s[35]),s[34]!==void 0&&(u.image_id=s[34]),i=new Ux({props:u}),la.push(()=>Ic(i,"is_dragging",h)),la.push(()=>Ic(i,"image_id",c)),s[52](i),i.$on("history",s[53]),i.$on("change",s[54]),i.$on("save",s[55]),i.$on("edit",s[56]),i.$on("clear",s[57]),i.$on("drag",s[58]),i.$on("upload",s[59]),i.$on("share",s[60]),i.$on("error",s[61]),i.$on("receive_null",s[62]),i.$on("error",s[63]),i.$on("download_error",s[64]),{c(){ps(e.$$.fragment),t=Ld(),ps(i.$$.fragment)},m(d,_){ys(e,d,_),Ea(d,t,_),ys(i,d,_),o=!0},p(d,_){const f=_[0]&536870914?Bd(a,[_[0]&536870912&&{autoscroll:d[29].autoscroll},_[0]&536870912&&{i18n:d[29].i18n},_[0]&2&&Fd(d[1])]):{};e.$set(f);const m={};_[0]&1073741824&&(m.border_region=d[30]),_[0]&67108864&&(m.canvas_size=d[26]),_[1]&32&&(m.layers=d[36]),_[1]&64&&(m.composite=d[37]),_[1]&128&&(m.background=d[38]),_[0]&512&&(m.root=d[9]),_[0]&131072&&(m.sources=d[17]),_[0]&64&&(m.label=d[6]),_[0]&128&&(m.show_label=d[7]),_[0]&134217728&&(m.fixed_canvas=d[27]),_[0]&1048576&&(m.brush=d[20]),_[0]&2097152&&(m.eraser=d[21]),_[0]&16777216&&(m.changeable=d[24].includes("apply")),_[0]&16777216&&(m.realtime=d[24].includes("change")||d[24].includes("input")),_[0]&536870912&&(m.i18n=d[29].i18n),_[0]&4194304&&(m.transforms=d[22]),_[0]&33554432&&(m.accept_blobs=d[25].accept_blobs),_[0]&8388608&&(m.layer_options=d[23]),_[0]&536870912&&(m.upload=d[49]),_[0]&524288&&(m.placeholder=d[19]),_[0]&4&&(m.full_history=d[2]),_[1]&1&&(m.webcam_options=d[31]),_[0]&256&&(m.show_download_button=d[8]),_[1]&2&&(m.theme_mode=d[32]),!n&&_[1]&16&&(n=!0,m.is_dragging=d[35],zc(()=>n=!1)),!r&&_[1]&8&&(r=!0,m.image_id=d[34],zc(()=>r=!1)),i.$set(m)},i(d){o||(pi(e.$$.fragment,d),pi(i.$$.fragment,d),o=!0)},o(d){bi(e.$$.fragment,d),bi(i.$$.fragment,d),o=!1},d(d){d&&Ia(t),bs(e,d),s[52](null),bs(i,d)}}}function e2(s){let e,t,i,n;const r=[{autoscroll:s[29].autoscroll},{i18n:s[29].i18n},s[1]];let o={};for(let a=0;a<r.length;a+=1)o=Rd(o,r[a]);return e=new Fc({props:o}),e.$on("clear_status",s[44]),i=new h_({props:{value:s[0]?.composite||null,label:s[6],show_label:s[7],show_download_button:s[8],selectable:s[12],show_share_button:s[16],i18n:s[29].i18n,show_fullscreen_button:s[28]}}),i.$on("select",s[45]),i.$on("share",s[46]),i.$on("error",s[47]),{c(){ps(e.$$.fragment),t=Ld(),ps(i.$$.fragment)},m(a,l){ys(e,a,l),Ea(a,t,l),ys(i,a,l),n=!0},p(a,l){const h=l[0]&536870914?Bd(r,[l[0]&536870912&&{autoscroll:a[29].autoscroll},l[0]&536870912&&{i18n:a[29].i18n},l[0]&2&&Fd(a[1])]):{};e.$set(h);const c={};l[0]&1&&(c.value=a[0]?.composite||null),l[0]&64&&(c.label=a[6]),l[0]&128&&(c.show_label=a[7]),l[0]&256&&(c.show_download_button=a[8]),l[0]&4096&&(c.selectable=a[12]),l[0]&65536&&(c.show_share_button=a[16]),l[0]&536870912&&(c.i18n=a[29].i18n),l[0]&268435456&&(c.show_fullscreen_button=a[28]),i.$set(c)},i(a){n||(pi(e.$$.fragment,a),pi(i.$$.fragment,a),n=!0)},o(a){bi(e.$$.fragment,a),bi(i.$$.fragment,a),n=!1},d(a){a&&Ia(t),bs(e,a),bs(i,a)}}}function t2(s){let e,t,i,n;const r=[Qx,Zx],o=[];function a(l,h){return l[18]?1:0}return e=a(s),t=o[e]=r[e](s),{c(){t.c(),i=Vx()},m(l,h){o[e].m(l,h),Ea(l,i,h),n=!0},p(l,h){let c=e;e=a(l),e===c?o[e].p(l,h):(Xx(),bi(o[c],1,1,()=>{o[c]=null}),qx(),t=o[e],t?t.p(l,h):(t=o[e]=r[e](l),t.c()),pi(t,1),t.m(i.parentNode,i))},i(l){n||(pi(t),n=!0)},o(l){bi(t),n=!1},d(l){l&&Ia(i),o[e].d(l)}}}function i2(s,e,t){let i,n,r,o,{elem_id:a=""}=e,{elem_classes:l=[]}=e,{visible:h=!0}=e,{value:c={background:null,layers:[],composite:null}}=e,{label:u}=e,{show_label:d}=e,{show_download_button:_}=e,{root:f}=e,{value_is_output:m=!1}=e,{height:g=350}=e,{width:p}=e,{_selectable:y=!1}=e,{container:b=!0}=e,{scale:w=null}=e,{min_width:A=void 0}=e,{loading_status:v}=e,{show_share_button:S=!1}=e,{sources:R=[]}=e,{interactive:C}=e,{placeholder:M}=e,{brush:k}=e,{eraser:P}=e,{transforms:L=[]}=e,{layers:B}=e,{attached_events:H=[]}=e,{server:F}=e,{canvas_size:O}=e,{fixed_canvas:Ie=!1}=e,{show_fullscreen_button:q=!0}=e,{full_history:te=null}=e,{gradio:E}=e,{border_region:$=0}=e,{webcam_options:ye}=e,{theme_mode:Pe}=e,ge,me=null;async function xe(){if(me){const _r={id:me};return t(34,me=null),_r}return await ge.get_data()}let ve;const ze=typeof window<"u"?window.requestAnimationFrame:T=>T();function at(){return new Promise(T=>{ze(()=>ze(()=>T()))})}async function z(){await at(),c&&(c.background||c.layers?.length||c.composite)&&E.dispatch("change")}function ue(){E.dispatch("apply")}function ne(){E.dispatch("change"),m||(E.dispatch("input"),Kx().then(T=>t(42,m=!1)))}const Ee=()=>E.dispatch("clear_status",v),I=({detail:T})=>E.dispatch("select",T),Ae=({detail:T})=>E.dispatch("share",T),lt=({detail:T})=>E.dispatch("error",T),Ye=()=>E.dispatch("clear_status",v),ae=(...T)=>E.client.upload(...T);function Fe(T){ve=T,t(35,ve)}function ei(T){me=T,t(34,me)}function Se(T){la[T?"unshift":"push"](()=>{ge=T,t(33,ge)})}const It=T=>t(2,te=T.detail),Nt=()=>ne(),ti=T=>ue(),cr=()=>E.dispatch("edit"),ur=()=>E.dispatch("clear"),un=({detail:T})=>t(35,ve=T),dn=()=>E.dispatch("upload"),wi=({detail:T})=>E.dispatch("share",T),Ui=({detail:T})=>{t(1,v=v||{}),t(1,v.status="error",v),E.dispatch("error",T)},_n=()=>t(0,c={background:null,layers:[],composite:null});function Ms(T){Hx.call(this,s,T)}const dr=T=>E.dispatch("error",T.detail);return s.$$set=T=>{"elem_id"in T&&t(3,a=T.elem_id),"elem_classes"in T&&t(4,l=T.elem_classes),"visible"in T&&t(5,h=T.visible),"value"in T&&t(0,c=T.value),"label"in T&&t(6,u=T.label),"show_label"in T&&t(7,d=T.show_label),"show_download_button"in T&&t(8,_=T.show_download_button),"root"in T&&t(9,f=T.root),"value_is_output"in T&&t(42,m=T.value_is_output),"height"in T&&t(10,g=T.height),"width"in T&&t(11,p=T.width),"_selectable"in T&&t(12,y=T._selectable),"container"in T&&t(13,b=T.container),"scale"in T&&t(14,w=T.scale),"min_width"in T&&t(15,A=T.min_width),"loading_status"in T&&t(1,v=T.loading_status),"show_share_button"in T&&t(16,S=T.show_share_button),"sources"in T&&t(17,R=T.sources),"interactive"in T&&t(18,C=T.interactive),"placeholder"in T&&t(19,M=T.placeholder),"brush"in T&&t(20,k=T.brush),"eraser"in T&&t(21,P=T.eraser),"transforms"in T&&t(22,L=T.transforms),"layers"in T&&t(23,B=T.layers),"attached_events"in T&&t(24,H=T.attached_events),"server"in T&&t(25,F=T.server),"canvas_size"in T&&t(26,O=T.canvas_size),"fixed_canvas"in T&&t(27,Ie=T.fixed_canvas),"show_fullscreen_button"in T&&t(28,q=T.show_fullscreen_button),"full_history"in T&&t(2,te=T.full_history),"gradio"in T&&t(29,E=T.gradio),"border_region"in T&&t(30,$=T.border_region),"webcam_options"in T&&t(31,ye=T.webcam_options),"theme_mode"in T&&t(32,Pe=T.theme_mode)},s.$$.update=()=>{s.$$.dirty[0]&1&&c&&z(),s.$$.dirty[0]&1&&t(39,i=c?.background||c?.layers?.length||c?.composite),s.$$.dirty[0]&1&&t(38,n=c?.background?new gr(c.background):null),s.$$.dirty[0]&1&&t(37,r=c?.composite?new gr(c.composite):null),s.$$.dirty[0]&1&&t(36,o=c?.layers?.map(T=>new gr(T))||[])},[c,v,te,a,l,h,u,d,_,f,g,p,y,b,w,A,S,R,C,M,k,P,L,B,H,F,O,Ie,q,E,$,ye,Pe,ge,me,ve,o,r,n,i,ue,ne,m,xe,Ee,I,Ae,lt,Ye,ae,Fe,ei,Se,It,Nt,ti,cr,ur,un,dn,wi,Ui,_n,Ms,dr]}class s2 extends Wx{constructor(e){super(),Yx(this,e,i2,t2,jx,{elem_id:3,elem_classes:4,visible:5,value:0,label:6,show_label:7,show_download_button:8,root:9,value_is_output:42,height:10,width:11,_selectable:12,container:13,scale:14,min_width:15,loading_status:1,show_share_button:16,sources:17,interactive:18,placeholder:19,brush:20,eraser:21,transforms:22,layers:23,attached_events:24,server:25,canvas_size:26,fixed_canvas:27,show_fullscreen_button:28,full_history:2,gradio:29,border_region:30,webcam_options:31,theme_mode:32,get_value:43},null,[-1,-1,-1])}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),j()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),j()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),j()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),j()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),j()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),j()}get show_download_button(){return this.$$.ctx[8]}set show_download_button(e){this.$$set({show_download_button:e}),j()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),j()}get value_is_output(){return this.$$.ctx[42]}set value_is_output(e){this.$$set({value_is_output:e}),j()}get height(){return this.$$.ctx[10]}set height(e){this.$$set({height:e}),j()}get width(){return this.$$.ctx[11]}set width(e){this.$$set({width:e}),j()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),j()}get container(){return this.$$.ctx[13]}set container(e){this.$$set({container:e}),j()}get scale(){return this.$$.ctx[14]}set scale(e){this.$$set({scale:e}),j()}get min_width(){return this.$$.ctx[15]}set min_width(e){this.$$set({min_width:e}),j()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),j()}get show_share_button(){return this.$$.ctx[16]}set show_share_button(e){this.$$set({show_share_button:e}),j()}get sources(){return this.$$.ctx[17]}set sources(e){this.$$set({sources:e}),j()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),j()}get placeholder(){return this.$$.ctx[19]}set placeholder(e){this.$$set({placeholder:e}),j()}get brush(){return this.$$.ctx[20]}set brush(e){this.$$set({brush:e}),j()}get eraser(){return this.$$.ctx[21]}set eraser(e){this.$$set({eraser:e}),j()}get transforms(){return this.$$.ctx[22]}set transforms(e){this.$$set({transforms:e}),j()}get layers(){return this.$$.ctx[23]}set layers(e){this.$$set({layers:e}),j()}get attached_events(){return this.$$.ctx[24]}set attached_events(e){this.$$set({attached_events:e}),j()}get server(){return this.$$.ctx[25]}set server(e){this.$$set({server:e}),j()}get canvas_size(){return this.$$.ctx[26]}set canvas_size(e){this.$$set({canvas_size:e}),j()}get fixed_canvas(){return this.$$.ctx[27]}set fixed_canvas(e){this.$$set({fixed_canvas:e}),j()}get show_fullscreen_button(){return this.$$.ctx[28]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),j()}get full_history(){return this.$$.ctx[2]}set full_history(e){this.$$set({full_history:e}),j()}get gradio(){return this.$$.ctx[29]}set gradio(e){this.$$set({gradio:e}),j()}get border_region(){return this.$$.ctx[30]}set border_region(e){this.$$set({border_region:e}),j()}get webcam_options(){return this.$$.ctx[31]}set webcam_options(e){this.$$set({webcam_options:e}),j()}get theme_mode(){return this.$$.ctx[32]}set theme_mode(e){this.$$set({theme_mode:e}),j()}get get_value(){return this.$$.ctx[43]}}const I2=Object.freeze(Object.defineProperty({__proto__:null,default:s2},Symbol.toStringTag,{value:"Module"}));export{fe as $,Eu as A,Xe as B,U as C,_e as D,D as E,No as F,mi as G,Kc as H,Zs as I,St as J,ul as K,ee as L,Z as M,lp as N,yp as O,X as P,pe as Q,Oo as R,pp as S,yn as T,Fo as U,se as V,$p as W,_l as X,gl as Y,Nm as Z,Yr as _,Tt as a,Jc as a0,ft as a1,Ru as a2,K as a3,J as a4,Up as a5,hb as a6,Tb as a7,zb as a8,Bb as a9,$b as aa,Ob as ab,rn as ac,M0 as ad,ju as ae,Sl as af,kl as ag,am as ah,Kl as ai,R0 as aj,we as ak,de as al,hm as am,Xl as an,Qo as ao,Yl as ap,Hn as aq,Qu as ar,I2 as as,sb as b,sn as c,Fn as d,De as e,Al as f,eb as g,ma as h,Pu as i,cu as j,Pt as k,ku as l,Vl as m,Mb as n,Pb as o,Rb as p,Gu as q,vm as r,Lb as s,sr as t,Y as u,lm as v,be as w,_s as x,Db as y,Kt as z};
//# sourceMappingURL=Index-C6BTZLvD.js.map
