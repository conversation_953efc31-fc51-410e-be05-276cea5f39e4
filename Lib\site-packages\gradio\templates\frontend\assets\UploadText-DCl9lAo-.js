/* empty css                                                        */import{U as R,I as S}from"./Upload-BktIH97M.js";const T=/^(#\s*)(.+)$/m;function z(r){const e=r.trim(),l=e.match(T);if(!l)return[!1,e||!1];const[t,,i]=l,n=i.trim();if(e===t)return[n,!1];const o=l.index!==void 0?l.index+t.length:0,_=e.substring(o).trim()||!1;return[n,_]}const{SvelteComponent:B,append:h,attr:v,check_outros:F,create_component:D,destroy_component:G,detach:d,element:y,empty:H,flush:k,group_outros:J,init:K,insert:m,mount_component:P,safe_not_equal:L,set_data:w,space:I,text:p,toggle_class:j,transition_in:N,transition_out:U}=window.__gradio__svelte__internal;function M(r){let e,l;return e=new R({}),{c(){D(e.$$.fragment)},m(t,i){P(e,t,i),l=!0},i(t){l||(N(e.$$.fragment,t),l=!0)},o(t){U(e.$$.fragment,t),l=!1},d(t){G(e,t)}}}function O(r){let e,l;return e=new S({}),{c(){D(e.$$.fragment)},m(t,i){P(e,t,i),l=!0},i(t){l||(N(e.$$.fragment,t),l=!0)},o(t){U(e.$$.fragment,t),l=!1},d(t){G(e,t)}}}function Q(r){let e=r[1](r[7][r[0]]||r[7].file)+"",l,t,i,n=r[3]!=="short"&&q(r);return{c(){l=p(e),t=I(),n&&n.c(),i=H()},m(o,f){m(o,l,f),m(o,t,f),n&&n.m(o,f),m(o,i,f)},p(o,f){f&3&&e!==(e=o[1](o[7][o[0]]||o[7].file)+"")&&w(l,e),o[3]!=="short"?n?n.p(o,f):(n=q(o),n.c(),n.m(i.parentNode,i)):n&&(n.d(1),n=null)},d(o){o&&(d(l),d(t),d(i)),n&&n.d(o)}}}function V(r){let e,l,t=r[6]&&A(r),i=r[5]&&C(r);return{c(){t&&t.c(),e=I(),i&&i.c(),l=H()},m(n,o){t&&t.m(n,o),m(n,e,o),i&&i.m(n,o),m(n,l,o)},p(n,o){n[6]?t?t.p(n,o):(t=A(n),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),n[5]?i?i.p(n,o):(i=C(n),i.c(),i.m(l.parentNode,l)):i&&(i.d(1),i=null)},d(n){n&&(d(e),d(l)),t&&t.d(n),i&&i.d(n)}}}function q(r){let e,l,t=r[1]("common.or")+"",i,n,o,f=(r[2]||r[1]("upload_text.click_to_upload"))+"",_;return{c(){e=y("span"),l=p("- "),i=p(t),n=p(" -"),o=I(),_=p(f),v(e,"class","or svelte-12ioyct")},m(a,u){m(a,e,u),h(e,l),h(e,i),h(e,n),m(a,o,u),m(a,_,u)},p(a,u){u&2&&t!==(t=a[1]("common.or")+"")&&w(i,t),u&6&&f!==(f=(a[2]||a[1]("upload_text.click_to_upload"))+"")&&w(_,f)},d(a){a&&(d(e),d(o),d(_))}}}function A(r){let e,l;return{c(){e=y("h2"),l=p(r[6]),v(e,"class","svelte-12ioyct")},m(t,i){m(t,e,i),h(e,l)},p(t,i){i&64&&w(l,t[6])},d(t){t&&d(e)}}}function C(r){let e,l;return{c(){e=y("p"),l=p(r[5]),v(e,"class","svelte-12ioyct")},m(t,i){m(t,e,i),h(e,l)},p(t,i){i&32&&w(l,t[5])},d(t){t&&d(e)}}}function W(r){let e,l,t,i,n,o;const f=[O,M],_=[];function a(c,b){return c[0]==="clipboard"?0:1}t=a(r),i=_[t]=f[t](r);function u(c,b){return c[6]||c[5]?V:Q}let g=u(r),s=g(r);return{c(){e=y("div"),l=y("span"),i.c(),n=I(),s.c(),v(l,"class","icon-wrap svelte-12ioyct"),j(l,"hovered",r[4]),v(e,"class","wrap svelte-12ioyct")},m(c,b){m(c,e,b),h(e,l),_[t].m(l,null),h(e,n),s.m(e,null),o=!0},p(c,[b]){let E=t;t=a(c),t!==E&&(J(),U(_[E],1,1,()=>{_[E]=null}),F(),i=_[t],i||(i=_[t]=f[t](c),i.c()),N(i,1),i.m(l,null)),(!o||b&16)&&j(l,"hovered",c[4]),g===(g=u(c))&&s?s.p(c,b):(s.d(1),s=g(c),s&&(s.c(),s.m(e,null)))},i(c){o||(N(i),o=!0)},o(c){U(i),o=!1},d(c){c&&d(e),_[t].d(),s.d()}}}function X(r,e,l){let t,i,{type:n="file"}=e,{i18n:o}=e,{message:f=void 0}=e,{mode:_="full"}=e,{hovered:a=!1}=e,{placeholder:u=void 0}=e;const g={image:"upload_text.drop_image",video:"upload_text.drop_video",audio:"upload_text.drop_audio",file:"upload_text.drop_file",csv:"upload_text.drop_csv",gallery:"upload_text.drop_gallery",clipboard:"upload_text.paste_clipboard"};return r.$$set=s=>{"type"in s&&l(0,n=s.type),"i18n"in s&&l(1,o=s.i18n),"message"in s&&l(2,f=s.message),"mode"in s&&l(3,_=s.mode),"hovered"in s&&l(4,a=s.hovered),"placeholder"in s&&l(8,u=s.placeholder)},r.$$.update=()=>{r.$$.dirty&256&&l(6,[t,i]=u?z(u):[!1,!1],t,(l(5,i),l(8,u)))},[n,o,f,_,a,i,t,g,u]}class x extends B{constructor(e){super(),K(this,e,X,W,L,{type:0,i18n:1,message:2,mode:3,hovered:4,placeholder:8})}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),k()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),k()}get message(){return this.$$.ctx[2]}set message(e){this.$$set({message:e}),k()}get mode(){return this.$$.ctx[3]}set mode(e){this.$$set({mode:e}),k()}get hovered(){return this.$$.ctx[4]}set hovered(e){this.$$set({hovered:e}),k()}get placeholder(){return this.$$.ctx[8]}set placeholder(e){this.$$set({placeholder:e}),k()}}export{x as U};
//# sourceMappingURL=UploadText-DCl9lAo-.js.map
