{"version": 3, "file": "range-OtVwhkKS.js", "sources": ["../../../../node_modules/.pnpm/d3-array@3.2.4/node_modules/d3-array/src/range.js"], "sourcesContent": ["export default function range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n"], "names": ["range", "start", "stop", "step", "n", "i"], "mappings": "AAAe,SAASA,EAAMC,EAAOC,EAAMC,EAAM,CAC/CF,EAAQ,CAACA,EAAOC,EAAO,CAACA,EAAMC,GAAQC,EAAI,UAAU,QAAU,GAAKF,EAAOD,EAAOA,EAAQ,EAAG,GAAKG,EAAI,EAAI,EAAI,CAACD,EAM9G,QAJIE,EAAI,GACJD,EAAI,KAAK,IAAI,EAAG,KAAK,MAAMF,EAAOD,GAASE,CAAI,CAAC,EAAI,EACpDH,EAAQ,IAAI,MAAMI,CAAC,EAEhB,EAAEC,EAAID,GACXJ,EAAMK,CAAC,EAAIJ,EAAQI,EAAIF,EAGzB,OAAOH,CACT", "x_google_ignoreList": [0]}