{"version": 3, "file": "rawTexture-Ce6_SCE0.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/rawTexture.js"], "sourcesContent": ["import { Texture } from \"./texture.js\";\n\n/**\n * Raw texture can help creating a texture directly from an array of data.\n * This can be super useful if you either get the data from an uncompressed source or\n * if you wish to create your texture pixel by pixel.\n */\nexport class RawTexture extends Texture {\n    /**\n     * Instantiates a new RawTexture.\n     * Raw texture can help creating a texture directly from an array of data.\n     * This can be super useful if you either get the data from an uncompressed source or\n     * if you wish to create your texture pixel by pixel.\n     * @param data define the array of data to use to create the texture (null to create an empty texture)\n     * @param width define the width of the texture\n     * @param height define the height of the texture\n     * @param format define the format of the data (RGB, RGBA... Engine.TEXTUREFORMAT_xxx)\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\n     * @param generateMipMaps define whether mip maps should be generated or not\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\n     * @param creationFlags specific flags to use when creating the texture (1 for storage textures, for eg)\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\n     * @param waitDataToBeReady If set to true Rawtexture will wait data to be set in order to be flaged as ready.\n     */\n    constructor(data, width, height, \n    /**\n     * Define the format of the data (RGB, RGBA... Engine.TEXTUREFORMAT_xxx)\n     */\n    format, sceneOrEngine, generateMipMaps = true, invertY = false, samplingMode = 3, type = 0, creationFlags, useSRGBBuffer, waitDataToBeReady) {\n        super(null, sceneOrEngine, !generateMipMaps, invertY, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, creationFlags);\n        this.format = format;\n        if (!this._engine) {\n            return;\n        }\n        if (!this._engine._caps.textureFloatLinearFiltering && type === 1) {\n            samplingMode = 1;\n        }\n        if (!this._engine._caps.textureHalfFloatLinearFiltering && type === 2) {\n            samplingMode = 1;\n        }\n        this._texture = this._engine.createRawTexture(data, width, height, format, generateMipMaps, invertY, samplingMode, null, type, creationFlags ?? 0, useSRGBBuffer ?? false);\n        this.wrapU = Texture.CLAMP_ADDRESSMODE;\n        this.wrapV = Texture.CLAMP_ADDRESSMODE;\n        this._waitingForData = !!waitDataToBeReady && !data;\n    }\n    /**\n     * Updates the texture underlying data.\n     * @param data Define the new data of the texture\n     */\n    update(data) {\n        this._getEngine().updateRawTexture(this._texture, data, this._texture.format, this._texture.invertY, null, this._texture.type, this._texture._useSRGBBuffer);\n        this._waitingForData = false;\n    }\n    /**\n     * Clones the texture.\n     * @returns the cloned texture\n     */\n    clone() {\n        if (!this._texture) {\n            return super.clone();\n        }\n        const rawTexture = new RawTexture(null, this.getSize().width, this.getSize().height, this.format, this.getScene(), this._texture.generateMipMaps, this._invertY, this.samplingMode, this._texture.type, this._texture._creationFlags, this._useSRGBBuffer);\n        rawTexture._texture = this._texture;\n        this._texture.incrementReferences();\n        return rawTexture;\n    }\n    isReady() {\n        return super.isReady() && !this._waitingForData;\n    }\n    /**\n     * Creates a luminance texture from some data.\n     * @param data Define the texture data\n     * @param width Define the width of the texture\n     * @param height Define the height of the texture\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\n     * @returns the luminance texture\n     */\n    static CreateLuminanceTexture(data, width, height, sceneOrEngine, generateMipMaps = true, invertY = false, samplingMode = 3) {\n        return new RawTexture(data, width, height, 1, sceneOrEngine, generateMipMaps, invertY, samplingMode);\n    }\n    /**\n     * Creates a luminance alpha texture from some data.\n     * @param data Define the texture data\n     * @param width Define the width of the texture\n     * @param height Define the height of the texture\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\n     * @returns the luminance alpha texture\n     */\n    static CreateLuminanceAlphaTexture(data, width, height, sceneOrEngine, generateMipMaps = true, invertY = false, samplingMode = 3) {\n        return new RawTexture(data, width, height, 2, sceneOrEngine, generateMipMaps, invertY, samplingMode);\n    }\n    /**\n     * Creates an alpha texture from some data.\n     * @param data Define the texture data\n     * @param width Define the width of the texture\n     * @param height Define the height of the texture\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\n     * @returns the alpha texture\n     */\n    static CreateAlphaTexture(data, width, height, sceneOrEngine, generateMipMaps = true, invertY = false, samplingMode = 3) {\n        return new RawTexture(data, width, height, 0, sceneOrEngine, generateMipMaps, invertY, samplingMode);\n    }\n    /**\n     * Creates a RGB texture from some data.\n     * @param data Define the texture data\n     * @param width Define the width of the texture\n     * @param height Define the height of the texture\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\n     * @param creationFlags specific flags to use when creating the texture (1 for storage textures, for eg)\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\n     * @returns the RGB alpha texture\n     */\n    static CreateRGBTexture(data, width, height, sceneOrEngine, generateMipMaps = true, invertY = false, samplingMode = 3, type = 0, creationFlags = 0, useSRGBBuffer = false) {\n        return new RawTexture(data, width, height, 4, sceneOrEngine, generateMipMaps, invertY, samplingMode, type, creationFlags, useSRGBBuffer);\n    }\n    /**\n     * Creates a RGBA texture from some data.\n     * @param data Define the texture data\n     * @param width Define the width of the texture\n     * @param height Define the height of the texture\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\n     * @param creationFlags specific flags to use when creating the texture (1 for storage textures, for eg)\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\n     * @param waitDataToBeReady if set to true this will force texture to wait for data to be set before it is considered ready.\n     * @returns the RGBA texture\n     */\n    static CreateRGBATexture(data, width, height, sceneOrEngine, generateMipMaps = true, invertY = false, samplingMode = 3, type = 0, creationFlags = 0, useSRGBBuffer = false, waitDataToBeReady = false) {\n        return new RawTexture(data, width, height, 5, sceneOrEngine, generateMipMaps, invertY, samplingMode, type, creationFlags, useSRGBBuffer, waitDataToBeReady);\n    }\n    /**\n     * Creates a RGBA storage texture from some data.\n     * @param data Define the texture data\n     * @param width Define the width of the texture\n     * @param height Define the height of the texture\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\n     * @returns the RGBA texture\n     */\n    static CreateRGBAStorageTexture(data, width, height, sceneOrEngine, generateMipMaps = true, invertY = false, samplingMode = 3, type = 0, useSRGBBuffer = false) {\n        return new RawTexture(data, width, height, 5, sceneOrEngine, generateMipMaps, invertY, samplingMode, type, 1, useSRGBBuffer);\n    }\n    /**\n     * Creates a R texture from some data.\n     * @param data Define the texture data\n     * @param width Define the width of the texture\n     * @param height Define the height of the texture\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\n     * @returns the R texture\n     */\n    static CreateRTexture(data, width, height, sceneOrEngine, generateMipMaps = true, invertY = false, samplingMode = Texture.TRILINEAR_SAMPLINGMODE, type = 1) {\n        return new RawTexture(data, width, height, 6, sceneOrEngine, generateMipMaps, invertY, samplingMode, type);\n    }\n    /**\n     * Creates a R storage texture from some data.\n     * @param data Define the texture data\n     * @param width Define the width of the texture\n     * @param height Define the height of the texture\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\n     * @returns the R texture\n     */\n    static CreateRStorageTexture(data, width, height, sceneOrEngine, generateMipMaps = true, invertY = false, samplingMode = Texture.TRILINEAR_SAMPLINGMODE, type = 1) {\n        return new RawTexture(data, width, height, 6, sceneOrEngine, generateMipMaps, invertY, samplingMode, type, 1);\n    }\n}\n//# sourceMappingURL=rawTexture.js.map"], "names": ["RawTexture", "Texture", "data", "width", "height", "format", "scene<PERSON><PERSON><PERSON><PERSON><PERSON>", "generateMipMaps", "invertY", "samplingMode", "type", "creationFlags", "useSRGBBuffer", "waitDataToBeReady", "rawTexture"], "mappings": "wCAOO,MAAMA,UAAmBC,CAAQ,CAmBpC,YAAYC,EAAMC,EAAOC,EAIzBC,EAAQC,EAAeC,EAAkB,GAAMC,EAAU,GAAOC,EAAe,EAAGC,EAAO,EAAGC,EAAeC,EAAeC,EAAmB,CACzI,MAAM,KAAMP,EAAe,CAACC,EAAiBC,EAAS,OAAW,OAAW,OAAW,OAAW,OAAW,OAAW,OAAW,OAAWG,CAAa,EAC3J,KAAK,OAASN,EACT,KAAK,UAGN,CAAC,KAAK,QAAQ,MAAM,6BAA+BK,IAAS,IAC5DD,EAAe,GAEf,CAAC,KAAK,QAAQ,MAAM,iCAAmCC,IAAS,IAChED,EAAe,GAEnB,KAAK,SAAW,KAAK,QAAQ,iBAAiBP,EAAMC,EAAOC,EAAQC,EAAQE,EAAiBC,EAASC,EAAc,KAAMC,EAAMC,GAAiB,EAAGC,GAAiB,EAAK,EACzK,KAAK,MAAQX,EAAQ,kBACrB,KAAK,MAAQA,EAAQ,kBACrB,KAAK,gBAAkB,CAAC,CAACY,GAAqB,CAACX,EAClD,CAKD,OAAOA,EAAM,CACT,KAAK,WAAY,EAAC,iBAAiB,KAAK,SAAUA,EAAM,KAAK,SAAS,OAAQ,KAAK,SAAS,QAAS,KAAM,KAAK,SAAS,KAAM,KAAK,SAAS,cAAc,EAC3J,KAAK,gBAAkB,EAC1B,CAKD,OAAQ,CACJ,GAAI,CAAC,KAAK,SACN,OAAO,MAAM,QAEjB,MAAMY,EAAa,IAAId,EAAW,KAAM,KAAK,QAAO,EAAG,MAAO,KAAK,QAAS,EAAC,OAAQ,KAAK,OAAQ,KAAK,SAAQ,EAAI,KAAK,SAAS,gBAAiB,KAAK,SAAU,KAAK,aAAc,KAAK,SAAS,KAAM,KAAK,SAAS,eAAgB,KAAK,cAAc,EACzP,OAAAc,EAAW,SAAW,KAAK,SAC3B,KAAK,SAAS,sBACPA,CACV,CACD,SAAU,CACN,OAAO,MAAM,QAAO,GAAM,CAAC,KAAK,eACnC,CAYD,OAAO,uBAAuBZ,EAAMC,EAAOC,EAAQE,EAAeC,EAAkB,GAAMC,EAAU,GAAOC,EAAe,EAAG,CACzH,OAAO,IAAIT,EAAWE,EAAMC,EAAOC,EAAQ,EAAGE,EAAeC,EAAiBC,EAASC,CAAY,CACtG,CAYD,OAAO,4BAA4BP,EAAMC,EAAOC,EAAQE,EAAeC,EAAkB,GAAMC,EAAU,GAAOC,EAAe,EAAG,CAC9H,OAAO,IAAIT,EAAWE,EAAMC,EAAOC,EAAQ,EAAGE,EAAeC,EAAiBC,EAASC,CAAY,CACtG,CAYD,OAAO,mBAAmBP,EAAMC,EAAOC,EAAQE,EAAeC,EAAkB,GAAMC,EAAU,GAAOC,EAAe,EAAG,CACrH,OAAO,IAAIT,EAAWE,EAAMC,EAAOC,EAAQ,EAAGE,EAAeC,EAAiBC,EAASC,CAAY,CACtG,CAeD,OAAO,iBAAiBP,EAAMC,EAAOC,EAAQE,EAAeC,EAAkB,GAAMC,EAAU,GAAOC,EAAe,EAAGC,EAAO,EAAGC,EAAgB,EAAGC,EAAgB,GAAO,CACvK,OAAO,IAAIZ,EAAWE,EAAMC,EAAOC,EAAQ,EAAGE,EAAeC,EAAiBC,EAASC,EAAcC,EAAMC,EAAeC,CAAa,CAC1I,CAgBD,OAAO,kBAAkBV,EAAMC,EAAOC,EAAQE,EAAeC,EAAkB,GAAMC,EAAU,GAAOC,EAAe,EAAGC,EAAO,EAAGC,EAAgB,EAAGC,EAAgB,GAAOC,EAAoB,GAAO,CACnM,OAAO,IAAIb,EAAWE,EAAMC,EAAOC,EAAQ,EAAGE,EAAeC,EAAiBC,EAASC,EAAcC,EAAMC,EAAeC,EAAeC,CAAiB,CAC7J,CAcD,OAAO,yBAAyBX,EAAMC,EAAOC,EAAQE,EAAeC,EAAkB,GAAMC,EAAU,GAAOC,EAAe,EAAGC,EAAO,EAAGE,EAAgB,GAAO,CAC5J,OAAO,IAAIZ,EAAWE,EAAMC,EAAOC,EAAQ,EAAGE,EAAeC,EAAiBC,EAASC,EAAcC,EAAM,EAAGE,CAAa,CAC9H,CAaD,OAAO,eAAeV,EAAMC,EAAOC,EAAQE,EAAeC,EAAkB,GAAMC,EAAU,GAAOC,EAAeR,EAAQ,uBAAwBS,EAAO,EAAG,CACxJ,OAAO,IAAIV,EAAWE,EAAMC,EAAOC,EAAQ,EAAGE,EAAeC,EAAiBC,EAASC,EAAcC,CAAI,CAC5G,CAaD,OAAO,sBAAsBR,EAAMC,EAAOC,EAAQE,EAAeC,EAAkB,GAAMC,EAAU,GAAOC,EAAeR,EAAQ,uBAAwBS,EAAO,EAAG,CAC/J,OAAO,IAAIV,EAAWE,EAAMC,EAAOC,EAAQ,EAAGE,EAAeC,EAAiBC,EAASC,EAAcC,EAAM,CAAC,CAC/G,CACL", "x_google_ignoreList": [0]}